{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        "files.associations": {
            "clock_config.h": "c",
            "appqueue.h": "c",
            "rlin30.h": "c",
            "linflexd_uart_driver.h": "c",
            "string.h": "c",
            "rtc.h": "c",
            "systemapi.h": "c",
            "event.h": "c",
            "apptask.h": "c",
            "limits": "c",
            "logapi.h": "c",
            "canif_cbk.h": "c",
            "navigate.h": "c"
        },
        // CMake公共配置
        "cmake.configureArgs": [
            "-DCMAKE_TOOLCHAIN_FILE=${workspaceFolder}/cmake/gcc.cmake",
            "-DARM_CPU=cortex-m33",
            "-DCMAKE_C_COMPILER_WORKS=ON",
            "-DCMAKE_CXX_COMPILER_WORKS=ON",
            "-DCMAKE_C_COMPILER=${env:ARM_TOOLCHAIN}/bin/arm-none-eabi-gcc",
            "-DCMAKE_CXX_COMPILER=${env:ARM_TOOLCHAIN}/bin/arm-none-eabi-g++",
            "-DCMAKE_ASM_COMPILER=${env:ARM_TOOLCHAIN}/bin/arm-none-eabi-gcc"
        ],
        "cmake.buildDirectory": "${workspaceFolder}/build",
        "cmake.buildToolArgs": [],
        "cmake.sourceDirectory": "${workspaceFolder}",
        "cmake.generator": "Ninja",
        "cmake.configureOnOpen": false, // 打开文件时不自动配置
        "cmake.ignoreKitEnv": true, // 忽略环境变量,表示使用配置文件中的配置
        "cmake.showSystemKits": true, // 显示系统环境工具包
        "cmake.enabledOutputParsers": [
            "cmake",
            "gcc",
            "gnuld",
            "msvc",
            "ghs",
            "diab",
            "iar"
        ],
        "clangd.arguments": [
            // 在后台自动分析文件（基于 compile_commands）
            "--background-index",
            // 标记 compile_commands.json 文件的目录位置
            "--compile-commands-dir=${workspaceFolder}/build",
            // 同时开启的任务数量
            "-j=12",
            // 全局补全（会自动补充头文件）
            "--all-scopes-completion",
            // 更详细的补全内容
            "--completion-style=detailed",
            // Include what you use（根据使用情况插入头文件）
            "--header-insertion=iwyu",
            // PCH 优化的位置（memory 表示存储在内存中）
            "--pch-storage=memory",
            // clang-format 默认样式，当没有 .clang-format 文件时应用
            "--fallback-style=WebKit",
            // 美化 JSON 输出
            "--pretty",
            // 启用 clang-tidy 诊断
            "--clang-tidy",
            // 标准库支持
            "--query-driver=${env:ARM_TOOLCHAIN}/bin/arm-none-eabi-*"
        ],
        "clangd.fallbackFlags": [
            "-std=c99",
        ],
        "clangd.serverCompletionRanking": true,
    }
}