#include "event.h"
#include "flexcan_driver.h"
#include "can_config.h"
#include "osif.h"
#include "projdefs.h"
#include "printf.h"
#include "FreeRTOS.h"
#include "status.h"
#include "task.h"
#include "r_can.h"
#include "testTask.h"
#include "LogApi.h"

#define TEST_TASK_STACK_SIZE 256
#define TEST_TASK_PRIORITY   1

TaskHandle_t TestTask_Handle = NULL;

#define CAN_INST   (3)
#define TX_MAILBOX (10UL)
#define TX_MSG_ID  (3UL)

TaskHandle_t sendTaskHandle    = NULL;
TaskHandle_t receiveTaskHandle = NULL;

const flexcan_enhance_rx_fifo_filter_table_t enhanceFifoFilterTable[] = {
    {
        .isRemoteFrame   = false,
        .isExtendedFrame = false,
        .filterScheme    = FLEXCAN_ENAHNCE_RXFIFO_FSCH_FILTER_MASK,
        .filter_table.scheme_filter_mask =
            {

                .filter = 0x1,
                .mask   = 0x7ff,
            },
    },
    // {
    //     .isRemoteFrame   = false,
    //     .isExtendedFrame = false,
    //     .filterScheme    = FLEXCAN_ENAHNCE_RXFIFO_FSCH_FILTER_MASK,
    //     .filter_table.scheme_filter_mask =
    //         {
    //             .filter = 0x4FEU,
    //             .mask   = 0x7FFU,
    //         },
    // },
    // {
    //     .isRemoteFrame   = false,
    //     .isExtendedFrame = true,
    //     .filterScheme    = FLEXCAN_ENAHNCE_RXFIFO_FSCH_FILTER_MASK,
    //     .filter_table.scheme_filter_mask =
    //         {
    //             .filter = 0x0CF003FEU,
    //             .mask   = 0x1FFFFFFFU,
    //         },
    // },
    // {
    //     .isRemoteFrame   = false,
    //     .isExtendedFrame = true,
    //     .filterScheme    = FLEXCAN_ENAHNCE_RXFIFO_FSCH_FILTER_MASK,
    //     .filter_table.scheme_filter_mask =
    //         {
    //             .filter = 0x0CE104FEU,
    //             .mask   = 0x1FFFFFFFU,
    //         },
    // },
    // {
    //     .isRemoteFrame   = false,
    //     .isExtendedFrame = false,
    //     .filterScheme    = FLEXCAN_ENAHNCE_RXFIFO_FSCH_RANGE,
    //     .filter_table.scheme_range_filter =
    //         {
    //             .filterLow  = 0x200U,
    //             .filterHigh = 0x210U,
    //         },
    // },
};

const flexcan_data_info_t rxMbStdInfo = {
    .msg_id_type = FLEXCAN_MSG_ID_STD,
    .data_length = 8,
    .fd_enable   = false,
    .fd_padding  = 0,
    .enable_brs  = false,
    .is_remote   = false,
};

#define STACK_SIZE 256
#define QUEUE_SIZE 10

QueueHandle_t xQueue;

void vTaskSend(void *pvParameters)
{
    PRINTF("TaskSend started\r\n");
    int32_t                   offset = 0;
    status_t                  status = STATUS_SUCCESS;
    flexcan_msgbuff_t         txMsg;
    const flexcan_data_info_t txMbStdInfo = {
        .msg_id_type = FLEXCAN_MSG_ID_STD,
        .data_length = 8,
        .fd_enable   = false,
        .fd_padding  = 0,
        .enable_brs  = false,
        .is_remote   = false,
    };

    txMsg.dataLen = 8;
    txMsg.msgId   = TX_MSG_ID;

    bool   result  = false;
    UINT8  channel = CAN_CHANNEL_4;
    UINT32 canId   = 0x321;
    UINT8  canDlc  = 8;
    UINT8  buf[8]  = {0, 1, 2, 3, 4, 5, 6, 7};
    while (1)
    {
        // 原来demo的发送方式
        // status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, TX_MAILBOX);
        // if (status != STATUS_BUSY)
        // {
        //     for (int i = 0; i < 8; i++)
        //     {
        //         txMsg.data[i] = offset + i;
        //     }
        //     status = FLEXCAN_DRV_ConfigTxMb(CAN_INST, TX_MAILBOX, &txMbStdInfo, txMsg.msgId);
        //     if (STATUS_SUCCESS != status)
        //     {
        //         return;
        //     }
        //     /* Send the information via CAN */
        //     status = FLEXCAN_DRV_Send(CAN_INST, TX_MAILBOX, &txMbStdInfo, txMsg.msgId, txMsg.data);
        //     if (status != STATUS_SUCCESS)
        //     {
        //         PRINTF("Send error\r\n");
        //     }
        //     else
        //     {
        //         PRINTF("Send success\r\n");
        //         PRINTF("Tx length= %d, id = %x\r\n", txMsg.dataLen, txMsg.msgId);
        //         for (int i = 0; i < txMsg.dataLen; i++)
        //         {
        //             PRINTF("%x ", txMsg.data[i]);
        //         }
        //         PRINTF("\r\n");
        //     }
        // }
        // else
        // {
        //     PRINTF("Send busy\r\n");
        // }
        // vTaskDelay(100 / portTICK_PERIOD_MS);  // send data every 100ms

        result = MCUCanSendData(channel, canId, canDlc, buf);
        if (result)
        {
            // PRINTF("Send success\r\n");
            PRINTF("Send success\r\n");
            // PRINTF("Tx length= %d, id = %x\r\n", canDlc, canId);
            PRINTF("Tx length= %d, id = %x\r\n", canDlc, canId);
            for (int i = 0; i < canDlc; i++)
            {
                // PRINTF("%x ", buf[i]);
                PRINTF("%x ", buf[i]);
            }
            PRINTF("\r\n");
        }
        else
        {
            PRINTF("Send error\r\n");
        }
        vTaskDelay(200);
    }
}

// 轮询邮箱+enhancefifo接收
void pollMailboxEnhanceFifoReceive(UINT8 channel)
{
    status_t          status = STATUS_SUCCESS;
    flexcan_msgbuff_t rxMsg;

    // 这里使用enhanceFifo接收，未配置滤波表表示接收所有数据
    // 轮询邮箱接收
    for (int can_controller_mailbox = 0; can_controller_mailbox < flexcan1_InitConfig.max_num_mb; can_controller_mailbox++)
    {
        status = FLEXCAN_DRV_GetTransferStatus(channel, (uint32_t)can_controller_mailbox);
        if (status != STATUS_BUSY)
        {
            // 增强fifo接收
            status |= FLEXCAN_DRV_EnhanceRxFifo(channel, &rxMsg);
            // 普通fifo接收
            // status |= FLEXCAN_DRV_RxFifo(channel, &rxMsg);
            // status = FLEXCAN_DRV_Receive(channel, TX_MAILBOX, &rxMsg);
            if (status == STATUS_SUCCESS)
            {
                PRINTF("\r\nRx length= %d, id = %x\r\n", rxMsg.dataLen, rxMsg.msgId);
                for (int i = 0; i < rxMsg.dataLen; i++)
                {
                    PRINTF("%x ", rxMsg.data[i]);
                }
                PRINTF("\r\n");
            }
            // else
            // {
            //     PRINTF("Receive error\r\n");
            // }
        }
    }
}

// fifo接收
void FifoReceive(UINT8 channel)
{
    status_t          status = STATUS_SUCCESS;
    flexcan_msgbuff_t rxMsg;

    status = FLEXCAN_DRV_GetTransferStatus(channel, 0);
    if (status != STATUS_BUSY)
    {
        status = FLEXCAN_DRV_RxFifo(channel, &rxMsg);
        if (status == STATUS_SUCCESS)
        {
            PRINTF("\r\nRx length= %d, id = %x\r\n", rxMsg.dataLen, rxMsg.msgId);
        }
    }
}

// 轮询邮箱+普通接收
void pollMailboxNormalReceive(UINT8 channel)
{
    status_t          status = STATUS_SUCCESS;
    flexcan_msgbuff_t rxMsg;

    // 轮询邮箱接收
    for (int can_controller_mailbox = 0; can_controller_mailbox < flexcan1_InitConfig.max_num_mb; can_controller_mailbox++)
    {
        // 配置接收邮箱
        // status |= FLEXCAN_DRV_ConfigRxMb(channel, can_controller_mailbox, &rxMbStdInfo, 0x0);
        // if (status != STATUS_SUCCESS)
        // {
        //     PRINTF("ConfigRxMb error\r\n");
        //     return;
        // }
        status |= FLEXCAN_DRV_GetTransferStatus(channel, (uint32_t)can_controller_mailbox);
        if (status != STATUS_BUSY)
        {
            status |= FLEXCAN_DRV_Receive(channel, can_controller_mailbox, &rxMsg);
            if (status != STATUS_SUCCESS)
            {
                PRINTF("Receive error\r\n");
            }
            else
            {
                PRINTF("\r\nRx length= %d, id = %x\r\n", rxMsg.dataLen, rxMsg.msgId);
                for (int i = 0; i < rxMsg.dataLen; i++)
                {
                    PRINTF("%x ", rxMsg.data[i]);
                }
                PRINTF("\r\n");
            }
        }
    }
}

// 回调函数，当收到消息时回调触发，输出消息
void printTheRxMsg(uint8_t instance, flexcan_event_type_t eventType, uint32_t buffIdx, flexcan_state_t *flexcanState)
{
    switch (eventType)
    {
    case FLEXCAN_EVENT_RX_COMPLETE:
    case FLEXCAN_EVENT_RXFIFO_COMPLETE:
        PRINTF("Receive complete\r\n");
        flexcan_msgbuff_t rxfifoMsg;
        status_t          status;
        UINT32            channel = instance;

        status = FLEXCAN_DRV_RxFifo(channel, &rxfifoMsg);
        if (status == STATUS_SUCCESS)
        {
            PRINTF("\r\nRx length= %d, id = %x\r\n", rxfifoMsg.dataLen, rxfifoMsg.msgId);
            for (int i = 0; i < rxfifoMsg.dataLen; i++)
            {
                PRINTF("%x ", rxfifoMsg.data[i]);
            }
        }
    case FLEXCAN_EVENT_RXFIFO_WARNING:
    case FLEXCAN_EVENT_RXFIFO_OVERFLOW:
    case FLEXCAN_EVENT_TX_COMPLETE:
    case FLEXCAN_EVENT_ENHANCE_RXFIFO_AVAILABLEDATA:
    case FLEXCAN_EVENT_ENHANCE_RXFIFO_WATERMARK:
    case FLEXCAN_EVENT_ENHANCE_RXFIFO_OVERFLOW:
    case FLEXCAN_EVENT_WAKEUP_TIMEOUT:
    case FLEXCAN_EVENT_WAKEUP_MATCH:
    case FLEXCAN_EVENT_SELF_WAKEUP:
    case FLEXCAN_EVENT_DMA_COMPLETE:
    case FLEXCAN_EVENT_DMA_ERROR:
    case FLEXCAN_EVENT_ERROR:
        break;
    }
}

void vTaskReceive(void *pvParameters)
{
    PRINTF("TaskReceive started\r\n");
    uint8_t           fifoFrameCnt = 0;
    status_t          status       = STATUS_SUCCESS;
    flexcan_msgbuff_t rxMsg;
    UINT8             channel        = CAN_CHANNEL_1;
    channel                          = Can_GetCanInstance(channel);
    flexcan_id_table_t fifoIdTable[] = {
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x000U,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x123,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x123,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x123,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x123,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x123,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x123,
        },
        {
            .isRemoteFrame   = false,
            .isExtendedFrame = false,
            .id              = 0x7FFU,
        },
    };

    // 注册回调函数
    // FLEXCAN_DRV_InstallEventCallback(channel, printTheRxMsg, NULL);

    // 配置rxfifo滤波表
    // FLEXCAN_DRV_ConfigRxFifo(channel, FLEXCAN_RX_FIFO_ID_FORMAT_A, fifoIdTable);

    // 设置接收掩码类型
    FLEXCAN_DRV_SetRxMaskType(channel, FLEXCAN_RX_MASK_GLOBAL);  // 这里的掩码类型是全局掩码，表示作用于所有邮箱

    // 设置接收掩码
    FLEXCAN_DRV_SetRxFifoGlobalMask(channel, FLEXCAN_MSG_ID_STD, 0x000U);  // idtype标准帧，id为0x

    FLEXCAN_DRV_SetRxFifoGlobalMask(channel, FLEXCAN_MSG_ID_EXT, 0x000U);  // 表示接收所有扩展帧

    while (1)
    {
        // pollMailboxEnhanceFifoReceive(channel);
        pollMailboxNormalReceive(channel);
        // FifoReceive(channel);
        // Can_MainFunction_Read();
        vTaskDelay(100);
    }
}

void StartTestTask(void)
{
    BaseType_t result = pdPASS;

    status_t status = STATUS_SUCCESS;
    // PRINTF("<----Flexcan enhanced fifo demo---->\r\n");
    PRINTF("TestTask started\r\n");

    xQueue = xQueueCreate(QUEUE_SIZE, sizeof(uint8_t));
    // status |= xTaskCreate(vTaskSend, "vTaskSend", STACK_SIZE, NULL, 1, &sendTaskHandle);
    // PRINTF("Task create status = %d\r\n", status);
    status |= xTaskCreate(vTaskReceive, "vTaskReceive", STACK_SIZE, NULL, 1, &receiveTaskHandle);
    if (pdFAIL == result)
    {
        PRINTF("StartTestTask() create the TestTask failed.\r\n");
    }
    else
    {
        PRINTF("StartTestTask() create the TestTask success.\r\n");
    }
}