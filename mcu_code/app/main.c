/*
 *  Copyright 2020-2023 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 *
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 *
 * @file main.c
 * @brief    
 *
 */

/********************头文件声明********************/
#include <string.h>
#include "sdk_project_config.h"
#include "FreeRTOS.h"
#include "HrApi.h"
#include "LogApi.h"
#include "event.h"
#include "portable.h"
#include "rlin30.h"
#include "LedTask.h"
#include "CanTask.h"
#include "BatTask.h"
#include "BtTask.h"
#include "DiagTask.h"
#include "IpcTask.h"
#include "LogTask.h"
#include "PmTask.h"
#include "UpdateTask.h"
#include "CommonTask.h"
#include "CANDataUploadTask.h"
#include "NvApi.h"
#include "CanMsgApi.h"
#include "etmr_common.h"
#include "etmr_config.h"
#include "airbag.h"
#include "SystemApi.h"

/********************宏定义********************/
#define RTC_INST        0
#define WAKUP_INST      0

/********************外部全局变量********************/

/********************外部函数接口********************/
extern void TaskQueueInit(void);

/********************内部全局变量********************/
StackType_t uxTimerTaskStack[configTIMER_TASK_STACK_DEPTH];

/********************内部函数接口********************/
static void AppTaskCreate(void);
static void MCUStartUpInit(void);
static void Board_Init(void);
void Rtc_Init(void);

int main(void)
{
    status_t status = STATUS_SUCCESS;
    BaseType_t result = pdPASS;

    Board_Init();
    MCUStartUpInit();
    AppTaskCreate();

    if (pdPASS == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Start Scheduler App Tasks. %.2fKB\r\n", xPortGetFreeHeapSize()/1024.0);
        vTaskStartScheduler();
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Start Scheduler App Tasks Failed.\r\n");
    }

    while (1)
    {
        if (status != STATUS_SUCCESS)
        {
            break;
        }
    }
}

static void Board_Init(void)
{
    CLOCK_SYS_Init(g_clockManConfigsArr, CLOCK_MANAGER_CONFIG_CNT, g_clockManCallbacksArr, CLOCK_MANAGER_CALLBACK_CNT);
    CLOCK_SYS_UpdateConfiguration(CLOCK_MANAGER_ACTIVE_INDEX, CLOCK_MANAGER_POLICY_AGREEMENT);
    PINS_DRV_Init(NUM_OF_CONFIGURED_PINS0, g_pin_mux_InitConfigArr0);
    DMA_DRV_Init(&dmaState, &dmaController_InitConfig, dmaChnState, dmaChnConfigArray, NUM_OF_CONFIGURED_DMA_CHANNEL);

#ifdef CAN_DATA_UPLOAD_MASTER_MODE
    SPI_DRV_MasterInit(SPI_INST_HANDLE, &lpspi_MasterConfig_2Mbps_State, &lpspi_MasterConfig_2Mbps);
#else
    SPI_DRV_SlaveInit(SPI_INST_HANDLE, &lpspi_SlaveConfig0_State, &lpspi_SlaveConfig0);
#endif

    LINFlexD_UART_DRV_Init(UART_PORT_BLE, &linflexd0_uart_config_115200bps_State, &linflexd0_uart_config_115200bps);
    LINFlexD_UART_DRV_Init(UART_PORT_IPC, &linflexd3_uart_config_576000bps_State, &linflexd3_uart_config_576000bps);
    
    ADC_DRV_ConfigConverter(0, &adc_config0);
    FLEXCAN_DRV_Init(1, &flexcan1_InitConfig_State, &flexcan1_InitConfig);
    FLEXCAN_DRV_Init(2, &flexcan2_InitConfig_State, &flexcan2_InitConfig);
    FLEXCAN_DRV_Init(3, &flexcan3_InitConfig_State, &flexcan3_InitConfig);
    FLEXCAN_DRV_Init(5, &flexcan5_InitConfig_State, &flexcan5_InitConfig);

    CAN1->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;
    CAN2->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;
    CAN3->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;
    CAN5->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;

    I2C_DRV_MasterInit(2, &I2C_MasterConfig0, &I2C_MasterConfig0_State);
    WDG_DRV_Init(0, &wdg_config0);
    Rtc_Init();

#if (PWM_SIM_ENABLED == 1)
    eTMR_DRV_Init(ETMR_PWM_SIM_INST, &ETMR_CM_Config1, &ETMR_CM_Config1_State);
    eTMR_DRV_InitPwm(ETMR_PWM_SIM_INST,&ETMR_PWM_Config1);
#endif

    eTMR_DRV_Init(ETMR_AIRBAG_PWM_INST, &ETMR_CM_Config0, &ETMR_CM_Config0_State);
    eTMR_DRV_InitInputCapture(ETMR_AIRBAG_PWM_INST, &ETMR_IC_Config0);
    HCU_DRV_Init(&hcu_config0, &hcu_config0_State);
    FLASH_DRV_Init(0, &flash_config0, &flash_config0_State);
    UTILITY_PRINT_Init();
    HCU_DRV_Init(&hcu_config0, &hcu_config0_State);
    POWER_SYS_Init(&powerConfigsArr, POWER_MANAGER_CONFIG_CNT, NULL, POWER_MANAGER_CALLBACK_CNT);
    INT_SYS_ConfigInit();
}

static void AppTaskCreate(void)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "App Task Create entered.\r\n");

    if (STATUS_SUCCESS != OSIF_MutexCreate(&g_mutexCANLog))
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Create g_mutexCANLog Failed!");
    }
    else
    {
        OSIF_MutexUnlock(&g_mutexCANLog);
    }

    StartLOGTask();
#if (1 == BT_TASK_ENABLED)
    StartBTTask();
#endif
    StartBATTask();
    StartLEDTask();
    StartCommonTask();
    StartCANTask();
    StartIPCTask();
    StartDIAGTask();
    StartUpdateTask();
    StartPMTask();
    StartCANDataUploadTask();
}

static void MCUStartUpInit(void)
{
    McuHrDeviceInit();
    LogInitRamData();
    McuPowerOnLog();
    TaskQueueInit();
    NvMcuPowerOnInitNvData();
    ReadResetWakeSource(); //需要在Nv初始化后执行
    RefreshUpgradeStatus(USER_UPGRADE_STATE_ADDR,0);
    RefreshUpgradeStatus(USER_SOFTRESET_COUNT_ADDR,0); 
    TaskInit();

    eTMR_DRV_Enable(ETMR_AIRBAG_PWM_INST);
    eTMR_DRV_EnableChnInt(ETMR_AIRBAG_PWM_INST,AIRBAG_CAPTURE_CHAN);

#if (PWM_SIM_ENABLED == 1)
    eTMR_DRV_Enable(ETMR_PWM_SIM_INST);
    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "StartUp Init finished airbag sig detect init.\r\n");
#endif

    GlobalParamsInit();
}

void vApplicationGetIdleTaskMemory(StaticTask_t **ppxIdleTaskTCBBuffer,
                                   StackType_t  **ppxIdleTaskStackBuffer,
                                   uint32_t      *pulIdleTaskStackSize)
{
    static StaticTask_t xIdleTaskTCB;
    static StackType_t  uxIdleTaskStack[configMINIMAL_STACK_SIZE];
    *ppxIdleTaskTCBBuffer = &xIdleTaskTCB;
    *ppxIdleTaskStackBuffer = uxIdleTaskStack;
    *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
}

void vApplicationGetTimerTaskMemory(StaticTask_t **ppxTimerTaskTCBBuffer,
                                    StackType_t  **ppxTimerTaskStackBuffer,
                                    uint32_t      *pulTimerTaskStackSize)
{
    static StaticTask_t xTimerTaskTCB;
    *ppxTimerTaskTCBBuffer = &xTimerTaskTCB;
    *ppxTimerTaskStackBuffer = uxTimerTaskStack;
    *pulTimerTaskStackSize = configTIMER_TASK_STACK_DEPTH;
}


void Rtc_Init(void)
{
    rtc_timedate_t defaultTime;
    RTC_DRV_Init(RTC_INST, &rtc_config0);
    RTC_DRV_GetCurrentTimeDate(RTC_INST, &defaultTime);

    if(defaultTime.year < PROJECT_INITIATE_YEAR)
    {
        defaultTime.year = 2010;
        defaultTime.month = 1;
        defaultTime.day = 1;
        defaultTime.hour = 8;
        defaultTime.minutes = 0;
        defaultTime.seconds = 0;
        RTC_DRV_SetTimeDate(RTC_INST, &defaultTime);
    }

    RTC_DRV_StartCounter(RTC_INST);
}
