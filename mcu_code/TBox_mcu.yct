{"project": {"clangd": {"enable": false, "path": ""}, "cmakeGcc": {"optimization": "-O1", "funcSection": true, "dataSection": true, "compOthers": "", "removeSection": true, "linkOthers": "", "charSigned": false, "bitunsigned": false, "oninline": false, "floatABI": "-mfloat-abi=hard", "library": "-specs=nosys.specs", "debuglevel": "-g", "gcov": false, "debugformat": "", "nocommon": false, "ffreestanding": false, "fnobuiltin": false, "singleprecisionconstant": false, "flto": false, "fnomoveloopinvariants": false, "printremoveSection": false, "noStartup": true, "suppress": {"variable": true, "parameter": true, "function": true, "setVariable": true, "result": true, "uninitialized": true, "compare": true, "aliasing": true, "pragmas": true, "format": true}}, "cmakeGeneral": {"genbin": false, "genhex": true, "genlst": false, "gens19": false, "userDefine": ""}, "debug": {"enable": true, "servertype": "jlink", "interface": "swd", "runToEntryPoint": "main", "gdbPath": "E:\\SEGGER\\JLink\\JLinkGDBServerCL.exe", "svdPath": "${workspaceFolder}/.vscode/chip.svd"}, "family": "YTM32B1ME0", "flexLink": true, "generate": {"folder": "board", "keepUserCode": true, "initBoard": true, "initBoardFuncName": "Board_Init", "enableVariants": false, "variantName": "", "variantList": []}, "git": {"enable": false, "path": ".cache,build,.vscode"}, "heapSize": "200", "ideDeviceName": "YTM32B1ME05G0MLLT", "initList": [{"funcName": "CLOCK_SYS_Init  ", "value": "CLOCK_SYS_Init(g_clockManConfigsArr,CLOCK_MANAGER_CONFIG_CNT,g_clockManCallbacksArr,CLOCK_MANAGER_CALLBACK_CNT);", "id": "07eb80f4-8c1f-4019-bbce-54c07c6d9c3e", "errorHandler": false, "successLabel": "0"}, {"funcName": "CLOCK_SYS_UpdateConfiguration", "value": "CLOCK_SYS_UpdateConfiguration(CLOCK_MANAGER_ACTIVE_INDEX,CLOCK_MANAGER_POLICY_AGREEMENT);", "id": "cf7cc048-3e8f-491f-a2da-ed36b03af9ac", "errorHandler": false, "successLabel": "0"}, {"funcName": "DMA_DRV_Init    ", "value": "DMA_DRV_Init(&dmaState,&dmaController_InitConfig,dmaChnState,dmaChnConfigArray,NUM_OF_CONFIGURED_DMA_CHANNEL);", "id": "3f5e2a6c-9504-445f-8c4b-b6422c831849", "errorHandler": false, "successLabel": "0"}, {"funcName": "SPI_DRV_MasterInit", "value": "SPI_DRV_MasterInit(3,&lpspi_MasterConfig_2Mbps_State,&lpspi_MasterConfig_2Mbps);", "id": "e7880170-15f8-46fc-8b8d-22090171b8a9", "errorHandler": false, "successLabel": "0"}, {"funcName": "LINFlexD_UART_DRV_Init", "value": "LINFlexD_UART_DRV_Init(3,&linflexd3_uart_config_921600bps_State,&linflexd3_uart_config_921600bps);", "id": "63f9017a-a3a8-40b6-a870-397e617fc227", "errorHandler": false, "successLabel": "0"}, {"funcName": "LINFlexD_UART_DRV_Init", "value": "LINFlexD_UART_DRV_Init(0,&linflexd0_uart_config_115200bps_State,&linflexd0_uart_config_115200bps);", "id": "ec1a5579-0f60-47d5-8283-0f3928f41e12", "errorHandler": false, "successLabel": "0"}, {"funcName": "LINFlexD_DRV_Init", "value": "LINFlexD_DRV_Init(4,&linflexd_lin_config0,&linflexd_lin_config0_State);", "id": "10d354be-3032-4286-92b9-d0b49ec0345d", "errorHandler": false, "successLabel": "0"}, {"funcName": "ADC_DRV_ConfigConverter", "value": "ADC_DRV_ConfigConverter(0,&adc_config0);", "id": "84b5e076-18f1-4c7d-a917-38988883459a", "errorHandler": false, "successLabel": "0"}, {"funcName": "FLEXCAN_DRV_Init", "value": "FLEXCAN_DRV_Init(1,&flexcan1_InitConfig_State,&flexcan1_InitConfig);", "id": "89860f8f-9745-45d3-8cbc-cd11b0ffec83", "errorHandler": false, "successLabel": "0"}, {"funcName": "FLEXCAN_DRV_Init", "value": "FLEXCAN_DRV_Init(2,&flexcan2_InitConfig_State,&flexcan2_InitConfig);", "id": "ebc92ebb-6e11-4b6d-a88b-4dfb6004adc2", "errorHandler": false, "successLabel": "0"}, {"funcName": "FLEXCAN_DRV_Init", "value": "FLEXCAN_DRV_Init(3,&flexcan3_InitConfig_State,&flexcan3_InitConfig);", "id": "b1838eb6-b039-400f-a9b3-6175be1fdcfb", "errorHandler": false, "successLabel": "0"}, {"funcName": "FLEXCAN_DRV_Init", "value": "FLEXCAN_DRV_Init(5,&flexcan5_InitConfig_State,&flexcan5_InitConfig);", "id": "53da3005-3fa3-4c33-8716-4a8c04983a8c", "errorHandler": false, "successLabel": "0"}, {"funcName": "I2C_DRV_MasterInit", "value": "I2C_DRV_MasterInit(2,&I2C_MasterConfig0,&I2C_MasterConfig0_State);", "id": "ab714244-ff0a-42af-889b-e0cdf1470b17", "errorHandler": false, "successLabel": "0"}, {"funcName": "RTC_DRV_Init    ", "value": "RTC_DRV_Init(0,&rtc_config0);", "id": "463ad000-5fdf-4684-bde9-e327edc6da13", "errorHandler": false, "successLabel": "0"}, {"funcName": "UTILITY_PRINT_Init", "value": "UTILITY_PRINT_Init();", "id": "2375c450-6c46-4f8e-b84d-e0e4f584b40d", "errorHandler": false, "successLabel": "0"}, {"funcName": "POWER_SYS_Init  ", "value": "POWER_SYS_Init(&powerConfigsArr,POWER_MANAGER_CONFIG_CNT,&powerStaticCallbacksConfigsArr,POWER_MANAGER_CALLBACK_CNT);", "id": "08bea0e1-2c61-46f0-84d3-45fe723e4fdc", "errorHandler": false, "successLabel": "0"}, {"funcName": "eTMR_DRV_Init   ", "value": "eTMR_DRV_Init(2,&ETMR_CM_Config0,&ETMR_CM_Config0_State);", "id": "f3d709cd-4e36-4a1a-b456-ba124f594505", "errorHandler": false, "successLabel": "0"}, {"funcName": "eTMR_DRV_InitInputCapture", "value": "eTMR_DRV_InitInputCapture(2,&ETMR_IC_Config0);", "id": "303f89c3-c5fa-46c6-b8b3-78793a0a5110", "errorHandler": false, "successLabel": "0"}, {"funcName": "PINS_DRV_Init   ", "value": "PINS_DRV_Init(NUM_OF_CONFIGURED_PINS0,g_pin_mux_InitConfigArr0);", "id": "3ca015f8-ecd7-4a06-bb36-f4ae1c263bbe", "errorHandler": false, "successLabel": "0"}, {"funcName": "INT_SYS_ConfigInit", "value": "INT_SYS_ConfigInit();", "id": "da12c7f3-86f8-4c30-9250-d9bb7fd9e8e4", "errorHandler": false, "successLabel": "0"}], "linkfile": "", "links": [], "name": "TBox_mcu", "partNumber": "YTM32B1ME05G0MLLT", "sdkVersion": "1_2_0", "sections": [], "stackSize": "400", "targetToolOpt": {"KEIL": {"ytlink": {"linkflags": ["--no_startup", "--keep=*(.isr_vector)", "--keep=*(.bvt_header)", "--keep=*(.sb_config_group)", "--keep=*(.sb_config_section)", "--keep=*(.sb_cmac)"]}}, "CMakeKEIL": {"ytlink": {"linkflags": ["--no_startup", "--keep=*(.isr_vector)", "--keep=*(.bvt_header)", "--keep=*(.sb_config_group)", "--keep=*(.sb_config_section)", "--keep=*(.sb_cmac)"]}}}, "toolOpt": {"addData": {"cdefines": ["USING_OS_FREERTOS"], "asmdefines": [], "cincludes": [], "aincludes": [], "cflags": [], "aflags": [], "linkflags": [], "depends": [], "asmincludes": [], "asmflags": [], "linkfalgs": []}, "deleteData": {"cdefines": [], "asmdefines": [], "cincludes": [], "aincludes": [], "cflags": [], "aflags": [], "linkflags": [], "depends": [], "asmincludes": [], "asmflags": [], "linkfalgs": []}}, "toolchain": "CMakeGCC", "toolchainVer": "", "type": "sdk", "useYtLink": true, "yctFileName": "TBox_mcu.yct"}, "data": {"adc": {"enable": {}, "data": [{"name": "adc_config0", "readonly": true, "inst": 0, "clockDivider": 3, "startTime": 48, "sampleTime": 10, "overrunMode": false, "autoOffEnable": false, "waitEnable": false, "triggerSource": 0, "trigger": "ADC_TRIGGER_SOFTWARE", "align": "ADC_ALIGN_RIGHT", "resolution": "ADC_RESOLUTION_12BIT", "dmaWaterMark": 0, "dmaEnable": false, "sequenceConfig": {"channels": [{"channel": "ADC_INPUTCHAN_EXT12"}, {"channel": "ADC_INPUTCHAN_EXT13"}, {"channel": "ADC_INPUTCHAN_EXT14"}, {"channel": "ADC_INPUTCHAN_EXT15"}], "sequenceMode": "ADC_CONV_LOOP", "sequenceIntEnable": true, "convIntEnable": false, "readyIntEnable": false, "sampIntEnable": false, "ovrunIntEnable": false}, "compareConfig": {"resolution": "ADC_RESOLUTION_12BIT", "compareEnable": false, "compareAllChannelEnable": false, "compHigh": 4095, "compLow": 0, "compIntEnable": false}}], "updateTime": 1711350784985, "lock": true}, "bvt": {"enable": {}, "data": {"startAddr": 16384, "keys": [{"index": 0, "rindex": 31, "key": "16157E2BA6D2AE288815F7AB3C4FCF09"}, {"index": 1, "rindex": 30, "key": "000102030405060708090A0B0C0D0E0F"}, {"index": 2, "rindex": 29, "key": "00112233445566778899AABBCCDDEEFF"}, {"index": 3, "rindex": 28, "key": "1032547698BADCFE67452301EFCDAB89"}, {"index": 4, "rindex": 27, "key": "7223E1AD2331D2FE411F23AF9810F2CD16157E2B0C0D0E0F"}, {"index": 5, "rindex": 26, "key": "3423FCAE2421FE34FE053821C2B2EF232331D2FE445566773423FCAEEFCDAB89"}, {"index": 6, "rindex": 25, "key": ""}, {"index": 7, "rindex": 24, "key": ""}, {"index": 8, "rindex": 23, "key": ""}, {"index": 9, "rindex": 22, "key": ""}, {"index": 10, "rindex": 21, "key": ""}, {"index": 11, "rindex": 20, "key": ""}, {"index": 12, "rindex": 19, "key": ""}, {"index": 13, "rindex": 18, "key": ""}, {"index": 14, "rindex": 17, "key": ""}, {"index": 15, "rindex": 16, "key": ""}, {"index": 16, "rindex": 15, "key": ""}, {"index": 17, "rindex": 14, "key": ""}, {"index": 18, "rindex": 13, "key": ""}, {"index": 19, "rindex": 12, "key": ""}, {"index": 20, "rindex": 11, "key": ""}, {"index": 21, "rindex": 10, "key": ""}, {"index": 22, "rindex": 9, "key": ""}, {"index": 23, "rindex": 8, "key": ""}, {"index": 24, "rindex": 7, "key": ""}, {"index": 25, "rindex": 6, "key": ""}, {"index": 26, "rindex": 5, "key": ""}, {"index": 27, "rindex": 4, "key": ""}, {"index": 28, "rindex": 3, "key": ""}, {"index": 29, "rindex": 2, "key": ""}, {"index": 30, "rindex": 1, "key": ""}, {"index": 31, "rindex": 0, "key": ""}], "bvt": {"BVT_MARK": 2774181210, "BOOT_CONFIG_WORD": "0x000200c1", "SBT_CONFIG_GROUP_ADDR": {"addr": "0x0007f830", "secure_boot_group_marker": 2898194397, "secure_boot_section_num": 5, "encrypt": true, "aes_key_size": 0, "key_slot": 0, "sections": [{"addr": 506016, "aes_key_size": 0, "key_slot": 1, "start_addr": "0x00004000", "length": "0x00000020", "cmac_addr": "0x0007f8f0", "raw": "a55a00010040000020000000f0f80700"}, {"addr": 506000, "aes_key_size": 0, "key_slot": 2, "start_addr": "0x00004000", "length": "0x00000200", "cmac_addr": "0x0007f8e0", "raw": "a55a00020040000000020000e0f80700"}, {"addr": 505984, "aes_key_size": 0, "key_slot": 3, "start_addr": "0x00004000", "length": "0x00004000", "cmac_addr": "0x0007f8d0", "raw": "a55a00030040000000400000d0f80700"}, {"addr": 505968, "aes_key_size": 1, "key_slot": 4, "start_addr": "0x00004000", "length": "0x00000010", "cmac_addr": "0x0007f8c0", "raw": "a55a01040040000010000000c0f80700"}, {"addr": 505952, "aes_key_size": 2, "key_slot": 5, "start_addr": "0x00004000", "length": "0x00000100", "cmac_addr": "0x0007f8b0", "raw": "a55a02050040000000010000b0f80700"}]}, "LC_CONFIG": "0x00000000", "CM7_0_MAIN_APP_ADDR": "0x00004000", "CM7_0_SECONDARY_APP_ADDR": "0x00000000", "CM7_1_MAIN_APP_ADDR": "0x00000000", "CM7_1_SECONDARY_APP_ADDR": "0x00000000", "CM7_2_MAIN_APP_ADDR": "0x00000000", "CM7_2_SECONDARY_APP_ADDR": "0x00000000", "APP_WDG_TIMEOUT": "0x00000bb8"}, "bvtData": {"bvtMarker": "BVT_VALID_MARK", "bvtPllEn": true, "bvtCpdiv": 0, "bvtScstEn": false, "bvtWdgRecfgEn": false, "bvtWdgClkSrc": 0, "bvtWdgEn": false, "bvtSbEn": true, "bvtSbStrict": true, "bvtCoreEn": true, "bvtAppEntry": "0x4000", "bvtWdgTimeout": 3000, "bvtLocation": "0x0007F800"}, "sbGroupData": {"sbGroupMarker": "SECURE_BOOT_GROUP_MARKER", "sbSectionNumber": 5, "sbSectionEncrypt": true, "sbKeySize": "KEY_LEN_128_BITS", "sbGroupKeySelect": 0}, "sbSectionData": [{"name": "secure_boot_section_config0", "sbSectionMarker": "SECURE_BOOT_SECTION_MARKER", "sbKeySize": "KEY_LEN_128_BITS", "sbSectionKeySelect": "1", "sbSectionStartAddr": "0x4000", "sbSectionLength": "0x20"}, {"name": "secure_boot_section_config1", "sbSectionMarker": "SECURE_BOOT_SECTION_MARKER", "sbKeySize": "KEY_LEN_128_BITS", "sbSectionKeySelect": "2", "sbSectionStartAddr": "0x4000", "sbSectionLength": "0x200"}, {"name": "secure_boot_section_config2", "sbSectionMarker": "SECURE_BOOT_SECTION_MARKER", "sbKeySize": "KEY_LEN_128_BITS", "sbSectionKeySelect": "3", "sbSectionStartAddr": "0x4000", "sbSectionLength": "0x4000"}, {"name": "secure_boot_section_config3", "sbSectionMarker": "SECURE_BOOT_SECTION_MARKER", "sbKeySize": "KEY_LEN_192_BITS", "sbSectionKeySelect": "4", "sbSectionStartAddr": "0x4000", "sbSectionLength": "0x10"}, {"name": "secure_boot_section_config4", "sbSectionMarker": "SECURE_BOOT_SECTION_MARKER", "sbKeySize": "KEY_LEN_256_BITS", "sbSectionKeySelect": "5", "sbSectionStartAddr": "0x4000", "sbSectionLength": "0x100"}], "keyGenerate": false, "sbAlwaysExist": false}, "updateTime": 1712807202551, "lock": true}, "can": {"enable": {}, "data": [{"name": "flexcan1_InitConfig", "readonly": true, "inst": 1, "max_num_mb": 64, "num_id_filters": "FLEXCAN_RX_FIFO_ID_FILTERS_8", "flexcanMode": "FLEXCAN_NORMAL_MODE", "payload": "FLEXCAN_PAYLOAD_SIZE_8", "fd_enable": false, "pe_clock": "FLEXCAN_CLK_SOURCE_OSC", "transfer_type": "FLEXCAN_RXFIFO_USING_INTERRUPTS", "rxFifoDMAChannel": 0, "rxFifoDMALastWord": 0, "is_enhance_rx_fifo_needed": false, "num_enhance_rx_fifo_filters": 0, "num_enhance_rx_fifo_extid_filters": 0, "num_enhance_rx_fifo_min_messages": 0, "is_rx_fifo_needed": false, "bte": false, "bitrate": {"propSeg": 5, "phaseSeg1": 8, "phaseSeg2": 2, "preDivider": 3, "rJumpwidth": 2}, "bitrate_cbt": {"propSeg": 6, "phaseSeg1": 3, "phaseSeg2": 2, "preDivider": 1, "rJumpwidth": 2}}, {"name": "flexcan2_InitConfig", "readonly": true, "inst": 2, "max_num_mb": 64, "num_id_filters": "FLEXCAN_RX_FIFO_ID_FILTERS_8", "flexcanMode": "FLEXCAN_NORMAL_MODE", "payload": "FLEXCAN_PAYLOAD_SIZE_8", "fd_enable": false, "pe_clock": "FLEXCAN_CLK_SOURCE_OSC", "transfer_type": "FLEXCAN_RXFIFO_USING_INTERRUPTS", "rxFifoDMAChannel": 0, "rxFifoDMALastWord": 0, "is_enhance_rx_fifo_needed": false, "num_enhance_rx_fifo_filters": 0, "num_enhance_rx_fifo_extid_filters": 0, "num_enhance_rx_fifo_min_messages": 0, "is_rx_fifo_needed": false, "bte": false, "bitrate": {"propSeg": 5, "phaseSeg1": 8, "phaseSeg2": 2, "preDivider": 3, "rJumpwidth": 2}, "bitrate_cbt": {"propSeg": 6, "phaseSeg1": 3, "phaseSeg2": 2, "preDivider": 1, "rJumpwidth": 2}}, {"name": "flexcan3_InitConfig", "readonly": true, "inst": 3, "max_num_mb": 32, "num_id_filters": "FLEXCAN_RX_FIFO_ID_FILTERS_8", "flexcanMode": "FLEXCAN_NORMAL_MODE", "payload": "FLEXCAN_PAYLOAD_SIZE_8", "fd_enable": false, "pe_clock": "FLEXCAN_CLK_SOURCE_OSC", "transfer_type": "FLEXCAN_RXFIFO_USING_INTERRUPTS", "rxFifoDMAChannel": 0, "rxFifoDMALastWord": 0, "is_enhance_rx_fifo_needed": false, "num_enhance_rx_fifo_filters": 0, "num_enhance_rx_fifo_extid_filters": 0, "num_enhance_rx_fifo_min_messages": 0, "is_rx_fifo_needed": false, "bte": false, "bitrate": {"propSeg": 5, "phaseSeg1": 8, "phaseSeg2": 2, "preDivider": 3, "rJumpwidth": 2}, "bitrate_cbt": {"propSeg": 6, "phaseSeg1": 3, "phaseSeg2": 2, "preDivider": 1, "rJumpwidth": 2}}, {"name": "flexcan5_InitConfig", "readonly": true, "inst": 5, "max_num_mb": 32, "num_id_filters": "FLEXCAN_RX_FIFO_ID_FILTERS_8", "flexcanMode": "FLEXCAN_NORMAL_MODE", "payload": "FLEXCAN_PAYLOAD_SIZE_8", "fd_enable": false, "pe_clock": "FLEXCAN_CLK_SOURCE_OSC", "transfer_type": "FLEXCAN_RXFIFO_USING_INTERRUPTS", "rxFifoDMAChannel": 0, "rxFifoDMALastWord": 0, "is_enhance_rx_fifo_needed": false, "num_enhance_rx_fifo_filters": 0, "num_enhance_rx_fifo_extid_filters": 0, "num_enhance_rx_fifo_min_messages": 0, "is_rx_fifo_needed": false, "bte": false, "bitrate": {"propSeg": 5, "phaseSeg1": 8, "phaseSeg2": 2, "preDivider": 3, "rJumpwidth": 2}, "bitrate_cbt": {"propSeg": 6, "phaseSeg1": 3, "phaseSeg2": 2, "preDivider": 1, "rJumpwidth": 2}}], "updateTime": 1711350786543, "lock": true}, "clock": {"enable": {}, "data": {"activeInst": 0, "version": "1", "clockArray": [{"name": "clock_config0", "data": {"firc": {"enable": true, "dsEnable": false, "freq": {"value": 96, "unit": "MHz", "raw": 96000000}}, "fxosc": {"enable": true, "dsEnable": false, "freq": {"value": 24, "unit": "MHz", "raw": 24000000}, "gain": 5, "mode": 0}, "sirc": {"enable": true, "dsEnable": true, "stbEnable": true, "pdEnable": true, "freq": {"value": 12, "unit": "MHz", "raw": 12000000}}, "sxosc": {"enable": true, "dsEnable": true, "pdEnable": true, "stbEnable": true, "freq": {"value": 32768, "unit": "Hz", "raw": 32768}, "gain": 0, "mode": 0}, "pll": {"enable": true, "refdivV": 1, "fbdivV": 10, "ref": 1, "freq": {"value": 120, "unit": "MHz", "raw": 120000000}, "dsEnable": false}, "cmu0": {"reset": false, "ref": 0, "enable": false, "compareHigh": 0, "compareLow": 0}, "cmu1": {"reset": false, "ref": 0, "enable": false, "compareHigh": 0, "compareLow": 0}, "cmu2": {"reset": false, "ref": 0, "enable": false, "compareHigh": 0, "compareLow": 0}, "cmu3": {"reset": false, "enable": false, "ref": 0, "compareHigh": 0, "compareLow": 0}, "clkOut": {"enable": false, "div": 1, "ref": 0}, "coreClock": {"div": 0, "ref": 1, "freq": {"value": 120, "unit": "MHz", "raw": 120000000}}, "fastClock": {"div": 0, "freq": {"value": 120, "unit": "MHz", "raw": 120000000}}, "slowClock": {"div": 2, "freq": {"value": 40, "unit": "MHz", "raw": 40000000}}, "periClocks": [{"name": "DMA_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "div": 0, "busClock": ["CORE_CLK"]}, {"name": "TRACE_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "div": 0, "busClock": ["FAST_BUS_CLK"]}, {"name": "EFM_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "div": 0, "busClock": ["SLOW_BUS_CLK"]}, {"name": "GPIO_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "div": 0, "busClock": ["CORE_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"]}, {"name": "PCTRLA_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "div": 0, "busClock": ["SLOW_BUS_CLK"]}, {"name": "PCTRLB_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "div": 0, "busClock": ["SLOW_BUS_CLK"]}, {"name": "PCTRLC_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "PCTRLD_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "PCTRLE_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "LINFlexD0_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK", "FAST_BUS_CLK"], "div": 0}, {"name": "LINFlexD1_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK", "FAST_BUS_CLK"], "div": 0}, {"name": "LINFlexD2_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK", "FAST_BUS_CLK"], "div": 0}, {"name": "LINFlexD3_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK", "FAST_BUS_CLK"], "div": 0}, {"name": "LINFlexD4_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK", "FAST_BUS_CLK"], "div": 0}, {"name": "LINFlexD5_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK", "FAST_BUS_CLK"], "div": 0}, {"name": "I2C0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "I2C1_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "I2C2_CLK", "gate": true, "busSrc": 0, "periSrc": 2, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "SPI0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "SPI1_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "SPI2_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "SPI3_CLK", "gate": true, "busSrc": 0, "periSrc": 3, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "SPI4_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "SPI5_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "FlexCAN0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK", "FXOSC_CLK"], "div": 0}, {"name": "FlexCAN1_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["FAST_BUS_CLK", "FXOSC_CLK"], "div": 0}, {"name": "FlexCAN2_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["FAST_BUS_CLK", "FXOSC_CLK"], "div": 0}, {"name": "FlexCAN3_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["FAST_BUS_CLK", "FXOSC_CLK"], "div": 0}, {"name": "FlexCAN4_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK", "FXOSC_CLK"], "div": 0}, {"name": "FlexCAN5_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["FAST_BUS_CLK", "FXOSC_CLK"], "div": 0}, {"name": "ADC0_CLK", "gate": true, "busSrc": 0, "periSrc": 3, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "ADC1_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "ACMP0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "PTU0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK"], "div": 0}, {"name": "PTU1_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK"], "div": 0}, {"name": "eTMR0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK", "TCLK0", "TCLK1", "TCLK2"], "div": 0}, {"name": "eTMR1_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["FAST_BUS_CLK", "TCLK0", "TCLK1", "TCLK2"], "div": 0}, {"name": "eTMR2_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["FAST_BUS_CLK", "TCLK0", "TCLK1", "TCLK2"], "div": 0}, {"name": "eTMR3_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK", "TCLK0", "TCLK1", "TCLK2"], "div": 0}, {"name": "eTMR4_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK", "TCLK0", "TCLK1", "TCLK2"], "div": 0}, {"name": "eTMR5_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK", "TCLK0", "TCLK1", "TCLK2"], "div": 0}, {"name": "TMR0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "pTMR0_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "lpTMR0_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "periClock": ["CLK_SRC_DISABLED", "CLK_SRC_FIRC", "CLK_SRC_SIRC", "CLK_SRC_FXOSC", "CLK_SRC_SXOSC", "CLK_SRC_PLL"], "div": 0}, {"name": "RTC_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK", "RTC_CLK_OSCIN", "RTC_CLK_CLKIN"], "div": 0}, {"name": "REGFILE_CLK", "gate": false, "busClock": ["SLOW_BUS_CLK"], "busSrc": 0, "periSrc": 0, "userCtrl": false, "div": 0}, {"name": "WKU_CLK", "gate": true, "busClock": ["SLOW_BUS_CLK"], "busSrc": 0, "periSrc": 0, "userCtrl": true, "div": 0}, {"name": "CRC_CLK", "gate": false, "busClock": ["CORE_CLK"], "busSrc": 0, "periSrc": 0, "userCtrl": false, "div": 0}, {"name": "TRNG_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "HCU_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "WDG_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK", "SIRC_CLK", "SXOSC_CLK"], "div": 0}, {"name": "EWDG_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "INTM_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "EMU_CLK", "gate": false, "busSrc": 0, "periSrc": 0, "userCtrl": false, "busClock": ["FAST_BUS_CLK"], "div": 0}, {"name": "SCU_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "PCU_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK"], "div": 0}, {"name": "RCU_CLK", "gate": true, "busSrc": 0, "periSrc": 0, "userCtrl": true, "busClock": ["SLOW_BUS_CLK", "SIRC_CLK"], "div": 0}]}}]}, "updateTime": 1715339597524, "lock": false}, "device": {"enable": {}, "data": {"pn": "YTM32B1ME05G0MLLT", "sdkVer": "1_2_0", "useFreeRtos": true, "useSecureBoot": true, "stack_top": "STACK_end", "vector_ram": "IVT_RAM_start", "vector_flash_region": ".isr_vector", "vector_flash": "IVT_start", "vector_flash_end": "IVT_end", "vectors": ["NMI_Handler", "HardF<PERSON>_Handler", "MemManage_Handler", "BusFault_Handler", "UsageFault_Handler", "Reserved", "Reserved", "Reserved", "Reserved", "SVC_Handler", "DebugMon_Handler", "Reserved", "Pend<PERSON>_Handler", "SysTick_Handler", "DMA0_IRQHandler", "DMA1_IRQHandler", "DMA2_IRQHandler", "DMA3_IRQHandler", "DMA4_IRQHandler", "DMA5_IRQHandler", "DMA6_IRQHandler", "DMA7_IRQHandler", "DMA8_IRQHandler", "DMA9_IRQHandler", "DMA10_IRQHandler", "DMA11_IRQHandler", "DMA12_IRQHandler", "DMA13_IRQHandler", "DMA14_IRQHandler", "DMA15_IRQHandler", "DMA_Error_IRQHandler", "FPU_IRQHandler", "EFM_IRQHandler", "EFM_Error_IRQHandler", "PCU_IRQHandler", "EFM_Ecc_IRQHandler", "WDG_IRQHandler", "RCU_IRQHandler", "I2C0_Master_IR<PERSON><PERSON><PERSON><PERSON>", "I2C0_Slave_IRQHandler", "SPI0_IRQHandler", "SPI1_IRQHandler", "SPI2_IRQHandler", "I2C1_Master_IR<PERSON><PERSON><PERSON><PERSON>", "I2C1_Slave_IRQHandler", "LINFlexD0_IRQHandler", "Reserved5_IRQHandler", "LINFlexD1_IRQHandler", "Reserved6_IRQHandler", "LINFlexD2_IRQHandler", "Reserved7_IRQHandler", "Reserved8_IRQHandler", "Reserved9_IRQHandler", "ADC0_IRQHandler", "ADC1_IRQHandler", "ACMP0_IRQHandler", "Reserved10_IRQHandler", "Reserved11_IRQHandler", "EMU_IRQHandler", "Reserved12_IRQHandler", "RTC_IRQHandler", "RTC_Seconds_IRQH<PERSON><PERSON>", "pTMR_Ch0_IRQHandler", "pTMR_Ch1_IRQHandler", "pTMR_Ch2_IRQHandler", "pTMR_Ch3_IRQHandler", "PTU0_IRQHandler", "Reserved13_IRQHandler", "Reserved14_IRQHandler", "Reserved15_IRQHandler", "Reserved16_IRQHandler", "SCU_IRQHandler", "lpTMR0_IRQHandler", "GPIOA_IRQHandler", "GPIOB_IRQHandler", "GPIOC_IRQHandler", "GPIOD_IRQHandler", "GPIOE_IRQHandler", "Reserved17_IRQHandler", "Reserved18_IRQHandler", "Reserved19_IRQHandler", "Reserved20_IRQHandler", "PTU1_IRQHandler", "Reserved21_IRQHandler", "Reserved22_IRQHandler", "Reserved23_IRQHandler", "Reserved24_IRQHandler", "Reserved25_IRQHandler", "Reserved26_IRQHandler", "Reserved27_IRQHandler", "Reserved28_IRQHandler", "Reserved29_IRQHandler", "CAN0_ORed_IRQHandler", "CAN0_Error_IRQHandler", "CAN0_Wake_Up_IRQHandler", "CAN0_ORed_0_15_MB_IRQHandler", "CAN0_ORed_16_31_MB_IRQHandler", "CAN0_ORed_32_47_MB_IRQHandler", "CAN0_ORed_48_63_MB_IRQHandler", "CAN1_ORed_IRQHandler", "CAN1_Error_IRQHandler", "CAN1_Wake_Up_IRQHandler", "CAN1_ORed_0_15_MB_IRQHandler", "CAN1_ORed_16_31_MB_IRQHandler", "CAN1_ORed_32_47_MB_IRQHandler", "CAN1_ORed_48_63_MB_IRQHandler", "CAN2_ORed_IRQHandler", "CAN2_Error_IRQHandler", "CAN2_Wake_Up_IRQHandler", "CAN2_ORed_0_15_MB_IRQHandler", "CAN2_ORed_16_31_MB_IRQHandler", "CAN2_ORed_32_47_MB_IRQHandler", "CAN2_ORed_48_63_MB_IRQHandler", "eTMR0_Ch0_Ch1_IRQHandler", "eTMR0_Ch2_Ch3_IRQHandler", "eTMR0_Ch4_Ch5_IRQHandler", "eTMR0_Ch6_Ch7_IRQHandler", "eTMR0_Fault_IRQHandler", "eTMR0_Ovf_IRQHandler", "eTMR1_Ch0_Ch1_IRQHandler", "eTMR1_Ch2_Ch3_IRQHandler", "eTMR1_Ch4_Ch5_IRQHandler", "eTMR1_Ch6_Ch7_IRQHandler", "eTMR1_Fault_IRQHandler", "eTMR1_Ovf_IRQHandler", "eTMR2_Ch0_Ch1_IRQHandler", "eTMR2_Ch2_Ch3_IRQHandler", "eTMR2_Ch4_Ch5_IRQHandler", "eTMR2_Ch6_Ch7_IRQHandler", "eTMR2_Fault_IRQHandler", "eTMR2_Ovf_IRQHandler", "eTMR3_Ch0_Ch1_IRQHandler", "eTMR3_Ch2_Ch3_IRQHandler", "eTMR3_Ch4_Ch5_IRQHandler", "eTMR3_Ch6_Ch7_IRQHandler", "eTMR3_Fault_IRQHandler", "eTMR3_Ovf_IRQHandler", "eTMR4_Ch0_Ch1_IRQHandler", "eTMR4_Ch2_Ch3_IRQHandler", "eTMR4_Ch4_Ch5_IRQHandler", "eTMR4_Ch6_Ch7_IRQHandler", "eTMR4_Fault_IRQHandler", "eTMR4_Ovf_IRQHandler", "eTMR5_Ch0_Ch1_IRQHandler", "eTMR5_Ch2_Ch3_IRQHandler", "eTMR5_Ch4_Ch5_IRQHandler", "eTMR5_Ch6_Ch7_IRQHandler", "eTMR5_Fault_IRQHandler", "eTMR5_Ovf_IRQHandler", "Reserved30_IRQHandler", "Reserved31_IRQHandler", "Reserved32_IRQHandler", "Reserved33_IRQHandler", "Reserved34_IRQHandler", "Reserved35_IRQHandler", "Reserved36_IRQHandler", "Reserved37_IRQHandler", "Reserved38_IRQHandler", "Reserved39_IRQHandler", "Reserved40_IRQHandler", "Reserved41_IRQHandler", "Reserved42_IRQHandler", "Reserved43_IRQHandler", "Reserved44_IRQHandler", "Reserved45_IRQHandler", "Reserved46_IRQHandler", "Reserved47_IRQHandler", "Reserved48_IRQHandler", "Reserved49_IRQHandler", "Reserved50_IRQHandler", "TRNG_IRQHandler", "HCU_IRQHandler", "INTM_IRQHandler", "TMR0_Ch0_IRQHandler", "TMR0_Ch1_IRQHandler", "TMR0_Ch2_IRQHandler", "TMR0_Ch3_IRQHandler", "LINFlexD3_IRQHandler", "LINFlexD4_IRQHandler", "LINFlexD5_IRQHandler", "I2C2_Master_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "I2C2_Slave_IRQHandler", "SPI3_IRQHandler", "SPI4_IRQHandler", "SPI5_IRQHandler", "CAN3_ORed_IRQHandler", "CAN3_Error_IRQHandler", "CAN3_Wake_Up_IRQHandler", "CAN3_ORed_0_15_MB_IRQHandler", "CAN3_ORed_16_31_MB_IRQHandler", "Reserved51_IRQHandler", "Reserved52_IRQHandler", "CAN4_ORed_IRQHandler", "CAN4_Error_IRQHandler", "CAN4_Wake_Up_IRQHandler", "CAN4_ORed_0_15_MB_IRQHandler", "CAN4_ORed_16_31_MB_IRQHandler", "Reserved53_IRQHandler", "Reserved54_IRQHandler", "CAN5_ORed_IRQHandler", "CAN5_Error_IRQHandler", "CAN5_Wake_Up_IRQHandler", "CAN5_ORed_0_15_MB_IRQHandler", "CAN5_ORed_16_31_MB_IRQHandler", "WKU_IRQHandler"], "linkfile": ""}, "updateTime": 1712807221072, "lock": false}, "dma": {"enable": {}, "data": {"userConfig": {"haltOnError": false}, "chs": [{"name": "dma_config0", "readonly": true, "virtChnConfig": 0, "source": "DMA_REQ_DISABLED", "callback": "NULL", "callbackParam": "NULL"}], "transEnable": false, "trans": [{"name": "dma_transfer_config0", "srcAddr": 0, "destAddr": 0, "srcOffset": 0, "destOffset": 0, "srcTransferSize": "DMA_TRANSFER_SIZE_1B", "destTransferSize": "DMA_TRANSFER_SIZE_1B", "srcModulo": "DMA_MODULO_OFF", "destModulo": "DMA_MODULO_OFF", "loopTransferConfig": {"triggerLoopIterationCount": 1, "srcOffsetEnable": false, "dstOffsetEnable": false, "triggerLoopOffset": 0, "transferLoopChnLinkEnable": false, "transferLoopChnLinkNumber": 0, "triggerLoopChnLinkEnable": false, "triggerLoopChnLinkNumber": 0}, "transferLoopByteCount": 0, "srcLastAddrAdjust": 0, "destLastAddrAdjust": 0, "interruptEnable": false}]}, "updateTime": 1712806230440, "lock": true}, "etmr": {"enable": {}, "data": {"common": [{"name": "ETMR_CM_Config0", "readonly": false, "etmrClockSource": "eTMR_CLOCK_SOURCE_INTERNALCLK", "etmrPrescaler": 128, "debugMode": false, "isTofIntEnabled": false, "syncMethod": {"regSyncFreq": 1, "regSyncSel": "REG_SYNC_DISABLED", "cntInitSyncSel": "CNT_SYNC_WITH_REG", "maskOutputSyncSel": "CHMASK_SYNC_WITH_REG", "regSyncTrigSrc": "DISABLE_TRIGGER", "cntInitSyncTrigSrc": "DISABLE_TRIGGER", "maskOutputSyncTrigSrc": "DISABLE_TRIGGER", "hwTrigRegLoadEnable": false, "hwTrigCounterLoadEnable": false, "hwTrigMaskLoadEnable": false}, "outputTrigConfig": {"trigSrc": "TRIGGER_FROM_MATCHING_EVENT", "pwmOutputChannel": 0, "outputTrigWidth": 0, "outputTrigFreq": 256, "modMatchTrigEnable": false, "midMatchTrigEnable": false, "initMatchTrigEnable": false, "channelTrigParamConfig": []}}], "pwm": {"enable": false, "data": []}, "ic": {"enable": true, "data": [{"name": "ETMR_IC_Config0", "readonly": false, "numChannels": 1, "countValue": 65535, "inputChConfig": [{"hwChannelId": 7, "edge": "eTMR_DUAL_EDGES", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": true, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}, {"hwChannelId": 0, "edge": "eTMR_NOT_SEL_EDGE", "measurementType": "eTMR_POS_PULSE_MEASUREMENT", "filterSampleCounter": 0, "filterSamplePeriod": 0, "dmaEnable": false, "interruptEnable": false, "enableNotification": false, "channelsCallbacks": "NULL", "channelsCallbacksParams": "NULL"}]}]}, "oc": {"enable": false, "data": []}, "qd": {"enable": false, "data": []}, "mc": {"enable": false, "data": []}}, "updateTime": 1711524814789, "lock": false}, "fee": {"enable": {}, "data": {"flashConfig": {"name": "FEEGenConfig", "readonly": true, "acEraseFunPtr": "NULL", "acWriteFunPtr": "NULL", "jobEndNotificationFunPtr": "Fee_JobEndNotification", "jobErrorNotificationFunPtr": "Fee_JobErrorNotification", "eDefaultMode": "MEMIF_MODE_SLOW", "maxReadFastMode": 512, "maxReadNormalMode": 256, "maxWriteFastMode": 128, "maxWriteNormalMode": 8, "sectorList": []}, "clusterConfig": [], "blockConfig": []}, "updateTime": 1711350786854, "lock": true}, "flash": {"enable": {}, "data": {"custAddr": false, "config": [{"name": "flash_config0", "readonly": false, "async": false, "disGlobalInt": false, "readVerify": false, "callback": "NULL"}]}, "updateTime": 1711350786548, "lock": true}, "freertos": {"enable": {}, "data": {"freq": 120000000, "tick": 1000, "maxPriority": 5, "minStack": 90, "maxTaskName": 10, "maxIntPriority": 1, "use16bitTick": false, "idleYield": true, "preemption": true, "numLocalStoragePointer": 0, "usePortOpt": false, "useTaskNotify": true, "useTimeSlice": true, "useNewlibReentrant": false, "enableBackward": true, "enablePosixErrno": false, "messageBufferType": "", "custAssert": false, "assertDef": "if((x)==0) {taskDISABLE_INTERRUPTS(); for(;;);}", "heapType": "heap_4", "heapSize": 65536, "appAllocHeap": false, "supportDynamicAlloc": true, "supportStaticAlloc": false, "useIdleHook": true, "useTickHook": true, "useMallocFailedHook": false, "checkStackOverflow": "", "useDaemonTaskStartupHook": false, "geneateStats": false, "timerForRunTimeStats": "", "getRunTimeValue": "xTaskGetTickCount()", "useTraceFacility": true, "useStatsFormatFunc": true, "useCoRoutines": false, "maxCoRoutinesPriority": 2, "useMutexes": true, "useRecursiveMutexs": true, "useCounterSemp": true, "useSoftTimer": true, "timerTaskPriority": 2, "timerQueLen": 10, "timerTaskStack": 180, "useTicklessIdel": false, "expectedIdleTime": 2, "preSleepFunc": "", "postSleepFunc": "", "queSetSize": 2, "useQueSets": true, "optApi": {"INCLUDE_vTaskPrioritySet": true, "INCLUDE_uxTaskPriorityGet": true, "INCLUDE_vTaskDelete": true, "INCLUDE_vTaskSuspend": true, "INCLUDE_vTaskDelayUntil": true, "INCLUDE_vTaskDelay": true, "INCLUDE_xTaskGetSchedulerState": true, "INCLUDE_xTaskGetCurrentTaskHandle": true, "INCLUDE_uxTaskGetStackHighWaterMark": true, "INCLUDE_xTaskGetIdleTaskHandle": false, "INCLUDE_eTaskGetState": true, "INCLUDE_xEventGroupSetBitFromISR": true, "INCLUDE_xTimerPendFunctionCall": true, "INCLUDE_xTaskAbortDelay": true, "INCLUDE_xTaskGetHandle": true, "INCLUDE_xTaskResumeFromISR": true, "INCLUDE_xQueueGetMutexHolder": true}, "cusInc": ""}, "updateTime": 1711350786494, "lock": true}, "hcu": {"enable": {}, "data": [{"name": "hcu_config0", "readonly": true, "swap": "MODE_SWAPPING_NO", "carryType": "HCU_USING_POLLING", "ingressDMAChannel": 0, "egressDMAChannel": 0}], "updateTime": 1711532296646, "lock": false}, "i2c": {"enable": {}, "data": {"master": [{"name": "I2C_MasterConfig0", "readonly": true, "slaveAddress": "0x45", "is10bitAddr": false, "operatingMode": "I2C_FAST_MODE", "baudRate": 100000, "baudRateHS": 0, "masterCode": 0, "transferType": "I2C_USING_INTERRUPTS", "dmaChannel": 0, "masterCallback": "NULL", "callbackParam": "NULL"}], "slave": [{"name": "I2C_SlaveConfig0", "readonly": true, "slaveAddress": "0x45", "is10bitAddr": false, "operatingMode": "I2C_STANDARD_MODE", "slaveListening": true, "transferType": "I2C_USING_INTERRUPTS", "dmaChannel": 0, "slaveCallback": "NULL", "callbackParam": "NULL"}]}, "updateTime": 1711350786552, "lock": true}, "interrupt": {"enable": {}, "data": [{"name": "NMI_IRQn", "num": -14, "enable": false, "priority": -2, "callback": "NMI_IRQHandler"}, {"name": "HardFault_IRQn", "num": -13, "enable": false, "priority": -1, "callback": "HardFault_IRQHandler"}, {"name": "MemManage_IRQn", "num": -12, "enable": false, "priority": 0, "callback": "MemManage_IRQHandler"}, {"name": "BusFault_IRQn", "num": -11, "enable": false, "priority": 0, "callback": "BusFault_IRQHandler"}, {"name": "UsageFault_IRQn", "num": -10, "enable": false, "priority": 0, "callback": "UsageFault_IRQHandler"}, {"name": "SVC_IRQn", "num": -5, "enable": false, "priority": 0, "callback": "SVC_IRQHandler"}, {"name": "DebugMon_IRQn", "num": -4, "enable": false, "priority": 0, "callback": "DebugMon_IRQHandler"}, {"name": "PendSV_IRQn", "num": -2, "enable": false, "priority": 0, "callback": "PendSV_IRQHandler"}, {"name": "SysTick_IRQn", "num": -1, "enable": false, "priority": 0, "callback": "SysTick_IRQHandler"}, {"name": "DMA0_IRQn", "num": 0, "enable": false, "priority": 0, "callback": "DMA0_IRQHandler"}, {"name": "DMA1_IRQn", "num": 1, "enable": false, "priority": 0, "callback": "DMA1_IRQHandler"}, {"name": "DMA2_IRQn", "num": 2, "enable": false, "priority": 0, "callback": "DMA2_IRQHandler"}, {"name": "DMA3_IRQn", "num": 3, "enable": false, "priority": 0, "callback": "DMA3_IRQHandler"}, {"name": "DMA4_IRQn", "num": 4, "enable": false, "priority": 0, "callback": "DMA4_IRQHandler"}, {"name": "DMA5_IRQn", "num": 5, "enable": false, "priority": 0, "callback": "DMA5_IRQHandler"}, {"name": "DMA6_IRQn", "num": 6, "enable": false, "priority": 0, "callback": "DMA6_IRQHandler"}, {"name": "DMA7_IRQn", "num": 7, "enable": false, "priority": 0, "callback": "DMA7_IRQHandler"}, {"name": "DMA8_IRQn", "num": 8, "enable": false, "priority": 0, "callback": "DMA8_IRQHandler"}, {"name": "DMA9_IRQn", "num": 9, "enable": false, "priority": 0, "callback": "DMA9_IRQHandler"}, {"name": "DMA10_IRQn", "num": 10, "enable": false, "priority": 0, "callback": "DMA10_IRQHandler"}, {"name": "DMA11_IRQn", "num": 11, "enable": false, "priority": 0, "callback": "DMA11_IRQHandler"}, {"name": "DMA12_IRQn", "num": 12, "enable": false, "priority": 0, "callback": "DMA12_IRQHandler"}, {"name": "DMA13_IRQn", "num": 13, "enable": false, "priority": 0, "callback": "DMA13_IRQHandler"}, {"name": "DMA14_IRQn", "num": 14, "enable": false, "priority": 0, "callback": "DMA14_IRQHandler"}, {"name": "DMA15_IRQn", "num": 15, "enable": false, "priority": 0, "callback": "DMA15_IRQHandler"}, {"name": "DMA_Error_IRQn", "num": 16, "enable": false, "priority": 0, "callback": "DMA_Error_IRQHandler"}, {"name": "FPU_IRQn", "num": 17, "enable": false, "priority": 0, "callback": "FPU_IRQHandler"}, {"name": "EFM_IRQn", "num": 18, "enable": false, "priority": 0, "callback": "EFM_IRQHandler"}, {"name": "EFM_Error_IRQn", "num": 19, "enable": false, "priority": 0, "callback": "EFM_Error_IRQHandler"}, {"name": "PCU_IRQn", "num": 20, "enable": false, "priority": 0, "callback": "PCU_IRQHandler"}, {"name": "EFM_Ecc_IRQn", "num": 21, "enable": false, "priority": 0, "callback": "EFM_Ecc_IRQHandler"}, {"name": "WDG_IRQn", "num": 22, "enable": false, "priority": 0, "callback": "WDG_IRQHandler"}, {"name": "RCU_IRQn", "num": 23, "enable": false, "priority": 0, "callback": "RCU_IRQHandler"}, {"name": "I2C0_Master_IRQn", "num": 24, "enable": false, "priority": 0, "callback": "I2C0_Master_IR<PERSON><PERSON><PERSON><PERSON>"}, {"name": "I2C0_Slave_IRQn", "num": 25, "enable": false, "priority": 0, "callback": "I2C0_Slave_IRQHandler"}, {"name": "SPI0_IRQn", "num": 26, "enable": false, "priority": 0, "callback": "SPI0_IRQHandler"}, {"name": "SPI1_IRQn", "num": 27, "enable": false, "priority": 0, "callback": "SPI1_IRQHandler"}, {"name": "SPI2_IRQn", "num": 28, "enable": false, "priority": 3, "callback": "SPI2_IRQHandler"}, {"name": "I2C1_Master_IRQn", "num": 29, "enable": false, "priority": 0, "callback": "I2C1_Master_IR<PERSON><PERSON><PERSON><PERSON>"}, {"name": "I2C1_Slave_IRQn", "num": 30, "enable": false, "priority": 0, "callback": "I2C1_Slave_IRQHandler"}, {"name": "LINFlexD0_IRQn", "num": 31, "enable": true, "priority": 3, "callback": "LINFlexD0_IRQHandler"}, {"name": "Reserved5_IRQn", "num": 32, "enable": false, "priority": 0, "callback": "Reserved5_IRQHandler"}, {"name": "LINFlexD1_IRQn", "num": 33, "enable": false, "priority": 0, "callback": "LINFlexD1_IRQHandler"}, {"name": "Reserved6_IRQn", "num": 34, "enable": false, "priority": 0, "callback": "Reserved6_IRQHandler"}, {"name": "LINFlexD2_IRQn", "num": 35, "enable": false, "priority": 0, "callback": "LINFlexD2_IRQHandler"}, {"name": "Reserved7_IRQn", "num": 36, "enable": false, "priority": 0, "callback": "Reserved7_IRQHandler"}, {"name": "Reserved8_IRQn", "num": 37, "enable": false, "priority": 0, "callback": "Reserved8_IRQHandler"}, {"name": "Reserved9_IRQn", "num": 38, "enable": false, "priority": 0, "callback": "Reserved9_IRQHandler"}, {"name": "ADC0_IRQn", "num": 39, "enable": true, "priority": 3, "callback": "ADC0_IRQHandler"}, {"name": "ADC1_IRQn", "num": 40, "enable": false, "priority": 0, "callback": "ADC1_IRQHandler"}, {"name": "ACMP0_IRQn", "num": 41, "enable": false, "priority": 0, "callback": "ACMP0_IRQHandler"}, {"name": "Reserved10_IRQn", "num": 42, "enable": false, "priority": 0, "callback": "Reserved10_IRQHandler"}, {"name": "Reserved11_IRQn", "num": 43, "enable": false, "priority": 0, "callback": "Reserved11_IRQHandler"}, {"name": "EMU_IRQn", "num": 44, "enable": false, "priority": 0, "callback": "EMU_IRQHandler"}, {"name": "Reserved12_IRQn", "num": 45, "enable": false, "priority": 0, "callback": "Reserved12_IRQHandler"}, {"name": "RTC_IRQn", "num": 46, "enable": true, "priority": 2, "callback": "RTC_IRQHandler"}, {"name": "RTC_Seconds_IRQn", "num": 47, "enable": true, "priority": 2, "callback": "RTC_Seconds_IRQH<PERSON><PERSON>"}, {"name": "pTMR_Ch0_IRQn", "num": 48, "enable": true, "priority": 4, "callback": "pTMR_Ch0_IRQHandler"}, {"name": "pTMR_Ch1_IRQn", "num": 49, "enable": true, "priority": 4, "callback": "pTMR_Ch1_IRQHandler"}, {"name": "pTMR_Ch2_IRQn", "num": 50, "enable": false, "priority": 0, "callback": "pTMR_Ch2_IRQHandler"}, {"name": "pTMR_Ch3_IRQn", "num": 51, "enable": false, "priority": 0, "callback": "pTMR_Ch3_IRQHandler"}, {"name": "PTU0_IRQn", "num": 52, "enable": false, "priority": 0, "callback": "PTU0_IRQHandler"}, {"name": "Reserved13_IRQn", "num": 53, "enable": false, "priority": 0, "callback": "Reserved13_IRQHandler"}, {"name": "Reserved14_IRQn", "num": 54, "enable": false, "priority": 0, "callback": "Reserved14_IRQHandler"}, {"name": "Reserved15_IRQn", "num": 55, "enable": false, "priority": 0, "callback": "Reserved15_IRQHandler"}, {"name": "Reserved16_IRQn", "num": 56, "enable": false, "priority": 0, "callback": "Reserved16_IRQHandler"}, {"name": "SCU_IRQn", "num": 57, "enable": false, "priority": 0, "callback": "SCU_IRQHandler"}, {"name": "lpTMR0_IRQn", "num": 58, "enable": false, "priority": 0, "callback": "lpTMR0_IRQHandler"}, {"name": "GPIOA_IRQn", "num": 59, "enable": true, "priority": 3, "callback": "GPIOA_IRQHandler"}, {"name": "GPIOB_IRQn", "num": 60, "enable": true, "priority": 3, "callback": "GPIOB_IRQHandler"}, {"name": "GPIOC_IRQn", "num": 61, "enable": true, "priority": 3, "callback": "GPIOC_IRQHandler"}, {"name": "GPIOD_IRQn", "num": 62, "enable": true, "priority": 3, "callback": "GPIOD_IRQHandler"}, {"name": "GPIOE_IRQn", "num": 63, "enable": true, "priority": 3, "callback": "GPIOE_IRQHandler"}, {"name": "Reserved17_IRQn", "num": 64, "enable": false, "priority": 0, "callback": "Reserved17_IRQHandler"}, {"name": "Reserved18_IRQn", "num": 65, "enable": false, "priority": 0, "callback": "Reserved18_IRQHandler"}, {"name": "Reserved19_IRQn", "num": 66, "enable": false, "priority": 0, "callback": "Reserved19_IRQHandler"}, {"name": "Reserved20_IRQn", "num": 67, "enable": false, "priority": 0, "callback": "Reserved20_IRQHandler"}, {"name": "PTU1_IRQn", "num": 68, "enable": false, "priority": 0, "callback": "PTU1_IRQHandler"}, {"name": "Reserved21_IRQn", "num": 69, "enable": false, "priority": 0, "callback": "Reserved21_IRQHandler"}, {"name": "Reserved22_IRQn", "num": 70, "enable": false, "priority": 0, "callback": "Reserved22_IRQHandler"}, {"name": "Reserved23_IRQn", "num": 71, "enable": false, "priority": 0, "callback": "Reserved23_IRQHandler"}, {"name": "Reserved24_IRQn", "num": 72, "enable": false, "priority": 0, "callback": "Reserved24_IRQHandler"}, {"name": "Reserved25_IRQn", "num": 73, "enable": false, "priority": 0, "callback": "Reserved25_IRQHandler"}, {"name": "Reserved26_IRQn", "num": 74, "enable": false, "priority": 0, "callback": "Reserved26_IRQHandler"}, {"name": "Reserved27_IRQn", "num": 75, "enable": false, "priority": 0, "callback": "Reserved27_IRQHandler"}, {"name": "Reserved28_IRQn", "num": 76, "enable": false, "priority": 0, "callback": "Reserved28_IRQHandler"}, {"name": "Reserved29_IRQn", "num": 77, "enable": false, "priority": 0, "callback": "Reserved29_IRQHandler"}, {"name": "CAN0_ORed_IRQn", "num": 78, "enable": false, "priority": 0, "callback": "CAN0_ORed_IRQHandler"}, {"name": "CAN0_Error_IRQn", "num": 79, "enable": false, "priority": 0, "callback": "CAN0_Error_IRQHandler"}, {"name": "CAN0_Wake_Up_IRQn", "num": 80, "enable": false, "priority": 0, "callback": "CAN0_Wake_Up_IRQHandler"}, {"name": "CAN0_ORed_0_15_MB_IRQn", "num": 81, "enable": false, "priority": 0, "callback": "CAN0_ORed_0_15_MB_IRQHandler"}, {"name": "CAN0_ORed_16_31_MB_IRQn", "num": 82, "enable": false, "priority": 0, "callback": "CAN0_ORed_16_31_MB_IRQHandler"}, {"name": "CAN0_ORed_32_47_MB_IRQn", "num": 83, "enable": false, "priority": 0, "callback": "CAN0_ORed_32_47_MB_IRQHandler"}, {"name": "CAN0_ORed_48_63_MB_IRQn", "num": 84, "enable": false, "priority": 0, "callback": "CAN0_ORed_48_63_MB_IRQHandler"}, {"name": "CAN1_ORed_IRQn", "num": 85, "enable": true, "priority": 3, "callback": "CAN1_ORed_IRQHandler"}, {"name": "CAN1_Error_IRQn", "num": 86, "enable": true, "priority": 3, "callback": "CAN1_Error_IRQHandler"}, {"name": "CAN1_Wake_Up_IRQn", "num": 87, "enable": false, "priority": 3, "callback": "CAN1_Wake_Up_IRQHandler"}, {"name": "CAN1_ORed_0_15_MB_IRQn", "num": 88, "enable": true, "priority": 3, "callback": "CAN1_ORed_0_15_MB_IRQHandler"}, {"name": "CAN1_ORed_16_31_MB_IRQn", "num": 89, "enable": true, "priority": 3, "callback": "CAN1_ORed_16_31_MB_IRQHandler"}, {"name": "CAN1_ORed_32_47_MB_IRQn", "num": 90, "enable": true, "priority": 3, "callback": "CAN1_ORed_32_47_MB_IRQHandler"}, {"name": "CAN1_ORed_48_63_MB_IRQn", "num": 91, "enable": true, "priority": 3, "callback": "CAN1_ORed_48_63_MB_IRQHandler"}, {"name": "CAN2_ORed_IRQn", "num": 92, "enable": true, "priority": 0, "callback": "CAN2_ORed_IRQHandler"}, {"name": "CAN2_Error_IRQn", "num": 93, "enable": true, "priority": 3, "callback": "CAN2_Error_IRQHandler"}, {"name": "CAN2_Wake_Up_IRQn", "num": 94, "enable": false, "priority": 3, "callback": "CAN2_Wake_Up_IRQHandler"}, {"name": "CAN2_ORed_0_15_MB_IRQn", "num": 95, "enable": true, "priority": 3, "callback": "CAN2_ORed_0_15_MB_IRQHandler"}, {"name": "CAN2_ORed_16_31_MB_IRQn", "num": 96, "enable": true, "priority": 3, "callback": "CAN2_ORed_16_31_MB_IRQHandler"}, {"name": "CAN2_ORed_32_47_MB_IRQn", "num": 97, "enable": true, "priority": 3, "callback": "CAN2_ORed_32_47_MB_IRQHandler"}, {"name": "CAN2_ORed_48_63_MB_IRQn", "num": 98, "enable": true, "priority": 3, "callback": "CAN2_ORed_48_63_MB_IRQHandler"}, {"name": "eTMR0_Ch0_Ch1_IRQn", "num": 99, "enable": false, "priority": 0, "callback": "eTMR0_Ch0_Ch1_IRQHandler"}, {"name": "eTMR0_Ch2_Ch3_IRQn", "num": 100, "enable": false, "priority": 0, "callback": "eTMR0_Ch2_Ch3_IRQHandler"}, {"name": "eTMR0_Ch4_Ch5_IRQn", "num": 101, "enable": false, "priority": 0, "callback": "eTMR0_Ch4_Ch5_IRQHandler"}, {"name": "eTMR0_Ch6_Ch7_IRQn", "num": 102, "enable": false, "priority": 0, "callback": "eTMR0_Ch6_Ch7_IRQHandler"}, {"name": "eTMR0_Fault_IRQn", "num": 103, "enable": false, "priority": 0, "callback": "eTMR0_Fault_IRQHandler"}, {"name": "eTMR0_Ovf_IRQn", "num": 104, "enable": false, "priority": 0, "callback": "eTMR0_Ovf_IRQHandler"}, {"name": "eTMR1_Ch0_Ch1_IRQn", "num": 105, "enable": false, "priority": 0, "callback": "eTMR1_Ch0_Ch1_IRQHandler"}, {"name": "eTMR1_Ch2_Ch3_IRQn", "num": 106, "enable": false, "priority": 0, "callback": "eTMR1_Ch2_Ch3_IRQHandler"}, {"name": "eTMR1_Ch4_Ch5_IRQn", "num": 107, "enable": false, "priority": 0, "callback": "eTMR1_Ch4_Ch5_IRQHandler"}, {"name": "eTMR1_Ch6_Ch7_IRQn", "num": 108, "enable": false, "priority": 0, "callback": "eTMR1_Ch6_Ch7_IRQHandler"}, {"name": "eTMR1_Fault_IRQn", "num": 109, "enable": false, "priority": 0, "callback": "eTMR1_Fault_IRQHandler"}, {"name": "eTMR1_Ovf_IRQn", "num": 110, "enable": false, "priority": 0, "callback": "eTMR1_Ovf_IRQHandler"}, {"name": "eTMR2_Ch0_Ch1_IRQn", "num": 111, "enable": false, "priority": 0, "callback": "eTMR2_Ch0_Ch1_IRQHandler"}, {"name": "eTMR2_Ch2_Ch3_IRQn", "num": 112, "enable": false, "priority": 0, "callback": "eTMR2_Ch2_Ch3_IRQHandler"}, {"name": "eTMR2_Ch4_Ch5_IRQn", "num": 113, "enable": false, "priority": 0, "callback": "eTMR2_Ch4_Ch5_IRQHandler"}, {"name": "eTMR2_Ch6_Ch7_IRQn", "num": 114, "enable": true, "priority": 2, "callback": "eTMR2_Ch6_Ch7_IRQHandler"}, {"name": "eTMR2_Fault_IRQn", "num": 115, "enable": false, "priority": 0, "callback": "eTMR2_Fault_IRQHandler"}, {"name": "eTMR2_Ovf_IRQn", "num": 116, "enable": false, "priority": 0, "callback": "eTMR2_Ovf_IRQHandler"}, {"name": "eTMR3_Ch0_Ch1_IRQn", "num": 117, "enable": false, "priority": 0, "callback": "eTMR3_Ch0_Ch1_IRQHandler"}, {"name": "eTMR3_Ch2_Ch3_IRQn", "num": 118, "enable": false, "priority": 0, "callback": "eTMR3_Ch2_Ch3_IRQHandler"}, {"name": "eTMR3_Ch4_Ch5_IRQn", "num": 119, "enable": false, "priority": 0, "callback": "eTMR3_Ch4_Ch5_IRQHandler"}, {"name": "eTMR3_Ch6_Ch7_IRQn", "num": 120, "enable": false, "priority": 0, "callback": "eTMR3_Ch6_Ch7_IRQHandler"}, {"name": "eTMR3_Fault_IRQn", "num": 121, "enable": false, "priority": 0, "callback": "eTMR3_Fault_IRQHandler"}, {"name": "eTMR3_Ovf_IRQn", "num": 122, "enable": false, "priority": 0, "callback": "eTMR3_Ovf_IRQHandler"}, {"name": "eTMR4_Ch0_Ch1_IRQn", "num": 123, "enable": false, "priority": 0, "callback": "eTMR4_Ch0_Ch1_IRQHandler"}, {"name": "eTMR4_Ch2_Ch3_IRQn", "num": 124, "enable": false, "priority": 0, "callback": "eTMR4_Ch2_Ch3_IRQHandler"}, {"name": "eTMR4_Ch4_Ch5_IRQn", "num": 125, "enable": false, "priority": 0, "callback": "eTMR4_Ch4_Ch5_IRQHandler"}, {"name": "eTMR4_Ch6_Ch7_IRQn", "num": 126, "enable": false, "priority": 0, "callback": "eTMR4_Ch6_Ch7_IRQHandler"}, {"name": "eTMR4_Fault_IRQn", "num": 127, "enable": false, "priority": 0, "callback": "eTMR4_Fault_IRQHandler"}, {"name": "eTMR4_Ovf_IRQn", "num": 128, "enable": false, "priority": 0, "callback": "eTMR4_Ovf_IRQHandler"}, {"name": "eTMR5_Ch0_Ch1_IRQn", "num": 129, "enable": false, "priority": 0, "callback": "eTMR5_Ch0_Ch1_IRQHandler"}, {"name": "eTMR5_Ch2_Ch3_IRQn", "num": 130, "enable": false, "priority": 0, "callback": "eTMR5_Ch2_Ch3_IRQHandler"}, {"name": "eTMR5_Ch4_Ch5_IRQn", "num": 131, "enable": false, "priority": 0, "callback": "eTMR5_Ch4_Ch5_IRQHandler"}, {"name": "eTMR5_Ch6_Ch7_IRQn", "num": 132, "enable": false, "priority": 0, "callback": "eTMR5_Ch6_Ch7_IRQHandler"}, {"name": "eTMR5_Fault_IRQn", "num": 133, "enable": false, "priority": 0, "callback": "eTMR5_Fault_IRQHandler"}, {"name": "eTMR5_Ovf_IRQn", "num": 134, "enable": false, "priority": 0, "callback": "eTMR5_Ovf_IRQHandler"}, {"name": "Reserved30_IRQn", "num": 135, "enable": false, "priority": 0, "callback": "Reserved30_IRQHandler"}, {"name": "Reserved31_IRQn", "num": 136, "enable": false, "priority": 0, "callback": "Reserved31_IRQHandler"}, {"name": "Reserved32_IRQn", "num": 137, "enable": false, "priority": 0, "callback": "Reserved32_IRQHandler"}, {"name": "Reserved33_IRQn", "num": 138, "enable": false, "priority": 0, "callback": "Reserved33_IRQHandler"}, {"name": "Reserved34_IRQn", "num": 139, "enable": false, "priority": 0, "callback": "Reserved34_IRQHandler"}, {"name": "Reserved35_IRQn", "num": 140, "enable": false, "priority": 0, "callback": "Reserved35_IRQHandler"}, {"name": "Reserved36_IRQn", "num": 141, "enable": false, "priority": 0, "callback": "Reserved36_IRQHandler"}, {"name": "Reserved37_IRQn", "num": 142, "enable": false, "priority": 0, "callback": "Reserved37_IRQHandler"}, {"name": "Reserved38_IRQn", "num": 143, "enable": false, "priority": 0, "callback": "Reserved38_IRQHandler"}, {"name": "Reserved39_IRQn", "num": 144, "enable": false, "priority": 0, "callback": "Reserved39_IRQHandler"}, {"name": "Reserved40_IRQn", "num": 145, "enable": false, "priority": 0, "callback": "Reserved40_IRQHandler"}, {"name": "Reserved41_IRQn", "num": 146, "enable": false, "priority": 0, "callback": "Reserved41_IRQHandler"}, {"name": "Reserved42_IRQn", "num": 147, "enable": false, "priority": 0, "callback": "Reserved42_IRQHandler"}, {"name": "Reserved43_IRQn", "num": 148, "enable": false, "priority": 0, "callback": "Reserved43_IRQHandler"}, {"name": "Reserved44_IRQn", "num": 149, "enable": false, "priority": 0, "callback": "Reserved44_IRQHandler"}, {"name": "Reserved45_IRQn", "num": 150, "enable": false, "priority": 0, "callback": "Reserved45_IRQHandler"}, {"name": "Reserved46_IRQn", "num": 151, "enable": false, "priority": 0, "callback": "Reserved46_IRQHandler"}, {"name": "Reserved47_IRQn", "num": 152, "enable": false, "priority": 0, "callback": "Reserved47_IRQHandler"}, {"name": "Reserved48_IRQn", "num": 153, "enable": false, "priority": 0, "callback": "Reserved48_IRQHandler"}, {"name": "Reserved49_IRQn", "num": 154, "enable": false, "priority": 0, "callback": "Reserved49_IRQHandler"}, {"name": "Reserved50_IRQn", "num": 155, "enable": false, "priority": 0, "callback": "Reserved50_IRQHandler"}, {"name": "TRNG_IRQn", "num": 156, "enable": false, "priority": 0, "callback": "TRNG_IRQHandler"}, {"name": "HCU_IRQn", "num": 157, "enable": false, "priority": 0, "callback": "HCU_IRQHandler"}, {"name": "INTM_IRQn", "num": 158, "enable": false, "priority": 0, "callback": "INTM_IRQHandler"}, {"name": "TMR0_Ch0_IRQn", "num": 159, "enable": false, "priority": 0, "callback": "TMR0_Ch0_IRQHandler"}, {"name": "TMR0_Ch1_IRQn", "num": 160, "enable": false, "priority": 0, "callback": "TMR0_Ch1_IRQHandler"}, {"name": "TMR0_Ch2_IRQn", "num": 161, "enable": false, "priority": 0, "callback": "TMR0_Ch2_IRQHandler"}, {"name": "TMR0_Ch3_IRQn", "num": 162, "enable": false, "priority": 0, "callback": "TMR0_Ch3_IRQHandler"}, {"name": "LINFlexD3_IRQn", "num": 163, "enable": true, "priority": 3, "callback": "LINFlexD3_IRQHandler"}, {"name": "LINFlexD4_IRQn", "num": 164, "enable": true, "priority": 3, "callback": "LINFlexD4_IRQHandler"}, {"name": "LINFlexD5_IRQn", "num": 165, "enable": false, "priority": 0, "callback": "LINFlexD5_IRQHandler"}, {"name": "I2C2_Master_IRQn", "num": 166, "enable": true, "priority": 3, "callback": "I2C2_Master_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "I2C2_Slave_IRQn", "num": 167, "enable": false, "priority": 0, "callback": "I2C2_Slave_IRQHandler"}, {"name": "SPI3_IRQn", "num": 168, "enable": true, "priority": 3, "callback": "SPI3_IRQHandler"}, {"name": "SPI4_IRQn", "num": 169, "enable": false, "priority": 0, "callback": "SPI4_IRQHandler"}, {"name": "SPI5_IRQn", "num": 170, "enable": false, "priority": 0, "callback": "SPI5_IRQHandler"}, {"name": "CAN3_ORed_IRQn", "num": 171, "enable": true, "priority": 3, "callback": "CAN3_ORed_IRQHandler"}, {"name": "CAN3_Error_IRQn", "num": 172, "enable": true, "priority": 3, "callback": "CAN3_Error_IRQHandler"}, {"name": "CAN3_Wake_Up_IRQn", "num": 173, "enable": false, "priority": 0, "callback": "CAN3_Wake_Up_IRQHandler"}, {"name": "CAN3_ORed_0_15_MB_IRQn", "num": 174, "enable": true, "priority": 3, "callback": "CAN3_ORed_0_15_MB_IRQHandler"}, {"name": "CAN3_ORed_16_31_MB_IRQn", "num": 175, "enable": true, "priority": 3, "callback": "CAN3_ORed_16_31_MB_IRQHandler"}, {"name": "Reserved51_IRQn", "num": 176, "enable": false, "priority": 0, "callback": "Reserved51_IRQHandler"}, {"name": "Reserved52_IRQn", "num": 177, "enable": false, "priority": 0, "callback": "Reserved52_IRQHandler"}, {"name": "CAN4_ORed_IRQn", "num": 178, "enable": false, "priority": 0, "callback": "CAN4_ORed_IRQHandler"}, {"name": "CAN4_Error_IRQn", "num": 179, "enable": false, "priority": 0, "callback": "CAN4_Error_IRQHandler"}, {"name": "CAN4_Wake_Up_IRQn", "num": 180, "enable": false, "priority": 0, "callback": "CAN4_Wake_Up_IRQHandler"}, {"name": "CAN4_ORed_0_15_MB_IRQn", "num": 181, "enable": false, "priority": 0, "callback": "CAN4_ORed_0_15_MB_IRQHandler"}, {"name": "CAN4_ORed_16_31_MB_IRQn", "num": 182, "enable": false, "priority": 0, "callback": "CAN4_ORed_16_31_MB_IRQHandler"}, {"name": "Reserved53_IRQn", "num": 183, "enable": false, "priority": 0, "callback": "Reserved53_IRQHandler"}, {"name": "Reserved54_IRQn", "num": 184, "enable": false, "priority": 0, "callback": "Reserved54_IRQHandler"}, {"name": "CAN5_ORed_IRQn", "num": 185, "enable": true, "priority": 3, "callback": "CAN5_ORed_IRQHandler"}, {"name": "CAN5_Error_IRQn", "num": 186, "enable": true, "priority": 3, "callback": "CAN5_Error_IRQHandler"}, {"name": "CAN5_Wake_Up_IRQn", "num": 187, "enable": false, "priority": 0, "callback": "CAN5_Wake_Up_IRQHandler"}, {"name": "CAN5_ORed_0_15_MB_IRQn", "num": 188, "enable": true, "priority": 3, "callback": "CAN5_ORed_0_15_MB_IRQHandler"}, {"name": "CAN5_ORed_16_31_MB_IRQn", "num": 189, "enable": true, "priority": 3, "callback": "CAN5_ORed_16_31_MB_IRQHandler"}, {"name": "WKU_IRQn", "num": 190, "enable": true, "priority": 2, "callback": "WKU_IRQHandler"}], "updateTime": 1711350786564, "lock": true}, "linflexd_lin": {"enable": {}, "data": [{"name": "linflexd_lin_config0", "baudrate": 19200, "breakLength": "LINFlexD_BREAK_13_BIT", "nodeFunction": true, "autobaudEnable": false, "timeoutEnable": true, "responseTimeoutValue": 15, "headerTimeoutValue": 20, "filter": []}], "updateTime": 1711350784967, "lock": false}, "linflexd_uart": {"enable": {}, "data": [{"name": "linflexd3_uart_config_921600bps", "readonly": true, "baudrate": 921600, "parityCheck": false, "parityType": "LINFlexD_UART_PARITY_EVEN", "stopBitsCount": "LINFlexD_UART_ONE_STOP_BIT", "wordLength": "LINFlexD_UART_8_BITS", "txTransferType": "LINFlexD_UART_USING_INTERRUPTS", "rxTransferType": "LINFlexD_UART_USING_INTERRUPTS", "txDMAChannel": 0, "rxDMAChannel": 0}, {"name": "linflexd0_uart_config_115200bps", "readonly": true, "baudrate": 115200, "parityCheck": false, "parityType": "LINFlexD_UART_PARITY_EVEN", "stopBitsCount": "LINFlexD_UART_ONE_STOP_BIT", "wordLength": "LINFlexD_UART_8_BITS", "txTransferType": "LINFlexD_UART_USING_INTERRUPTS", "rxTransferType": "LINFlexD_UART_USING_INTERRUPTS", "txDMAChannel": 0, "rxDMAChannel": 0}, {"name": "linflexd4_uart_config_115200bps", "readonly": true, "baudrate": 115200, "parityCheck": false, "parityType": "LINFlexD_UART_PARITY_EVEN", "stopBitsCount": "LINFlexD_UART_ONE_STOP_BIT", "wordLength": "LINFlexD_UART_8_BITS", "txTransferType": "LINFlexD_UART_USING_INTERRUPTS", "rxTransferType": "LINFlexD_UART_USING_INTERRUPTS", "txDMAChannel": 0, "rxDMAChannel": 0}, {"name": "linflexd3_uart_config_576000bps", "readonly": true, "baudrate": 576000, "parityCheck": false, "parityType": "LINFlexD_UART_PARITY_EVEN", "stopBitsCount": "LINFlexD_UART_ONE_STOP_BIT", "wordLength": "LINFlexD_UART_8_BITS", "txTransferType": "LINFlexD_UART_USING_INTERRUPTS", "rxTransferType": "LINFlexD_UART_USING_INTERRUPTS", "txDMAChannel": 0, "rxDMAChannel": 0}], "updateTime": 1713515829599, "lock": false}, "pins": {"enable": {}, "data": [{"base": "PCTRLE", "gpioBase": "GPIOE", "num": "2", "label": "PTE_15", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "1", "userLabel": "WHEEL_TICK_IO", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "5", "label": "PTE_11", "feature": "LINFlexD3_TX", "alt": "PCTRL_MUX_ALT5", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 1, "pullSelect": "PCTRL_INTERNAL_PULL_UP_ENABLED", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "UART_TXD_4G", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "6", "label": "PTE_10", "feature": "LINFlexD3_RX", "alt": "PCTRL_MUX_ALT5", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 1, "pullSelect": "PCTRL_INTERNAL_PULL_UP_ENABLED", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "UART_RXD_4G", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "7", "label": "PTE_13", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "T106_STATUS_MCU", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "8", "label": "PTE_5", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "B_KEY_INPUT", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "9", "label": "PTE_4", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "I_KEY_INPUT", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "15", "label": "PTB_7", "feature": "EXTAL", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "24MHz_FXOSC", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "16", "label": "PTB_6", "feature": "XTAL", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "24MHz_FXOSC", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "17", "label": "PTE_14", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "1", "userLabel": "FWD_IO", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "18", "label": "PTE_3", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "SOS_KEY_INPUT", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "19", "label": "PTE_12", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "MCU_power_wifi", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "20", "label": "PTD_17", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "MCU_POWER_VCC8V", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "21", "label": "PTD_16", "feature": "EXTAL32", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "22", "label": "PTD_15", "feature": "XTAL32", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "23", "label": "PTE_9", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "ACC_INPUT", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "24", "label": "PTD_14", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "1", "userLabel": "MCU_RST_GNSS", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "25", "label": "PTD_13", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "T106_NET_STATUS_MCU", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "26", "label": "PTE_8", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_INT_RISING_EDGE", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "BLE_OUTPUT_INT", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "27", "label": "PTB_5", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "MCU_WAKEUP_T106", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "29", "label": "PTC_3", "feature": "LINFlexD0_TX", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "MCU_UART2_TX", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "30", "label": "PTC_2", "feature": "LINFlexD0_RX", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "MCU_UART2_RX", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "31", "label": "PTD_7", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "LED1", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "32", "label": "PTD_6", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "LED2", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "33", "label": "PTD_5", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "RTC_INT", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "34", "label": "PTD_12", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "0", "userLabel": "BLE_WAKEUP_CTL", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "35", "label": "PTD_11", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "1", "userLabel": "BLE_RST_CTL", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "39", "label": "PTC_1", "feature": "CAN3_TX", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "40", "label": "PTC_0", "feature": "CAN3_RX", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "41", "label": "PTD_9", "feature": "LINFlexD4_TX", "alt": "PCTRL_MUX_ALT2", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "42", "label": "PTD_8", "feature": "LINFlexD4_RX", "alt": "PCTRL_MUX_ALT2", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "43", "label": "PTC_17", "feature": "ADC0_SE15", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "44", "label": "PTC_16", "feature": "ADC0_SE14", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "45", "label": "PTC_15", "feature": "ADC0_SE13", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "46", "label": "PTC_14", "feature": "ADC0_SE12", "alt": "PCTRL_PIN_DISABLED", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "47", "label": "PTB_3", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "0", "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "48", "label": "PTB_2", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "49", "label": "PTC_13", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "51", "label": "PTC_11", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "52", "label": "PTC_10", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "53", "label": "PTB_1", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "54", "label": "PTB_0", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "55", "label": "PTC_9", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "56", "label": "PTC_8", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "57", "label": "PTA_7", "feature": "I2C2_SCL", "alt": "PCTRL_MUX_ALT5", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "58", "label": "PTA_6", "feature": "I2C2_SDA", "alt": "PCTRL_MUX_ALT5", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "59", "label": "PTE_7", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "62", "label": "PTA_17", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "63", "label": "PTB_17", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "64", "label": "PTB_16", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "1", "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "65", "label": "PTB_15", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "66", "label": "PTB_14", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "67", "label": "PTB_13", "feature": "CAN2_TX", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "68", "label": "PTB_12", "feature": "CAN2_RX", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "69", "label": "PTD_4", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "70", "label": "PTD_3", "feature": "CAN5_TX", "alt": "PCTRL_MUX_ALT5", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLD", "gpioBase": "GPIOD", "num": "71", "label": "PTD_2", "feature": "CAN5_RX", "alt": "PCTRL_MUX_ALT5", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "72", "label": "PTA_3", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "73", "label": "PTA_2", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "74", "label": "PTB_11", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "75", "label": "PTB_10", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "76", "label": "PTB_9", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLB", "gpioBase": "GPIOB", "num": "77", "label": "PTB_8", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "78", "label": "PTA_1", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "79", "label": "PTA_0", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "80", "label": "PTC_7", "feature": "CAN1_TX", "alt": "PCTRL_MUX_ALT3", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "81", "label": "PTC_6", "feature": "CAN1_RX", "alt": "PCTRL_MUX_ALT3", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "82", "label": "PTA_16", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "83", "label": "PTA_15", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "84", "label": "PTE_6", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "1", "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "85", "label": "PTE_2", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "88", "label": "PTA_14", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "89", "label": "PTA_13", "feature": "SPI3_PCS0", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "90", "label": "PTA_12", "feature": "SPI3_SCK", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "91", "label": "PTA_11", "feature": "SPI3_SIN", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "92", "label": "PTA_10", "feature": "SPI3_SOUT", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "93", "label": "PTE_1", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "94", "label": "PTE_0", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_INPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "95", "label": "PTC_5", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "96", "label": "PTC_4", "feature": "JTAG_TCK SWD_CLK", "alt": "PCTRL_MUX_ALT7", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "97", "label": "PTA_5", "feature": "RESET_b", "alt": "PCTRL_MUX_ALT7", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "98", "label": "PTA_4", "feature": "JTAG_TMS SWD_IO", "alt": "PCTRL_MUX_ALT7", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "99", "label": "PTA_9", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLA", "gpioBase": "GPIOA", "num": "100", "label": "PTA_8", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLC", "gpioBase": "GPIOC", "num": "50", "label": "PTC_12", "feature": "GPIO", "alt": "PCTRL_MUX_AS_GPIO", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "GPIO_OUTPUT_DIRECTION", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": "0", "userLabel": "", "rateSelect": "PCTRL_FAST_SLEW_RATE"}, {"base": "PCTRLE", "gpioBase": "GPIOE", "num": "1", "label": "PTE_16", "feature": "eTMR2_CH7", "alt": "PCTRL_MUX_ALT4", "intCfg": "PCTRL_DMA_INT_DISABLED", "direction": "", "pullEnable": 0, "pullSelect": "", "filter": false, "passiveFilter": false, "strength": "PCTRL_LOW_DRIVE_STRENGTH", "intFlag": false, "init": 0, "userLabel": "AIRBAG", "rateSelect": "PCTRL_FAST_SLEW_RATE"}], "updateTime": 1711532289267, "lock": true}, "power": {"enable": {}, "data": {"user": [{"name": "RUN", "powerMode": "POWER_MANAGER_RUN", "sleepOnExitValue": false}, {"name": "PowerDown", "powerMode": "POWER_MANAGER_POWERDOWN", "sleepOnExitValue": false}], "callback": []}, "updateTime": 1711350784975, "lock": true}, "ptmr": {"enable": {}, "data": {"name": "PTMR_Config", "readonly": true, "enableRunInDebug": false, "channel": [{"channel": 0, "periodUnits": "pTMR_PERIOD_UNITS_MICROSECONDS", "period": 10000, "chainChannel": false, "isInterruptEnabled": true}, {"channel": 1, "periodUnits": "pTMR_PERIOD_UNITS_MICROSECONDS", "period": 100000, "chainChannel": false, "isInterruptEnabled": true}, {"channel": 2, "periodUnits": "pTMR_PERIOD_UNITS_MICROSECONDS", "period": 500000, "chainChannel": false, "isInterruptEnabled": true}]}, "updateTime": 1711350784714, "lock": true}, "rtc": {"enable": {}, "data": [{"name": "rtc_config0", "readonly": true, "compensationInterval": 0, "compensation": 0, "clockSource": "RTC_CLK_SRC_OSC_32KHZ", "clockOutConfig": "RTC_CLKOUT_DISABLED", "debugEnable": false, "rtcAlarmConfig": {"data1": "2024-02-29T16:00:00.000Z", "data2": "2024-03-01T08:26:16.000Z", "alarmTime": {"year": 2024, "month": 3, "day": 1, "hour": 16, "minutes": 26, "seconds": 16}, "repetitionInterval": 0, "numberOfRepeats": 0, "repeatForever": false, "alarmIntEnable": false, "rtcAlarmCallback": "NULL", "callbackParams": "NULL"}, "rtcOverflowConfig": {"overflowIntEnable": false, "rtcOverflowCallback": "NULL", "callbackParams": "NULL"}, "rtcSecondsConfig": {"secondsIntConfig": "RTC_INT_1HZ", "secondsIntEnable": true, "rtcSecondsCallback": "NULL", "callbackParams": "NULL"}}], "updateTime": 1711350786556, "lock": false}, "spi": {"enable": {}, "data": {"master": [{"name": "lpspi_MasterConfig_2Mbps", "readonly": true, "baudrate": 200000, "pcs": "SPI_PCS0", "polarity": "SPI_ACTIVE_LOW", "isPcsContinuous": false, "bitcount": 8, "clkPhase": "SPI_CLOCK_PHASE_1ST_EDGE", "clkPolarity": "SPI_SCK_ACTIVE_HIGH", "lsbFirst": false, "txDMAChannel": 0, "rxDMAChannel": 0, "transferType": "SPI_USING_INTERRUPTS", "callback": "NULL", "callbackParam": "NULL", "width": "SPI_SINGLE_BIT_XFER"}], "slave": [{"name": "lpspi_SlaveConfig0", "readonly": true, "pcs": "SPI_PCS0", "polarity": "SPI_ACTIVE_LOW", "bitcount": 8, "clkPhase": "SPI_CLOCK_PHASE_1ST_EDGE", "clkPolarity": "SPI_SCK_ACTIVE_HIGH", "lsbFirst": false, "txDMAChannel": 0, "rxDMAChannel": 0, "transferType": "SPI_USING_INTERRUPTS", "callback": "NULL", "callbackParam": "NULL", "width": "SPI_SINGLE_BIT_XFER"}]}, "updateTime": 1711350786497, "lock": true}, "utility_print": {"enable": {}, "data": {"mode": "full_printf", "rtt": false, "initUart": true, "initUartInst": 0, "initUartName": "linflexd0_uart_config_115200bps"}, "updateTime": 1711532296654, "lock": true}, "wdg": {"enable": {}, "data": [{"name": "wdg_config0", "readonly": true, "clockSource": "WDG_SXOSC_CLOCK", "opMode": {"deepsleep": false, "debug": false}, "updateEnable": false, "intEnable": false, "winEnable": false, "windowValue": 0, "timeoutValue": 32768, "apbErrorResetEnable": 0}], "updateTime": 1711350786579, "lock": true}, "wkup": {"enable": {}, "data": [{"resetName": "wkup_resetConfig0", "pinsName": "wkup_pinsConfig0", "readonly": true, "wakeupEn": true, "filterEn": false, "filterClkSrc": "WKU_FILTER_CLK_SIRC", "wkupPins": []}], "updateTime": 1711350784971, "lock": true}, "ytlink": {"enable": {}, "data": [{"name": "P_FLASH", "level": 0, "isFlash": true, "id": "9ffa6a74-339d-44c8-ae55-7afd70fcecd7", "size": "0x100000", "startAddress": 0, "flags": "50.00%", "children": [{"name": "IVT", "level": 1, "id": "2315c9ba-7031-4f64-a7ef-e6879fd98618", "size": "1024", "order": 0, "startAddress": "0x4000", "isFlash": true, "boundary": "UPPER", "x": 200, "y": -310, "children": [{"name": "IVT", "level": 2, "id": "6baab512-6f23-40fc-ace2-3895338576d4", "size": "", "order": 0, "startAddress": 0, "isFlash": true, "x": 500, "y": -270, "children": [{"name": "IVT", "level": 3, "id": "0627beab-7123-40c5-a18d-b9bcf8e1296d", "size": "", "order": 0, "startAddress": 0, "isFlash": true, "x": 790, "y": -270, "children": [{"name": "isr_vector", "level": 4, "id": "10a3d117-4a5c-428f-8462-c10daca96e93", "size": "", "order": 0, "startAddress": 0, "isFlash": true, "flags": "KEEP", "x": 1130, "y": -270}]}]}]}, {"name": "TEXT", "level": 1, "id": "5dacd8b2-a943-4507-a9df-17ddcbbd07d5", "size": "0x7B400", "order": 2, "startAddress": "0x4400", "isFlash": true, "boundary": "UPPER", "x": 200, "y": -250, "children": [{"name": "COPY", "level": 2, "id": "a27335d8-5cf1-4761-8494-6dad793d4095", "size": "", "order": 2, "startAddress": 0, "isFlash": true, "x": 500, "y": 70, "children": [{"name": "CODE_FLASH", "level": 3, "id": "4c03ad2b-89c2-410f-a6d5-1ac63e348b28", "size": "", "order": 0, "align": 4, "startAddress": 0, "flags": "NULL", "isFlash": true, "x": 800, "y": 80}, {"name": "DATA_FLASH", "level": 3, "id": "0670194a-8864-4c69-bab9-c9abf3e2cdcb", "size": "", "order": 0, "align": 4, "startAddress": 0, "flags": "NULL", "isFlash": true, "x": 1130, "y": 80}]}, {"name": "TEXT", "level": 2, "id": "7b2b296e-e4ab-4ac2-a7c2-d4ef382a74a8", "size": "", "order": 0, "startAddress": 0, "isFlash": true, "x": 500, "y": -110, "children": [{"name": "TEXT", "level": 3, "id": "79ac2245-96a6-45c2-bcde-7c487fdad01c", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": true, "x": 800, "y": -110, "children": [{"name": "text", "level": 4, "id": "732b76e1-9154-44bd-82dc-dfce0db96064", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": true, "x": 1130, "y": -70, "wildcard": true}, {"name": "rodata", "level": 4, "id": "aa3d2cd7-5f0e-46fa-9861-ee1ee470efa4", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": true, "x": 1130, "y": -130, "binFile": "", "wildcard": true}]}]}, {"name": "ARM", "level": 2, "id": "70d2211e-2e86-40d9-8441-f391dc248280", "size": "", "order": 1, "startAddress": "", "flags": "", "isFlash": true, "x": 500, "y": -40, "children": [{"name": "ARM", "level": 3, "id": "d3a2d077-bba9-4bdf-b6e2-735d90480621", "size": "", "order": 0, "startAddress": "", "flags": "", "isFlash": true, "x": 800, "y": -30, "children": [{"name": "ARM.exidx", "level": 4, "id": "71c48f4c-5d57-408d-98a8-acf8835d4f5c", "size": "", "order": 0, "startAddress": "", "flags": "NULL", "isFlash": true, "wildcard": true, "x": 1130, "y": -10}]}]}]}, {"name": "Boot", "level": 1, "id": "bd6b1b7b-4864-4128-b42d-093e108502dd", "size": "0x4000", "order": 0, "startAddress": "0x0", "flags": "", "isFlash": true, "boundary": "UPPER", "x": 200, "y": -380}, {"name": "BVT", "level": 1, "id": "7ca1b3f5-5bf5-4f28-8630-f0dafc228794", "size": "0X800", "order": 3, "startAddress": "0x7f800", "flags": "", "isFlash": true, "boundary": "UPPER", "x": 200, "y": 180, "children": [{"name": "BVT", "level": 2, "id": "8eb868df-f047-41a3-99b6-56a556489e9f", "size": "", "order": 0, "startAddress": "", "flags": "", "isFlash": true, "x": 500, "y": 180, "children": [{"name": "BVT", "level": 3, "id": "c5b0acfd-7b69-4567-a476-51d13f9f5eb5", "size": "", "order": 0, "startAddress": "", "flags": "", "isFlash": true, "x": 800, "y": 180, "children": [{"name": "bvt_header", "level": 4, "id": "12c325cd-f4ba-4184-b47e-0881ed14547b", "size": "", "order": 0, "startAddress": "", "flags": "KEEP", "isFlash": true, "align": 4, "x": 1130, "y": 150}, {"name": "sb_config_group", "level": 4, "id": "fc9ed204-ef3c-4df3-9496-f78f58bd7bb1", "size": "", "order": 1, "startAddress": "", "flags": "KEEP", "isFlash": true, "align": 16, "x": 1130, "y": 210}, {"name": "sb_config_section", "level": 4, "id": "ad500a86-d42f-4980-be88-7b6675287afd", "size": "", "order": 2, "startAddress": "", "flags": "KEEP", "isFlash": true, "align": 16, "x": 1130, "y": 270}, {"name": "sb_cmac", "level": 4, "id": "64055bc0-3699-47f6-b1a2-f94b2f38fcbb", "size": "", "order": 3, "startAddress": "", "flags": "KEEP", "isFlash": true, "align": 16, "x": 1130, "y": 330}]}]}]}], "x": -180, "y": -260}, {"name": "D-FLASH", "level": 0, "isFlash": true, "id": "e8be57ed-cb5d-473e-b380-947fb73563c5", "size": "0x40000", "startAddress": "0x100000", "y": 180, "flags": "0.00%", "x": -180, "children": []}, {"name": "RAM", "level": 0, "isFlash": false, "id": "71eff99e-ec18-4495-b4d3-8748da44c927", "size": "0x20000", "startAddress": "0x1FFF0000", "y": 530, "flags": "100.00%", "x": -180, "children": [{"name": "RAM", "level": 1, "id": "041e1a12-a38d-474a-8611-3c5fe39ad680", "size": "0x1F800", "order": 1, "startAddress": "0x1fff0400", "isFlash": false, "boundary": "UPPER", "x": 200, "y": 520, "children": [{"name": "BSS", "level": 2, "id": "8512e2b3-ab47-456f-9c32-b532eb942f24", "size": "", "order": 1, "startAddress": 0, "isFlash": false, "clear": true, "x": 510, "y": 440, "children": [{"name": "BSS", "level": 3, "id": "c9976186-8bd7-4212-b091-574a82d6befd", "size": "", "order": 0, "startAddress": 0, "flags": "NOLOAD", "isFlash": false, "x": 780, "y": 440, "clear": true, "children": [{"name": "bss", "level": 4, "id": "6f24916e-dd23-4673-9c01-734802c0155a", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "x": 1110, "y": 440, "wildcard": true}]}]}, {"name": "DATA", "level": 2, "id": "fb79c358-1b80-4539-9e23-8d08877dcdd0", "size": "", "order": 1, "startAddress": 0, "isFlash": false, "x": 510, "y": 550, "children": [{"name": "CODE_RAM", "level": 3, "id": "ecd50e6e-163f-4c04-a0fc-18a14c4c86f7", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "x": 780, "y": 540, "align": 4, "copyFrom": "4c03ad2b-89c2-410f-a6d5-1ac63e348b28", "children": [{"name": "code_ram", "level": 4, "id": "f767db5f-c8bb-4683-b572-e39617dccb6c", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "x": 1110, "y": 540}]}, {"name": "DATA_RAM", "level": 3, "id": "de9985d9-e89a-4e88-8da6-2c6128c635ec", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "x": 780, "y": 630, "align": 4, "copyFrom": "0670194a-8864-4c69-bab9-c9abf3e2cdcb", "children": [{"name": "data", "level": 4, "id": "c5361333-f443-40b9-8566-ac8d8ef209e2", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "x": 1110, "y": 630, "wildcard": true}]}]}], "init": "NORMAL"}, {"name": "STACK", "level": 1, "id": "1c0909ef-5ee3-4499-b278-40e777069370", "size": "0x400", "order": 0, "startAddress": "0x2000fc00", "isFlash": false, "boundary": "LOWER", "x": 200, "y": 700, "children": [{"name": "STACK", "level": 2, "id": "0d1dd0ef-0478-4096-880a-db88f3301f88", "size": "", "order": 0, "startAddress": 0, "isFlash": false, "x": 510, "y": 740, "children": [{"name": "STACK", "level": 3, "id": "08b94bc2-7906-47c6-b1f0-f9afcecff0f4", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "x": 780, "y": 740, "groupSize": "1024"}]}], "init": "NORMAL"}, {"name": "IVT_RAM", "level": 1, "id": "91022760-091d-4db2-9815-f7feb273710d", "size": "0x400", "order": 0, "startAddress": "0x1fff0000", "isFlash": false, "init": "NORMAL", "boundary": "UPPER", "x": 200, "y": 410, "children": [{"name": "IVT_RAM", "level": 2, "id": "9755c110-d819-47e1-a09d-abb77c1f76da", "size": "", "order": 0, "startAddress": 0, "isFlash": false, "x": 510, "y": 350, "children": [{"name": "IVT_RAM", "level": 3, "id": "8d153954-ea4e-4071-898e-2531bdfc2fa2", "size": "", "order": 0, "startAddress": 0, "flags": "NULL", "isFlash": false, "groupSize": "0X400", "align": 1024, "x": 780, "y": 350}]}]}]}], "updateTime": 1711534387461, "lock": false}}}