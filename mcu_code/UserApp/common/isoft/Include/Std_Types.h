/*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <MemMap.h>
 *  @brief      <Briefly describe file(one line)>
 *  
 *  <Compiler: All Compilers    MCU:All MCUs>
 *  
 *  <AUTHOR> xue hua>
 *  @date       <2013-02-27>
 */
/*============================================================================*/
/* @req STD003 @req STD004 @req STD014 */
#ifndef STD_TYPES_H
#define STD_TYPES_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130227  chenxuehua  Initial version
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define STD_TYPES_VENDOR_ID  62
#define STD_TYPES_MODULE_ID  0
#define STD_TYPES_AR_MAJOR_VERSION  1
#define STD_TYPES_AR_MINOR_VERSION  1
#define STD_TYPES_AR_PATCH_VERSION  2
#define STD_TYPES_SW_MAJOR_VERSION  1
#define STD_TYPES_SW_MINOR_VERSION  0
#define STD_TYPES_SW_PATCH_VERSION  0
#define STD_TYPES_VENDOR_API_INFIX  0

/*=======[I N C L U D E S]====================================================*/
/* @req STD016 @req STD001 */
#include "Platform_Types.h"
#include "Compiler.h"

/*=======[M A C R O S]========================================================*/
/* @req STD011 @req STD006 */
#ifndef STATUSTYPEDEFINED
#define STATUSTYPEDEFINED
#ifndef E_OK
#define E_OK        0x00
#endif
typedef unsigned char StatusType; /* OSEK compliance */
#endif
#ifndef E_NOT_OK
#define E_NOT_OK    0x01
#endif

/* @req STD007 */
#define STD_LOW        0x00 /* Physical state 0V         */
#define STD_HIGH    0x01 /* Physical state 5V or 3.3V */

/* @req STD013 */
#define STD_IDLE    0x00 /* Logical state active */
#define STD_ACTIVE    0x01 /* Logical state idle   */

/* @req STD010 */
#ifndef STD_OFF
#define STD_OFF        0x00
#endif

#ifndef STD_ON
#define STD_ON        0x01
#endif


#define VARIANT_PRE_COMPILE    0x00 /* module config use variant pre compile */
#define VARIANT_LINK_TIME      0x01 /* module config use variant link time */
#define VARIANT_POST_BUILD     0x02 /* module config use variant post build */

/*=======[T Y P E   D E F I N I T I O N S]====================================*/
/*
 * This type can be used as standard API return type which is shared between the 
 * RTE and the BSW modules.
 * 0x02-0x3F used for user specific errors, 0x40-0xFF used for RTE 
 */ 
typedef uint8 Std_ReturnType;/* @req STD005 */

/* @req STD015 */
/* 
 * This type shall be used to request the version of a BSW module using the 
 * <Module name>_GetVersionInfo() function.
 */
typedef struct
{
    uint16 vendorID;
    uint16 moduleID;
    uint8  instanceID;
    uint8  sw_major_version;
    uint8  sw_minor_version;
    uint8  sw_patch_version;
}Std_VersionInfoType;

#endif /* end of STD_TYPES_H */

/*=======[E N D   O F   F I L E]==============================================*/
