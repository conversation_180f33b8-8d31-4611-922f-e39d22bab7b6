/*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Compiler_Cfg.h>
 *  @brief      <Briefly describe file(one line)>
 *  
 *  <Compiler: CodeWarriar    MCU:MPC55XX>
 *  
 *  <AUTHOR> xue hua>
 *  @date       <2013-02-27>
 */
/*============================================================================*/
#ifndef COMPILER_CFG_H
#define COMPILER_CFG_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130227  chenxuehua  Initial version
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define COMPILER_CFG_VENDOR_ID         62
#define COMPILER_CFG_MODULE_ID         0
#define COMPILER_CFG_AR_MAJOR_VERSION  2
#define COMPILER_CFG_AR_MINOR_VERSION  0
#define COMPILER_CFG_AR_PATCH_VERSION  2
#define COMPILER_CFG_SW_MAJOR_VERSION  1
#define COMPILER_CFG_SW_MINOR_VERSION  0
#define COMPILER_CFG_SW_PATCH_VERSION  0
#define COMPILER_CFG_VENDOR_API_INFIX  0

/* @req COMPILER055 @req COMPILER054 */
/*=======[M A C R O S]========================================================*/

/* @req COMPILER044 @req COMPILER040 */
/*=======[Can_MemoryAndPointerClasses]========================================*/
/* Configurable memory class for code. */
#define CAN_CODE

/* Configurable memory class for ISR code. */
#define CAN_CODE_FAST

/* 
 * Configurable memory class for all global or static variables that are never 
 * initialized. 
 */
#define CAN_VAR_NOINIT

/* 
 * Configurable memory class for all global or static variables that are 
 * initialized only after power on reset. 
 */
#define CAN_VAR_POWER_ON_INIT

/*
 * Configurable memory class for all global or static variables that are 
 * initialized after every reset. 
 */
#define CAN_VAR

/* 
 * Configurable memory class for all global or static variables that will 
 * be accessed frequently. 
 */
#define CAN_VAR_NOINIT_FAST

/* 
 * Configurable memory class for all global or static variables that have at 
 * be accessed frequently. 
 */
#define CAN_VAR_POWER_ON_INIT_FAST

/* 
 * Configurable memory class for all global or static variables that have at 
 * be accessed frequently. 
 */
#define CAN_VAR_FAST 

/* Configurable memory class for global or static constants. */
#define CAN_CONST

/*
 * Configurable memory class for global or static constants that will be 
 * accessed frequently.
 */
#define CAN_CONST_FAST

/* Configurable memory class for global or static constants in post build. */
#define CAN_CONST_PBCFG

/*
 * Configurable memory class for pointers to applicaiton data(expected to be 
 * in RAM or ROM)passed via API.
 */
#define CAN_APPL_DATA

/* 
 * Configurable memory class for pointers to applicaiton constants(expected to
 * be certainly in ROM,for instance point of Init-function)passed via API.
 */
#define CAN_APPL_CONST

/* 
 * Configurable memory class for pointers to applicaiton functions(e.g. call 
 * back function pointers).
 */
#define CAN_APPL_CODE

/*=======[CanIf_MemoryAndPointerClasses]========================================*/
#define CANIF_CODE

#define CANIF_CODE_FAST

#define CANIF_VAR_NOINIT

#define CANIF_VAR_POWER_ON_INIT

#define CANIF_VAR

#define CANIF_VAR_NOINIT_FAST

#define CANIF_VAR_POWER_ON_INIT_FAST

#define CANIF_VAR_FAST 

#define CANIF_CONST

#define CANIF_CONST_FAST

#define CANIF_CONST_PBCFG

#define CANIF_APPL_DATA

#define CANIF_APPL_CONST

#define CANIF_APPL_CODE


/* @req COMPILER044 @req COMPILER040 */
/*=======[OSEKCOM_MemoryAndPointerClasses]====================================*/
/* Configurable memory class for code. */
#define COM_CODE

/* Configurable memory class for ISR code. */
#define COM_CODE_FAST

/* 
 * Configurable memory class for all global or static variables that are never 
 * initialized. 
 */
#define COM_VAR_NOINIT

/* 
 * Configurable memory class for all global or static variables that are 
 * initialized only after power on reset. 
 */
#define COM_VAR_POWER_ON_INIT

/*
 * Configurable memory class for all global or static variables that are 
 * initialized after every reset. 
 */
#define COM_VAR

/* 
 * Configurable memory class for all global or static variables that will 
 * be accessed frequently. 
 */
#define COM_VAR_NOINIT_FAST

/* 
 * Configurable memory class for all global or static variables that have at 
 * be accessed frequently. 
 */
#define COM_VAR_POWER_ON_INIT_FAST

/* 
 * Configurable memory class for all global or static variables that have at 
 * be accessed frequently. 
 */
#define COM_VAR_FAST

/* Configurable memory class for global or static constants. */
#define COM_CONST

/*
 * Configurable memory class for global or static constants that will be 
 * accessed frequently.
 */
#define COM_CONST_FAST

/* Configurable memory class for global or static constants in post build. */
#define COM_CONST_PBCFG

/*
 * Configurable memory class for pointers to applicaiton data(expected to be 
 * in RAM or ROM)passed via API.
 */
#define COM_APPL_DATA

/* 
 * Configurable memory class for pointers to applicaiton constants(expected to
 * be certainly in ROM,for instance point of Init-function)passed via API.
 */
#define COM_APPL_CONST

/* 
 * Configurable memory class for pointers to applicaiton functions(e.g. call 
 * back function pointers).
 */
#define COM_APPL_CODE
/*=======[CanSM_MemoryAndPointerClasses]========================================*/
#define CANSM_CODE

#define CANSM_CODE_FAST

#define CANSM_VAR_NOINIT

#define CANSM_VAR_POWER_ON_INIT

#define CANSM_VAR

#define CANSM_VAR_NOINIT_FAST

#define CANSM_VAR_POWER_ON_INIT_FAST

#define CANSM_VAR_FAST 

#define CANSM_CONST

#define CANSM_CONST_FAST

#define CANSM_CONST_PBCFG

#define CANSM_APPL_DATA

#define CANSM_APPL_CONST

#define CANSM_APPL_CODE

/*=======[NmIf_MemoryAndPointerClasses]========================================*/
#define NM_CODE

#define NM_CODE_FAST

#define NM_VAR_NOINIT

#define NM_VAR_POWER_ON_INIT

#define NM_VAR

#define NM_VAR_NOINIT_FAST

#define NM_VAR_POWER_ON_INIT_FAST

#define NM_VAR_FAST 

#define NM_CONST

#define NM_CONST_FAST

#define NM_CONST_PBCFG

#define NM_APPL_DATA

#define NM_APPL_CONST

#define NM_APPL_CODE

/*=======[OsekNm_MemoryAndPointerClasses]========================================*/
#define OSEKNM_CODE

#define OSEKNM_CODE_FAST

#define OSEKNM_VAR_NOINIT

#define OSEKNM_VAR_POWER_ON_INIT

#define OSEKNM_VAR

#define OSEKNM_VAR_NOINIT_FAST

#define OSEKNM_VAR_POWER_ON_INIT_FAST

#define OSEKNM_VAR_FAST 

#define OSEKNM_CONST

#define OSEKNM_CONST_FAST

#define OSEKNM_CONST_PBCFG

#define OSEKNM_APPL_DATA

#define OSEKNM_APPL_CONST

#define OSEKNM_APPL_CODE

/*=======[ComM_MemoryAndPointerClasses]========================================*/
#define COMM_CODE

#define COMM_CODE_FAST

#define COMM_VAR_NOINIT

#define COMM_VAR_POWER_ON_INIT

#define COMM_VAR

#define COMM_VAR_NOINIT_FAST

#define COMM_VAR_POWER_ON_INIT_FAST

#define COMM_VAR_FAST 

#define COMM_CONST

#define COMM_CONST_FAST

#define COMM_CONST_PBCFG

#define COMM_APPL_DATA

#define COMM_APPL_CONST

#define COMM_APPL_CODE

/******************************************/
#define DCM_CODE

#define DCM_CODE_FAST

#define DCM_VAR_NOINIT

#define DCM_VAR_POWER_ON_INIT

#define DCM_VAR

#define DCM_VAR_NOINIT_FAST

#define DCM_VAR_POWER_ON_INIT_FAST

#define DCM_VAR_FAST 

#define DCM_CONST

#define DCM_CONST_FAST

#define DCM_CONST_PBCFG

#define DCM_APPL_DATA

#define DCM_APPL_CONST

#define DCM_APPL_CODE

/******************************************/
#define CANTP_CODE

#define CANTP_CODE_FAST

#define CANTP_VAR_NOINIT

#define CANTP_VAR_POWER_ON_INIT

#define CANTP_VAR

#define CANTP_VAR_NOINIT_FAST

#define CANTP_VAR_POWER_ON_INIT_FAST

#define CANTP_VAR_FAST 

#define CANTP_CONST

#define CANTP_CONST_FAST

#define CANTP_CONST_PBCFG

#define CANTP_APPL_DATA

#define CANTP_APPL_CONST

#define CANTP_APPL_CODE
#endif /* end of COMPILER_CFG_H */

/*=======[E N D   O F   F I L E]==============================================*/
