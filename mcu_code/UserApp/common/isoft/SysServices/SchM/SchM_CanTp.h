/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       SchM_CanTp.h
 *  @brief      <Briefly describe file(one line)>
 *  
 *  <Compiler: CANTP    MCU:CANTP>
 *  
 *  <AUTHOR>
 *  @date       2013-4-10
 */
/*============================================================================*/


#ifndef SCHM_CANTP_H
#define SCHM_CANTP_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       
 * 
 *  
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define SCHM_CANTP_H_AR_MAJOR_VERSION  2
#define SCHM_CANTP_H_AR_MINOR_VERSION  3
#define SCHM_CANTP_H_AR_PATCH_VERSION  0
#define SCHM_CANTP_H_SW_MAJOR_VERSION  1
#define SCHM_CANTP_H_SW_MINOR_VERSION  0
#define SCHM_CANTP_H_SW_PATCH_VERSION  0

/*=======[I N C L U D E S]====================================================*/

#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */

/*=======[M A C R O S]========================================================*/
#define CANTP_EXCLUSIVE_AREA_CHANNEL 0
#define CANTP_EXCLUSIVE_AREA_STATE 1

#define SchM_Enter_CanTp(InstanceID, ExclusiceArea)
#define SchM_Exit_CanTp(InstanceID, ExclusiceArea)
/*=======[T Y P E   D E F I N I T I O N S]====================================*/

/*=======[E X T E R N A L   D A T A]==========================================*/

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/

/*=======[I N T E R N A L   D A T A]==========================================*/

/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/

#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif /* SCHM_CANTP_H */

/*=======[E N D   O F   F I L E]==============================================*/
