/*================================================================================================*/
/** Copyright (C) 2009-2012, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <SchM_OsekNm.h>
 *  @brief      <OsekNm> 
 *  <AUTHOR>
 *  @date       <2014-05-06>
 */
/*================================================================================================*/
#ifndef SCHM_OSEKNM_H
#define SCHM_OSEKNM_H

/*=======[R E V I S I O N   H I S T O R Y]========================================================*/
/*  <VERSION>    <DATE>    <AUTHOR>         <REVISION LOG>
 *  V1.0.0       20140506  wbn    Initial version
 * 
 */
/*================================================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===================================================*/
#define SCHM_OSEKNM_H_AR_MAJOR_VERSION  2U
#define SCHM_OSEKNM_H_AR_MINOR_VERSION  5U
#define SCHM_OSEKNM_H_AR_PATCH_VERSION  3U
#define SCHM_OSEKNM_H_SW_MAJOR_VERSION  1U
#define SCHM_OSEKNM_H_SW_MINOR_VERSION  0U
#define SCHM_OSEKNM_H_SW_PATCH_VERSION  0U

/*=======[I N C L U D E S]========================================================================*/
#define OSEKNM_AREA_EVENT 0
#define OSEKNM_AREA_RXBUF 1
#define OSEKNM_AREA_TXBUF 2
#define OSEKNM_AREA_STATUS 3

#define SchM_Enter_OsekNm(Instance, ExclusiveArea) SuspendAllInterrupts()
#define SchM_Exit_OsekNm(Instance, ExclusiveArea) ResumeAllInterrupts()
 
#endif /*#ifndef SCHM_OSEKNM_H*/

