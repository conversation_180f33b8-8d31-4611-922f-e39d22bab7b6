/*================================================================================================*/
/** Copyright (C) 2009-2012, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <SchM_ComM.h>
 *  @brief      <Communication Manager Module Header File> 
 *  <AUTHOR>
 *  @date       <2014-02-24>
 */
/*================================================================================================*/
#ifndef SCHM_COMM_H
#define SCHM_COMM_H

/*=======[R E V I S I O N   H I S T O R Y]========================================================*/
/*  <VERSION>    <DATE>    <AUTHOR>         <REVISION LOG>
 *  V1.0.0       20140224  pinghai.xiong    Initial version
 * 
 */
/*================================================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===================================================*/
#define SCHM_COMM_H_AR_MAJOR_VERSION  1U
#define SCHM_COMM_H_AR_MINOR_VERSION  1U
#define SCHM_COMM_H_AR_PATCH_VERSION  2U
#define SCHM_COMM_H_SW_MAJOR_VERSION  1U
#define SCHM_COMM_H_SW_MINOR_VERSION  0U
#define SCHM_COMM_H_SW_PATCH_VERSION  0U

/*=======[I N C L U D E S]========================================================================*/
#include "ComStack_Types.h"

/**************************************************************************************************/
/*
 * Brief: This function shall perform the processingof the AUTOSAR ComM activities that are not 
 *        directly initiated by the calls e.g. from the RTE. There shall be one dedicated Main 
          Function for each instance of ComM.  
 * ServiceId: 0x60
 * Sync/Async: Asynchronous
 * Reentrancy: Non Reentrant
 * Param-Name[in]: Channel
 * Param-Name[out]: None
 * Param-Name[in/out]: None
 * Return: None
 * PreCondition: ComM shall be initialized.
 * CallByAPI: None
 */
/**************************************************************************************************/
FUNC(void, COMM_CODE) ComM_MainFunction(NetworkHandleType Channel);

//#define ComM_MainFunction_00() (ComM_MainFunction((NetworkHandleType)0u))
//#define ComM_MainFunction_01() (ComM_MainFunction((NetworkHandleType)1u))
//#define ComM_MainFunction_02() (ComM_MainFunction((NetworkHandleType)2u))

#endif /*#ifndef SCHM_COMM_H*/

