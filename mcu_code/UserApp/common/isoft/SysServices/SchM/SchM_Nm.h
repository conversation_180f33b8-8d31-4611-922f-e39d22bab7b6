/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *
 *  @file       <SchM_Nm.h>
 *  @brief      <Briefly describe file(one line)>
 *
 *  <Compiler: CodeWarrior V2.7    MCU:MPC5634>
 *
 *  <AUTHOR>
 *  @date       <15-07-2013>
 */
/*============================================================================*/
#ifndef SCHM_NM_H
#define SCHM_NM_H

#include "r_can.h"

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130715  bo.zeng     Initial version
 *
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define SCHM_NM_H_AR_MAJOR_VERSION  1
#define SCHM_NM_H_AR_MINOR_VERSION  1
#define SCHM_NM_H_AR_PATCH_VERSION  0

#define SCHM_NM_H_SW_MAJOR_VERSION  1
#define SCHM_NM_H_SW_MINOR_VERSION  0
#define SCHM_NM_H_SW_PATCH_VERSION  1


/*=======[M A C R O S]========================================================*/
#define INTERRUPT_PROTECTION_AREA    0U
#define EXCLUSIVE_AREA_1             1U



#define SchM_Enter_NM(Exclusive_Area) SuspendAllInterrupts()

#define SchM_Exit_NM(Exclusive_Area) ResumeAllInterrupts()
 
#endif /* SCHM_NM_H */
