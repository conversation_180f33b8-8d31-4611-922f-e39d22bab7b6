/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <EcuM.h>
 *  @brief      <EcuM>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-05-10>
 */
/*============================================================================*/



#ifndef _ECUM_H
#define _ECUM_H

#define ECUM_H_AR_MAJOR_VERSION 1

#define ECUM_H_AR_MINOR_VERSION 3

#include "ComStack_Types.h"
#include "EcuM_Types.h"




extern FUNC(void, EcuM_CODE)
EcuM_Init(void);

extern FUNC(Std_ReturnType, EcuM_CODE)
EcuM_ComM_RequestRUN(NetworkHandleType channel);

extern FUNC(Std_ReturnType, EcuM_CODE)
EcuM_ComM_ReleaseRUN(NetworkHandleType channel);

extern FUNC(boolean, EcuM_CODE)
EcuM_ComM_HasRequestedRUN(NetworkHandleType channel);

extern FUNC(void, EcuM_CODE)
EcuM_MainFunction(void);

#endif  /* #ifndef _ECUM_H*/

