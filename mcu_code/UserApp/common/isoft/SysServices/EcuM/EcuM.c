/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <EcuM.c>
 *  @brief      <EcuM>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-06-25>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130625   Tommy      Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "EcuM.h"
#include "r_can.h"
#include "CanIf.h"
#include "ComM_EcuM.h"
#include "ComM.h"
#include "EcuM_Cbk.h"

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
boolean EcuM_CanWakeupEvent;

FUNC(void, EcuM_CODE)
EcuM_Init(void)
{
  EcuM_CanWakeupEvent = FALSE;
}

FUNC(void, EcuM_CODE)
EcuM_SetWakeupEvent(EcuM_WakeupSourceType sources)
{
  sources = sources;/*avoid compile error*/
  EcuM_CanWakeupEvent = TRUE;
}


FUNC(void, EcuM_CODE)
EcuM_ValidateWakeupEvent(EcuM_WakeupSourceType sources)
{
   sources = sources;/*avoid compile error*/
   return;
}

FUNC(void, EcuM_CODE) EcuM_CheckWakeup(EcuM_WakeupSourceType sources)
{
  sources = sources;/*avoid compile error*/
#if (STD_ON == CANIF_WAKEUP_VALIDATION)
	(void)CanIf_CheckWakeup(sources);
#endif
}

FUNC(Std_ReturnType, EcuM_CODE)
EcuM_ComM_RequestRUN(NetworkHandleType channel)
{
	  channel = channel;/*avoid compile error*/
      return E_OK;	
}

FUNC(Std_ReturnType, EcuM_CODE)
EcuM_ComM_ReleaseRUN(NetworkHandleType channel)
{
	  channel = channel;/*avoid compile error*/
      return E_OK;		
}

FUNC(boolean, EcuM_CODE)
EcuM_ComM_HasRequestedRUN(NetworkHandleType channel)
{
	  channel = channel;/*avoid compile error*/
      return TRUE;	
}

FUNC(void, EcuM_CODE)
EcuM_MainFunction(void)
{

    if(EcuM_CanWakeupEvent == TRUE)
    {
        EcuM_CanWakeupEvent = FALSE;    
        ComM_RequestComMode(0, COMM_FULL_COMMUNICATION);
    }
}