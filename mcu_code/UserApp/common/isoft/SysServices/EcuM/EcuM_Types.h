


#ifndef ECUM_TYPES_H
#define ECUM_TYPES_H

#include "Std_Types.h"


typedef enum
{
	ECUM_WKSOURCE_INTERNAL_RESET = 0x04,

	/** Reset by external watchdog (bit 4), if
	 *  detection supported by hardware */
	ECUM_WKSOURCE_EXTERNAL_WDG = 0x10,

	/** Reset by internal watchdog (bit 3) */
	ECUM_WKSOURCE_INTERNAL_WDG = 0x08,

	/** Power cycle (bit 0) */
	ECUM_WKSOURCE_POWER = 0x01,

	/** ~0 to the power of 29 */
	ECUM_WKSOURCE_ALL_SOURCES1 = 0x3FFF,
    ECUM_WKSOURCE_ALL_SOURCES2 = 0xFF,
    ECUM_WKSOURCE_ALL_SOURCES3 =0xFF,
	/** Hardware reset (bit 1).
	 *  If hardware cannot distinguish between a
	 *  power cycle and a reset reason, then this
	 *  shall be the default wakeup source */
	ECUM_WKSOURCE_RESET = 0x02
}EcuM_WakeupSourceType;

extern void EcuM_CheckWakeup(EcuM_WakeupSourceType Source);

#endif  // #ifndef ECUM_TYPES_H










