/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <EcuM_Cbk.h>
 *  @brief      <EcuM>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-05-10>
 */
/*============================================================================*/



#ifndef _ECUM_CBK_H
#define _ECUM_CBK_H

#define ECUM_H_AR_MAJOR_VERSION 1

#define ECUM_H_AR_MINOR_VERSION 3

#include "Std_Types.h"
#include "EcuM_Types.h"




extern FUNC(void, EcuM_CODE)
EcuM_SetWakeupEvent(EcuM_WakeupSourceType sources);

extern FUNC(void, EcuM_CODE)
EcuM_ValidateWakeupEvent(EcuM_WakeupSourceType sources);



#endif  /* #ifndef _ECUM_H*/

