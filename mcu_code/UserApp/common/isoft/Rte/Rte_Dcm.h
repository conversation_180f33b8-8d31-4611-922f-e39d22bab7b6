/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file      
 *  @brief     
 *  
 *  
 *  <AUTHOR>
 *  @date       2013-4-7
 */
/*============================================================================*/

#ifndef RTE_DCM_H
#define RTE_DCM_H

#include "Dcm_Types.h"
#include "Rte_Dcm.h"
#include <ctype.h>  // 用于 isdigit 函数
#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */


#define  UDS_SPEED_LIMIT_RAW_VALUE         192 //  3/0.015625 = 192

    //检查挡位
#define CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode) \
    do { \
        if (GetGearPosition() > 0x01) { \
            *(ErrorCode) = DCM_E_CONDITIONSNOTCORRECT; \
            return E_NOT_OK; \
        } \
    } while (0)

#define CHECK_GEAR_POSITION_AND_RETURN() \
    do { \
        if (GetGearPosition() > 0x01) { \
            return E_NOT_OK; \
        } \
    } while (0)
    //检查使能标志
#define CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(EnableFlag,ErrorCode) \
    do { \
        if ((EnableFlag) != 0x00 && (EnableFlag) != 0x01) { \
            *(ErrorCode) = DCM_E_REQUESTOUTOFRANGE; \
            return E_NOT_OK; \
        } \
    } while (0)
    //检查车速
#define CHECK_VEHICLE_SPEED_AND_RETURN_E_CODE(ErrorCode) \
    do { \
        if (GetVehicleSpeed() > UDS_SPEED_LIMIT_RAW_VALUE) { \
            *(ErrorCode) = DCM_E_VEHICLESPEEDTOOHIGH; \
            return E_NOT_OK; \
        } \
    } while (0)

#define CHECK_PORT_STRING_AND_IN_RANGE(data, len, ErrorCode)    \
    do {                                                          \
        uint32_t value = 0;                                        \
        int i = 0;                                        \
        /* 逐个字符检查是否为数字字符，并计算数字值 */               \
        while (i < (len) && isdigit((data)[i]))                 \
        {                  \
            value = value * 10 + (data)[i] - '0';                  \
            i++;                                                   \
        }                                                          \
        /* 检查数字后是否有其他非法字符（允许末尾有空格） */             \
        for (; i < (len); i++)                                  \
        {                                   \
            if ((data)[i] != ' ' && (data)[i] != '\0')          \
            {           \
                *(ErrorCode) = DCM_E_REQUESTOUTOFRANGE;                     \
                return E_NOT_OK;                                   \
            }                                                      \
        }                                                          \
        /* 检查转换后的数字是否在端口范围内 (0到65535) */             \
        if (value > 65535)                                      \
        {                                       \
            *(ErrorCode) = DCM_E_REQUESTOUTOFRANGE;                         \
            return E_NOT_OK;                                       \
        }                                                          \
        *(ErrorCode) = E_OK;                                       \
    } while (0)

#define READ_STRING_DATA_AND_COPY(dest, src, dest_len)               \
    do {                                                      \
        memset((dest), 0x20, (dest_len));                     \
        size_t len = strlen((char *)(src));                  \
        if (len > (dest_len))                                        \
        {                               \
            len = (dest_len);                                 \
        }                                                     \
        memcpy((dest), (src), len);                           \
    } while (0)

#define COPY_FILTERED_STRING_DATA(dest, src, max_len)      \
    do {                                                   \
        memset((dest), 0x00, (max_len));                   \
        for (int i = 0; i < (max_len); i++)                 \
        {                                                   \
            if ((src)[i] == 0x00 || (src)[i] == ' ')        \
            {                                               \
                break;                                     \
            }                                              \
            (dest)[i] = (src)[i];                          \
        }                                                  \
    } while (0)

/*=======[M A C R O S]========================================================*/

Std_ReturnType  StartProtocol(Dcm_ProtocolType  ProtocolID);
Std_ReturnType  StopProtocol(Dcm_ProtocolType  ProtocolID);
Std_ReturnType  UdsExtendCompKey(uint8 *key);
Std_ReturnType  Rte_EcuReset(uint8 ResetType, Dcm_NegativeResponseCodeType* ErrorCode);
void Rte_CommunicaitonReset(void);
Std_ReturnType  Rte_CommunicaitonControl(uint8 subFunc, uint8 comType,Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType  UdsExtendGetSeed(uint8 *SecurityAccessRecord,uint8 *Seed,Dcm_NegativeResponseCodeType *ErrorCode);
Std_ReturnType  UdsChangeDefaultIndication(Dcm_SesType  SesCtrlTypeOld,   Dcm_SesType  SesCtrlTypeNew);
Std_ReturnType  UdsGetDefaultSesChgPermission(Dcm_SesType  SesCtrlTypeActive,Dcm_SesType  SesCtrlTypeNew);
Std_ReturnType  UdsProgrammingChangeIndication(Dcm_SesType  SesCtrlTypeOld,   Dcm_SesType  SesCtrlTypeNew);
Std_ReturnType  UdsProgrammingGetSesChgPermission(Dcm_SesType  SesCtrlTypeActive,Dcm_SesType  SesCtrlTypeNew);
Std_ReturnType  UdsChangeExtendIndication(Dcm_SesType  SesCtrlTypeOld,   Dcm_SesType  SesCtrlTypeNew);
Std_ReturnType  UdsGetExtendSesChgPermission(Dcm_SesType  SesCtrlTypeActive,Dcm_SesType  SesCtrlTypeNew);
Std_ReturnType  UdsIndicationService(uint8 SID,uint8 *RequestData,uint16 DataSize);
void Rte_EnableAllDtcsRecord(void);


Std_ReturnType Rte_ReadVehicleManufacturerSparePartNumber(uint8* data);
Std_ReturnType Rte_ReadSystemSupplierIdentifier(uint8* data);
Std_ReturnType Rte_ReadSystemSupplierEcuHardWareVersionNumber(uint8* data);
Std_ReturnType Rte_ReadSystemSupplierEcuSoftWareVersionNumber(uint8* data);
Std_ReturnType Rte_ReadSystemSupplierEcuBootVersionNumber(uint8* data);
Std_ReturnType Rte_ReadEcuSerialNumber(uint8* data);
Std_ReturnType Rte_ReadVin(uint8* data);
Std_ReturnType Rte_WriteVin(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadSystemName(uint8* data);
Std_ReturnType Rte_ReadCurrentActiveSession(uint8* data);

Std_ReturnType Rte_ReadIMEI(uint8* data);
Std_ReturnType Rte_ReadICCID(uint8* data);

Std_ReturnType Rte_ReadSoftwareId(uint8* data);
Std_ReturnType Rte_ReadHardwareId(uint8* data);
Std_ReturnType Rte_ReadTBoxSerialNumber(uint8* data);
Std_ReturnType Rte_ReadSoftUpdateDate(uint8* data);
Std_ReturnType Rte_ReadTUID(uint8* data);
Std_ReturnType Rte_ReadTBoxAuthStatus(uint8* data);
Std_ReturnType Rte_ReadSimNumber(uint8* data);
Std_ReturnType Rte_ReadTboxCertificateID(uint8* data);
Std_ReturnType Rte_ReadLink1Addr(uint8* data);
Std_ReturnType Rte_WriteLink1Addr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink1Port(uint8* data);
Std_ReturnType Rte_WriteLink1Port(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink2Addr(uint8* data);
Std_ReturnType Rte_WriteLink2Addr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink2Port(uint8* data);
Std_ReturnType Rte_WriteLink2Port(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink3Addr(uint8* data);
Std_ReturnType Rte_WriteLink3Addr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink3Port(uint8* data);
Std_ReturnType Rte_WriteLink3Port(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink3BiAuthPort(uint8* data);
Std_ReturnType Rte_WriteLink3BiAuthPort(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);


Std_ReturnType Rte_ReadThirdPartyLinkAddr(uint8* data);
Std_ReturnType Rte_WriteThirdPartyLinkAddr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadThirdPartyLinkPort(uint8* data);
Std_ReturnType Rte_WriteThirdPartyLinkPort(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink3Username(uint8* data);
Std_ReturnType Rte_WriteLink3Username(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink3Password(uint8* data);
Std_ReturnType Rte_WriteLink3Password(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);

Std_ReturnType Rte_ReadWakeupTimeInterval(uint8* data);
Std_ReturnType Rte_WriteWakeupTimeInterval(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);


Std_ReturnType Rte_ReadAutoRechargeUnderVoltage(uint8* data);
Std_ReturnType Rte_WriteAutoRechargeUnderVoltage(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink1SSLEnable(uint8* data);
Std_ReturnType Rte_WriteLink1SSLEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink2SSLEnable(uint8* data);
Std_ReturnType Rte_WriteLink2SSLEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadOfflineCheckFlag(uint8* data);
Std_ReturnType Rte_WriteOfflineCheckFlag(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadACTimeoutMinutes(uint8* data);
Std_ReturnType Rte_WriteACTimeoutMinutes(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadTimedWakeupEnable(uint8* data);
Std_ReturnType Rte_WriteTimedWakeupEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadNetworkWakeupEnable(uint8* data);
Std_ReturnType Rte_WriteNetworkWakeupEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadGlobalLogEnable(uint8* data);
Std_ReturnType Rte_WriteGlobalLogEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);


Std_ReturnType Rte_ReadLink1Enable(uint8* data);
Std_ReturnType Rte_WriteLink1Enable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink2Enable(uint8* data);
Std_ReturnType Rte_WriteLink2Enable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLink3Enable(uint8* data);
Std_ReturnType Rte_WriteLink3Enable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadThirdPartyLinkEnable(uint8* data);
Std_ReturnType Rte_WriteThirdPartyLinkEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadCertUpdateTime(uint8* data);
Std_ReturnType Rte_ReadDataResendTestTime(uint8* data);
Std_ReturnType Rte_WriteDataResendTestTime(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLevel3AlarmTestTime(uint8* data);
Std_ReturnType Rte_WriteLevel3AlarmTestTime(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
// Std_ReturnType Rte_ReadVehicleRestrictFlag(uint8* data);
// Std_ReturnType Rte_WriteVehicleRestrictFlag(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadLockCarStatusValue(uint8* data);
Std_ReturnType Rte_WriteLockCarStatusValue(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadSpeedLimitStatusValue(uint8* data);
Std_ReturnType Rte_WriteSpeedLimitStatusValue(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);
Std_ReturnType Rte_ReadSpeedLimitValue(uint8* data);
Std_ReturnType Rte_WriteSpeedLimitValue(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);


Std_ReturnType Rte_ReadVehicleOfflineConfig(uint8* data);
Std_ReturnType Rte_WriteVehicleOfflineConfig(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode);

//动态DID
Std_ReturnType Rte_ReadEcuBatteryVoltage(uint8* data);
Std_ReturnType Rte_ReadVehicleSpeed(uint8* data);
Std_ReturnType Rte_ReadOdometer(uint8* data);
Std_ReturnType Rte_ReadLowPowerMode(uint8* data);
Std_ReturnType Rte_ReadDateTime(uint8* data);
Std_ReturnType Rte_ReadVehiclePowerMode(uint8* data);
Std_ReturnType Rte_ReadGearPosition(uint8* data);
Std_ReturnType Rte_ReadCellularSignalStrength(uint8* data);
Std_ReturnType Rte_ReadGnssSignalStrength(uint8* data);
Std_ReturnType Rte_ReadWifiSignalStrength(uint8* data);
Std_ReturnType Rte_ReadLink1TcpStatus(uint8* data);
Std_ReturnType Rte_ReadLink2TcpStatus(uint8* data);
Std_ReturnType Rte_ReadLink3TcpStatus(uint8* data);
Std_ReturnType Rte_ReadPowerManagementMode(uint8* data);
Std_ReturnType Rte_ReadMileageClearCount(uint8* data);
Std_ReturnType Rte_ReadTboxSavesMileageValues(uint8* data);

Std_ReturnType Rte_ReadRdsSoftwareIn(uint8* data);
Std_ReturnType Rte_ReadRdsSoftwareOut(uint8* data);
Std_ReturnType Rte_ReadRdsSoftwareSupplierIdentifier(uint8* data);


#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif /* RTE_DCM_H */
