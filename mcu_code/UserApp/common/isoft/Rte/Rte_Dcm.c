/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       
 *  @brief      
 *  
 *  
 *  <AUTHOR>
 *  @date       2013-4-7
 */
/*============================================================================*/
/******************************* references ************************************/
#include "FreeRTOS.h"
#include "Rte_Dcm.h"
#include "Dem.h"
#include <stdlib.h>
#include <string.h>
#include "LogApi.h"
#include "PmApi.h"
#include "NvApi.h"
#include "UpdateApi.h"
#include "UDS.h"
#include "BtApi.h"
#include "event.h"
#include "LedApi.h"

/************************外部全局变量****************************/
extern PmInfo                       g_pmInfo;
extern StaticDidInfo                g_staticDid;
extern StaticDid2Info               g_staticDid2;
extern DynamicDidInfo               g_dynamicDid;
extern CommonInfo                   g_commonInfo;
extern DtcInfo                      g_dtcInfo;
extern UpdateInfo                   g_updateInfo;
extern uint8                        g_dtcRecordEnable;
extern CarInfo                      g_carInfo;
extern uint32_t                     g_osCurrentTickTime;
extern NvItemInfo                   g_nvInfoMap[NV_MAX_NUMBER];

extern TotalDidInfo                 g_totalDidInfo;
/************************全局变量****************************/
uint32_t g_extendSeed = 0;
static uint8_t g_CurrentActiveSession = 0x00;

unsigned int T[]= {
        0xd76aa478,0xe8c7b756,0x242070db,0xc1bdceee,
        0xf57c0faf,0x4787c62a,0xa8304613,0xfd469501,0x698098d8,
        0x8b44f7af,0xffff5bb1,0x895cd7be,0x6b901122,0xfd987193,
        0xa679438e,0x49b40821,0xf61e2562,0xc040b340,0x265e5a51,
        0xe9b6c7aa,0xd62f105d,0x02441453,0xd8a1e681,0xe7d3fbc8,
        0x21e1cde6,0xc33707d6,0xf4d50d87,0x455a14ed,0xa9e3e905,
        0xfcefa3f8,0x676f02d9,0x8d2a4c8a,0xfffa3942,0x8771f681,
        0x6d9d6122,0xfde5380c,0xa4beea44,0x4bdecfa9,0xf6bb4b60,
        0xbebfbc70,0x289b7ec6,0xeaa127fa,0xd4ef3085,0x04881d05,
        0xd9d4d039,0xe6db99e5,0x1fa27cf8,0xc4ac5665,0xf4292244,
        0x432aff97,0xab9423a7,0xfc93a039,0x655b59c3,0x8f0ccc92,
        0xffeff47d,0x85845dd1,0x6fa87e4f,0xfe2ce6e0,0xa3014314,
        0x4e0811a1,0xf7537e82,0xbd3af235,0x2ad7d2bb,0xeb86d391 };

uint32_t  A_state0,B_state1,C_state2,D_state3; //
uint32_t  a,b,c,d ;
uint32_t  M_information[16]={0x46584748,0x46454245,0x4E584C57, 0x32324759,
                             0x80535030,0x00000000,0x000000000,0x00000000,0x00000000,0x00000000,0x00000000,
                             0x00000000,0x00000000, 0x00000000,0x00000098,0x00000000};

uint8_t Mask[4]={0x0F,0xB9,0xC3,0x0C};                   //Mask array 掩


uint32_t bit_move(uint32_t val,char n)
{
    uint32_t buffer_data;
    buffer_data=((val>>(32-n))|(val<<n));
    return buffer_data;
}
uint32_t F_function(uint32_t x,uint32_t y,uint32_t z)
{
    uint32_t buffer_value;
    buffer_value=(x&y)|((~x)&z);
    return buffer_value;
}
uint32_t G_function(uint32_t x,uint32_t y,uint32_t z)
{
    uint32_t buffer_value1;
    buffer_value1=(x&z)|(y&(~z));
    return buffer_value1;
}
uint32_t H_function(uint32_t x,uint32_t y,uint32_t z)
{
    uint32_t buffer_value2;
    buffer_value2=x^y^z;
    return buffer_value2;
}
uint32_t I_function(uint32_t x,uint32_t y,uint32_t z)
{
    uint32_t buffer_value3;
    buffer_value3=y^(x|(~z));
    return buffer_value3;
}
uint32_t FF_function(uint32_t at,uint32_t bt,uint32_t ct,uint32_t dt,uint32_t Mj,char s ,uint32_t ti)
{
    uint32_t buffer_value4;
    buffer_value4=F_function(bt,ct,dt);
    buffer_value4=at+buffer_value4+Mj+ti;
    at=bit_move(buffer_value4,s);
    buffer_value4=bt+at;
    return buffer_value4;//a=b+((a+F(b,c,d)+Mj+ti)<<<s);
}
uint32_t GG_function(uint32_t at,uint32_t bt,uint32_t ct,uint32_t dt,uint32_t Mj,char s ,uint32_t ti)
{
    uint32_t buffer_value5;
    buffer_value5=at+G_function(bt,ct,dt)+Mj+ti;
    at=bit_move(buffer_value5,s);
    buffer_value5=bt+at;
    return buffer_value5;
}
uint32_t HH_function(uint32_t at,uint32_t bt,uint32_t ct,uint32_t dt,uint32_t Mj,char s ,uint32_t ti)
{
    uint32_t buffer_value6;
    buffer_value6=at+H_function(bt,ct,dt)+Mj+ti;
    at=bit_move(buffer_value6,s);
    buffer_value6=bt+at;
    return buffer_value6;
}
uint32_t II_function(uint32_t at,uint32_t bt,uint32_t ct,uint32_t dt,uint32_t Mj,char s ,uint32_t ti)
{
    uint32_t buffer_value7;
    buffer_value7=at+I_function(bt,ct,dt)+Mj+ti;
    at=bit_move(buffer_value7,s);
    buffer_value7=bt+at;
    return buffer_value7;
}
void HASH_CODE()
{
    A_state0=0x67452301;
    B_state1=0xEFCDAB89;
    C_state2=0x98BADCFE;
    D_state3=0x10325476;
    a=0x67452301;
    b=0xEFCDAB89;
    c=0x98BADCFE;
    d=0x10325476;
//------------------------------第一轮
    a=FF_function(a,b,c,d,M_information[0],7,0xd76aa478);
    d=FF_function(d,a,b,c,M_information[1],12,0xe8c7b756);
    c=FF_function(c,d,a,b,M_information[2],17,0x242070db);
    b=FF_function(b,c,d,a,M_information[3],22,0xc1bdceee);

    a=FF_function(a,b,c,d,M_information[4],7,0xf57c0faf);
    d=FF_function(d,a,b,c,M_information[5],12,0x4787c62a);
    c=FF_function(c,d,a,b,M_information[6],17,0xa8304613);
    b=FF_function(b,c,d,a,M_information[7],22,0xfd469501);

    a=FF_function(a,b,c,d,M_information[8],7,0x698098d8);
    d=FF_function(d,a,b,c,M_information[9],12,0x8b44f7af);
    c=FF_function(c,d,a,b,M_information[10],17,0xffff5bb1);
    b=FF_function(b,c,d,a,M_information[11],22,0x895cd7be);

    a=FF_function(a,b,c,d,M_information[12],7,0x6b901122);
    d=FF_function(d,a,b,c,M_information[13],12,0xfd987193);
    c=FF_function(c,d,a,b,M_information[14],17,0xa679438e);
    b=FF_function(b,c,d,a,M_information[15],22,0x49b40821);
    //第二轮
    a=GG_function(a,b,c,d,M_information[1],5,0xf61e2562);
    d=GG_function(d,a,b,c,M_information[6],9,0xc040b340);
    c=GG_function(c,d,a,b,M_information[11],14,0x265e5a51);
    b=GG_function(b,c,d,a,M_information[0],20,0xe9b6c7aa);

    a=GG_function(a,b,c,d,M_information[5],5,0xd62f105d);
    d=GG_function(d,a,b,c,M_information[10],9,0x02441453);
    c=GG_function(c,d,a,b,M_information[15],14,0xd8a1e681);
    b=GG_function(b,c,d,a,M_information[4],20,0xe7d3fbc8);

    a=GG_function(a,b,c,d,M_information[9],5,0x21e1cde6);
    d=GG_function(d,a,b,c,M_information[14],9,0xc33707d6);
    c=GG_function(c,d,a,b,M_information[3],14,0xf4d50d87);
    b=GG_function(b,c,d,a,M_information[8],20,0x455a14ed);

    a=GG_function(a,b,c,d,M_information[13],5,0xa9e3e905);
    d=GG_function(d,a,b,c,M_information[2],9,0xfcefa3f8);
    c=GG_function(c,d,a,b,M_information[7],14,0x676f02d9);
    b=GG_function(b,c,d,a,M_information[12],20,0x8d2a4c8a);
    //第三轮
    a=HH_function(a,b,c,d,M_information[5],4,0xfffa3942);
    d=HH_function(d,a,b,c,M_information[8],11,0x8771f681);
    c=HH_function(c,d,a,b,M_information[11],16,0x6d9d6122);
    b=HH_function(b,c,d,a,M_information[14],23,0xfde5380c);

    a=HH_function(a,b,c,d,M_information[1],4,0xa4beea44);
    d=HH_function(d,a,b,c,M_information[4],11,0x4bdecfa9);
    c=HH_function(c,d,a,b,M_information[7],16,0xf6bb4b60);
    b=HH_function(b,c,d,a,M_information[10],23,0xbebfbc70);

    a=HH_function(a,b,c,d,M_information[13],4,0x289b7ec6);
    d=HH_function(d,a,b,c,M_information[0],11,0xeaa127fa);
    c=HH_function(c,d,a,b,M_information[3],16,0xd4ef3085);
    b=HH_function(b,c,d,a,M_information[6],23,0x04881d05);
    a=HH_function(a,b,c,d,M_information[9],4,0xd9d4d039);
    d=HH_function(d,a,b,c,M_information[12],11,0xe6db99e5);
    c=HH_function(c,d,a,b,M_information[15],16,0x1fa27cf8);
    b=HH_function(b,c,d,a,M_information[2],23,0xc4ac5665);
//第四轮
    a=II_function(a,b,c,d,M_information[0],6,0xf4292244);
    d=II_function(d,a,b,c,M_information[7],10,0x432aff97);
    c=II_function(c,d,a,b,M_information[14],15,0xab9423a7);
    b=II_function(b,c,d,a,M_information[5],21,0xfc93a039);

    a=II_function(a,b,c,d,M_information[12],6,0x655b59c3);
    d=II_function(d,a,b,c,M_information[3],10,0x8f0ccc92);
    c=II_function(c,d,a,b,M_information[10],15,0xffeff47d);
    b=II_function(b,c,d,a,M_information[1],21,0x85845dd1);

    a=II_function(a,b,c,d,M_information[8],6,0x6fa87e4f);
    d=II_function(d,a,b,c,M_information[15],10,0xfe2ce6e0);
    c=II_function(c,d,a,b,M_information[6],15,0xa3014314);
    b=II_function(b,c,d,a,M_information[13],21,0x4e0811a1);

    a=II_function(a,b,c,d,M_information[4],6,0xf7537e82);
    d=II_function(d,a,b,c,M_information[11],10,0xbd3af235);
    c=II_function(c,d,a,b,M_information[2],15,0x2ad7d2bb);
    b=II_function(b,c,d,a,M_information[9],21,0xeb86d391);
    A_state0=A_state0+a;
    B_state1=B_state1+b;
    C_state2=C_state2+c;
    D_state3=D_state3+d;
}

void see2key_AllECU_G202P(uint8_t *seed, uint8_t *key,uint8_t  *Mask,uint8_t level)
{
    uint8_t SK_Temp[4]={0xc0,0xc1,0xc2,0xc3};
    uint8_t buffer[4]={0x57,0x4c,0x45,0x31};
    uint8_t SK[6]={0x08,0x04,0xdd,0x97,0x25,0x89};
    uint32_t    buffer_data,Max_seed;
    int i=0;
    M_information[0]=Mask[3]*0x1000000+Mask[2]*0x10000+Mask[1]*0x100+Mask[0];
    M_information[1]=seed[3]*0x1000000+seed[2]*0x10000+seed[1]*0x100+seed[0];
    HASH_CODE();
    buffer_data=D_state3%0x100000;
    SK[0]= buffer_data%0x100;
    buffer_data=D_state3%0x1000;
    SK[1]= buffer_data/0x10;
    buffer_data=C_state2%0x100000;
    SK[2]=buffer_data/0x1000;
    SK[3]=B_state1%0x100;
    buffer_data=A_state0/0x10000;
    SK[4]=buffer_data%0x100;
    buffer_data=A_state0%0x1000;
    SK[5]= buffer_data/0x10;
//----------------依据Seed动态信息动态摘取4个byte的SK---//
    Max_seed=seed[0];
    for(i=1;i<4;i++)
    {
        if(Max_seed<seed[i])    { Max_seed=seed[i]; }
    }
//-------------------------------------动态选择SK
    switch(Max_seed/0x10)
    {
        case 0 : SK_Temp[0]=SK[0];SK_Temp[1]=SK[1];SK_Temp[2]=SK[2];SK_Temp[3]=SK[3];break;
        case 1: SK_Temp[0]=SK[1];SK_Temp[1]=SK[2];SK_Temp[2]=SK[3];SK_Temp[3]=SK[4];break;
        case 2: SK_Temp[0]=SK[2];SK_Temp[1]=SK[3];SK_Temp[2]=SK[4];SK_Temp[3]=SK[5];break;
        case 3: SK_Temp[0]=SK[3];SK_Temp[1]=SK[4];SK_Temp[2]=SK[5];SK_Temp[3]=SK[0];break;
        case 4: SK_Temp[0]=SK[4];SK_Temp[1]=SK[5];SK_Temp[2]=SK[0];SK_Temp[3]=SK[1];break;
        case 5: SK_Temp[0]=SK[5];SK_Temp[1]=SK[0];SK_Temp[2]=SK[1];SK_Temp[3]=SK[2];break;
        case 6: SK_Temp[0]=SK[0];SK_Temp[1]=SK[3];SK_Temp[2]=SK[2];SK_Temp[3]=SK[1];break;
        case 7: SK_Temp[0]=SK[1];SK_Temp[1]=SK[4];SK_Temp[2]=SK[3];SK_Temp[3]=SK[2];break;
        case 8: SK_Temp[0]=SK[5];SK_Temp[1]=SK[3];SK_Temp[2]=SK[4];SK_Temp[3]=SK[2];break;
        case 9: SK_Temp[0]=SK[4];SK_Temp[1]=SK[4];SK_Temp[2]=SK[1];SK_Temp[3]=SK[5];break;
        case 10: SK_Temp[0]=SK[2];SK_Temp[1]=SK[4];SK_Temp[2]=SK[2];SK_Temp[3]=SK[5];break;
        case 11: SK_Temp[0]=SK[4];SK_Temp[1]=SK[3];SK_Temp[2]=SK[0];SK_Temp[3]=SK[1];break;
        case 12: SK_Temp[0]=SK[2];SK_Temp[1]=SK[5];SK_Temp[2]=SK[2];SK_Temp[3]=SK[3];break;
        case 13: SK_Temp[0]=SK[3];SK_Temp[1]=SK[5];SK_Temp[2]=SK[2];SK_Temp[3]=SK[1];break;
        case 14: SK_Temp[0]=SK[1];SK_Temp[1]=SK[3];SK_Temp[2]=SK[5];SK_Temp[3]=SK[4];break;
        case 15: SK_Temp[0]=SK[5];SK_Temp[1]=SK[4];SK_Temp[2]=SK[0];SK_Temp[3]=SK[1];break;
        default : break;
    }
//---------------------------------------------------------------------
    if(level==1)
    {
        for(i=0;i<4;i++)
        {
            buffer[i]=seed[i]^SK_Temp[i];
        }
        key[0]=((buffer[2]&0x27)<<4)|((buffer[3]&0xf0)>>3);
        key[1]=((buffer[0]&0xF0)<<2)|((buffer[1]&0xf8)>>3);
        key[2]=((buffer[3]&0x0c)<<2)|((buffer[0]&0x63)>>3);
        key[3]=(buffer[1]&0x0F)|((buffer[3]&0xF3)>>4);
    }
//-------------------------------------------------------------------
    else if(level==51)
    {
        for(i=0;i<4;i++)
        {
            buffer[i]=seed[i]^SK_Temp[i];
        }
        key[0]=((buffer[3]&0x0F))|((buffer[2]&0xf0));
        key[1] = ((buffer[0] & 0xC3) << 4) | ((buffer[3] & 0x3C) >> 2);
        key[2]=((buffer[2]&0x2F)>> 3)|((buffer[1]&0x0F)<< 4);
        key[3]=((buffer[1]&0x1F)<<2)|((buffer[0]&0xF0)>>4);
    }
//-----------------------------------------------------=======
    else if(level==9)
    {
        for(i=0;i<4;i++)
        {
            buffer[i]=(seed[i]>>1)^(SK_Temp[i]<<1);
        }
        key[0]=((buffer[3]&0xF0 ) >> 2)|((buffer[3]&0xf0) << 2);
        key[1]=((buffer[2]&0x0f)<< 4)|((buffer[2]&0xf0) >> 1);
        key[2]=((buffer[1]&0x3c)>> 4)|((buffer[2]&0x3e)<< 4);
        key[3]=(buffer[0]&0xf0)|((buffer[1]&0x5f)>>4);

    }
}


Std_ReturnType  StartProtocol(Dcm_ProtocolType  ProtocolID)
{
    return E_OK;
}

Std_ReturnType  StopProtocol(Dcm_ProtocolType  ProtocolID)
{
    return E_OK;
}

Std_ReturnType  UdsExtendCompKey(uint8 *key)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    uint8_t key2[4] = {0};
    uint32 calcKey = 0;
    uint32 getKey = 0;
    Std_ReturnType ret = E_COMPARE_KEY_FAILED;

    uint8_t seed[4] = {0};
    seed[0] = (uint8)(g_extendSeed >> 24);
    seed[1] = (uint8)(g_extendSeed >> 16);
    seed[2] = (uint8)(g_extendSeed >> 8);
    seed[3] = (uint8)(g_extendSeed);

    see2key_AllECU_G202P(seed,key2,Mask,1);

    // SystemApiLogPrintf(LOG_INFO_OUTPUT,"calcKey: %X getKey:%X g_extendSeed:%X ret:%d\r\n", calcKey, getKey, g_extendSeed, ret);

    if (memcmp(key, key2, 4) == 0) // 比较两个数组前4个字节是否相等
    {
        SecurityEventLog(SEC_MCU_UDS_ATH_SUC, "UDS Authentication Success");
        ret = E_OK;
    }
    else
    {
        SecurityEventLog(SEC_MCU_UDS_ATH_FAL, "UDS Authentication Fail");
    }
    return ret;
}


Std_ReturnType Rte_EcuReset(uint8 ResetType, Dcm_NegativeResponseCodeType* ErrorCode)
{

    CHECK_VEHICLE_SPEED_AND_RETURN_E_CODE(ErrorCode);
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    if(0x01 == ResetType)
    {
        g_updateInfo.status = UPDATE_STATUS_SOFT_RESET;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "HardReset tbox mcu and arm ...\r\n");
    }
    else if(0x03 == ResetType)
    {
        if (MODULE_POWER_IDLE == GetPowerModuleStatus())
        {
            Msg msg = {0};
            msg.event  = EVENT_ID_HEARTBEAT_TIMEOUT;
            msg.len    = 0;
            msg.lparam = 0;
            SystemSendMessage(TASK_ID_PM, msg);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "SoftReset tbox arm ...\r\n");
        }
        else
        {
            *ErrorCode = DCM_E_CONDITIONSNOTCORRECT;
            return E_NOT_OK;
        }
    }

    return E_OK;
}

void Rte_CommunicaitonReset(void)
{

    g_commonInfo.udsCtrlNmPduStatus = DCM_UDS0X28_ENABLE_RX_AND_TX;
    g_commonInfo.udsCtrlNormPduStatus = DCM_UDS0X28_ENABLE_RX_AND_TX;

}
Std_ReturnType Rte_CommunicaitonControl(uint8 subFunc, uint8 comType,Dcm_NegativeResponseCodeType* ErrorCode)
{
    Std_ReturnType ret = E_OK;
    CHECK_VEHICLE_SPEED_AND_RETURN_E_CODE(ErrorCode);
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    bool CtrlNmMessage = ((comType & 0x02) == 0x02);//控制网络管理消息
    bool CtrlAppMessage = ((comType & 0x01) == 0x01);//控制应用报文

    if(CtrlNmMessage)
    {
        g_commonInfo.udsCtrlNmPduStatus = subFunc;
    }

    if(CtrlAppMessage)
    {
        g_commonInfo.udsCtrlNormPduStatus = subFunc;
    }

    return ret;
}

Std_ReturnType  UdsExtendGetSeed(uint8 *SecurityAccessRecord,uint8 *Seed,Dcm_NegativeResponseCodeType *ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    g_extendSeed = rand() << 16;
    g_extendSeed += rand();
    g_extendSeed += g_osCurrentTickTime;

    Seed[0] = (uint8)(g_extendSeed >> 24);
    Seed[1] = (uint8)(g_extendSeed >> 16);
    Seed[2] = (uint8)(g_extendSeed >> 8);
    Seed[3] = (uint8)(g_extendSeed);

    // SystemApiLogPrintf(LOG_INFO_OUTPUT,"g_extendSeed:%X Seed:%02X%02X%02X%02X\r\n", g_extendSeed, Seed[0], Seed[1], Seed[2], Seed[3]);

    return E_OK;
}

/*默认会话改变通知*/
Std_ReturnType  UdsChangeDefaultIndication(Dcm_SesType  SesCtrlTypeOld,   Dcm_SesType  SesCtrlTypeNew)
{
    g_CurrentActiveSession = SesCtrlTypeNew;

    return E_OK;
}

Std_ReturnType  UdsGetDefaultSesChgPermission(Dcm_SesType  SesCtrlTypeActive,Dcm_SesType  SesCtrlTypeNew)
{
    return E_OK;
}


/*扩展会话改变通知*/
Std_ReturnType  UdsChangeExtendIndication(Dcm_SesType  SesCtrlTypeOld,   Dcm_SesType  SesCtrlTypeNew)
{
    g_CurrentActiveSession = SesCtrlTypeNew;
    return E_OK;
}

Std_ReturnType  UdsGetExtendSesChgPermission(Dcm_SesType  SesCtrlTypeActive,Dcm_SesType  SesCtrlTypeNew)
{

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "enter extend session\r\n");
    return E_OK;
}

/*编程会话改变通知*/
Std_ReturnType  UdsProgrammingChangeIndication(Dcm_SesType  SesCtrlTypeOld,   Dcm_SesType  SesCtrlTypeNew)
{
//    g_CurrentActiveSession = SesCtrlTypeNew;
    return E_OK;
}

Std_ReturnType  UdsProgrammingGetSesChgPermission(Dcm_SesType  SesCtrlTypeActive,Dcm_SesType  SesCtrlTypeNew)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "0x%02x,0x%02x\r\n",SesCtrlTypeActive, SesCtrlTypeNew); 

    if((0x03 == SesCtrlTypeActive)&&(0x02 == SesCtrlTypeNew))
    {
        return E_SESSION_NOT_ALLOWED;  /*不支持进入boot*/
        //return E_FORCE_RCRRP;

    }
    else if((0x03 != SesCtrlTypeActive)&&(0x02 == SesCtrlTypeNew))
    {
        return E_SESSION_NOT_ALLOWED;
    }
    else
    {
        return E_OK;
    }
}


Std_ReturnType  UdsIndicationService(uint8 SID,uint8 *RequestData,uint16 DataSize)
{
    return E_OK;
}

void Rte_EnableAllDtcsRecord(void)
{
    g_dtcRecordEnable = 0x01;
}


/*********************************************DID****************************************************************************/
Std_ReturnType Rte_ReadVehicleManufacturerSparePartNumber(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.tboxSparePartNumber, TBOX_SPARE_PARTNUMBER_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadSoftwareId(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxSoftwareId, TBOX_SOFTWARE_ID_LEN);
    return E_OK;
}
Std_ReturnType Rte_ReadHardwareId(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxHardwareId, TBOX_HARDWARE_ID_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadSystemName(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxName, TBOX_NAME_LEN);
    return E_OK;
}


Std_ReturnType Rte_ReadSystemSupplierIdentifier(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxSystemSuppierIdentifier, TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadSystemSupplierEcuHardWareVersionNumber(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxHardVersion, TBOX_HARD_VERSION_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadSystemSupplierEcuSoftWareVersionNumber(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxSoftVersion, TBOX_SOFT_VERSION_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadSystemSupplierEcuBootVersionNumber(uint8* data)
{

    READ_STRING_DATA_AND_COPY(data, g_totalDidInfo.tboxBootVersion, TBOX_BOOT_VERSION_LEN);
    return E_OK;
}




Std_ReturnType Rte_ReadEcuSerialNumber(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.tboxId, TBOX_ID_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadVin(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.vinCode, TBOX_VIN_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteVin(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_carInfo.vinCode, data, TBOX_VIN_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write VIN failed:%d\r\n", errorCode);
    }
    BtTaskVinCodeWriteHook();

    return E_OK;
}

Std_ReturnType Rte_ReadCurrentActiveSession(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    data[0] = g_CurrentActiveSession;
    return E_OK;
}




Std_ReturnType Rte_ReadIMEI(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.imei, TBOX_IMEI_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadICCID(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.iccid, TBOX_ICCID_LEN);
    return E_OK;
}








//终端软件最后的更新日期
Std_ReturnType Rte_ReadSoftUpdateDate(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    memcpy(data, g_carInfo.softUpdateDate, TBOX_SOFTWARE_UPDATE_DATE_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadVehicleOfflineConfig(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    memcpy(data, g_carInfo.vehicleOfflineConfig, VEHICLE_OFFLINE_CONFIG_LEN);
    return E_OK;
}
Std_ReturnType Rte_WriteVehicleOfflineConfig(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    VehicleConfigData vehicleConfigData = {0};
    memcpy(&vehicleConfigData, data, VEHICLE_OFFLINE_CONFIG_LEN);

    if (vehicleConfigData.powerType > 3 ||
        vehicleConfigData.seatCount > 3 ||
        vehicleConfigData.tireSize > 3 ||
        vehicleConfigData.brakeFunctionConfig > 7)
    {
        *ErrorCode = DCM_E_REQUESTOUTOFRANGE;
        return E_NOT_OK;
    }

    memcpy(g_carInfo.vehicleOfflineConfig, data, VEHICLE_OFFLINE_CONFIG_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write VIN failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the TBox Serial Number
Std_ReturnType Rte_ReadTBoxSerialNumber(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    // Copy the last 20 bytes of tuid to data
    memset(data,0x20,TBOX_SERIAL_NUMBER_LEN);
    uint32_t tuidLen =  strlen((char *)g_carInfo.tuid);
    if (tuidLen >= TBOX_SERIAL_NUMBER_LEN)
    {
        memcpy(data, g_carInfo.tuid + (tuidLen - TBOX_SERIAL_NUMBER_LEN), TBOX_SERIAL_NUMBER_LEN);
    }
    return E_OK;
}
// Function to read the TUID
Std_ReturnType Rte_ReadTUID(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.tuid, TUID_LEN);
    return E_OK;
}

// Function to write the TUID
Std_ReturnType Rte_WriteTUID(uint8* data)
{

    NvErrorCode errorCode = NV_NO_ERROR;
    memcpy(g_carInfo.tuid, data, TUID_LEN);
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write TUID failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the TBOX Auth Status
Std_ReturnType Rte_ReadTBoxAuthStatus(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.tboxAuthStatus;
    return E_OK;
}



// Function to read the SIM Number
Std_ReturnType Rte_ReadSimNumber(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_carInfo.simNumber, TBOX_SIM_NUMBER_LEN);
    return E_OK;
}


// Function to read the Wakeup Time Interval
Std_ReturnType Rte_ReadWakeupTimeInterval(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint32_t value = g_carInfo.wakeupTimeInterval;
    data[0] = value >> 24;
    data[1] = value >> 16;
    data[2] = value >> 8;
    data[3] = value ;
    return E_OK;
}

// Function to write the Wakeup Time Interval
Std_ReturnType Rte_WriteWakeupTimeInterval(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.wakeupTimeInterval = data[0] << 24 | data[1] << 16 | data[2] << 8 | data[3];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Wakeup Time Interval failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the Auto Recharge Under Voltage threshold
Std_ReturnType Rte_ReadAutoRechargeUnderVoltage(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint32_t value = g_carInfo.AutoRechargeUnderVoltage;
    data[0] = value >> 24;
    data[1] = value >> 16;
    data[2] = value >> 8;
    data[3] = value ;
    return E_OK;
}

// Function to write the Auto Recharge Under Voltage threshold
Std_ReturnType Rte_WriteAutoRechargeUnderVoltage(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.AutoRechargeUnderVoltage = data[0] << 24 | data[1] << 16 | data[2] << 8 | data[3];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Auto Recharge Under Voltage failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the Link 1 SSL Enable flag
Std_ReturnType Rte_ReadLink1SSLEnable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.link1SSLEnable;
    return E_OK;
}

// Function to write the Link 1 SSL Enable flag
Std_ReturnType Rte_WriteLink1SSLEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.link1SSLEnable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link 1 SSL Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the Link 2 SSL Enable flag
Std_ReturnType Rte_ReadLink2SSLEnable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.link2SSLEnable;
    return E_OK;
}

// Function to write the Link 2 SSL Enable flag
Std_ReturnType Rte_WriteLink2SSLEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.link2SSLEnable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link 2 SSL Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the Offline Check flag
Std_ReturnType Rte_ReadOfflineCheckFlag(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.OfflineCheckFlag;
    return E_OK;
}

// Function to write the Offline Check flag
Std_ReturnType Rte_WriteOfflineCheckFlag(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.OfflineCheckFlag = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Offline Check Flag failed:%d\r\n", errorCode);

    }
    return E_OK;
}

// Function to read the AC Timeout in minutes
Std_ReturnType Rte_ReadACTimeoutMinutes(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint32_t value = g_carInfo.acTimeoutMinutes;
    data[0] = value >> 24;
    data[1] = value >> 16;
    data[2] = value >> 8;
    data[3] = value ;
    return E_OK;
}

// Function to write the AC Timeout in minutes
Std_ReturnType Rte_WriteACTimeoutMinutes(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.acTimeoutMinutes = data[0] << 24 | data[1] << 16 | data[2] << 8 | data[3];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write AC Timeout Minutes failed:%d\r\n", errorCode);

    }
    return E_OK;
}

Std_ReturnType Rte_ReadTimedWakeupEnable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.timedWakeupEnable;
    return E_OK;
}

Std_ReturnType Rte_WriteTimedWakeupEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.timedWakeupEnable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Timed Wakeup Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}
Std_ReturnType Rte_ReadNetworkWakeupEnable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.networkWakeupEnable;
    return E_OK;
}

Std_ReturnType Rte_WriteNetworkWakeupEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.networkWakeupEnable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Network Wakeup Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}

Std_ReturnType Rte_ReadGlobalLogEnable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    data[0] = g_carInfo.globalLogEnable;
    return E_OK;
}

Std_ReturnType Rte_WriteGlobalLogEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.globalLogEnable = data[0];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Global Log Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}

Std_ReturnType Rte_ReadLink1Enable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.link1Enable;
    return E_OK;
}

Std_ReturnType Rte_WriteLink1Enable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.link1Enable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Third Party Link Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink2Enable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.link2Enable;
    return E_OK;
}

Std_ReturnType Rte_WriteLink2Enable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.link2Enable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Third Party Link Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink3Enable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.link3Enable;
    return E_OK;
}

Std_ReturnType Rte_WriteLink3Enable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.link3Enable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Third Party Link Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}


Std_ReturnType Rte_ReadThirdPartyLinkEnable(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    *data = g_carInfo.thirdPartyLinkEnable;
    return E_OK;
}

Std_ReturnType Rte_WriteThirdPartyLinkEnable(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);

    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.thirdPartyLinkEnable = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Third Party Link Enable failed:%d\r\n", errorCode);

    }
    return E_OK;
}

Std_ReturnType Rte_ReadCertUpdateTime(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    memcpy(data, g_carInfo.certUpdateTime, CERT_UPDATE_TIME_LEN);
    return E_OK;
}


Std_ReturnType Rte_ReadDataResendTestTime(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint16_t value = g_carInfo.dataResendTestTime;
    data[0] = value >> 8;
    data[1] = value;
    return E_OK;
}

Std_ReturnType Rte_WriteDataResendTestTime(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.dataResendTestTime = (data[0] << 8) | data[1];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Data Resend Test Time failed:%d\r\n", errorCode);

    }
    return E_OK;
}
Std_ReturnType Rte_ReadLevel3AlarmTestTime(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint16_t value = g_carInfo.level3AlarmTestTime;
    data[0] = value >> 8;
    data[1] = value;
    return E_OK;
}

Std_ReturnType Rte_WriteLevel3AlarmTestTime(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.level3AlarmTestTime = (data[0] << 8) | data[1];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Level 3 Alarm Test Time failed:%d\r\n", errorCode);
    }
    return E_OK;
}
// Std_ReturnType Rte_ReadVehicleRestrictFlag(uint8* data)
// {
//     CHECK_GEAR_POSITION_AND_RETURN();
//     data[0] = g_carInfo.vehicleRestrictFlag;
//     return E_OK;
// }

// Std_ReturnType Rte_WriteVehicleRestrictFlag(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
// {
//     CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
//     if (data[0] != 0x00)
//     {
//         *ErrorCode = DCM_E_REQUESTOUTOFRANGE;
//         return E_NOT_OK;
//     }
//
//     NvErrorCode errorCode = NV_NO_ERROR;
//     g_carInfo.vehicleRestrictFlag = data[0];
//     errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
//     if (NV_NO_ERROR != errorCode)
//     {
//         SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Level 3 Alarm Test Time failed:%d\r\n", errorCode);
//     }
//     return E_OK;
// }

Std_ReturnType Rte_ReadLockCarStatusValue(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    data[0] = g_carInfo.lockCarStatus;
    return E_OK;
}

Std_ReturnType Rte_WriteLockCarStatusValue(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);

    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.lockCarStatus = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Lock Car Status Value failed:%d\r\n", errorCode);
    }
    return E_OK;
}

Std_ReturnType Rte_ReadSpeedLimitStatusValue(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    data[0] = g_carInfo.speedLimitStatus;
    return E_OK;
}

Std_ReturnType Rte_WriteSpeedLimitStatusValue(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_ENABLE_FLAG_AND_RETURN_E_CODE(*data,ErrorCode);

    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.speedLimitStatus = *data;
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Speed Limit Status Value failed:%d\r\n", errorCode);
    }
    return E_OK;
}

Std_ReturnType Rte_ReadSpeedLimitValue(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    data[0] = g_carInfo.speedLimitValue;
    return E_OK;
}

Std_ReturnType Rte_WriteSpeedLimitValue(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    // 这里不需要像vehicleRestrictFlag那样检查是否为0，因为限速值可以是任何值
    // 如果需要范围检查，可以添加类似的代码
    // if (data[0] > MAX_SPEED_LIMIT) {
    //     *ErrorCode = DCM_E_REQUESTOUTOFRANGE;
    //     return E_NOT_OK;
    // }

    NvErrorCode errorCode = NV_NO_ERROR;
    g_carInfo.speedLimitValue = data[0];
    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8 *)&g_carInfo, g_nvInfoMap[NV_ID_CAR_INFO].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Speed Limit Value failed:%d\r\n", errorCode);
    }
    return E_OK;
}





Std_ReturnType Rte_ReadTboxCertificateID(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.tboxCertificateID, TBOX_CERT_LEN);
    return E_OK;
}

Std_ReturnType Rte_ReadLink1Addr(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link1Addr, TBOX_LINK_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink1Addr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link1Addr, data, TBOX_LINK_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Cert Update Time failed:%d\r\n", errorCode);

    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink1Port(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link1Port, TBOX_LINK_PORT_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink1Port(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_PORT_STRING_AND_IN_RANGE(data,TBOX_LINK_PORT_LEN,ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link1Port, data, TBOX_LINK_PORT_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link1Port failed:%d\r\n", errorCode);
    }
    return E_OK;
}

Std_ReturnType Rte_ReadLink2Addr(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link2Addr, TBOX_LINK_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink2Addr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link2Addr, data, TBOX_LINK_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link2Addr failed:%d\r\n", errorCode);

    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink2Port(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link2Port, TBOX_LINK_PORT_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink2Port(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_PORT_STRING_AND_IN_RANGE(data,TBOX_LINK_PORT_LEN,ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link2Port, data, TBOX_LINK_PORT_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link2Port failed:%d\r\n", errorCode);
    }
    return E_OK;
}


Std_ReturnType Rte_ReadLink3Addr(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link3Addr, TBOX_LINK_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink3Addr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link3Addr, data, TBOX_LINK_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link3Addr failed:%d\r\n", errorCode);
    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink3Port(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link3Port, TBOX_LINK_PORT_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink3Port(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_PORT_STRING_AND_IN_RANGE(data,TBOX_LINK_PORT_LEN,ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link3Port, data, TBOX_LINK_PORT_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link3Port failed:%d\r\n", errorCode);
    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink3BiAuthPort(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid.link3BiAuthPort, TBOX_LINK_PORT_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink3BiAuthPort(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_PORT_STRING_AND_IN_RANGE(data,TBOX_LINK_PORT_LEN,ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid.link3BiAuthPort, data, TBOX_LINK_PORT_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID, (uint8 *)&g_staticDid, g_nvInfoMap[NV_ID_STATIC_DID].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write link3BiAuthPort failed:%d\r\n", errorCode);
    }
    return E_OK;
}


Std_ReturnType Rte_ReadThirdPartyLinkAddr(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid2.thirdPartyLinkAddr, TBOX_LINK_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteThirdPartyLinkAddr(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid2.thirdPartyLinkAddr, data, TBOX_LINK_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID2, (uint8 *)&g_staticDid2, g_nvInfoMap[NV_ID_STATIC_DID2].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write ThirdPartyLinkAddr failed:%d\r\n", errorCode);
    }
    return E_OK;
}
Std_ReturnType Rte_ReadThirdPartyLinkPort(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid2.thirdPartyLinkPort, TBOX_LINK_PORT_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteThirdPartyLinkPort(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);
    CHECK_PORT_STRING_AND_IN_RANGE(data,TBOX_LINK_PORT_LEN,ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid2.thirdPartyLinkPort, data, TBOX_LINK_PORT_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID2, (uint8 *)&g_staticDid2, g_nvInfoMap[NV_ID_STATIC_DID2].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write ThirdPartyLinkPort failed:%d\r\n", errorCode);
    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink3Username(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid2.link3Username, TBOX_LINK_USERNAME_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink3Username(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid2.link3Username, data, TBOX_LINK_USERNAME_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID2, (uint8 *)&g_staticDid2, g_nvInfoMap[NV_ID_STATIC_DID2].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link3AddrUsername failed:%d\r\n", errorCode);
    }
    return E_OK;
}
Std_ReturnType Rte_ReadLink3Password(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_staticDid2.link3Password, TBOX_LINK_PASSWORD_LEN);
    return E_OK;
}

Std_ReturnType Rte_WriteLink3Password(uint8* data, uint16 dataLength, Dcm_NegativeResponseCodeType* ErrorCode)
{
    CHECK_GEAR_POSITION_AND_RETURN_E_CODE(ErrorCode);

    COPY_FILTERED_STRING_DATA(g_staticDid2.link3Password, data, TBOX_LINK_PASSWORD_LEN);
    NvErrorCode errorCode = NvApiWriteData(NV_ID_STATIC_DID2, (uint8 *)&g_staticDid2, g_nvInfoMap[NV_ID_STATIC_DID2].nvValidLen);
    if (NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rte Write Link3PortPassword failed:%d\r\n", errorCode);
    }
    return E_OK;
}






Std_ReturnType Rte_ReadEcuBatteryVoltage(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.ecuBatteryVoltage;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadVehicleSpeed(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint16_t value = g_dynamicDid.vehicleSpeed;
    data[0] = value >> 8;
    data[1] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadOdometer(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint32_t value = g_dynamicDid.odometer;
    data[0] = value >> 24;
    data[1] = value >> 16;
    data[2] = value >> 8;
    data[3] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadLowPowerMode(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.lowPowerMode;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadDateTime(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    memcpy(data, g_dynamicDid.dateTime, sizeof(g_dynamicDid.dateTime));
    return E_OK;
}

Std_ReturnType Rte_ReadVehiclePowerMode(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.vehiclePowerMode;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadGearPosition(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.gearPosition;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadCellularSignalStrength(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.cellularSignalStrength;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadGnssSignalStrength(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.gnssSignalStrength;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadWifiSignalStrength(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.wifiSignalStrength;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadLink1TcpStatus(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.link1TcpStatus;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadLink2TcpStatus(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    uint8_t value = g_dynamicDid.link2TcpStatus;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadLink3TcpStatus(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.link3TcpStatus;
    data[0] = value;
    return E_OK;
}

Std_ReturnType Rte_ReadPowerManagementMode(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.powerManagementMode;
    data[0] = value;
    return E_OK;
}
Std_ReturnType Rte_ReadMileageClearCount(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint8_t value = g_dynamicDid.mileageClearCount;
    data[0] = value;
    return E_OK;
}
Std_ReturnType Rte_ReadTboxSavesMileageValues(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();
    uint32_t value = g_dynamicDid.tboxSaveMileageValues;
    data[0] = value >> 24;
    data[1] = value >> 16;
    data[2] = value >> 8;
    data[3] = value;
    return E_OK;
}


Std_ReturnType Rte_ReadRdsSoftwareIn(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_dynamicDid.rdsSoftwareIn,RDS_SOFTWARE_IN_LEN);
    return E_OK;
}
Std_ReturnType Rte_ReadRdsSoftwareOut(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_dynamicDid.rdsSoftwareOut,RDS_SOFTWARE_OUT_LEN);
    return E_OK;
}
Std_ReturnType Rte_ReadRdsSoftwareSupplierIdentifier(uint8* data)
{
    CHECK_GEAR_POSITION_AND_RETURN();

    READ_STRING_DATA_AND_COPY(data, g_dynamicDid.rdsSoftwareSupplierIdentifier,RDS_SOFTWARE_SUPPLIER_IDENTIFIER_LEN);
    return E_OK;
}
