/*============================================================================*/
/*  Copyright (C) 2009-2016, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Dcm_AppFunc.h>
 *  @brief      <>
 *  
 *  <Compiler: IAR v7.50.2    MCU:NXP MKS22F256>
 *  
 *  <AUTHOR> wen>
 *  @date       <30-05-2016>
 */
/*============================================================================*/
#ifndef DEM_H
#define DEM_H



/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20160530  luowen       Initial version
 * 
 */
/*============================================================================*/

/*=======[M I S R A C  R U L E  V I O L A T I O N]============================*/
/*  <MESSAGE ID>    <CODE LINE>    <REASON>    
 *  
 *                                 
 *                                 
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define DEM_H_VENDOR_ID  0
#define DEM_H_MODULE_ID  0
#define DEM_H_AR_MAJOR_VERSION  3
#define DEM_H_AR_MINOR_VERSION  1
#define DEM_H_AR_PATCH_VERSION  5
#define DEM_H_SW_MAJOR_VERSION  1
#define DEM_H_SW_MINOR_VERSION  0
#define DEM_H_SW_PATCH_VERSION  0
#define DEM_H_VENDOR_API_INFIX  0

#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */

/*=======[I N C L U D E S]====================================================*/
#include "Dem_Types.h"


/*=======[M A C R O S]========================================================*/
/**
* Report error status of BSWs.
* @param eventId  ID of event
* @param eventStatus status of event
*/
/** @req DEM-APIR-005[Dem206]*/
/**@req DEM-FUNR-208[Dem107]*/


#define DTC_AVAILABILITY_MASK_VALUE    0x09
#define DTC_FORMATE_IDENTIFIER         0x01

/* SpecificCauseCode of NRC 0x22 */
#define DEM_NRC_SCC_BSW                0x00
#define DEM_NRC_SCC_XXX                0x01

/*
*Definition of Event IDs  定义故障码类型
*/
typedef enum
{
    DTC_CAN_BUS_OFF_INDEX = 0x00,
    DTC_EXT_POWER_VOL_LOW_INDEX,
    DTC_EXT_POWER_VOL_HIGH_INDEX,
    DTC_LOST_COM_WITH_HECU_INDEX,
    DTC_LOST_COM_WITH_BCM_INDEX,
    DTC_LOST_COM_WITH_BMS_INDEX,
    DTC_LOST_COM_WITH_MCU_INDEX,
    DTC_LOST_COM_WITH_DCDC_INDEX,
    DTC_LOST_COM_WITH_ABS_INDEX,
    DTC_LOST_COM_WITH_IC_INDEX,
    DTC_LOST_COM_WITH_APU_INDEX,
    DTC_GNSS_MODULE_FAULT_INDEX,
    DTC_REMOTE_COM_FAULT_INDEX,
    DTC_SIM_CARD_FAULT_INDEX,
    DTC_NETWORK_MODULE_FAILURE_INDEX,
    DTC_GNSS_ANT_OPEN_INDEX,
    DTC_GNSS_ANT_SHORT_INDEX,
    DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX,
    DTC_EMMC_STORAGE_CHIP_FAULT_INDEX,
    DTC_EMMC_STORAGE_FULL_INDEX,
    DTC_NETWORK_MODULE_FLASH_FULL_INDEX,
    DTC_MAX_INDEX
}DtcIndexType;

typedef enum
{
    DTC_CAN_BUS_OFF_VALUE                    = 0xC07488,        // Info CAN Bus Off
    DTC_EXT_POWER_VOL_LOW_VALUE              = 0x911716,        //供电电压电量过低
    DTC_EXT_POWER_VOL_HIGH_VALUE             = 0x911717,        //供电电压电量过高
    DTC_LOST_COM_WITH_HECU_VALUE             = 0xC12287,        // Lost communication with HECU
    DTC_LOST_COM_WITH_BCM_VALUE              = 0xC14087,         // Lost communication with BCM
    DTC_LOST_COM_WITH_BMS_VALUE              = 0xC1BF87,         // Lost communication with BMS
    DTC_LOST_COM_WITH_MCU_VALUE              = 0xC11087,         // Lost communication with MCU
    DTC_LOST_COM_WITH_DCDC_VALUE             = 0xC1BD87,         // Lost communication with DCDC
    DTC_LOST_COM_WITH_ABS_VALUE              = 0xC15A87,         // Lost communication with ABS
    DTC_LOST_COM_WITH_IC_VALUE               = 0xC15587,          // Lost communication with IC
    DTC_LOST_COM_WITH_APU_VALUE              = 0xC14B87,         // Lost communication with APU
    DTC_GNSS_MODULE_FAULT_VALUE              = 0x950000,           // 定位模块故障
    DTC_REMOTE_COM_FAULT_VALUE               = 0x950102,           // 远程通信故障
    DTC_SIM_CARD_FAULT_VALUE                 = 0x950200,           // SIM卡不可用
    DTC_NETWORK_MODULE_FAILURE_VALUE         = 0x950501,           // 联网模块（模组）故障、
    DTC_GNSS_ANT_OPEN_VALUE                  = 0x950613,        // GPS antenna open
    DTC_GNSS_ANT_SHORT_VALUE                 = 0x950611,        // GPS antenna short to GND
    DTC_INTERNAL_BATTERY_DISCONNECTED_VALUE  = 0x950713,          // 内部备用电池未连接
    DTC_EMMC_STORAGE_CHIP_FAULT_VALUE        = 0x950844,         // eMMC存储芯片异常
    DTC_EMMC_STORAGE_FULL_VALUE              = 0x950804,         // eMMC存储空间满
    DTC_NETWORK_MODULE_FLASH_FULL_VALUE      = 0x950944,        // 通信模块内部FLASH存储空间满
}DtcIndexHexType;





/*=======[T Y P E   D E F I N I T I O N S]====================================*/


/*=======[E X T E R N A L   D A T A]==========================================*/


/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
uint8 Dem_GetTranslationType(void);
Std_ReturnType Dem_GetDTCStatusAvailabilityMask(uint8 *availabilityMask);
Std_ReturnType Dem_SetDTCFilter(uint8 dtcStatusMask, uint8 dtcType, uint8 memoryType, uint8 filterSeverity, uint8 severityType, uint8 filterFault);
Std_ReturnType Dem_GetNumberOfFilteredDTC(uint16 *dtcFilterNum);
Std_ReturnType Dem_GetNextFilteredDTC(uint32 *dtc, uint8 *dtcStatus, uint8 dtcType);
Std_ReturnType Dem_GetStatusOfDTC(uint32 dtc, uint8 dtcType, uint8 memoryType, uint8 *dtcStatus);
Std_ReturnType Dem_DisableDTCRecordUpdate(void);
Std_ReturnType Dem_EnableDTCRecordUpdate(void);
Std_ReturnType Dem_GetFreezeFrameDataByDTC(uint32 dtc, uint8 dtcType,uint8 memoryType, uint8 index, uint8 *buf, uint16 *len);
Std_ReturnType Dem_ClearDTC(uint32 groupOfDTC, uint8 dtcType,uint8 memoryType);
void Dem_WriteDTC(uint8 dtcIndex, uint8 dtcStatus);
void Dem_FreshHistoryDTC(void);
void DiagWriteBusOffDTC(void);

int Dem_GetAllDTCStatus(uint8 *buf ,uint16 *len,uint8 dtcMask);
int Dem_GetAllDTC(uint8 *dtcInfoBuf ,uint16 *dtcByteLen, uint8 dtcMask, uint16 *dtcCnt);
uint8 Dem_GetDtcRecordEnableState(void);





/*=======[I N T E R N A L   D A T A]==========================================*/

/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/

#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif  /* end of DEM_H */

/*=======[E N D   O F   F I L E]==============================================*/

