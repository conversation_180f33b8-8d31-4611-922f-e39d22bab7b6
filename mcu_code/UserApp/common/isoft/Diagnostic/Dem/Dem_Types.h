/************************************************************************
*				        ESEC UESTC
* 	 Copyright (C) 2005-2011 ESEC UESTC. All Rights Reserved.
***********************************************************************/

/**
 * Log:  
 * Revision 1.0 2011-5-6上午08:36:11 stanley
 * description: create
 *
 */

/**
 * @file 	Dem_Types.h
 * @brief
 *	<li>Function： definitions for types of DEM module</li>
 *  <li>Design Points：</li>
 *  <p>
 *     Definition for types, including standard types, configuration types, runtime types
 *  </p>
 * <AUTHOR>
 * @date 	2011-5-6
 * 
 */

#ifndef DEM_TYPES_H_
#define DEM_TYPES_H_

/****************************** references *********************************/
#include "Std_Types.h"

#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */

/****************************** declarations *********************************/
/** @req DEM-FUNR-001[Dem153], @req DEM-FUNR-002[Dem154] Internal identifier of a diagnostic event. 0 is not a valid value. */
typedef uint16 Dem_EventIdType;

typedef uint8 Dem_EventStatusType;
#define DEM_EVENT_STATUS_PASSED 0x00
#define DEM_EVENT_STATUS_FAILED 0x01
#define DEM_EVENT_STATUS_PREPASSED 0x02
#define DEM_EVENT_STATUS_PREFAILED 0x03
#define DEM_EVENT_STATUS_FDC_THRESHOLD_REACHED 0x04

/** @req DEM-CFGR-012[Dem678_Conf] DTC value of the selected group of DTC */
typedef uint32 Dem_DTCGroupType;

/* selects all DTCs */
#define DEM_DTC_GROUP_ALL_DTCS          (uint32)0xFFFFFF

/* selects group of OBD-relevant DTCs */
#define DEM_DTC_GROUP_EMISSION_REL_DTCS 0x000000

/** @req DEM-CFGR-018[Dem644_Conf] kind of DTC */
typedef uint8 Dem_DTCKindType;

/* select all DTCs */   //DTC 分类型  P C B U 码
#define DEM_DTC_KIND_ALL_DTCS          0x01U

/* select OBD-relevant DTCs */
#define DEM_DTC_KIND_EMISSION_REL_DTCS 0x02U

/*zxl add*/
#define DEM_REPORT_DTC_SUPPORTED 0x0A

/** @req DEM-FUNR-014[Dem010], @req DEM-FUNR-015[Dem548] This type is used to define the location of the events. The definition and use of the different memory types is OEM-specific */
typedef uint8 Dem_DTCOriginType;

/**@req DEM-FUNR-015[Dem548]*/
/* event iformation located in the primary memory */  // 保存NV 或 RAM里
#define DEM_DTC_ORIGIN_PRIMARY_MEMORY   0x01U

/* event information located in the mirror memory */
#define DEM_DTC_ORIGIN_MIRROR_MEMORY    0x02U

/* event information is lcated in the permanent memory */
#define DEM_DTC_ORIGIN_PERMANENT_MEMORY 0x03U

/* event information located in the secondary memory */
#define DEM_DTC_ORIGIN_SECONDARY_MEMORY 0x04U

/**@req DEM-FUNR-025[Dem006]*/
/** @req DEM-FUNR-025[Dem006] In this data-byte each bit has an individual meaning. The bit is set to 1 when the condition holds. For example, if the 2nd bit(0x02) is set to 1, this means that the test failed this operation cycle. If the bit is set to 0, it has not yet failed this cycle. */
/**@req DEM-FUNR-246[Dem014]*/
typedef uint8 Dem_EventStatusExtendedType;

/*status mask for set filter for all event*/
#define DEM_DTC_STATUS_MASK_ALL                              0x00

/* UDS DTC status bit 0: test failed */
#define DEM_EVENT_STATUS_TEST_FAILED                         0x01U

/* UDS DTC status bit 1: test failed this operation cycle */
#define DEM_EVENT_STATUS_TEST_FAILED_THIS_OPCYC              0x02U

/* UDS DTC status bit 2: Pending DTC */
#define DEM_EVENT_STATUS_PENDING_DTC                         0x04U

/* UDS DTC status bit 3: confirmed DTC */
#define DEM_EVENT_STATUS_CONFIRMED_DTC                       0x08U

/* UDS DTC status bit 4: test not completed since last clear */
#define DEM_EVENT_STATUS_TEST_NOT_COMPLETED_SINCE_LAST_CLEAR 0x10U

/* UDS DTC status bit 5: test failed since last clear */
#define DEM_EVENT_STATUS_TEST_FAILED_SINCE_LAST_CLEAR        0x20U

/* UDS DTC status bit 6: test bit completed this operation cyle */
#define DEM_EVENT_STATUS_TEST_NOT_COMPLETED_THIS_OPCYC       0x40U

/* UDS DTC status 7: warning indicator requested */
#define DEM_EVENT_STATUS_WARNING_INDICATOR_REQUESTED         0x80U

/** @req DEM-CFGR-018[Dem645_Conf] severity of DTC */
typedef uint8 Dem_DTCSeverityType;

/* no severity information available */  //没有严重信息可用
#define DEM_SEVERITY_NO_SEVERITY        0x00

/* maitanance required */
#define DEM_SEVERITY_MAINTENANCE_ONLY   0x20U

/* check at next halt */
#define DEM_SEVERITY_CHECK_AT_NEXT_HALT 0x40U

/* check immediately */
#define DEM_SEVERITY_CHECK_IMMEDIATELY  0x80U

/*type Dem_FilterForFDCType */
typedef uint8 Dem_FilterForFDCType;

/* fault detection counter information used */
#define DEM_FILTER_FOR_FDC_YES 0x00

/* fault detection counter information not used */ //错误检查计数信息不能使用
#define DEM_FILTER_FOR_FDC_NO  0x01U

/* type Dem_FilterWithSeverityType */
typedef uint8 Dem_FilterWithSeverityType;

/* severity information used */
#define DEM_FILTER_WITH_SEVERITY_YES 0x00U

/* severity information not used */  //严重程度信息不使用
#define DEM_FILTER_WITH_SEVERITY_NO  0x01U

/*## type Dem_RatioIdType */
typedef uint8 Dem_RatioIdType;

/* used to return the status of updating the DTC filter */
typedef uint8 Dem_ReturnSetDTCFilterType;

/* filter was accepted */
#define DEM_FILTER_ACCEPTED 0x00

/* wrong filter selected */
#define DEM_WRONG_FILTER    0x01U

#define DEM_BEYOND_PARA     0x02U

/* used to return the status of Dem_GetStatusOfDTC */
typedef uint8 Dem_ReturnGetStatusOfDTCType;

/* status of DTC is OK */
#define DEM_STATUS_OK              0x00

/* wrong DTC */
#define DEM_STATUS_WRONG_DTC       0x01U

/* wrong DTC origin */
#define DEM_STATUS_WRONG_DTCORIGIN 0x02U

/* DTC kind wrong */
#define DEM_STATUS_WRONG_DTCKIND   0x03U

/* DTC failed */
#define DEM_STATUS_FAILED          0x04U

/************************************************/
/* used to return the status of Dem_ClearDTC */
typedef uint8 Dem_ReturnClearDTCType;

/* DTC successfully cleared */
#define DEM_CLEAR_OK              0x00

/* wrong DTC */
#define DEM_CLEAR_WRONG_DTC       0x01U

/* wrong DTC origin */
#define DEM_CLEAR_WRONG_DTCORIGIN 0x02U

/* DTC kind wrong */
#define DEM_CLEAR_WRONG_DTCKIND   0x03U

/* DTC not cleared */
#define DEM_CLEAR_FAILED          0x04U

/* clearing of DTC is pending */
#define DEM_CLEAR_PENDING         0x05U

/************************************************/
/** @req DEM-APIR-902, @req DEM-APIR-903, @req DEM-APIR-904, @req DEM-APIR-905 Used to return the status of Dem_DisableDTCStorage and Dem_EnableDTCStorage, Dem_DisableEventStatusUpdate and Dem_EnableEventStatusUpdate */
typedef uint8 Dem_ReturnControlDTCStorageType;

/* DTC storage control successful */
#define DEM_CONTROL_DTC_STORAGE_OK   0x00

/* DTC storage control not successful */
#define DEM_CONTROL_DTC_STORAGE_N_OK 0x01U

/* DTC setting control not successful because group of DTC war wrong */
#define DEM_CONTROL_DTC_WRONG_DTCGROUP 0x02U

/* used to return the status of Dem_GetExtendedDataRecordByDTC */
typedef uint8 Dem_ReturnGetExtendedDataRecordByDTCType;

/* extended data record successfully returned */
#define DEM_RECORD_OK               0x00

/* wrong DTC */
#define DEM_RECORD_WRONG_DTC        0x01U

/* origin wrong */
#define DEM_RECORD_WRONG_DTCORIGIN  0x02U

/* DTC kind wrong */
#define DEM_RECORD_WRONG_DTCKIND    0x03U

/* record number wrong */
#define DEM_RECORD_WRONG_NUMBER     0x04U

/* provided buffer too small */
#define DEM_RECORD_WRONG_BUFFERSIZE 0x05U

/* the requested value is currently not available. The caller can retry later */
#define DEM_RECORD_PENDING          0x06U

/* used to return the status of Dem_GetFreezeFrameDataByDTC */
typedef uint8 Dem_ReturnGetFreezeFrameDataByDTCType;

/* freeze frame data successfully returned */
#define DEM_GET_FFDATABYDTC_OK                 0x00

/* wrong DTC */
#define DEM_GET_FFDATABYDTC_WRONG_DTC          0x01U

/* wrong DTC origin */
#define DEM_GET_FFDATABYDTC_WRONG_DTCORIGIN    0x02U

/* DTC kind wrong */
#define DEM_GET_FFDATABYDTC_WRONG_DTCKIND      0x03U

/* wrong record number */
#define DEM_GET_FFDATABYDTC_WRONG_RECORDNUMBER 0x04U

/* provided buffer size too small */
#define DEM_GET_FFDATABYDTC_WRONG_BUFFERSIZE   0x05U

/* the requested value is currently not available. The caller can retry later */
#define DEM_GET_FFDATABYDTC_PENDING            0x06U

/* used to return the status of Dem_GetNextFilteredDTC */
typedef uint8 Dem_ReturnGetNextFilteredDTCType;

/* returned next filtered DTC */
#define DEM_FILTERED_OK              0x00

/* No DTC matched */
#define DEM_FILTERED_NO_MATCHING_DTC 0x01U

/* DTC kind wrong */
#define DEM_FILTERED_WRONG_DTCKIND   0x02U

/* requested value is currently not available. the caller can retry later */
#define DEM_FILTERED_PENDING         0x03U

/* used to return the status of Dem_GetNumberOfFilteredDTC */
typedef uint8 Dem_ReturnGetNumberOfFilteredDTCType;

/* get number of DTC was successful */
#define DEM_NUMBER_OK      0x00

/* get number of DTC failed */
#define DEM_NUMBER_FAILED  0x01U

/* get number of DTC is pending */
#define DEM_NUMBER_PENDING 0x02U

/* type of supported DTC format */
typedef uint8 Dem_DTCTypeSupportedType;

/**@req DEM-FUNR-009[Dem013]*/
/* DEM_ISO15031_6 */
#define DEM_ISO15031_6 0x00

/* DEM_ISO14229_1 */
#define DEM_ISO14229_1 0x01U

/* DEM_ISO11992_4 */
#define DEM_ISO11992_4 0x02U



#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif /* DEM_TYPES_H_ */
