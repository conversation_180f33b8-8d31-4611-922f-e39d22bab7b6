/*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <>
 *  @brief      <Briefly describe file(one line)>
 *
 *  <Compiler: XXX    MCU:XXX>
 *
 *  <AUTHOR>
 *  @date       <20-03-2013>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>                         */
/*  V1.0.0       2013-3-20  chenms    Initial version                         */
/*  V1.0.1       2016-7-05  luowen    Get UDS rx pduid from config file       */
/*                                    while simulating received 11 01         */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/

/*=======[I N C L U D E S]====================================================*/
#include <string.h>
#include "Dem.h"
#include "NvApi.h"
#include "LogApi.h"
#include "DiagApi.h"
#include "UDS.h"
#include "Can_Cfg.h"
#include "LedApi.h"

/*=======[V E R S I O N  C H E C K]===========================================*/




/***************************************************/
extern DtcInfo  g_dtcInfo;
extern CommonInfo g_commonInfo;
extern uint32  g_dtcValueList[];

/****************************************************
 ****************************************************
****************************************************/
uint16  g_dtcFilterNum = 0;
uint8   g_dtcStatusMask = 0;
uint8   g_dtcRecordEnable = 0x01;
static uint8 g_dtcCodeSupportedCnt = DTC_MAX_INDEX;
/*******************public functions******************************/
/*************************************************
函数名称: Dem_GetDTCStatusAvailabilityMask
函数功能: 获取故障码有效掩码
输入参数: availabilityMask
输出参数: Std_ReturnType
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/02/07
*************************************************/
Std_ReturnType Dem_GetDTCStatusAvailabilityMask(uint8 *availabilityMask)
{
    if(NULL == availabilityMask)
    {
        return E_NOT_OK;
    }

    *availabilityMask = DTC_AVAILABILITY_MASK_VALUE;

    return E_OK;
}

/*************************************************
函数名称: Dem_GetTranslationType
函数功能: 获取故障码类型
输入参数: 无
输出参数: uint8
函数返回类型值：故障码类型
编写者: liaoyonggang
编写日期 :2017/02/07
*************************************************/
uint8 Dem_GetTranslationType(void)
{
    return DTC_FORMATE_IDENTIFIER;
}

/*************************************************
函数名称: Dem_SetDTCFilter
函数功能: 设置故障码过滤
输入参数: 无
输出参数: uint8
函数返回类型值：故障码类型
编写者: liaoyonggang
编写日期 :2017/02/07
*************************************************/
Std_ReturnType Dem_SetDTCFilter(uint8 dtcStatusMask, uint8 dtcType, uint8 memoryType, uint8 filterSeverity, uint8 severityType, uint8 filterFault)
{
    uint8 index = 0;
    
    //if(0x00 == dtcStatusMask)
    //{
    //    return DEM_BEYOND_PARA;
    //}

    //if(!(dtcStatusMask&DTC_AVAILABILITY_MASK_VALUE))
    //{
    //    return DEM_WRONG_FILTER;
    //}

    g_dtcFilterNum = 0;
    g_dtcStatusMask = dtcStatusMask;
    for(index = 0; index < g_dtcCodeSupportedCnt; index++)
    {
        if((g_dtcStatusMask&g_dtcInfo.dtc[index].status) || (DCM_REPORTSUPPORTEDDTC == dtcType))
        {
            g_dtcFilterNum++;  
        }
    }

    return DEM_FILTER_ACCEPTED;
}

/*************************************************
函数名称: Dem_GetNumberOfFilteredDTC
函数功能: 获取过滤后故障码的个数
输入参数: 无
输出参数: uint16
函数返回类型值：过滤故障码个数
编写者: liaoyonggang
编写日期 :2017/02/08
*************************************************/
Std_ReturnType Dem_GetNumberOfFilteredDTC(uint16 *dtcFilterNum)
{
    if(NULL == dtcFilterNum)
    {
        return DEM_NUMBER_FAILED;
    }
    else
    {
        *dtcFilterNum = g_dtcFilterNum;
        return DEM_NUMBER_OK;
    }
}

/*************************************************
函数名称: Dem_GetNextFilteredDTC
函数功能: 获取下一个故障码和状态
输入参数: *dtc       ---故障码值指针
          *dtcStatus ---故障码状态值
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/08
*************************************************/
Std_ReturnType Dem_GetNextFilteredDTC(uint32 *dtc, uint8 *dtcStatus, uint8 dtcType)
{   
    static uint16 index = 0;
    static uint8  count = 0;
    if((NULL == dtc) ||(NULL == dtcStatus))
    {
        return DEM_FILTERED_PENDING;
    }
    do
    {
        //匹配值相比
        if((g_dtcStatusMask&g_dtcInfo.dtc[index].status) || (dtcType == DCM_REPORTSUPPORTEDDTC))
        {
            *dtc = g_dtcValueList[index];
            *dtcStatus = g_dtcInfo.dtc[index].status;
            index++;
            count++;
            if(g_dtcFilterNum == count)
            {
                index = 0;
                count = 0;
            }
            return DEM_FILTERED_OK;
        }
        index++;
    }while(index < g_dtcCodeSupportedCnt);

    if(g_dtcCodeSupportedCnt == index)
    {
        index = 0;
    }
    
    return DEM_FILTERED_NO_MATCHING_DTC;
}

/*************************************************
函数名称: Dem_GetStatusOfDTC
函数功能: 获取指定故障码状态
输入参数: dtc ---匹配的故障码
          dtcType ---保存的故障码类型
          memoryType ---保存的内存类型
          *dtcStatus ---故障码状态值
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/09
*************************************************/
Std_ReturnType Dem_GetStatusOfDTC(uint32 dtc, uint8 dtcType, uint8 memoryType, uint8 *dtcStatus)
{
    uint8 index = 0;
    
    if(NULL == dtcStatus)
    {
        return DEM_STATUS_FAILED;
    }

    for(index = 0; index < g_dtcCodeSupportedCnt; index++)
    {
        if(dtc == g_dtcValueList[index])
        {
            *dtcStatus = g_dtcInfo.dtc[index].status;
            return DEM_STATUS_OK;
        }
    }

    if(g_dtcCodeSupportedCnt == index)
    {
        return DEM_STATUS_WRONG_DTC;
    }
    else
    {
        return DEM_STATUS_OK;
    }
}


/*************************************************
函数名称: Dem_DisableDTCRecordUpdate
函数功能: 禁止记录故障码
输入参数: 无
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/09
*************************************************/
Std_ReturnType Dem_DisableDTCRecordUpdate(void)
{
    if (GetGearPosition() > 0x01)
    {
        return DEM_CONTROL_DTC_STORAGE_N_OK;
    }
    g_dtcRecordEnable = 0x00;
    return E_OK;
}

/*************************************************
函数名称: Dem_EnableDTCRecordUpdate
函数功能: 允许记录故障码
输入参数: 无
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/09
*************************************************/
Std_ReturnType Dem_EnableDTCRecordUpdate(void)
{
    if (GetGearPosition() > 0x01)
    {
        return DEM_CONTROL_DTC_STORAGE_N_OK;
    }
    g_dtcRecordEnable = 0x01;
    /*Notify ECU Update DTC*/
    DiagDtcDeviceTableInit();
    DiagDtcNotifyArmStartCheck();
    return E_OK;
}

/*************************************************
函数名称: Dem_GetFreezeFrameData
函数功能: 读取故障码冻结帧数据
输入参数: dtcIndex       --- 故障码索引值
          *buf       --- 保存快照内容
          *len       ----快照内容长度
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/09
修改：benyulong 2018/08/13
*************************************************/
static Std_ReturnType Dem_GetFreezeFrameData(uint8 dtcIndex,uint8 index, uint8 *buf, uint16 *len)
{
    if((NULL == buf)||(NULL == len))
    {
        return DEM_GET_FFDATABYDTC_WRONG_BUFFERSIZE;
    }

    uint32_t offset = 1;
    uint8_t num = 0;
    DtcSnapshot *dtcSnapshotArray = g_dtcInfo.dtc[dtcIndex].dtcSnapshot;

    if (index > 0 && index <= DTC_SNAPSHOT_COUNT_MAX)
    {
        DtcSnapshot dtcSnapshot = dtcSnapshotArray[index - 1];
        if (dtcSnapshot.isNotNull)
        {
            num ++;
            buf[offset++] = dtcSnapshot.ecuBatteryVoltage;
            buf[offset++] = dtcSnapshot.vehicleSpeed >> 8;
            buf[offset++] = dtcSnapshot.vehicleSpeed;
            buf[offset++] = dtcSnapshot.odometer >> 24;
            buf[offset++] = dtcSnapshot.odometer >> 16;
            buf[offset++] = dtcSnapshot.odometer >> 8;
            buf[offset++] = dtcSnapshot.odometer;
            buf[offset++] = dtcSnapshot.lowPowerMode;
            memcpy(&buf[offset], dtcSnapshot.dateTime, sizeof(dtcSnapshot.dateTime));
            offset += sizeof(dtcSnapshot.dateTime);
            buf[offset++] = dtcSnapshot.vehiclePowerMode;
            buf[offset++] = dtcSnapshot.gearPosition;
        }

        buf[0] = num;
        *len = offset;
    }
    else if(index == 0xFF)
    {
        for (int i = 0; i < DTC_SNAPSHOT_COUNT_MAX; ++i)
        {
            DtcSnapshot dtcSnapshot = dtcSnapshotArray[i];
            if (dtcSnapshot.isNotNull)
            {
                num ++;
                buf[offset++] = dtcSnapshot.ecuBatteryVoltage;
                buf[offset++] = dtcSnapshot.vehicleSpeed >> 8;
                buf[offset++] = dtcSnapshot.vehicleSpeed;
                buf[offset++] = dtcSnapshot.odometer >> 24;
                buf[offset++] = dtcSnapshot.odometer >> 16;
                buf[offset++] = dtcSnapshot.odometer >> 8;
                buf[offset++] = dtcSnapshot.odometer;
                buf[offset++] = dtcSnapshot.lowPowerMode;
                memcpy(&buf[offset],dtcSnapshot.dateTime, sizeof(dtcSnapshot.dateTime));
                offset += sizeof(dtcSnapshot.dateTime);
                buf[offset++] = dtcSnapshot.vehiclePowerMode;
                buf[offset++] = dtcSnapshot.gearPosition;
            }
        }
        buf[0] = num;
        *len = offset;
    }
    else
    {
        return DEM_GET_FFDATABYDTC_WRONG_DTC;
    }

    return DEM_GET_FFDATABYDTC_OK;
}

/*************************************************
函数名称: Dem_GetStatusOfDTC  Dem_GetAllDTC
函数功能: 获取所有故障码状态
输入参数: *dtcInfo ---返回故障码值
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: Young
编写日期 :2018/06/
*************************************************/
int Dem_GetAllDTCStatus(uint8 *buf ,uint16 *len,uint8 dtcMask)
{
    if(NULL == buf || NULL == len || *len < g_dtcCodeSupportedCnt)
    {
        return -1;
    }
    for(int index = 0; index < g_dtcCodeSupportedCnt; index++)
    {
        buf[index]= g_dtcInfo.dtc[index].status & dtcMask;
    }
    *len = g_dtcCodeSupportedCnt;
    return 0;
}

/*************************************************
函数名称: Dem_GetStatusOfDTC  Dem_GetAllDTC
函数功能: 获取所有故障码状态
输入参数: *dtcInfo ---返回故障码值
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: Young
编写日期 :2018/06/
*************************************************/
int Dem_GetAllDTC(uint8 *dtcInfoBuf ,uint16 *dtcByteLen, uint8 dtcMask, uint16 *dtcCnt)
{
    uint16 dtc_num=0;
    uint16 info_index=0;

    if(NULL == dtcInfoBuf || NULL == dtcCnt || NULL == dtcByteLen)
    {
         return -1;
    }

    for(int index = 0; index < g_dtcCodeSupportedCnt; index++)
    {
        if((g_dtcInfo.dtc[index].status & dtcMask) || (dtcMask == DEM_REPORT_DTC_SUPPORTED))
        {
            dtcInfoBuf[info_index++]=(uint8)(g_dtcValueList[index]>>16);
            dtcInfoBuf[info_index++]=(uint8)(g_dtcValueList[index]>>8);
            dtcInfoBuf[info_index++]=(uint8)(g_dtcValueList[index]);
            dtcInfoBuf[info_index++]=(uint8)(g_dtcInfo.dtc[index].status);
            dtc_num++;
        }
    }

    *dtcByteLen = info_index;
    *dtcCnt = dtc_num;

    return 0;
}


/*************************************************
函数名称: Dem_GetFreezeFrameDataByDTC
函数功能: 读取故障码冻结帧数据
输入参数: dtc     --- 匹配故障码
          dtcType --- 故障码类型
          memoryType --- 保存的内存类型
          index      --- 快照索引值
          *buf       --- 保存快照内容
          *len       ----快照内容长度
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/09
修改：benyulong 2018/08/13
*************************************************/
Std_ReturnType Dem_GetFreezeFrameDataByDTC(uint32 dtc, uint8 dtcType,uint8 memoryType, uint8 index, uint8 *buf, uint16 *len)
{

    Std_ReturnType ret = DEM_GET_FFDATABYDTC_OK;

    if((NULL == buf)||(NULL == len))
    {
        ret = DEM_GET_FFDATABYDTC_WRONG_BUFFERSIZE;
        return ret;
    }

    for(int dtcIndex = 0; dtcIndex < g_dtcCodeSupportedCnt; dtcIndex++)
    {
        if(dtc == g_dtcValueList[dtcIndex])
        {
            ret = Dem_GetFreezeFrameData(dtcIndex,index, buf, len);
            break;
        }
    }
    return ret;
}

/*************************************************
函数名称: Dem_WriteFreezeFrameDataByDTC
函数功能: 写入故障码冻结帧数据
输入参数: dtcIndex  --- 故障码索引值
          dtcStatus --- 故障码状态值
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/02/09
修改：benyulong 2018/08/13
*************************************************/
void Dem_WriteFreezeFrameDataByDTC(uint8 dtcIndex,  DtcInfo  *dtcInfo)
{    
    if(NULL == dtcInfo)
    {
        return;
    }

    DynamicDidInfo *dynamicDidInfo =  GetDynamicDid();
    DtcSnapshot *dtcSnapshotArray = dtcInfo->dtc[dtcIndex].dtcSnapshot;
    uint8_t index = dtcInfo->dtc[dtcIndex].index;

    if (index < 2)
    {
        dtcSnapshotArray[index].isNotNull = true;
        dtcSnapshotArray[index].ecuBatteryVoltage = dynamicDidInfo->ecuBatteryVoltage;
        dtcSnapshotArray[index].odometer = dynamicDidInfo->odometer;
        dtcSnapshotArray[index].lowPowerMode = dynamicDidInfo->lowPowerMode;
        memcpy(dtcInfo->dtc[dtcIndex].dtcSnapshot[index].dateTime,dynamicDidInfo->dateTime, sizeof(dynamicDidInfo->dateTime));
        dtcSnapshotArray[index].vehiclePowerMode = dynamicDidInfo->vehiclePowerMode;
        dtcSnapshotArray[index].gearPosition = dynamicDidInfo->gearPosition;

        dtcInfo->dtc[dtcIndex].index ++;
    }
    else if(index >=2 )
    {
        dtcSnapshotArray[index].isNotNull = true;
        dtcSnapshotArray[index].ecuBatteryVoltage = dtcSnapshotArray[1].ecuBatteryVoltage;
        dtcSnapshotArray[index].odometer = dtcSnapshotArray[1].odometer;
        dtcSnapshotArray[index].lowPowerMode = dtcSnapshotArray[1].lowPowerMode;
        memcpy(dtcInfo->dtc[dtcIndex].dtcSnapshot[index].dateTime,dtcSnapshotArray[1].dateTime, sizeof(dynamicDidInfo->dateTime));
        dtcSnapshotArray[index].vehiclePowerMode = dtcSnapshotArray[1].vehiclePowerMode;
        dtcSnapshotArray[index].gearPosition = dtcSnapshotArray[1].gearPosition;

        dtcSnapshotArray[1].isNotNull = true;
        dtcSnapshotArray[1].ecuBatteryVoltage = dynamicDidInfo->ecuBatteryVoltage;
        dtcSnapshotArray[1].odometer = dynamicDidInfo->odometer;
        dtcSnapshotArray[1].lowPowerMode = dynamicDidInfo->lowPowerMode;
        memcpy(dtcInfo->dtc[dtcIndex].dtcSnapshot[1].dateTime,dynamicDidInfo->dateTime, sizeof(dynamicDidInfo->dateTime));
        dtcSnapshotArray[1].vehiclePowerMode = dynamicDidInfo->vehiclePowerMode;
        dtcSnapshotArray[1].gearPosition = dynamicDidInfo->gearPosition;

        dtcInfo->dtc[dtcIndex].index ++;
        if (dtcInfo->dtc[dtcIndex].index == DTC_SNAPSHOT_COUNT_MAX)
        {
            dtcInfo->dtc[dtcIndex].index = 2;
        }
    }
}

/* Clear dtc not support other group of dtc according to TBOX_UDSonCANDiagnosticSpecification_V3.2.xlsx */ 
//#define UDS0X14_NOT_SUPPORT_OTHER_GROUP_OF_DTC
/*************************************************
函数名称: Dem_ClearDTC
函数功能: 清除故障码
输入参数: dtc     --- 匹配故障码
          dtcType --- 故障码类型
          memoryType --- 保存的内存类型
输出参数: Std_ReturnType
函数返回类型值：返回当前结果
编写者: liaoyonggang
编写日期 :2017/02/09
*************************************************/
Std_ReturnType Dem_ClearDTC(uint32 groupOfDTC, uint8 dtcType,uint8 memoryType)
{
    if (GetGearPosition() > 0x01)
    {
        return DEM_CLEAR_FAILED;
    }
    Std_ReturnType ret = DEM_CLEAR_OK;

    int dtcIndex = 0;
    for(dtcIndex = 0; dtcIndex < g_dtcCodeSupportedCnt; dtcIndex++)
    {
        if(groupOfDTC == g_dtcValueList[dtcIndex] || (groupOfDTC & DEM_DTC_GROUP_ALL_DTCS) == DEM_DTC_GROUP_ALL_DTCS)
        {
            break;
        }
    }

    if (dtcIndex == g_dtcCodeSupportedCnt)
    {
        ret = DEM_CLEAR_WRONG_DTC;
    }
    if (ret == DEM_CLEAR_OK)
    {
        Msg msg;
        uint8_t buf[10] = {0x00};
        buf[0] = DTC_SYSTEM_REQ;
        buf[1] = DTC_REQ_CLEAR;
        buf[2] = DTC_REQ_CLEAR;
        buf[3] = groupOfDTC >> 24;
        buf[4] = groupOfDTC >> 16;
        buf[5] = groupOfDTC >> 8;
        buf[6] = groupOfDTC;
        buf[7] = dtcType;
        buf[8] = memoryType;
        msg.event = EVENT_ID_DIAG_DTC_EVENT;
        msg.len = 9;
        msg.lparam = (uint32_t)&buf;
        SystemSendMessage(TASK_ID_DIAG, msg);
    }

    return ret;
}



/*************************************************
函数名称: Dem_WriteDTC
函数功能: 写入故障码
输入参数: dtcIndex  --- 故障码索引值
          dtcStatus --- 故障码状态值
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/02/09
*************************************************/
void Dem_WriteDTC(uint8 dtcIndex, uint8 dtcStatus)
{
    // 检查DTC记录是否启用
    if (0x00 == g_dtcRecordEnable)
    {
        return;
    }

    NvErrorCode errorCode = NV_NO_ERROR;
    DtcInfo *dtcInfo = GetDtcInfo();

    // 检查当前状态是否需要更新
    uint8 currentStatus = dtcInfo->dtc[dtcIndex].status & 0x01;
    if (dtcStatus == currentStatus)
    {
        return; // 无状态变化，无需处理
    }

    // 更新DTC状态
    if ((dtcStatus & 0x01) == 0x00)
    {
        // 清除故障
        dtcInfo->dtc[dtcIndex].status &= ~(1 << 0);
        dtcInfo->dtc[dtcIndex].accCount = 0;
    }
    else
    {
        // 记录故障并写入冻结帧数据
        dtcInfo->dtc[dtcIndex].status = 0x09;
        dtcInfo->dtc[dtcIndex].accCount = 0;
        Dem_WriteFreezeFrameDataByDTC(dtcIndex, dtcInfo);
    }

    // 写入NV存储
    errorCode = NvApiWriteData(NV_ID_DTC_INFO + dtcIndex, (uint8 *)&dtcInfo->dtc[dtcIndex], sizeof(DtcType));
    if (errorCode != NV_NO_ERROR)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "NV write failed! DTC index: %u, Error: %d\r\n", dtcIndex, errorCode);
    }
    SystemApiLogPrintf(LOG_INFO_OUTPUT,
                       "DTC code: 0x%x, status: 0x%02x, NV update status: %d\r\n",
                       g_dtcValueList[dtcIndex],
                       dtcInfo->dtc[dtcIndex].status,
                       errorCode);

}

/*************************************************
函数名称: Dem_FreshHistoryDTC
函数功能: 刷新历史故障码
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/04/26
*************************************************/
void Dem_FreshHistoryDTC(void)
{
    uint8 index = 0;
    DtcInfo  dtcInfo;
    static WorkStatus accStatus = WORK_STATUS_INACTIVE;
    NvErrorCode errorCode = NV_NO_ERROR;

    if(WORK_STATUS_ACTIVE == g_commonInfo.accStatus)
    {
        if(WORK_STATUS_ACTIVE == accStatus)
        {
            return;
        }
        else
        {
            accStatus = WORK_STATUS_ACTIVE;
        }
    }
    else
    {
        accStatus = WORK_STATUS_INACTIVE;
        return;
    }

    memcpy(&dtcInfo, &g_dtcInfo, sizeof(DtcInfo));
    for(index = 0; index < g_dtcCodeSupportedCnt; index++)
    {
        if(0x08 == (dtcInfo.dtc[index].status&0x08))
        {
            dtcInfo.dtc[index].status &= ~(1 << 0);
        }
    }
    for(UINT8 index = DTC_CAN_BUS_OFF_INDEX; index < DTC_MAX_INDEX; index++)
    {
        errorCode = NvApiWriteData(NV_ID_DTC_INFO+index, (uint8 *)&dtcInfo.dtc[index], sizeof(DtcType));
        if(NV_NO_ERROR != errorCode)
        {
           SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Fresh History DTC index:%d into NV failed:%d\r\n", index, errorCode);
        }
    }
}

/*************************************************
函数名称: Dem_GetDtcRecordEnableState
函数功能: 获取DTC检查开关
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: 郑浩
编写日期 :2022/02/14
*************************************************/
uint8 Dem_GetDtcRecordEnableState(void)
{
    return g_dtcRecordEnable;
}


