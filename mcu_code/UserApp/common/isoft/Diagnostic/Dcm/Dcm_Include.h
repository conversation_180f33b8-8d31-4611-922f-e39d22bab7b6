/*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file        <Dcm_Include.h>
 *  @brief       <>
 *
 *  <Compiler: CodeWarrior    MCU:XXX>
 *
 *  <AUTHOR> maosen>
 *  @date       <2013-03-20>
 */
/*============================================================================*/

#ifndef DCM_INCLUDE_H
#define DCM_INCLUDE_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>                         */
/*  V1.0.0       2013-3-20  chenms     Initial version                        */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define DCM_INCLUDE_H_VENDOR_ID  0
#define DCM_INCLUDE_H_MODULE_ID  0
#define DCM_INCLUDE_H_AR_MAJOR_VERSION  3
#define DCM_INCLUDE_H_AR_MINOR_VERSION  3
#define DCM_INCLUDE_H_AR_PATCH_VERSION  0
#define DCM_INCLUDE_H_SW_MAJOR_VERSION  1
#define DCM_INCLUDE_H_SW_MINOR_VERSION  0
#define DCM_INCLUDE_H_SW_PATCH_VERSION  0

/****************************** references *********************************/
#include "Dcm.h"
#include "Dcm_Cbk.h"
#include "Dcm_CfgType.h"
#include "Dcm_Internal.h"
#include "Dcm_Dependence.h"
#include "DcmDsl.h"
#include "DcmDsd.h"
#include "DcmDsp.h"
#include "FreeRTimer.h"

/****************************** declarations *********************************/
FUNC(void, DCM_CODE)Dcm_ComM_NoComModeEntered(void);
FUNC(void, DCM_CODE)Dcm_ComM_SilentComModeEntered(void);
FUNC(void, DCM_CODE)Dcm_ComM_FullComModeEntered(void);
/****************************** definitions *********************************/

#endif /* DCM_INCLUDE_H_ */
