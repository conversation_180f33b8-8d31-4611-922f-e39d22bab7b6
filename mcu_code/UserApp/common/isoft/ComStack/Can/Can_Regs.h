/*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <Can.h>
 *  @brief      <Can Registers define>
 *  
 *  <Compiler: CodeWarrior V2.7    MCU:MPC55XX>
 *  
 *  <AUTHOR>
 *  @date       <15-07-2013>
 */
/*============================================================================*/
#ifndef  CAN_REGS_H
#define  CAN_REGS_H
/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0      20130715    bo.zeng    Initial version
 *  V1.0.1       20131101  jianan.liu  1. Clear buffer when controller of CAN goes
 *                                        to STOP;
 *                                     2. Make the code more united in the same
 *                                        series of chips
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define CAN_REG_H_AR_MAJOR_VERSION  2
#define CAN_REG_H_AR_MINOR_VERSION  4
#define CAN_REG_H_AR_PATCH_VERSION  0
#define CAN_REG_H_SW_MAJOR_VERSION  1
#define CAN_REG_H_SW_MINOR_VERSION  0
#define CAN_REG_H_SW_PATCH_VERSION  1

/*=======[I N C L U D E S]====================================================*/
#include "ComStack_Types.h"

/*=======[M A C R O S]========================================================*/
#define CAN_DATA_LENGTH     8

#define CAN_MBID_ID_EXTENDED    0x1FFFFFFFU      /* extended (bits 0-28) ID */
#define CAN_MBID_ID_STANDARD  0x1FFC0000U        /* standard (bits 18-28) ID */

/* software interrupt flag */
#define SWF_WAK_MASK          0x01U
#define SWF_BOFF_MASK         0x02U
#define SWF_ERR_MASK          0x04U
#define SWF_TWRN_MASK         0x08U
#define SWF_RWRN_MASK         0x10U
#define SWF_RX_MASK           0x20U
#define SWF_TX_MASK           0x40U

#define CAN_LOW_MB_NUMBER                 32U

#define CAN_CHANNEL_NUM                    6
#define CAN_RX_BUFFER_NEW_DATA_STATUS      3
#define CAN_RX_BUFFER_NUMBER               96
#define CAN_RX_FIFO_NUMBER                 8
#define CAN_TX_RX_FIFO_NUMBER              18
#define CAN_TX_BUFFER_NUMBER               96
#define CAN_TX_BUFFER_NEW_DATA_STATUS      3
#define CAN_RULE_PAGE_NUMBER               16
#define CAN_TEST_PAGE_NUMBER               64

#define CAN_RX_IDE_MASK                    0x80000000       /* IDE bit, 0=standard ID, 1=extended ID */
#define CAN_RX_RTR_MASK                    0x40000000       
#define CAN_RX_ID_EXTENDED                 0x1FFFFFFFU      /* extended (bits 0-28) ID */
#define CAN_RX_ID_STANDARD                 0x000007FFU      /* standard (bits 18-28) ID */
#define CAN_RX_DLC_MASK                    0xF0000000U      
#define CAN_RX_BUFFER_MASK                 0x00008000U      
#define CAN_TX_TMTAR_MASK                  0x02

typedef struct 
{
    uint32  cs;                     /* control and status */
    uint32  id;                     /* identifier */
    uint8   data[CAN_DATA_LENGTH];  /* data */
} Can_MBRegType;

typedef struct
{
    uint32 GAFLID;                  /* Receive Rule ID Register */           
    uint32 GAFLM;                   /* Receive Rule Mask Register */ 
    uint32 GAFLP0;                  /* Receive Rule Pointer 0 Register */
    uint32 GAFLP1;                  /* Receive Rule Pointer 1 Register */
} Can_RuleRegType;

typedef struct
{
    uint32 RMID;                    /* Receive Buffer ID Register */           
    uint32 RMPTR;                   /* Receive Buffer Pointer Register */ 
    uint32 RMDF0;                   /* Receive Buffer Data Field 0 Register */
    uint32 RMDF1;                   /* Receive Buffer Data Field 1 Register */ 
}Can_RxMsgReg;

typedef struct
{
    uint32 RFID;                    /* Receive FIFO Buffer Access ID Register */           
    uint32 RFPTR;                   /* Receive FIFO Buffer Access Pointer Register */ 
    uint32 RFDF0;                   /* Receive FIFO Buffer Access Data Field 0 Register */
    uint32 RFDF1;                   /* Receive FIFO Buffer Access Data Field 1 Register */ 
}Can_RxFifoReg;

typedef struct
{
    uint32 CFID;                    /* Transmit/receive FIFO Buffer Access ID Register */           
    uint32 CFPTR;                   /* Transmit/receive FIFO Buffer Access Pointer Register */ 
    uint32 CFDF0;                   /* Transmit/receive FIFO Buffer Access Data Field 0 Register */
    uint32 CFDF1;                   /* Transmit/receive FIFO Buffer Access Data Field 1 Register */ 
}Can_TxRxFifoReg;

typedef struct
{
    uint32 TMID;                    /* Transmit Buffer ID Register */           
    uint32 TMPTR;                   /* Transmit Buffer Pointer Register */ 
    uint32 TMDF0;                   /* Transmit Buffer Data Field 0 Register */
    uint32 TMDF1;                   /* Transmit Buffer Data Field 1 Register */ 
}Can_TxMsgReg;


typedef struct channelInfo
{
    uint32 CFG;                     /* Channel Configuration Register */          
    uint32 CTR;                     /* Channel Control Register */
    uint32 STS;                     /* Channel Status Register */
    uint32 ERFL;                    /* Channel Error Flag Register */
}ChannelInfo;


typedef struct
{
    ChannelInfo channelInfo[CAN_CHANNEL_NUM];
    uint8       reserved0[36];
    uint32      GCFG;              /* Global Configuration Register */
    uint32      GCTR;              /* Global Control Register */
    uint32      GSTS;              /* Global Status Register */
    uint32      GERFL;             /* Global Error Flag Register */
    uint32      GTSC;              /* Global Timestamp Counter Register */
    uint32      GAFLECTR;          /* Receive Rule Entry Control Register */
    uint32      GAFLCFG0;          /* Receive Rule Configuration Register 0 */
    uint32      GAFLCFG1;          /* Receive Rule Configuration Register 1 */
    uint32      RMNB;              /* Receive Buffer Number Register */
    uint32      RMND[CAN_RX_BUFFER_NEW_DATA_STATUS];    /* Receive Buffer New Data Register */
    uint8       reserved1[4];
    uint32      RFCC[CAN_RX_FIFO_NUMBER];      /* Receive FIFO Buffer Configuration and Control Register */
    uint32      RFSTS[CAN_RX_FIFO_NUMBER];     /* Receive FIFO Buffer Status Register */
    uint32      RFPCTR[CAN_RX_FIFO_NUMBER];    /* Receive FIFO Buffer Pointer Control Register */
    uint32      CFCC[CAN_TX_RX_FIFO_NUMBER];   /* Transmit/receive FIFO Buffer Configuration and Control Register */
    uint8       reserved2[24];
    uint32      CFSTS[CAN_TX_RX_FIFO_NUMBER];   /* Transmit/receive FIFO Buffer Status Register */
    uint8       reserved3[24];
    uint32      CFPCTR[CAN_TX_RX_FIFO_NUMBER];   /* Transmit/receive FIFO Buffer Pointer Control Register */
    uint8       reserved4[24];
    uint32      FESTS;              /* FIFO Empty Status Register */
    uint32      FFSTS;              /* FIFO Full Status Register */
    uint32      FMSTS;              /* FIFO Message Lost Status Register */
    uint32      RFISTS;             /* Receive FIFO Buffer Interrupt Flag Status Register */
    uint32      CFRISTS;            /* FIFO Message Lost Status Register */
    uint32      CFTISTS;            /* Transmit/receive FIFO Buffer Transmit Interrupt Flag Status Register */
    uint8       TMC[CAN_TX_BUFFER_NUMBER];            /* Transmit Buffer Control Register*/
    uint8       reserved5[32];   
    uint8       TMSTS[CAN_TX_BUFFER_NUMBER];          /* Transmit Buffer Status Register*/
    uint8       reserved6[32];
    uint32      TMTRSTS[CAN_TX_BUFFER_NEW_DATA_STATUS];   /* Transmit Buffer Transmit Request Status Register*/
    uint8       reserved7[4];
    uint32      TMTARSTS[CAN_TX_BUFFER_NEW_DATA_STATUS];   /* Transmit Buffer Transmit Abort Request Status Register*/
    uint8       reserved8[4];
    uint32      TMTCSTS[CAN_TX_BUFFER_NEW_DATA_STATUS];   /* Transmit Buffer Transmit Complete Status Register*/
    uint8       reserved9[4];
    uint32      TMTASTS[CAN_TX_BUFFER_NEW_DATA_STATUS];   /* Transmit Buffer Transmit Abort Status Register*/
    uint8       reserved10[4];
    uint32      TMIEC[CAN_TX_BUFFER_NEW_DATA_STATUS];   /* Transmit Buffer Interrupt Enable Configuration Register*/
    uint8       reserved11[4];
    uint32      TXQCC[CAN_CHANNEL_NUM];   /* Transmit Queue Configuration and Control Register*/
    uint8       reserved12[8];
    uint32      TXQSTS[CAN_CHANNEL_NUM];  /* Transmit Queue Status Register*/
    uint8       reserved13[8];
    uint32      TXQPCTR[CAN_CHANNEL_NUM]; /* Transmit Queue Pointer Control Register*/
    uint8       reserved14[8];
    uint32      THLCC[CAN_CHANNEL_NUM]; /* Transmit History Configuration and Control Register*/
    uint8       reserved15[8];
    uint32      THLSTS[CAN_CHANNEL_NUM]; /* Transmit History Status Register*/
    uint8       reserved16[8];
    uint32      THLPCTR[CAN_CHANNEL_NUM]; /* Transmit History Pointer Control Register*/
    uint8       reserved17[8];
    uint32      GTINTSTS0;             /* Global TX Interrupt Status Register 0 */
    uint32      GTINTSTS1;             /* Global TX Interrupt Status Register 1 */
    uint32      GTSTCFG;               /* Global Test Configuration Register */
    uint32      GTSTCTR;               /* Global Test Control Register */
    uint8       reserved18[12];
    uint32      GLOCKK;               /* Global Lock Key Register */   
    uint8       reserved19[128];
    Can_RuleRegType   RxRule[CAN_RULE_PAGE_NUMBER];     /* RX RULE REG */
    Can_RxMsgReg      RxMsg[CAN_RX_BUFFER_NUMBER];      /* RX MSG REG */
    uint8       reserved20[512];
    Can_RxFifoReg     RxFifo[CAN_RX_FIFO_NUMBER];       /* RX FIFO REG */
    Can_TxRxFifoReg   TxRxFifo[CAN_TX_RX_FIFO_NUMBER];  /* TX RX FIFO REG */
    uint8       reserved21[96];
    Can_TxMsgReg      TxMsg[CAN_TX_BUFFER_NUMBER];      /* TX MSG REG */
    uint8       reserved22[512];
    uint32      THLACC[CAN_CHANNEL_NUM];                /* Transmit History Access Register */
    uint8       reserved23[232];
    uint32      RPGACC[CAN_TEST_PAGE_NUMBER];           /* RAM Test Page Access Register */
}Can_RegType;    //liaoyonggang

#endif  /* #ifndef  CAN_REGS_H */

/*=======[E N D   O F   F I L E]==============================================*/



