/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Nm.c>
 *  @brief      <Nm>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-08-26>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130826   liujn      Initial version
 */
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define NM_C_AR_MAJOR_VERSION   1U
#define NM_C_AR_MINOR_VERSION   1U
#define NM_C_AR_PATCH_VERSION   0U
#define NM_C_SW_MAJOR_VERSION   1U
#define NM_C_SW_MINOR_VERSION   0U
#define NM_C_SW_PATCH_VERSION   1U

/*=======[I N C L U D E S]====================================================*/
#include "FreeRTOS.h"
#include "AppTask.h"
#include "Nm.h"
#if (STD_ON == NM_DEV_ERROR_DETECT)
#include "Det.h"
#endif
#include "Nm_Cbk.h"
#include "CanNm.h"
#include "LogApi.h"             // liaoyonggang xiugai
#include "ComM.h" 
#include "IpcApi.h"

extern CommonInfo g_commonInfo;
extern UINT32     g_AliveOstick;
extern UINT32     g_osCurrentTickTime;


/*=======[V E R S I O N  C H E C K]===========================================*/
#if(NM_C_AR_MAJOR_VERSION != NM_H_AR_MAJOR_VERSION)
    #error "Nm.c:Mismatch in Specification Major Version"
#endif

#if(NM_C_AR_MINOR_VERSION != NM_H_AR_MINOR_VERSION)
    #error "Nm.c:Mismatch in Specification Minor Version"
#endif

#if(NM_C_AR_PATCH_VERSION != NM_H_AR_PATCH_VERSION)
    #error "Nm.c:Mismatch in Specification Patch Version"
#endif

#if(NM_C_SW_MAJOR_VERSION != NM_H_SW_MAJOR_VERSION)
    #error "Nm.c:Mismatch in Specification Major Version"
#endif

#if(NM_C_SW_MINOR_VERSION != NM_H_SW_MINOR_VERSION)
    #error "Nm.c:Mismatch in Specification Minor Version"
#endif

#if (STD_ON == NM_DEV_ERROR_DETECT)
#if( 2u != DET_H_AR_MAJOR_VERSION)
    #error "Nm.c:Mismatch in Specification Major Version"
#endif

#if( 2u != DET_H_AR_MINOR_VERSION)
    #error "Nm.c:Mismatch in Specification Minor Version"
#endif

#endif /* end of STD_ON == NM_DEV_ERROR_DETECT */

#if( 1U != NM_CBK_H_AR_MAJOR_VERSION)
    #error "Nm.c:Mismatch in Specification Major Version"
#endif

#if( 1U != NM_CBK_H_AR_MINOR_VERSION)
    #error "Nm.c:Mismatch in Specification Minor Version"
#endif

/*=======[M A C R O S]========================================================*/
#define NM_FR_GATEWAY_ROUNDS           0x10U
#define NM_REQ_FLAGE                   0x02U
#define NM_SLEEP_CANCEL_FLAG           0x01U
#define NM_PRE_SLEEP_FLAG              0x00U

/*=======[I N T E R N A L   D A T A]==========================================*/
#define  NM_START_SEC_VAR_POWER_ON_INIT_UNSPECIFIED   /*chenms  2015-5-4*/
#include "Nm_MemMap.h"
/* Nm init states */
#if (STD_ON == NM_DEV_ERROR_DETECT )
STATIC VAR(Nm_NmStateType, NM_VAR_POWER_ON_INIT) Nm_State = NM_UNINIT;
#endif

#define NM_STOP_SEC_VAR_POWER_ON_INIT_UNSPECIFIED   /*chenms  2015-5-4*/
#include "Nm_MemMap.h"

#define NM_START_SEC_VAR_UNSPECIFIED
#include "Nm_MemMap.h"
#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
/* The state and timer of Nm channels */
STATIC VAR(Nm_ChannelAttType, NM_VAR) Nm_ChannelAtt[NM_NUMBER_OF_CHANNELS]; 
#endif
#define NM_STOP_SEC_VAR_UNSPECIFIED
#include "Nm_MemMap.h"

/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
/* Check the state of nm channels */
STATIC FUNC(Nm_ReturnType, NM_CODE) Nm_CheckState(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
/* Start the timer of nm channles */
STATIC FUNC(void, NM_CODE) Nm_StartTimer(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
/* Request channels which have been sleeped */
STATIC FUNC(Nm_ReturnType, NM_CODE) Nm_CallRequest(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
/* Sleep nm channels */
STATIC FUNC(void, NM_CODE) Nm_SetTimerAndSleep(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
/* Clear timres */
STATIC FUNC(void, NM_CODE) Nm_ClearTimer(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

#endif

#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
/* Find nm channel Id */
STATIC FUNC(NetworkHandleType, NM_CODE) 
Nm_FindChannelNum
(
    const NetworkHandleType NetworkHandle
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/

/*************************************************************************/
/*
 * Brief               Initializes the NM Interface
 * ServiceId           0x00 
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm030 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_Init(void)
{
    #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
    uint8 index;
    /* Set the state and timer of each channel */
    for(index = 0; index < NM_NUMBER_OF_CHANNELS; index++)
    {
        Nm_ChannelAtt[index].NmChannelState = NM_SLEEP_CANCEL_FLAG;
        Nm_ChannelAtt[index].NmTimer = 0x0000U;
    }
    #endif /* #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED) */
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    Nm_State = NM_INIT;
    #endif
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
/*************************************************************************/
/*
 * Brief               This function calls the <BusNm>_PassiveStartUp function
 * ServiceId           0x01 
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm31 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_PassiveStartUp
( 
    const NetworkHandleType NetworkHandle 
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    /** @req Nm023 */
    #if(STD_ON == NM_DEV_ERROR_DETECT)

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        /** @req Nm025 */
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_PASSIVESTARTUP, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_PASSIVESTARTUP, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if(STD_ON == NM_DEV_ERROR_DETECT) */

    /* Call <BusNm>_PassiveStartUp */
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /** @req Nm006 */
        switch(Nm_Channels[channelNum].NmBusType) 
        {   
            /* Call the function of CanNm */
            #if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
            case NM_BUSNM_CANNM:                                            
              ret = CanNm_PassiveStartUp(channelNum);
              break;
            #endif

            /* Call the function of FrNm */
            #if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
            case NM_BUSNM_FRNM:             
              ret = FrNm_PassiveStartUp(channelNum);                    
              break;
            #endif

            /* Call the function of LinNm */
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            case NM_BUSNM_LINNM:                
              ret = LinNm_PassiveStartUp(channelNum);                   
              break;
            #endif

            /* Call the function of OsekNm */
            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_PassiveStartUp(channelNum);                  
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function calls the <BusNm>_NetworkRequest
 * ServiceId           0x02 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_OFF == NM_PASSIVE_MODE_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm032 */
#if (STD_OFF == NM_PASSIVE_MODE_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_NetworkRequest
( 
    const NetworkHandleType NetworkHandle 
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKREQUEST, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKREQUEST, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* coordinator */
        #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)        
        Nm_ChannelAtt[channelNum].NmChannelState |= (NM_REQ_FLAGE | NM_SLEEP_CANCEL_FLAG);
        #endif

        /* Call <BusNm>_NetworkRequest */
        switch(Nm_Channels[channelNum].NmBusType) 
        {   
            #if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                            
            //   ret = CanNm_NetworkRequest(channelNum);
              break;
            #endif

            #if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_NetworkRequest(channelNum);                
              break;
            #endif

            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_NetworkRequest(channelNum);               
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:
              ret = OsekNm_NetworkRequest(channelNum);
              break;
            #endif
          
            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function calls the <BusNm>_NetworkRelease 
 * ServiceId           0x03 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_OFF == NM_PASSIVE_MODE_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm046 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_NetworkRelease
(
    const NetworkHandleType NetworkHandle 
)
{
    Nm_ReturnType ret = NM_E_OK;
    Nm_ReturnType checkStateRet = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKRELEASE, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKRELEASE, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */ 

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* coordinator */
        #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)        
        /* Clear Sleep request flag */
        Nm_ChannelAtt[channelNum].NmChannelState &= NM_SLEEP_CANCEL_FLAG;

        /* check the state of Nm channles */
        checkStateRet = Nm_CheckState();

        /* star timer */
        if(NM_E_OK == checkStateRet)
        {
            Nm_StartTimer();
        }

        #else
        /* Call <BusNm>_NetworkRelease */
        switch(Nm_Channels[channelNum].NmBusType) 
        {   
            #if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                            
            //   ret = CanNm_NetworkRelease(channelNum);    
              break;
            #endif

            #if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_NetworkRelease(channelNum);                    
              break;
            #endif
          
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_NetworkRelease(channelNum);                   
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_NetworkRelease(channelNum);                  
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
        #endif /* #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED) */
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if (STD_OFF == NM_PASSIVE_MODE_ENABLED) */

/*************************************************************************/
/*
 * Brief               This function disable the NM PDU transmission
 * ServiceId           0x04 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_COM_CONTROL_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm033 */
#if (STD_ON == NM_COM_CONTROL_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_DisableCommunication
( 
    const NetworkHandleType NetworkHandle 
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_DISABLECOMMUNICATION, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_DISABLECOMMUNICATION, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_DisableCommunication */
        switch(Nm_Channels[channelNum].NmBusType) 
        {   
            #if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                            
              ret = CanNm_DisableCommunication(channelNum);
              break;
            #endif

            #if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_DisableCommunication(channelNum);                  
              break;
            #endif
         
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_DisableCommunication(channelNum);                 
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_DisableCommunication(channelNum);                    
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function enable the NM PDU transmission 
 * ServiceId           0x05 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_OFF == NM_PASSIVE_MODE_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm034 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_EnableCommunication
( 
    const NetworkHandleType NetworkHandle 
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_ENABLECOMMUNICATION, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_ENABLECOMMUNICATION, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_EnableCommunication */
        switch(Nm_Channels[channelNum].NmBusType) 
        {   
            #if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                            
              ret = CanNm_EnableCommunication(channelNum);
              break;
            #endif

            #if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_EnableCommunication(channelNum);                   
              break;
            #endif
          
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_EnableCommunication(channelNum);                  
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_EnableCommunication(channelNum);                 
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if (STD_ON == NM_COM_CONTROL_ENABLED ) */

/*************************************************************************/
/*
 * Brief               Set user data for NM messages transmitted next on the bus 
 * ServiceId           0x06 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        (STD_ON == NM_USER_DATA_ENABLED) && (STD_ON == NM_PASSIVE_MODE_ENABLED)
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm035 */
#if ((STD_ON == NM_USER_DATA_ENABLED) && (STD_OFF == NM_PASSIVE_MODE_ENABLED))
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_SetUserData
(
    const NetworkHandleType NetworkHandle,
    CONSTP2CONST(uint8, AUTOMATIC, NM_APPL_DATA) nmUserDataPtr 
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Call <BusNm>_PassiveStartUp */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_SETUSERDATA, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_SETUSERDATA, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    if((nmUserDataPtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_SETUSERDATA, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_SetUserData */
        switch(Nm_Channels[channelNum].NmBusType)
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                        
              ret = CanNm_SetUserData(channelNum, nmUserDataPtr);               
            break;
            #endif
        
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED)
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_SetUserData(channelNum, nmUserDataPtr);                
            break;
            #endif

            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_SetUserData(channelNum, nmUserDataPtr);               
              break;
            #endif
        
            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_SetUserData(channelNum, nmUserDataPtr);              
              break;
            #endif
        
            default :
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    } 
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if ((STD_ON == NM_USER_DATA_ENABLED) && (STD_OFF == NM_PASSIVE_MODE_ENABLED)) */

/*************************************************************************/
/*
 * Brief               Get user data out of the last successfully received NM message 
 * ServiceId           0x07 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_USER_DATA_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm036 */
#if ( STD_ON == NM_USER_DATA_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetUserData
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmUserDataPtr,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmNodeIdPtr
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETUSERDATA, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETUSERDATA, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    /* If nmUserDataPtr is not NULL */
    if((nmUserDataPtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETUSERDATA, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 

    /* If nmNodeIdPtr is not NULL */
    if((nmNodeIdPtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETUSERDATA, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_GetUserData */
        switch(Nm_Channels[channelNum].NmBusType) 
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                        
              ret = CanNm_GetUserData(channelNum, nmUserDataPtr);               
              break;
            #endif
        
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_GetUserData(channelNum, nmUserDataPtr);                
              break;
            #endif
        
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_GetUserData(channelNum, nmUserDataPtr);               
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_GetUserData(channelNum, nmUserDataPtr);              
              break;
            #endif

            default:
              ret = NM_E_NOT_OK;
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if ( STD_ON == NM_USER_DATA_ENABLED ) */

/*************************************************************************/
/*
 * Brief               Get the whole PDU data out of the most recently received NM message 
 * ServiceId           0x08 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmPduData
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        (STD_ON == NM_NODE_ID_ENABLED) || 
 *                     (STD_ON == NM_NODE_DETECTION_ENABLED) || 
 *                     (STD_ON == NM_USER_DATA_ENABLED)
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm037 */
#if (( STD_ON == NM_NODE_ID_ENABLED ) || ( STD_ON == NM_NODE_DETECTION_ENABLED ) || (STD_ON == NM_USER_DATA_ENABLED ))
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetPduData
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmPduData
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETPDUDATA, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETPDUDATA, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    /* If nmPduData is not NULL */
    if((nmPduData == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETPDUDATA, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_GetPduData */
        switch(Nm_Channels[channelNum].NmBusType) 
        {  
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)    
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:   
              ret = CanNm_GetPduData(channelNum, nmPduData);       
              break;
            #endif
            
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM: 
              ret = FrNm_GetPduData(channelNum, nmPduData);    
              break;
            #endif
                            
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_GetPduData(channelNum, nmPduData);    
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_GetPduData(channelNum, nmPduData);    
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if (( STD_ON == NM_NODE_ID_ENABLED ) || ( STD_ON == NM_NODE_DETECTION_ENABLED ) || (STD_ON == NM_USER_DATA_ENABLED )) */

/*************************************************************************/
/*
 * Brief               Set Repeat Message Request Bit for NM messages transmitted next on the bus 
 * ServiceId           0x09 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_NODE_DETECTION_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm038 */
#if ( STD_ON == NM_NODE_DETECTION_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_RepeatMessageRequest
(
    const NetworkHandleType NetworkHandle
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_REPEATMESSAGEREQUEST, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_REPEATMESSAGEREQUEST, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_RepeatMessageRequest*/
        switch(Nm_Channels[channelNum].NmBusType) 
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)  
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                            
              ret = CanNm_RepeatMessageRequest(channelNum);
              break;
            #endif
            
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_RepeatMessageRequest(channelNum);                  
              break;
            #endif
                    
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_RepeatMessageRequest(channelNum);                 
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_RepeatMessageRequest(channelNum);                    
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;                                            
              break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if ( STD_ON == NM_NODE_DETECTION_ENABLED ) */

/*************************************************************************/
/*
 * Brief               Get node identifier out of the last successfully received NM-message 
 * ServiceId           0x0a 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmNodeIdPtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_NODE_ID_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm039 */
#if ( STD_ON == NM_NODE_ID_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetNodeIdentifier
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmNodeIdPtr
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETNODEIDENTIFIER, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETNODEIDENTIFIER, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    /* If nmNodeIdPtr is NULL */
    if((nmNodeIdPtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETNODEIDENTIFIER, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_GetNodeIdentifier */
        switch(Nm_Channels[channelNum].NmBusType) 
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:
              ret = CanNm_GetNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif
        
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_GetNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif
        
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_GetNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_GetNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;
            break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get node identifier configured for the local node 
 * ServiceId           0x0b 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmNodeIdPtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm040 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetLocalNodeIdentifier
( 
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmNodeIdPtr
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETLOCALNODEIDENTIFIER, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETLOCALNODEIDENTIFIER, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    /* If nmNodeIdPtr is NULL */
    if((nmNodeIdPtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETLOCALNODEIDENTIFIER, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_GetLocalNodeIdentifier */
        switch(Nm_Channels[channelNum].NmBusType) 
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:
              ret = CanNm_GetLocalNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif
        
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_GetLocalNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif
        
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_GetLocalNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_GetLocalNodeIdentifier(channelNum, nmNodeIdPtr);
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;
            break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if ( STD_ON == NM_NODE_ID_ENABLED ) */ 

/*************************************************************************/
/*
 * Brief               Check if remote sleep indication takes place or not 
 * ServiceId           0x0d 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmRemoteSleepIndPtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_REMOTE_SLEEP_IND_ENABLE
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm042 */
#if (STD_ON == NM_REMOTE_SLEEP_IND_ENABLE)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_CheckRemoteSleepIndication
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(boolean, AUTOMATIC, NM_APPL_DATA) nmRemoteSleepIndPtr
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_CHECKREMOTESLEEPINDICATION, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_CHECKREMOTESLEEPINDICATION, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    /* If nmRemoteSleepIndPtr is NULL */
    if((nmRemoteSleepIndPtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_CHECKREMOTESLEEPINDICATION, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_CheckRemoteSleepIndication */
        switch(Nm_Channels[channelNum].NmBusType) 
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:
              ret = CanNm_CheckRemoteSleepIndication(channelNum, nmRemoteSleepIndPtr );
              break;
            #endif
        
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:
              ret = FrNm_CheckRemoteSleepIndication(channelNum, nmRemoteSleepIndPtr );
              break;
            #endif
        
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_CheckRemoteSleepIndication(channelNum, nmRemoteSleepIndPtr );
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_CheckRemoteSleepIndication(channelNum, nmRemoteSleepIndPtr );
              break;
            #endif

            default: 
              ret = NM_E_NOT_OK;
            break;
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if (STD_ON == NM_REMOTE_SLEEP_IND_ENABLE) */

/*************************************************************************/
/*
 * Brief               Returns the state of the network management 
 * ServiceId           0x0e 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmStatePtr, nmModePtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
/** @req Nm043 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetState
(
    const NetworkHandleType NetworkHandle, 
    CONSTP2VAR(Nm_StateType, AUTOMATIC, NM_APPL_DATA) nmStatePtr,
    CONSTP2VAR(Nm_ModeType, AUTOMATIC, NM_APPL_DATA) nmModePtr
)
{
    Nm_ReturnType ret = NM_E_OK;
    NetworkHandleType channelNum = 0;

    /* Find nm chanle ID */
    channelNum = Nm_FindChannelNum(NetworkHandle);

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETSTATE, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 
    /* If NetworkHandle is right value */
    if((NM_NUMBER_OF_CHANNELS == channelNum) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETSTATE, NM_E_HANDLE_UNDEF);
        ret = NM_E_NOT_OK;
    } 

    /* If nmStatePtr is NULL */
    if((nmStatePtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETSTATE, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 

    /* If nmModePtr is NULL */
    if((nmModePtr == NULL_PTR) && (NM_E_OK == ret))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETSTATE, NM_E_PARAM_POINTER);
        ret = NM_E_NOT_OK;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */

    #if(STD_ON == NM_DEV_ERROR_DETECT)
    if(NM_E_OK == ret)
    {
    #endif 
        /* Call <BusNm>_GetState */
        switch(Nm_Channels[channelNum].NmBusType) 
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)  
            /* Call the function of CanNm */
            case NM_BUSNM_CANNM:                                        
              ret = CanNm_GetState(channelNum, nmStatePtr, nmModePtr);   
              break;
            #endif
          
            #if (STD_ON == NM_BUSNM_FRNM_ENABLED )
            /* Call the function of FrNm */
            case NM_BUSNM_FRNM:                 
              ret = FrNm_GetState(channelNum, nmStatePtr, nmModePtr);               
              break;
            #endif
           
            #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
            /* Call the function of LinNm */
            case NM_BUSNM_LINNM:                
              ret = LinNm_GetState(channelNum, nmStatePtr, nmModePtr);              
              break;
            #endif
                
            #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
            /* Call the function of OsekNm */
            case NM_BUSNM_OSEKNM:               
              ret = OsekNm_GetState(channelNum, nmStatePtr, nmModePtr);             
              break;
            #endif

            default:
              ret = NM_E_NOT_OK;                                            
            break;   
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that a NM-message has been received in the Bus-Sleep Mode 
 * ServiceId           0x11 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm104 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_NetworkStartIndication
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKSTARTINDICATION, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKSTARTINDICATION, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 

    /* call back Notification function of up layer */
    if(TRUE == flag)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        (void)ComM_Nm_NetworkStartIndication(Nm_Channels[nmNetworkHandle].NmComMChannel); 
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 

    return;
}

#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
/*************************************************************************/
/*
 * Brief               Notification that the network management has entered Network Mode 
 * ServiceId           0x12 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm105 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_NetworkMode
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKMODE, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_NETWORKMODE, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 
    if(TRUE == flag)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        /* call back Notification function of up layer */
        (void)ComM_Nm_NetworkMode(Nm_Channels[nmNetworkHandle].NmComMChannel);
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 

    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that the network management has entered Prepare Bus-Sleep Mode
 * ServiceId           0x13 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm106 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
/** @req NM012.partially.3 */
Nm_PrepareBusSleepMode
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_PREPAREBUSSLEEPMODE, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_PREPAREBUSSLEEPMODE, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 

    if(TRUE == flag)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        /* call back Notification function of up layer */
        (void)ComM_Nm_PrepareBusSleepMode(Nm_Channels[nmNetworkHandle].NmComMChannel); 
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 

    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that the network management has entered Bus-Sleep Mode
 * ServiceId           0x14 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm107 */
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
/** @req NM012.partially.4 */
Nm_BusSleepMode
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_BUSSLEEPMODE, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_BUSSLEEPMODE, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 

    if(TRUE == flag)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        /* call back Notification function of up layer */
        (void)ComM_Nm_BusSleepMode(Nm_Channels[nmNetworkHandle].NmComMChannel);
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 

    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received
 * ServiceId           0x15 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm112 */
#if(NM_PDU_RX_INDICATION_ENABLED == STD_ON)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_PduRxIndication
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_PDURXINDICATION, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_PDURXINDICATION, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if(NM_PDU_RX_INDICATION_ENABLED == STD_ON) */

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received 
 * ServiceId           0x1B 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#if(STD_OFF == NM_PASSIVE_MODE_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_TxTimeoutException
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_TXTIMEOUTEXCEPTION, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_TXTIMEOUTEXCEPTION, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if(STD_OFF == NM_PASSIVE_MODE_ENABLED) */

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received
 * ServiceId           0x17 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           down layer
 */
/*************************************************************************/
#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_RemoteSleepIndication
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_REMOTESLEEPINDICATION, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (TRUE == flag))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_REMOTESLEEPINDICATION, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 
    if(TRUE == flag)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        /* Clear Sleep Cancel flag */
        Nm_ChannelAtt[nmNetworkHandle].NmChannelState &= NM_REQ_FLAGE;

        /* check the state of Nm channles */
        ret = Nm_CheckState();

        /* star timer */
        if(NM_E_OK == ret)
        {
            Nm_StartTimer();
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received
 * ServiceId           0x18 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           down layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_RemoteSleepCancelation
( 
    const NetworkHandleType nmNetworkHandle 
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    boolean flag = TRUE;

    /* If Nm has been init */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_REMOTESLEEPCANCELATION, NM_E_UNINIT);
        flag = FALSE;
    } 
    /* If nmNetworkHandle is right value */
    if((nmNetworkHandle >= NM_NUMBER_OF_CHANNELS) && (flag == TRUE))
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_REMOTESLEEPCANCELATION, NM_E_HANDLE_UNDEF);
        flag = FALSE;
    } 

    if(TRUE == flag)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        /** @req Nm012 */
        /* Set Sleep cancel flag */
        Nm_ChannelAtt[nmNetworkHandle].NmChannelState |= NM_SLEEP_CANCEL_FLAG;
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

#endif /* #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED) */


/*************************************************************************/
/*
 * Brief               Notification that the CAN Generic NM state has changed
 * ServiceId           0x16 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle, nmPreviousState, nmCurrentState
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm114 */
#if (NM_STATE_CHANGE_IND_ENABLED == STD_ON)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
#include "CanApi.h"
FUNC(void, NM_CODE) 
Nm_StateChangeNotification
( 
    const NetworkHandleType nmNetworkHandle,
    const Nm_StateType nmPreviousState, 
    const Nm_StateType nmCurrentState
)
{
    #if (STD_ON == NM_DEV_ERROR_DETECT )
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_STATECHANGENOTIFICATION, NM_E_UNINIT);
    } 
    if(nmNetworkHandle >= NM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_STATECHANGENOTIFICATION, NM_E_HANDLE_UNDEF);
    } 
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "nm pre is %d, cur is %d, %d\r\n", nmPreviousState, nmCurrentState,OsekNm_GetCurrentState());
    g_commonInfo.Nm_State = nmCurrentState;
    #endif
    /*CAN Ë¯Ãß*/
    if((NM_STATE_READY_SLEEP >= nmPreviousState)&&(NM_STATE_REPEAT_MESSAGE == nmCurrentState))
    {
        g_AliveOstick = g_osCurrentTickTime;
        CanNetStatusNotifyFunction(WORK_STATUS_ACTIVE);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "alive Nm tick %d, %d, %d\r\n", g_AliveOstick, nmPreviousState, nmCurrentState);
    }

    if((NM_STATE_PREPARE_BUS_SLEEP == nmPreviousState)&&(NM_STATE_BUS_SLEEP == nmCurrentState))
    {
        CanNetStatusNotifyFunction(WORK_STATUS_INACTIVE);
    }
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if (NM_STATE_CHANGE_IND_ENABLED == STD_ON) */

/*************************************************************************/
/*
 * Brief               This function implements the processes of the NM 
 *                     Interface, which need a fix cyclic scheduling
 * ServiceId           0x10 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           down layer
 */
/*************************************************************************/
/** @req Nm118 @req Nm020 */
#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
FUNC(void, NM_CODE) 
Nm_MainFunction(void)
{
    Nm_ReturnType ret = NM_E_OK;
    uint8 index = 0;
    boolean flag = FALSE;

    #if (STD_ON == NM_DEV_ERROR_DETECT )
    /** @req Nm121 */
    if(NM_UNINIT == Nm_State)
    {
        Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_MAINFUNCTION, NM_E_UNINIT);
        ret = NM_E_NOT_OK;
    } 

    if(NM_E_OK == ret)
    {
    #endif /* #if (STD_ON == NM_DEV_ERROR_DETECT ) */
        /** @req Nm002 @req Nm003 */
        /* If all timers were started? Timers are always started at the same time, so just judge one timer */
        for(index = 0; index < NM_NUMBER_OF_CHANNELS; index++)
        {
            if(0 != Nm_ChannelAtt[index].NmTimer)
            {
                flag = TRUE;
            }
        }
        if(TRUE == flag)
        {
            /** @req Nm004 @req Nm115 */
            /* If all channels are preparing to sleep */
            if(NM_E_OK == Nm_CheckState())
            {
                /* Sleep all channels */
                Nm_SetTimerAndSleep();
            }
            /* Wakeup the channels which were sleeped */
            else
            {
                /** @req Nm116 */
                ret = Nm_CallRequest();
                if(NM_E_OK ==ret)
                {
                    /* Clear timer */
                    Nm_ClearTimer();
                }
            }
        }
    #if(STD_ON == NM_DEV_ERROR_DETECT)
    }
    #endif 
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function check the state of nm channels
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          No-Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           Nm_MainFunction,Nm_RemoteSleepIndication,Nm_NetworkRelease 
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
STATIC FUNC(Nm_ReturnType, NM_CODE) 
Nm_CheckState(void)
{
    Nm_ReturnType ret = NM_E_OK; 
    uint8 index;

    SchM_Enter_NM(EXCLUSIVE_AREA_1);
    for(index = 0; ((index < NM_NUMBER_OF_CHANNELS) && (NM_E_OK == ret)); index++)
    {
        /* If nm dose not prepare to sleep, break for */
        if(NM_PRE_SLEEP_FLAG != Nm_ChannelAtt[index].NmChannelState)
        {
            ret = NM_E_NOT_OK;
        }
    }
    SchM_Exit_NM(EXCLUSIVE_AREA_1);
    return ret;
}

#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
/*************************************************************************/
/*
 * Brief               This function start timers
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          No-Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           Nm_RemoteSleepIndication,Nm_NetworkRelease
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
STATIC FUNC(void, NM_CODE) 
Nm_StartTimer(void)
{
    uint8 index;

    SchM_Enter_NM(EXCLUSIVE_AREA_1);
    for(index = 0; index < NM_NUMBER_OF_CHANNELS; index++)
    {
        /* Start timers */
        switch(Nm_Channels[index].NmBusType)
        {
            #if (STD_ON == NM_BUSNM_CANNM_ENABLED)
            /* Set the channel of CanNm timer */
            case NM_BUSNM_CANNM:
              Nm_ChannelAtt[index].NmTimer = NM_AUTOSAR_GATEWAY_ROUNDS + 1U;
              break;
            #endif

            #if (STD_ON == NM_BUSNM_FRNM_ENABLED)
            /* Set the channel of FrNm timer */
            case NM_BUSNM_FRNM:
              Nm_ChannelAtt[index].NmTimer = NM_FR_GATEWAY_ROUNDS + 1U;
              break;
            #endif

            #if (NM_BUSNM_LINNM_ENABLED == STD_ON)
            /* Set the channel of LinNm timer */
            case NM_BUSNM_LINNM:
              Nm_ChannelAtt[index].NmTimer = 1U;
              break;
            #endif

            #if (NM_BUSNM_OSEKNM_ENABLED == STD_ON)
            /* Set the channel of OsekNm timer */
            case NM_BUSNM_OSEKNM:
              Nm_ChannelAtt[index].NmTimer = NM_OSEK_GATEWAY_ROUNDS + 1U;
              break;
            #endif

            default :
              break;
        }
    }
    SchM_Exit_NM(EXCLUSIVE_AREA_1);
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
/*************************************************************************/
/*
 * Brief               This function wakeup the channels which were sleeped
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          No-Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           Nm_RemoteSleepIndication,Nm_NetworkRelease
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
STATIC FUNC(Nm_ReturnType, NM_CODE) 
Nm_CallRequest(void)
{
    NetworkHandleType networkNum = 0;
    Nm_ReturnType ret = NM_E_NOT_OK;
    boolean flag  = TRUE;

    SchM_Enter_NM(EXCLUSIVE_AREA_1);
    for(networkNum = 0; networkNum < NM_NUMBER_OF_CHANNELS; networkNum++)
    {
        /* Request network which has been sleeped */
        if((NM_PRE_SLEEP_FLAG == Nm_ChannelAtt[networkNum].NmChannelState) && (0 == Nm_ChannelAtt[networkNum].NmTimer))
        {
            switch(Nm_Channels[networkNum].NmBusType) 
            {   
                #if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
                /* Call the function of CanNm */
                case NM_BUSNM_CANNM:                                            
                //   ret = CanNm_NetworkRequest(networkNum);
                  break;
                #endif

                #if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
                /* Call the function of FrNm */
                case NM_BUSNM_FRNM:
                  ret = FrNm_NetworkRequest(networkNum);
                  break;
                #endif
              
                #if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
                /* Call the function of LinNm */
                case NM_BUSNM_LINNM:                
                  ret = LinNm_NetworkRequest(networkNum);
                  break;
                #endif

                #if (NM_BUSNM_OSEKNM_ENABLED  == STD_ON)
                /* Call the function of OsekNm */
                case NM_BUSNM_OSEKNM:               
                  ret = OsekNm_NetworkRequest(networkNum);
                  break;
                #endif

                default: 
                  ret = NM_E_NOT_OK;                                            
                  break;
            }
            if(NM_E_OK == ret)
            {
                /* If request is successfully, set the state of channel to NM_SLEEP_CANCEL_FLAG */
                Nm_ChannelAtt[networkNum].NmChannelState |= NM_SLEEP_CANCEL_FLAG;
            }
            else
            {
                flag = FALSE;
            }
        }
    }
    SchM_Exit_NM(EXCLUSIVE_AREA_1);
    if(TRUE == flag)
    {
        /* Clear timers */
        Nm_ClearTimer();
    }
    return ret;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function sleep nm channels
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          No-Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           Nm_MainFunction
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
STATIC FUNC(void, NM_CODE) 
Nm_SetTimerAndSleep(void)
{
    NetworkHandleType index;
    Nm_ReturnType ret = NM_E_NOT_OK;

    SchM_Enter_NM(EXCLUSIVE_AREA_1);
    for(index = 0; index < NM_NUMBER_OF_CHANNELS; index++)
    {
        /* If Nm timers are started */
        if(0 < Nm_ChannelAtt[index].NmTimer)
        {
            /* NmTimer-- */
            Nm_ChannelAtt[index].NmTimer--;
            
            /* If timer is 0, Call <BusNm>_RequestBusSynchroniztion and <BusNm>_NetworkRelease */
            if(0 == Nm_ChannelAtt[index].NmTimer)
            {
                /** @req Nm001 @req Nm051 */
                switch(Nm_Channels[index].NmBusType) 
                {   
                    #if (NM_BUSNM_CANNM_ENABLED == STD_ON)
                    /* Call the function of CanNm */
                    case NM_BUSNM_CANNM:
                    //   ret = CanNm_RequestBusSynchronization(index);
                      if(NM_E_OK == ret)
                      {
                        //   (void)CanNm_NetworkRelease(index);
                      }
                      break;
                    #endif

                    #if (NM_BUSNM_FRNM_ENABLED == STD_ON)
                    /* Call the function of FrNm */
                    case NM_BUSNM_FRNM:
                      ret = FrNm_RequestBusSynchronization(index);
                      if(NM_E_OK == ret)
                      {
                          (void)FrNm_NetworkRelease(index);
                      }
                      break;
                    #endif

                    #if (NM_BUSNM_LINNM_ENABLED == STD_ON)
                    /* Call the function of LinNm */
                    case NM_BUSNM_LINNM:
                      ret = LinNm_RequestBusSynchronization(index);
                      if(NM_E_OK == ret)
                      {
                          (void)LinNm_NetworkRelease(index);
                      }
                      break;
                    #endif

                    #if (NM_BUSNM_OSEKNM_ENABLED == STD_ON)
                    /* Call the function of OsekNm */
                    case NM_BUSNM_OSEKNM:
                      ret = OsekNm_RequestBusSynchronization(index);
                      if(NM_E_OK == ret)
                      {
                          (void)OsekNm_NetworkRelease(index);
                      }
                      break;
                    #endif
                  
                    default: 
                      break;
                }
            }
        }
    }
    SchM_Exit_NM(EXCLUSIVE_AREA_1);
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function clear timers
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          No-Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           Nm_CallRequest,Nm_MainFunction
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
STATIC FUNC(void, NM_CODE) 
Nm_ClearTimer(void)
{
    uint8 index;

    SchM_Enter_NM(EXCLUSIVE_AREA_1);
    /* Set all timers to 0 */
    for(index = 0; index < NM_NUMBER_OF_CHANNELS; index++)
    {
        Nm_ChannelAtt[index].NmTimer = 0;
    }
    SchM_Exit_NM(EXCLUSIVE_AREA_1);
    return;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif /* #if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED) */

/*************************************************************************/
/*
 * Brief               This function find nm chanle number
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          No-Reentrant
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NetworkHandleType
 * PreCondition        None
 * CallByAPI           many apis in Nm.c
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
STATIC FUNC(NetworkHandleType, NM_CODE) 
Nm_FindChannelNum
( 
    const NetworkHandleType NetworkHandle
)
{
    NetworkHandleType index;
    boolean flag = TRUE;
    NetworkHandleType channelNum = 0;

    /* Find Nm channel Id */
    for(index = 0; ((index < NM_NUMBER_OF_CHANNELS) && (TRUE == flag)); index++)
    {
        if(Nm_Channels[index].NmComMChannel == NetworkHandle)
        {
            /* If find the Id break for */
            flag = FALSE;
            channelNum = index;
        }
    }
    /* Dose not find nm channel Id, return NM_NUMBER_OF_CHANNELS */
    if(TRUE == flag)
    {
        channelNum = NM_NUMBER_OF_CHANNELS;
    }
    
    return channelNum;
}
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
/*=======[E N D   O F   F I L E]==============================================*/
