/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Nm_Cbk.h>
 *  @brief      <Nm>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-08-26>
 */
/*============================================================================*/
#ifndef NM_CBK_H
#define NM_CBK_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130826   liujn      Initial version
 * 
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define NM_CBK_H_AR_MAJOR_VERSION        1U
#define NM_CBK_H_AR_MINOR_VERSION        1U

#include "Nm_Cfg.h"

/*************************************************************************/
/*
 * Brief               Notification that a NM-message has been received in the Bus-Sleep Mode 
 * ServiceId           0x11 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_NetworkStartIndication
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that the network management has entered Network Mode 
 * ServiceId           0x12 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_NetworkMode
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that the network management has entered Prepare Bus-Sleep Mode
 * ServiceId           0x13 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_PrepareBusSleepMode
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that the network management has entered Bus-Sleep Mode
 * ServiceId           0x14 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_BusSleepMode
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received
 * ServiceId           0x15 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#if(NM_PDU_RX_INDICATION_ENABLED == STD_ON)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_PduRxIndication
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received
 * ServiceId           0x18 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           down layer
 */
/*************************************************************************/
#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_RemoteSleepCancelation
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received
 * ServiceId           0x17 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           down layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_RemoteSleepIndication
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Notification that a NM message has been received 
 * ServiceId           0x1B 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#if(STD_OFF == NM_PASSIVE_MODE_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_TxTimeoutException
( 
    const NetworkHandleType nmNetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Notification that the CAN Generic NM state has changed
 * ServiceId           0x16 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle, nmPreviousState, nmCurrentState
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           down layer
 */
/*************************************************************************/
#if (NM_STATE_CHANGE_IND_ENABLED == STD_ON)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_StateChangeNotification
( 
    const NetworkHandleType nmNetworkHandle,
    const Nm_StateType nmPreviousState, 
    const Nm_StateType nmCurrentState
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

#endif /* end of NM_CBK_H */
/*=======[E N D   O F   F I L E]==============================================*/
