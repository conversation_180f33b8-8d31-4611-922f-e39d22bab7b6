/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <NmStack_Types.h>
 *  @brief      <Nm>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-08-26>
 */
/*============================================================================*/
#ifndef NM_STACK_TYPES_H
#define NM_STACK_TYPES_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130826   liujn      Initial version
 */
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define NM_STACK_TYPES_H_AR_MAJOR_VERSION   1U
#define NM_STACK_TYPES_H_AR_MINOR_VERSION   1U
#define NM_STACK_TYPES_H_AR_PATCH_VERSION   0U
#define NM_STACK_TYPES_H_SW_MAJOR_VERSION   1U
#define NM_STACK_TYPES_H_SW_MINOR_VERSION   0U
#define NM_STACK_TYPES_H_SW_PATCH_VERSION   1U

/*=======[T Y P E   D E F I N I T I O N S]====================================*/
/** @req Nm117 */
/* States of the network management state machine */
typedef enum 
{
    NM_STATE_UNINIT = 0,
    NM_STATE_BUS_SLEEP,
    NM_STATE_PREPARE_BUS_SLEEP,
    NM_STATE_READY_SLEEP,
    NM_STATE_NORMAL_OPERATION,
    NM_STATE_REPEAT_MESSAGE,
    NM_STATE_SYNCHRONIZE
}Nm_StateType;

/* Return type for NM functions. Derived from Std_ReturnType. */
typedef enum 
{
    NM_E_OK,
    NM_E_NOT_OK,
    NM_E_NOT_EXECUTED
}Nm_ReturnType;

/* Operational modes of the network management */
typedef enum
{
    NM_MODE_BUS_SLEEP,
    NM_MODE_PREPARE_BUS_SLEEP,
    NM_MODE_SYNCHRONIZE,
    NM_MODE_NETWORK
}Nm_ModeType;
#endif

/*=======[E N D   O F   F I L E]==============================================*/
