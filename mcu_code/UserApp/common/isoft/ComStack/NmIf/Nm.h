/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Nm.h>
 *  @brief      <Nm>
 *  
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *  
 *  <AUTHOR>
 *  @date       <2013-08-26>
 */
/*============================================================================*/
#ifndef NM_H_
#define NM_H_

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20130826   liujn      Initial version
 * 
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define NM_MODULE_ID            29U
#define NM_H_VENDOR_ID          62U
#define NM_H_AR_MAJOR_VERSION   1U
#define NM_H_AR_MINOR_VERSION   1U
#define NM_H_AR_PATCH_VERSION   0U
#define NM_H_SW_MAJOR_VERSION   1U
#define NM_H_SW_MINOR_VERSION   0U
#define NM_H_SW_PATCH_VERSION   1U

/*=======[I N C L U D E S]====================================================*/
#include "Nm_Cfg.h"
#include "ComM_Nm.h"
#include "SchM_Nm.h"
#include "Std_Types.h"
#include "NmStack_Types.h"
#if (STD_ON == NM_DEV_ERROR_DETECT)
#include "Det.h"
#endif
#if (NM_BUSNM_LINNM_ENABLED  == STD_ON)
#include "LinNm.h"
#endif
#if (NM_BUSNM_CANNM_ENABLED  == STD_ON)
#include "CanNm.h"
#endif
#if(STD_ON == NM_BUSNM_OSEKNM_ENABLED)
#include "OsekNm.h"
#endif
#if (NM_BUSNM_FRNM_ENABLED  == STD_ON)
#include "FrNm.h"
#endif

/*=======[M A C R O S]========================================================*/
#define NM_INSTANCE_ID            1U

/* network manager work channel */
#define NM_NETWORK_CHANNEL_ID     0x00

/** @req Nm026 @req Nm025 */
#if(STD_ON == NM_DEV_ERROR_DETECT) 
#define NM_E_UNINIT         0x00U
#define NM_E_HANDLE_UNDEF   0x01U
#define NM_E_PARAM_POINTER  0x02U

/* service ID */
#define NM_SERVICEID_PASSIVESTARTUP                     0x01U
#define NM_SERVICEID_NETWORKREQUEST                     0x02U
#define NM_SERVICEID_NETWORKRELEASE                     0x03U
#define NM_SERVICEID_DISABLECOMMUNICATION               0x04U
#define NM_SERVICEID_ENABLECOMMUNICATION                0x05U
#define NM_SERVICEID_SETUSERDATA                        0x06U
#define NM_SERVICEID_GETUSERDATA                        0x07U
#define NM_SERVICEID_GETPDUDATA                         0x08U
#define NM_SERVICEID_REPEATMESSAGEREQUEST               0x09U
#define NM_SERVICEID_GETNODEIDENTIFIER                  0x0AU
#define NM_SERVICEID_GETLOCALNODEIDENTIFIER             0x0BU
#define NM_SERVICEID_CHECKREMOTESLEEPINDICATION         0x0DU
#define NM_SERVICEID_GETSTATE                           0x0EU
#define NM_SERVICEID_GETVERSIONINFO                     0x0FU
#define NM_SERVICEID_NETWORKSTARTINDICATION             0x11U
#define NM_SERVICEID_NETWORKMODE                        0x12U
#define NM_SERVICEID_PREPAREBUSSLEEPMODE                0x13U
#define NM_SERVICEID_BUSSLEEPMODE                       0x14U
#define NM_SERVICEID_PDURXINDICATION                    0x15U
#define NM_SERVICEID_TXTIMEOUTEXCEPTION                 0x1BU
#define NM_SERVICEID_REMOTESLEEPINDICATION              0x17U
#define NM_SERVICEID_REMOTESLEEPCANCELATION             0x18U
#define NM_SERVICEID_STATECHANGENOTIFICATION            0x16U
#define NM_SERVICEID_MAINFUNCTION                       0x10U
#endif

/*=======[T Y P E   D E F I N I T I O N S]====================================*/
#if (NM_BUSNM_CANNM_ENABLED  == STD_OFF)
/* BusNm Type */
typedef enum
{
    NM_BUSNM_CANNM,
    NM_BUSNM_FRNM,
    NM_BUSNM_LINNM,
    NM_BUSNM_OSEKNM,
    NM_BUSNM_UNDEF
}Nm_BusNmType;

/* Nm state */
typedef enum
{
    NM_UNINIT,
    NM_INIT
}Nm_NmStateType;

/* configuration of the bus channe */
typedef struct
{
    const Nm_BusNmType          NmBusType;
    const NetworkHandleType     NmComMChannel;
} Nm_ChannelType;
#endif

#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
/* channel of Nm attribute */
typedef struct
{
    uint8 NmChannelState;
    uint16 NmTimer;
}Nm_ChannelAttType;
#endif

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
/*************************************************************************/
/*
 * Brief               Initializes the NM Interface
 * ServiceId           0x00 
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_Init(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/** This service returns the version information of this module */
/** @req Nm091 @req Nm044 */
#if(NM_VERSION_INFO_API == STD_ON)
#if(STD_ON == NM_DEV_ERROR_DETECT)
#define Nm_GetVersionInfo(VersionInfo) \
    do{\
        if (NULL_PTR == (VersionInfo))\
        { \
            Det_ReportError(NM_MODULE_ID, NM_INSTANCE_ID, NM_SERVICEID_GETVERSIONINFO, NM_E_PARAM_POINTER);\
        }\
        else\
        {\
            (VersionInfo)->vendorID = NM_H_VENDOR_ID; \
            (VersionInfo)->moduleID = NM_MODULE_ID; \
            (VersionInfo)->instanceID = NM_INSTANCE_ID; \
            (VersionInfo)->sw_major_version = NM_H_SW_MAJOR_VERSION; \
            (VersionInfo)->sw_minor_version = NM_H_SW_MINOR_VERSION; \
            (VersionInfo)->sw_patch_version = NM_H_SW_PATCH_VERSION; \
        }\
    }while(0)
#else
#define Nm_GetVersionInfo(VersionInfo) \
    do{\
            (VersionInfo)->vendorID = NM_H_VENDOR_ID; \
            (VersionInfo)->moduleID = NM_MODULE_ID; \
            (VersionInfo)->instanceID = NM_INSTANCE_ID; \
            (VersionInfo)->sw_major_version = NM_H_SW_MAJOR_VERSION; \
            (VersionInfo)->sw_minor_version = NM_H_SW_MINOR_VERSION; \
            (VersionInfo)->sw_patch_version = NM_H_SW_PATCH_VERSION; \
    }while(0)  
#endif
#else
#define Nm_GetVersionInfo(VersionInfo)
#endif

/*************************************************************************/
/*
 * Brief               This function calls the <BusNm>_PassiveStartUp function
 * ServiceId           0x01 
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_PassiveStartUp
( 
    const NetworkHandleType NetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function calls the <BusNm>_NetworkRequest
 * ServiceId           0x02 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_OFF == NM_PASSIVE_MODE_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if (STD_OFF == NM_PASSIVE_MODE_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_NetworkRequest
( 
    const NetworkHandleType NetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function calls the <BusNm>_NetworkRelease 
 * ServiceId           0x03 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_OFF == NM_PASSIVE_MODE_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_NetworkRelease
(
    const NetworkHandleType NetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               This function disable the NM PDU transmission
 * ServiceId           0x04 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_COM_CONTROL_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if (STD_ON == NM_COM_CONTROL_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_DisableCommunication
( 
    const NetworkHandleType NetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function enable the NM PDU transmission 
 * ServiceId           0x05 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_OFF == NM_PASSIVE_MODE_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_EnableCommunication
( 
    const NetworkHandleType NetworkHandle 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Set user data for NM messages transmitted next on the bus 
 * ServiceId           0x06 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        (STD_ON == NM_USER_DATA_ENABLED) && (STD_ON == NM_PASSIVE_MODE_ENABLED)
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if ((STD_ON == NM_USER_DATA_ENABLED) && (STD_OFF == NM_PASSIVE_MODE_ENABLED))
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_SetUserData
(
    const NetworkHandleType NetworkHandle,
    CONSTP2CONST(uint8, AUTOMATIC, NM_APPL_DATA) nmUserDataPtr 
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Get user data out of the last successfully received NM message 
 * ServiceId           0x07 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_USER_DATA_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if ( STD_ON == NM_USER_DATA_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetUserData
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmUserDataPtr,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmNodeIdPtr
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Get the whole PDU data out of the most recently received NM message 
 * ServiceId           0x08 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmPduData
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        (STD_ON == NM_NODE_ID_ENABLED) || 
 *                     (STD_ON == NM_NODE_DETECTION_ENABLED) || 
 *                     (STD_ON == NM_USER_DATA_ENABLED)
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if (( STD_ON == NM_NODE_ID_ENABLED ) || ( STD_ON == NM_NODE_DETECTION_ENABLED ) || (STD_ON == NM_USER_DATA_ENABLED ))
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetPduData
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmPduData
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Set Repeat Message Request Bit for NM messages transmitted next on the bus 
 * ServiceId           0x09 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_NODE_DETECTION_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if ( STD_ON == NM_NODE_DETECTION_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_RepeatMessageRequest
(
    const NetworkHandleType NetworkHandle
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Get node identifier out of the last successfully received NM-message 
 * ServiceId           0x0a 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmNodeIdPtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_NODE_ID_ENABLED
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if ( STD_ON == NM_NODE_ID_ENABLED )
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetNodeIdentifier
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmNodeIdPtr
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get node identifier configured for the local node 
 * ServiceId           0x0b 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmNodeIdPtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetLocalNodeIdentifier
( 
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(uint8, AUTOMATIC, NM_APPL_DATA) nmNodeIdPtr
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Check if remote sleep indication takes place or not 
 * ServiceId           0x0d 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmRemoteSleepIndPtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        STD_ON == NM_REMOTE_SLEEP_IND_ENABLE
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if (STD_ON == NM_REMOTE_SLEEP_IND_ENABLE)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_CheckRemoteSleepIndication
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(boolean, AUTOMATIC, NM_APPL_DATA) nmRemoteSleepIndPtr
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Returns the state of the network management 
 * ServiceId           0x0e 
 * Sync/Async          Synchronous
 * Reentrancy          Non-Reentrant for the same NetworkHandle, reentrant otherwise
 * Param-Name[in]      NetworkHandle
 * Param-Name[out]     nmStatePtr, nmModePtr
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(Nm_ReturnType, NM_CODE) 
Nm_GetState
(
    const NetworkHandleType NetworkHandle,
    CONSTP2VAR(Nm_StateType, AUTOMATIC, NM_APPL_DATA) nmStatePtr,
    CONSTP2VAR(Nm_ModeType, AUTOMATIC, NM_APPL_DATA) nmModePtr
);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This function implements the processes of the NM 
 *                     Interface, which need a fix cyclic scheduling
 * ServiceId           0x10 
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmNetworkHandle
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        STD_ON == NM_COORDINATOR_SUPPORT_ENABLED
 * CallByAPI           down layer
 */
/*************************************************************************/
#if(STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
#define NM_START_SEC_CODE
#include "Nm_MemMap.h"
extern FUNC(void, NM_CODE) 
Nm_MainFunction(void);
#define NM_STOP_SEC_CODE
#include "Nm_MemMap.h"
#endif

#define NM_START_SEC_CONST_UNSPECIFIED
#include "Nm_MemMap.h"
extern CONST(Nm_ChannelType, NM_CONST) Nm_Channels[NM_NUMBER_OF_CHANNELS];
#define NM_STOP_SEC_CONST_UNSPECIFIED
#include "Nm_MemMap.h"

#endif /* end of NM_H_ */
/*=======[E N D   O F   F I L E]==============================================*/
