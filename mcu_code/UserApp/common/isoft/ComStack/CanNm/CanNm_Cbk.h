/*============================================================================*/
/*  Copyright (C) 2015-2020, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <CanNm_Cbk.h>
 *  @brief      <CanNm>
 *
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *
 *  <AUTHOR>
 *  @date       <2016-->
 */
/*============================================================================*/

#ifndef CANNM_CBK_H
#define CANNM_CBK_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       2016      SherryShen  Initial version
 */
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define CANNM_CBK_H_AR_MAJOR_VERSION  3U
#define CANNM_CBK_H_AR_MINOR_VERSION  2U
#define CANNM_CBK_H_AR_PATCH_VERSION  0U
#define CANNM_CBK_H_SW_MAJOR_VERSION  1U
#define CANNM_CBK_H_SW_MINOR_VERSION  0U
#define CANNM_CBK_H_SW_PATCH_VERSION  0U

#include "CanNm.h"

/* @req CANNM034 */
#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
/*************************************************************************/
/*
 * Brief               This service confirms a previous successfully processed
 *                     CAN transmit request.
 * ServiceId           0x0F
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      canNmRxPduId:Identification of the network through PDU-ID;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_TxConfirmation
(
    PduIdType canNmTxPduId
);
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */


/*************************************************************************/
/*
 * Brief               This service  indicates a successful reception of a
 *                     received NM message to the CanNm after passing all
 *                     filters and validation checks.
 * ServiceId           0x10
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      canNmRxPduId:Identification of the network through PDU-ID;
 *                     PduInfoPtr:Contains the length (SduLength) of the received
 *                     I-PDU and a pointer to a buffer (SduDataPtr) containing
 *                     the I-PDU.
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_RxIndication
(
    PduIdType canNmRxPduId,
    P2CONST(PduInfoType, AUTOMATIC, CANNM_APPL_DATA) PduInfoPtr
);

#endif /* CANNM_CBK_H */
