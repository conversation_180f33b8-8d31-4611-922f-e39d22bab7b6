/*============================================================================*/
/*  Copyright (C) 2015-2020, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <CanNm.c>
 *  @brief      <CanNm>
 *
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *
 *  <AUTHOR>
 *  @date       <2016- ->
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       2016     SherryShen      Initial version
 */
/* These MISRA-C Rules not obeyed */

/* MISRA-C:2004 Rule 17.4,
 * The integer value 1 is being added or subtracted from a pointer.
 */
/* MISRA-C:2004 Rule 19.1,
 * #include statements in a file should only be preceded by other preprocessor directives or comments.
 */

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define CANNM_C_AR_MAJOR_VERSION   3U
#define CANNM_C_AR_MINOR_VERSION   2U
#define CANNM_C_AR_PATCH_VERSION   0U
#define CANNM_C_SW_MAJOR_VERSION   1U
#define CANNM_C_SW_MINOR_VERSION   0U
#define CANNM_C_SW_PATCH_VERSION   0U

/*===========================[I N C L U D E S]=================================*/
#include <string.h>
#include "CanNm.h"
#include "CanNm_Cbk.h"
#include "Nm_Cbk.h"
#include "ComM_Nm.h"
#include "LogApi.h"
#include "PmApi.h"
#include "HrApi.h"
#include "NvApi.h"

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
#include "CanIf_Cfg.h"
#include "CanIf.h"
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

#if ((CANNM_VARIANT_CFG == CANNM_VARIANT_PB) \
     || (CANNM_VARIANT_CFG == CANNM_VARIANT_LT))
#include "CanNm_LCfg.h"
#endif

#if (CANNM_VARIANT_CFG == CANNM_VARIANT_PB)
#include "CanNm_PBCfg.h"
#endif

extern CommonInfo g_commonInfo;
extern uint8_t g_canNetStatus;


/* @req CANNM200 */
/*=========================[V E R S I O N  C H E C K]==========================*/

/*=============================[M A C R O S]===================================*/
#define CANNM_DEFAULT_PDU_LEN                        0x08u
#define CANNM_OFFSET_ONE_BYTE                        0x01u
#define CANNM_DEFAULT_USER_DATA                      0xFFu
/* @req CANNM045 */
#define CANNNM_REPEAT_MESSAGE_REQUEST_CBV_MASK       0x01u

/*========================[R U N T I M E  D A T A]==============================*/
typedef struct
{
    boolean started;
    TickType remainNmTimeoutTime;
    TickType startTime;
}CanNm_NmTimerType;

typedef struct
{
    boolean started;
    TickType remainRptMsgTime;
    TickType startTime;
}CanNm_RptMsgTimerType;

typedef struct
{
    boolean started;
    TickType remainWbsTTime;
    TickType startTime;
}CanNm_WBSTimerType;

#if(STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
typedef struct
{
    boolean started;
    TickType remainCycleTime;
    TickType startTime;
}CanNm_CycleTimerType;
#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
typedef struct
{
    boolean started;
    TickType remainMsgTxToutTime;
    TickType startTime;
}CanNm_MsgTimeoutTimerType;
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */


#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
typedef struct
{
    boolean started;
    TickType remainRemoteSleepTime;
    TickType startTime;
}CanNm_RemoteSleepIndTimerType;
#endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

typedef struct
{
    Nm_ModeType  nmMode;
    Nm_StateType nmState;
    boolean      netRequest;
    boolean      txEnable;
    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    boolean      busLoadEn;
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */
    uint8        rxPduData[CANNM_DEFAULT_PDU_LEN];
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    uint8        txPduData[CANNM_DEFAULT_PDU_LEN];
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
    boolean      remoteSleepInd;
    CanNm_RemoteSleepIndTimerType       remoteSleepIndTimer;
    #endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */
    CanNm_NmTimerType       nmTimer;
    CanNm_RptMsgTimerType   rptMsgTimer;
    CanNm_WBSTimerType       wbsTimer;
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    CanNm_CycleTimerType       CycleTimer;
    #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
    CanNm_MsgTimeoutTimerType   msgToutTimer;
    #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    /* immediate */
    uint8 immediateTransmissions;
}CanNm_InnerChannelType;

/* represent CanNm init or not */
STATIC VAR(boolean, CANNM_VAR_POWER_ON_INIT) CanNm_InitFlag = FALSE;

/*Runtime internal structure variant, used to store inner time and state */
VAR(CanNm_InnerChannelType, CANNM_VAR) CanNm_ChannelRuntime[CANNM_NUMBER_OF_CHANNELS];

#if (CANNM_VARIANT_CFG == CANNM_VARIANT_PB)
STATIC P2CONST(CanNm_ConfigType, CANNM_VAR, CANNM_CONST_PBCFG) CanNm_ConfigStd;
#endif /* (CANNM_VARIANT_CFG == CANNM_VARIANT_PB) */

#if (CANNM_VARIANT_CFG == CANNM_VARIANT_PB)
#define CANNM_NETWORK_HANDLE(channelIndex)   CanNm_ConfigStd->canNmNetworkHandle[channelIndex]
#else
#define CANNM_NETWORK_HANDLE(channelIndex)   CanNm_NetworkHandle[channelIndex]
#endif /* (CANNM_VARIANT_CFG == CANNM_VARIANT_PB) */

extern Nm_ChannelType Nm_Channels[];
/****************************************************************************************************/
/*========================= L O C A L  F U N C T I O N S  D E C L A R A T I O N ====================*/
/****************************************************************************************************/
/* Internal function, used to copy data */


/* Internal function, used to find pdu id */
STATIC FUNC(boolean, CANNM_CODE)
CanNm_FindPduId
(
    PduIdType pduId,
    boolean txDirEnabled,
    P2VAR(uint8,AUTOMATIC, CANNM_VAR) channelIndex
);

/* Internal function, used to find channel index by channel handle */
STATIC FUNC(boolean, CANNM_CODE)
CanNm_FindChannelIndex
(
    NetworkHandleType nmChannelHandle,
    P2VAR(uint8, AUTOMATIC, CANNM_VAR) channelIndex
);

/* Internal function, used to start repeat message timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartRepeatMsgTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to stop repeat message timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopRepeatMsgTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to start wait bus sleep timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartWaitBusSleepTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to stop wait bus sleep timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopWaitBusSleepTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to start NM timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartNMTimeoutTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to stop NM timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopNMTimeoutTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

#if (STD_ON == CANNM_USER_DATA_ENABLED)
/* Internal function, used to get user data by position */
STATIC FUNC(uint8, CANNM_CODE)
CanNm_GetUserDataBytePos
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef
);


/* Internal function, used to get user data length */
STATIC FUNC(uint8, CANNM_CODE)
CanNm_GetUserDataLen
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef
);
#endif /* (STD_ON == CANNM_USER_DATA_ENABLED) */

#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
/* Internal function, used to start remote sleep indication timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartRemoteSleepIndTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to stop remote sleep indication timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopRemoteSleepIndTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);
#endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
/* Internal function, used to start message timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartMsgTimeoutTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to stop message timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopMsgTimeoutTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */

/* Internal function, used to start message cycle timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartMsgCycleTimer
(
    uint16 cycleTime,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to stop message cycle timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopMsgCycleTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

/* Internal function, called when normal operation state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_NormalOperationToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when ready sleep state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_ReadySleepToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when bus sleep state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_BusSleepToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when prepare bus sleep mode to bus sleep mode */
STATIC FUNC(void, CANNM_CODE)
CanNm_PrepareBusSleepToBusSleep
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when prepare bus sleep mode to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_PrepareBusSleepToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when ready sleep state to normal operation state */
STATIC FUNC(void, CANNM_CODE)
CanNm_ReadySleepToNormalOperation
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when repeat message state to normal operation state */
STATIC FUNC(void, CANNM_CODE)
CanNm_RepeatMsgToNormalOperation
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when noemal operation state to normal operation state */
STATIC FUNC(void, CANNM_CODE)
CanNm_NormalToNormal
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when repeat message state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_RepeatMsgToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when repeat message state to ready sleep state */
STATIC FUNC(void, CANNM_CODE)
CanNm_RepeatMsgToReadySleep
(
    #if ((STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED))
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    #endif
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when normal operation state to ready sleep state */
STATIC FUNC(void, CANNM_CODE)
CanNm_NormalOperationToReadySleep
(
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    #endif
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, called when ready sleep state to prepare bus sleep state */
STATIC FUNC(void, CANNM_CODE)
CanNm_ReadySleepToPrepareBusSleep
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to check if NM timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckNmTimeoutTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to used to check if repeat message timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckRepeatMsgTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to check if wait bus sleep timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckWaitBusSleepTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/* Internal function, used to transmit CanNm message */
STATIC FUNC(void, CANNM_CODE)
CanNm_Transmit
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_APPL_DATA) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/* Internal function, used to check if message cycle timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckMsgCycleTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
/* Internal function, used to check if remote sleep indication timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckRemoteSleepIndTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);
#endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
/* Internal function, used to check if message Tx timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckMsgTxTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

#if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
/* Internal function, used to clear control bit vector */
STATIC FUNC(void, CANNM_CODE)
CanNm_ClearCbv
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);
#endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */

STATIC FUNC(void, CANNM_CODE)
CanNm_ProcessUserdataByte0
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

STATIC FUNC(void, CANNM_CODE)
CanNm_ProcessUserdataByte1
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

STATIC FUNC(void, CANNM_CODE)
CanNm_ProcessUserdataByte2
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
);

/****************************************************************************************************/
/*========================= L O C A L  F U N C T I O N S  D E F I N I T I O N ======================*/
/****************************************************************************************************/

/* Internal function, used to find pdu id */
STATIC FUNC(boolean, CANNM_CODE)
CanNm_FindPduId
(
    PduIdType pduId,
    boolean txDirEnabled,
    P2VAR(uint8, AUTOMATIC, CANNM_VAR) channelIndex
)
{
    uint8 index;
    boolean findChannel = FALSE;

    for(index = 0u; ((index < CANNM_NUMBER_OF_CHANNELS) && (FALSE == findChannel)); index++)
    {
         const CanNm_ChannelConfigType  *channelConfigRef = &CanNm_ChannelConfigData[index];

         if((TRUE == channelConfigRef->channelActive) && (
           #if(STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
           ((TRUE == txDirEnabled) &&
           (pduId == channelConfigRef->TxPduId)) ||
           #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
           ((FALSE == txDirEnabled) &&
           (pduId == (const PduIdType)CANNM_NETWORK_HANDLE(index)))))
         {
             findChannel = TRUE;
             *channelIndex = index;
         }
    }

    return findChannel;
}

/* Internal function, used to find channel index by channel handle */
STATIC FUNC(boolean, CANNM_CODE)
CanNm_FindChannelIndex
(
    NetworkHandleType nmChannelHandle,
    P2VAR(uint8, AUTOMATIC, CANNM_VAR) channelIndex
)
{
    uint8 index;
    boolean findChannel = FALSE;

    for(index = 0; ((index < CANNM_NUMBER_OF_CHANNELS) && (FALSE == findChannel)); index++)
    {
         const CanNm_ChannelConfigType  *channelConfigRef = &CanNm_ChannelConfigData[index];

         if((TRUE == channelConfigRef->channelActive) && (nmChannelHandle == channelConfigRef->ChannelId))
         {
             findChannel  = TRUE;
             *channelIndex = index;
         }
    }

    return findChannel;
}

/* Internal function, used to start repeat message timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartRepeatMsgTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->rptMsgTimer.started = TRUE;
    (void)GetCounterValue(CANNM_OS_COUNTER_ID, &innerChannelConf->rptMsgTimer.startTime);
    innerChannelConf->rptMsgTimer.remainRptMsgTime = configChannelRef->repeatMsgTime;

    return;
}

/* Internal function, used to stop repeat message timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopRepeatMsgTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->rptMsgTimer.started = FALSE;
    innerChannelConf->rptMsgTimer.remainRptMsgTime = 0u;

    return;
}

/* Internal function, used to start wait bus sleep timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartWaitBusSleepTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->wbsTimer.started = TRUE;
    (void)GetCounterValue(CANNM_OS_COUNTER_ID, &innerChannelConf->wbsTimer.startTime);
    innerChannelConf->wbsTimer.remainWbsTTime = configChannelRef->waitBusSleepTime;

    return;
}

/* Internal function, used to stop wait bus sleep timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopWaitBusSleepTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->wbsTimer.started = FALSE;
    innerChannelConf->wbsTimer.remainWbsTTime = 0u;

    return;
}

/* Internal function, used to start NM timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartNMTimeoutTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    /* @req CANNM206 */
    innerChannelConf->nmTimer.started = TRUE;
    (void)GetCounterValue(CANNM_OS_COUNTER_ID, &innerChannelConf->nmTimer.startTime);
    innerChannelConf->nmTimer.remainNmTimeoutTime = configChannelRef->timeoutTime;

    return;
}

/* Internal function, used to stop NM timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopNMTimeoutTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmTimer.started = FALSE;
    innerChannelConf->nmTimer.remainNmTimeoutTime = 0u;

    return;
}

#if (STD_ON == CANNM_USER_DATA_ENABLED)
/* Internal function, used to get user data by position */
STATIC FUNC(uint8, CANNM_CODE)
CanNm_GetUserDataBytePos
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_VAR) configChannelRef
)
{
    uint8 dataPos = 0x0u;

    #if (STD_ON == CANNM_NODE_ID_ENABLED)
    if (configChannelRef->pduNidPosition == CANNM_PDU_OFF)
    {
    }
    else
    {
        dataPos += CANNM_OFFSET_ONE_BYTE;
    }
    #endif /* (STD_ON == CANNM_NODE_ID_ENABLED) */
    if (configChannelRef->pduCbvPosition == CANNM_PDU_OFF)
    {
    }
    else
    {
        dataPos += CANNM_OFFSET_ONE_BYTE;
    }

    return dataPos;
}


/* Internal function, used to get user data length */
STATIC FUNC(uint8, CANNM_CODE)
CanNm_GetUserDataLen
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef
)
{
    uint8 userDataPos = CanNm_GetUserDataBytePos(configChannelRef);
    uint8 userDataLen = 0x0u;

    if ((configChannelRef->pduLength - userDataPos) >= configChannelRef->userDataLength)
    {
        /*if Pdu Bytes which  can be used as User Data is larger, use actual configured user data byte*/
        userDataLen = configChannelRef->userDataLength;
    }
    else
    {
        /*if Pdu Bytes which  can be used as User Data is smaller, configure error, use counted bytes*/
        userDataLen = configChannelRef->pduLength - userDataPos;
    }

    return userDataLen;
}
#endif /* (STD_ON == CANNM_USER_DATA_ENABLED) */

#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
/* Internal function, used to start remote sleep indication timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartRemoteSleepIndTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->remoteSleepIndTimer.started = TRUE;
    (void)GetCounterValue(CANNM_OS_COUNTER_ID, &innerChannelConf->remoteSleepIndTimer.startTime);
    innerChannelConf->remoteSleepIndTimer.remainRemoteSleepTime = configChannelRef->remoteSleepIndTime;

    return;
}

/* Internal function, used to stop remote sleep indication timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopRemoteSleepIndTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->remoteSleepIndTimer.started = FALSE;
    innerChannelConf->remoteSleepIndTimer.remainRemoteSleepTime = 0u;

    return;
}
#endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
/* Internal function, used to start message timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartMsgTimeoutTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->msgToutTimer.started = TRUE;
    (void)GetCounterValue(CANNM_OS_COUNTER_ID, &innerChannelConf->msgToutTimer.startTime);
    innerChannelConf->msgToutTimer.remainMsgTxToutTime = configChannelRef->msgTimeoutTime;

    return;
}

/* Internal function, used to stop message timeout timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopMsgTimeoutTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->msgToutTimer.started = FALSE;
    innerChannelConf->msgToutTimer.remainMsgTxToutTime = 0u;

    return;
}
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */

/* Internal function, used to start message cycle timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StartMsgCycleTimer
(
    uint16 cycleTime,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->CycleTimer.started = TRUE;
    (void)GetCounterValue(CANNM_OS_COUNTER_ID, &innerChannelConf->CycleTimer.startTime);
    innerChannelConf->CycleTimer.remainCycleTime = cycleTime;

    return;
}
/* Internal function, used to stop message cycle timer */
STATIC FUNC(void, CANNM_CODE)
CanNm_StopMsgCycleTimer
(
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->CycleTimer.started = FALSE;
    innerChannelConf->CycleTimer.remainCycleTime = 0x0u;

    return;
}
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

/* Internal function, called when normal operation state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_NormalOperationToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_REPEAT_MESSAGE;

    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    if (TRUE == configChannelRef->busLoadReductionActive)
    {
        /* @req CANNM156*/
        innerChannelConf->busLoadEn = FALSE;
    }
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleOffset), innerChannelConf);
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    CanNm_StartRepeatMsgTimer(configChannelRef, innerChannelConf);
    /* @req CanNm152 */
    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
    if (TRUE == innerChannelConf->remoteSleepInd)
    {
        innerChannelConf->remoteSleepInd = FALSE;
        Nm_RemoteSleepCancelation(configChannelRef->ChannelId);
    }
    #endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId,NM_STATE_NORMAL_OPERATION,NM_STATE_REPEAT_MESSAGE); 
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */

    return;
}

/* Internal function, called when ready sleep state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_ReadySleepToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_REPEAT_MESSAGE;

    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    if (TRUE == configChannelRef->busLoadReductionActive)
    {
        /* @req CANNM156*/
        innerChannelConf->busLoadEn = FALSE;
    }
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */

    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    /* @req CANNM005 */
    CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleOffset), innerChannelConf);
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    CanNm_StartRepeatMsgTimer(configChannelRef, innerChannelConf);

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_READY_SLEEP, NM_STATE_REPEAT_MESSAGE);  
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */

    return;
}

/* Internal function, called when bus sleep state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_BusSleepToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    /* @req CANNM128, @req CANNM129 */
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_REPEAT_MESSAGE;

    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    if (TRUE == configChannelRef->busLoadReductionActive)
    {
        /* @req CANNM156*/
        innerChannelConf->busLoadEn = FALSE;
    }
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */

    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    /* @req CANNM005 */
    CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleOffset), innerChannelConf);
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    CanNm_StartRepeatMsgTimer(configChannelRef, innerChannelConf);

    /* @req CANNM096 */
    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_BUS_SLEEP, NM_STATE_REPEAT_MESSAGE);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */
    /* @req CANNM093, @req CANNM097 */
    //Nm_NetworkMode(configChannelRef->ChannelId);
    //ComM_Nm_NetworkMode(Nm_Channels[configChannelRef->ChannelId].NmComMChannel);   // liaoyonggang xiugai
    #if (STD_ON == CANNM_USER_CONTROL_ENABLED)
    (void)ConfigCanPinStatus(ALLCAN_PIN_INTERRUPT_DISENABLE);
    #endif /* (STD_ON == CANNM_USER_CONTROL_ENABLED) */
    return;
}

/* Internal function, called when prepare bus sleep mode to bus sleep mode */
STATIC FUNC(void, CANNM_CODE)
CanNm_PrepareBusSleepToBusSleep
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_BUS_SLEEP;
    innerChannelConf->nmState = NM_STATE_BUS_SLEEP;

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_PREPARE_BUS_SLEEP, NM_STATE_BUS_SLEEP);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */
    /* @req CANNM093, @req CANNM097, @req CANNM126 */
    //Nm_BusSleepMode(configChannelRef->ChannelId);
    //ComM_Nm_BusSleepMode(Nm_Channels[configChannelRef->ChannelId].NmComMChannel);      // liaoyonggang xiugai
    #if (STD_ON == CANNM_USER_CONTROL_ENABLED)
    (void)PmCanIntStbFunction();
    #endif /* (STD_ON == CANNM_USER_CONTROL_ENABLED) */
    return;
}

/* Internal function, called when prepare bus sleep mode to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_PrepareBusSleepToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    /* @req CANNM123, @req CANNM124 */
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_REPEAT_MESSAGE;

    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    if (TRUE == configChannelRef->busLoadReductionActive)
    {
        /* @req CANNM156*/
        innerChannelConf->busLoadEn = FALSE;
    }
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */

    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    #if (STD_ON == CANNM_IMMEDIATE_RESTART_ENABLED)
    /* @req CANNM122 */
    CanNm_Transmit(configChannelRef, innerChannelConf);
    #elif (STD_OFF == CANNM_IMMEDIATE_RESTART_ENABLED)
    CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleOffset), innerChannelConf);
    #endif /* (STD_ON == CANNM_IMMEDIATE_RESTART_ENABLED) */
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    CanNm_StartRepeatMsgTimer(configChannelRef, innerChannelConf);

    /* @req CANNM096 */
    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_PREPARE_BUS_SLEEP, NM_STATE_REPEAT_MESSAGE);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */
    /* @req CANNM093, @req CANNM097 */
    //Nm_NetworkMode(configChannelRef->ChannelId);
    //ComM_Nm_NetworkMode(Nm_Channels[configChannelRef->ChannelId].NmComMChannel);   // liaoyonggang xiugai
    #if (STD_ON == CANNM_USER_CONTROL_ENABLED)
    (void)ConfigCanPinStatus(ALLCAN_PIN_INTERRUPT_DISENABLE);
    #endif /* (STD_ON == CANNM_USER_CONTROL_ENABLED) */
    return;
}

/* Internal function, called when ready sleep state to normal operation state */
STATIC FUNC(void, CANNM_CODE)
CanNm_ReadySleepToNormalOperation
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_NORMAL_OPERATION;

    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    if (TRUE == configChannelRef->busLoadReductionActive)
    {
        /* @req CANNM157*/
        innerChannelConf->busLoadEn = TRUE;
    }
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */

    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
    if (TRUE == innerChannelConf->txEnable)
    {
        /* @req CANNM150 */
        CanNm_StartRemoteSleepIndTimer(configChannelRef, innerChannelConf);
    }
    #endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

    /* @req CANNM116 */
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    if (TRUE == innerChannelConf->txEnable)
    {
        CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleOffset), innerChannelConf);
    }
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_READY_SLEEP, NM_STATE_NORMAL_OPERATION);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */

    return;
}

/* Internal function, called when repeat message state to normal operation state */
STATIC FUNC(void, CANNM_CODE)
CanNm_RepeatMsgToNormalOperation
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_NORMAL_OPERATION;

    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    if (TRUE == configChannelRef->busLoadReductionActive)
    {
        /* @req CANNM157*/
        innerChannelConf->busLoadEn = TRUE;
    }
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */

    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
    if (TRUE == innerChannelConf->txEnable)
    {
        /* @req CANNM150 */
        CanNm_StartRemoteSleepIndTimer(configChannelRef, innerChannelConf);
    }
    #endif/* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

    /* @req CANNM107 */
    #if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
    CanNm_ClearCbv(configChannelRef, innerChannelConf);
    #endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */

    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_REPEAT_MESSAGE, NM_STATE_NORMAL_OPERATION);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */

    return;
}

/* Internal function, called when normal operation state to normal operation state */
STATIC FUNC(void, CANNM_CODE)
CanNm_NormalToNormal
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_NORMAL_OPERATION;

    /* @req CANNM117 */
    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);

    /* @req CANNM081 */
    #if (STD_ON== CANNM_DEV_ERROR_DETECT)
    /* @req CANNM019 */
    Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_MAINFUNCTION_SERVICEID, CANNM_E_DEV_NETWORK_TIMEOUT);
    #endif /* (STD_ON== CANNM_DEV_ERROR_DETECT) */
    #if (STD_ON == CANNM_DEM_ERROR_DETECT)
    /* @req CANNM020, @req CANNM194 */
    Dem_ReportErrorStatus(CANNM_E_NETWORK_TIMEOUT, DEM_EVENT_STATUS_FAILED);
    #endif /* (STD_ON == CANNM_DEM_ERROR_DETECT) */

    return;
}

/* Internal function, called when repeat message state to repeat message state */
STATIC FUNC(void, CANNM_CODE)
CanNm_RepeatMsgToRepeatMsg
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    innerChannelConf->nmState = NM_STATE_REPEAT_MESSAGE;

    /* @req CANNM101 */
    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);

    /* @req CANNM081 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    /* @req CANNM019 */
    Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_MAINFUNCTION_SERVICEID, CANNM_E_DEV_NETWORK_TIMEOUT);
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */
    #if (STD_ON == CANNM_DEM_ERROR_DETECT)
    /* @req CANNM020, @req CANNM193 */
    Dem_ReportErrorStatus(CANNM_E_NETWORK_TIMEOUT, DEM_EVENT_STATUS_FAILED);
    #endif /* (STD_ON == CANNM_DEM_ERROR_DETECT) */

    return;
}

/* Internal function, called when repeat message state to ready sleep state */
STATIC FUNC(void, CANNM_CODE)
CanNm_RepeatMsgToReadySleep
(
    #if ((STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED))
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    #endif
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    /* @req CANNM108 */
    innerChannelConf->nmState = NM_STATE_READY_SLEEP;

    /* @req CANNM096 */
    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    /* @req CANNM051 */
    CanNm_StopMsgCycleTimer(innerChannelConf);
    #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
    /* @req CANNM068 */
    CanNm_StopMsgTimeoutTimer(innerChannelConf);
    #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    /* @req CANNM107 */
    #if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
    CanNm_ClearCbv(configChannelRef, innerChannelConf);   
    #endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */
    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_REPEAT_MESSAGE, NM_STATE_READY_SLEEP);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */

    return;
}

/* Internal function, called when normal operation state to ready sleep state */
STATIC FUNC(void, CANNM_CODE)
CanNm_NormalOperationToReadySleep
(
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    #endif
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_NETWORK;
    /* @req CANNM108 */
    innerChannelConf->nmState = NM_STATE_READY_SLEEP;

    /* @req CANNM096 */
    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    /* @req CANNM051 */
    CanNm_StopMsgCycleTimer(innerChannelConf);
    #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
    /* @req CANNM068 */
    CanNm_StopMsgTimeoutTimer(innerChannelConf);
    #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_NORMAL_OPERATION, NM_STATE_READY_SLEEP);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */

    return;
}

/* Internal function, called when ready sleep state to prepare bus sleep state */
STATIC FUNC(void, CANNM_CODE)
CanNm_ReadySleepToPrepareBusSleep
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->nmMode = NM_MODE_PREPARE_BUS_SLEEP;
    innerChannelConf->nmState = NM_STATE_PREPARE_BUS_SLEEP;

    CanNm_StartWaitBusSleepTimer(configChannelRef, innerChannelConf);
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    /* @req CANNM114 */
    //Nm_PrepareBusSleepMode(configChannelRef->ChannelId);
    //ComM_Nm_PrepareBusSleepMode(Nm_Channels[configChannelRef->ChannelId].NmComMChannel);    // liaoyonggang xiugai
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */
    /* @req CANNM166, @req CANNM167 */
    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(configChannelRef->ChannelId, NM_STATE_READY_SLEEP, NM_STATE_PREPARE_BUS_SLEEP);
    #endif /* (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED) */
    return;
}

/* Internal function, used to check if NM timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckNmTimeoutTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    TickType elapsedTick;

    if(TRUE == innerChannelConf->nmTimer.started)
    {
        (void)GetElapsedCounterValue(CANNM_OS_COUNTER_ID,
                                     &innerChannelConf->nmTimer.startTime,
                                     &elapsedTick);
        if (innerChannelConf->nmTimer.remainNmTimeoutTime <= elapsedTick)
        {
            innerChannelConf->nmTimer.remainNmTimeoutTime = 0u;

            switch(innerChannelConf->nmState)
            {
                case NM_STATE_REPEAT_MESSAGE:
                    CanNm_RepeatMsgToRepeatMsg(configChannelRef, innerChannelConf);
                    break;

                case NM_STATE_NORMAL_OPERATION:
                    /* @req CANNM109 */
                    CanNm_NormalToNormal(configChannelRef, innerChannelConf);
                    break;

                case NM_STATE_READY_SLEEP:
                    CanNm_ReadySleepToPrepareBusSleep(configChannelRef, innerChannelConf);
                    break;

                default:
                    break;
            }
        }
        else
        {
            innerChannelConf->nmTimer.remainNmTimeoutTime = innerChannelConf->nmTimer.remainNmTimeoutTime - elapsedTick;
        }
    }

    return;
}

/* Internal function, used to used to check if repeat message timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckRepeatMsgTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    TickType elapsedTick;

    /* @req CANNM102 */
    if (TRUE == innerChannelConf->rptMsgTimer.started)
    {
        (void)GetElapsedCounterValue(CANNM_OS_COUNTER_ID,
                                     &innerChannelConf->rptMsgTimer.startTime,
                                     &elapsedTick);
        if (innerChannelConf->rptMsgTimer.remainRptMsgTime <= elapsedTick)
        {
            innerChannelConf->rptMsgTimer.remainRptMsgTime = 0x0;
            innerChannelConf->rptMsgTimer.started = FALSE;

            if (TRUE == innerChannelConf->netRequest)
            {
                /* @req CANNM103 */
                CanNm_RepeatMsgToNormalOperation(configChannelRef, innerChannelConf);
            }
            else
            {
                /* @req CANNM106 */
                CanNm_RepeatMsgToReadySleep(
                #if ((STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED))
                configChannelRef,
                #endif
                innerChannelConf);
            }
        }
        else
        {
            innerChannelConf->rptMsgTimer.remainRptMsgTime = innerChannelConf->rptMsgTimer.remainRptMsgTime - elapsedTick;
        }
    }

    return;
}

/* Internal function, used to check if wait bus sleep timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckWaitBusSleepTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    TickType elapsedTick;

    if (TRUE == innerChannelConf->wbsTimer.started)
    {
        (void)GetElapsedCounterValue(CANNM_OS_COUNTER_ID,
                                     &innerChannelConf->wbsTimer.startTime,
                                     &elapsedTick);
        if (innerChannelConf->wbsTimer.remainWbsTTime <= elapsedTick)
        {
            innerChannelConf->wbsTimer.remainWbsTTime = 0x0u;
            innerChannelConf->wbsTimer.started = FALSE;

            /* @req CANNM088 */
            CanNm_PrepareBusSleepToBusSleep(configChannelRef, innerChannelConf);
        }
        else
        {
            innerChannelConf->wbsTimer.remainWbsTTime = innerChannelConf->wbsTimer.remainWbsTTime - elapsedTick;
        }
    }

    return;
}

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/* Internal function, used to transmit CanNm message */
STATIC FUNC(void, CANNM_CODE)
CanNm_Transmit
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    int   retVal = E_NOT_OK;
    BackupRamInfo *backupRamInfo = NULL;
    backupRamInfo = (BackupRamInfo *)GetBackupRamInfo();
    PduInfoType pduInfo;


    /* @req CANNM100 */
    if (TRUE == innerChannelConf->txEnable)
    {
        /* @req UserDefine Cbv */
        switch (backupRamInfo->mcuWakeupSource)
        {
        case TBOX_WAKEUP_CAN1:
        case TBOX_WAKEUP_CAN2:
        case TBOX_WAKEUP_CAN3:
        case TBOX_WAKEUP_CAN4:
            if (WORK_STATUS_INACTIVE == g_commonInfo.accStatus && (g_canNetStatus == 0x00))
            {
                innerChannelConf->txPduData[1] &= ~(1 << 4);
            }
            else
            {
                innerChannelConf->txPduData[1] |= (1 << 4);
            }
            break;
        case TBOX_WAKEUP_BPLUS:
        case TBOX_WAKEUP_ACC:
            if (WORK_STATUS_INACTIVE == g_commonInfo.accStatus && (g_canNetStatus == 0x00))
            {
                innerChannelConf->txPduData[1] &= ~(1 << 4);
            }
            else
            {
                innerChannelConf->txPduData[1] |= (1 << 4);
            }
            break;
        default:
            innerChannelConf->txPduData[1] |= (1 << 4);
            break;
        }

        if(NM_STATE_REPEAT_MESSAGE == innerChannelConf->nmState)
        {
            innerChannelConf->txPduData[1] |= (1 << 0);
        }
        else
        {
            innerChannelConf->txPduData[1] &= ~(1 << 0);
        }

        CanNm_ProcessUserdataByte0(configChannelRef, innerChannelConf);
        CanNm_ProcessUserdataByte1(configChannelRef, innerChannelConf);
        CanNm_ProcessUserdataByte2(configChannelRef, innerChannelConf);

        pduInfo.SduDataPtr = innerChannelConf->txPduData;
        pduInfo.SduLength = CANNM_DEFAULT_PDU_LEN;

        if((g_commonInfo.udsCtrlNmPduStatus & 0x01) == 0x00)
        {
            // McuTxNmMessage(activeWakeup,repeatMessageRequest);
            retVal = CanIf_Transmit(configChannelRef->TxPduId,&pduInfo);    // liaoyonggang xiugai
        }

        /* @req CANNM064 */
        #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
        CanNm_StartMsgTimeoutTimer(configChannelRef, innerChannelConf);
        #if (STD_ON == CANNM_DEM_ERROR_DETECT)
        if (E_NOT_OK == retVal)
        {
            /* @req CANNM081, @req CANNM073, @req CANNM020 */
            Dem_ReportErrorStatus(CANNM_E_CANIF_TRANSMIT_ERROR, DEM_EVENT_STATUS_FAILED);
        }
        #endif /* (STD_ON == CANNM_DEM_ERROR_DETECT) */
        #else /* (STD_ON == CANNM_IMMEDIATE_TXCONF_ENABLED) */
        CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelConf);
        #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
    }

    return;
}

/* Internal function, used to check if message cycle timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckMsgCycleTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    TickType elapsedTick;

    if (TRUE == innerChannelConf->CycleTimer.started)
    {
        (void)GetElapsedCounterValue(CANNM_OS_COUNTER_ID,
                                     &innerChannelConf->CycleTimer.startTime,
                                     &elapsedTick);
        if (innerChannelConf->CycleTimer.remainCycleTime <= elapsedTick)
        {
            innerChannelConf->CycleTimer.remainCycleTime = 0x0u;

            /* @req CANNM040 */
            if (1 < innerChannelConf->immediateTransmissions)
            {
                innerChannelConf->immediateTransmissions--;
                CanNm_StartMsgCycleTimer((uint16)(configChannelRef->immediateCycleTime), innerChannelConf);
            }
            else
            {
                CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleTime), innerChannelConf);
            }
            
            if (TRUE == innerChannelConf->txEnable)
            {
                /* @req CANNM032 */
                CanNm_Transmit(configChannelRef, innerChannelConf);
            }
        }
        else
        {
            innerChannelConf->CycleTimer.remainCycleTime = innerChannelConf->CycleTimer.remainCycleTime - elapsedTick;
        }
    }

    return;
}

#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
/* Internal function, used to check if remote sleep indication timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckRemoteSleepIndTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    TickType elapsedTick;

    if (TRUE == innerChannelConf->remoteSleepIndTimer.started)
    {
        (void)GetElapsedCounterValue(CANNM_OS_COUNTER_ID,
                                     &innerChannelConf->remoteSleepIndTimer.startTime,
                                     &elapsedTick);
        if (innerChannelConf->remoteSleepIndTimer.remainRemoteSleepTime <= elapsedTick)
        {
            innerChannelConf->remoteSleepIndTimer.remainRemoteSleepTime = 0x0u;
            innerChannelConf->remoteSleepIndTimer.started = FALSE;

            innerChannelConf->remoteSleepInd = TRUE;
            Nm_RemoteSleepIndication(configChannelRef->ChannelId);
        }
        else
        {
            innerChannelConf->remoteSleepIndTimer.remainRemoteSleepTime = innerChannelConf->remoteSleepIndTimer.remainRemoteSleepTime - elapsedTick;
        }
    }

    return;
}
#endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
/* Internal function, used to check if message Tx timeout timer is timeout */
STATIC FUNC(void, CANNM_CODE)
CanNm_CheckMsgTxTimer
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    TickType elapsedTick;

    if (TRUE == innerChannelConf->msgToutTimer.started)
    {
        (void)GetElapsedCounterValue(CANNM_OS_COUNTER_ID,
                                     &innerChannelConf->msgToutTimer.startTime,
                                     &elapsedTick);
        if (innerChannelConf->msgToutTimer.remainMsgTxToutTime <= elapsedTick)
        {
            innerChannelConf->msgToutTimer.remainMsgTxToutTime = 0x0u;
            innerChannelConf->msgToutTimer.started = FALSE;

            /* @req CANNM066 */
            //Nm_TxTimeoutException(configChannelRef->ChannelId);  // liaoyonggang xiugai
        }
        else
        {
            innerChannelConf->msgToutTimer.remainMsgTxToutTime = innerChannelConf->msgToutTimer.remainMsgTxToutTime - elapsedTick;
        }
    }

    return;
}
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */


#if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
/* Internal function, used to clear control bit vector */
STATIC FUNC(void, CANNM_CODE)
CanNm_ClearCbv
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    if (configChannelRef->pduCbvPosition != CANNM_PDU_OFF)
    {
        innerChannelConf->rxPduData[configChannelRef->pduCbvPosition] = 0x0u;
    }

    return;
}
#endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */


/*************************************************************************/
/*
 * Brief               This service Initialize the complete CanNm module.
 * ServiceId           0x00
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      cannmConfigPtr:Pointer to a selected configuration structure ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_Init
(
    CONSTP2CONST(CanNm_ConfigType, AUTOMATIC, CANNM_CONST_PBCFG) cannmConfigPtr
)
{
    uint8 canNmLoop = 0u;

    #if (CANNM_VARIANT_CFG == CANNM_VARIANT_PB)
    boolean funcFlag = TRUE;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    /* for post build,report error if NULL_PTR */
    if (NULL_PTR == cannmConfigPtr)
    {
        #if (STD_ON == CANNM_DEM_ERROR_DETECT)
        /* @req CANNM018, @req CANNM020 */
        Dem_ReportErrorStatus(CANNM_E_INIT_FAILED, DEM_EVENT_STATUS_FAILED);
        #endif /* (STD_ON == CANNM_DEM_ERROR_DETECT) */
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_INIT_SERVICEID, CANNM_E_NULL_POINTER);
        funcFlag = FALSE;
        return;
    }
    else
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */
    {
        CanNm_ConfigStd = cannmConfigPtr;
    }

    if (TRUE == funcFlag)
    #endif /* (CANNM_VARIANT_CFG == CANNM_VARIANT_PB) */
    {
        if (FALSE == CanNm_InitFlag)
        {
            /* @req CANNM041 */
            for(canNmLoop = 0; canNmLoop < CANNM_NUMBER_OF_CHANNELS; canNmLoop++)
            {
                P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) InnerChannelRef = &CanNm_ChannelRuntime[canNmLoop];
                P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) ConfigChannelRef = &CanNm_ChannelConfigData[canNmLoop];

                /* @req CANNM060 */
                if (TRUE == ConfigChannelRef->channelActive)
                {
                    #if (STD_ON == CANNM_USER_DATA_ENABLED)
                    uint8 userDataOffset =0x0u;
                    uint8 userDataLen = 0x0u;
                    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                    uint8* userDataPtr = NULL_PTR;
                    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                    /* @req CANNM144 */
                    InnerChannelRef->nmMode = NM_MODE_BUS_SLEEP;
                    /* @req CANNM141 */
                    InnerChannelRef->nmState = NM_STATE_BUS_SLEEP;
                    /* @req CANNM143 */
                    InnerChannelRef->netRequest = FALSE;
                    /* @req CANNM169*/
                    InnerChannelRef->txEnable = TRUE;
                    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
                    /* @req CANNM023*/
                    InnerChannelRef->busLoadEn = FALSE;
                    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */

                    /* @req CANNM061, @req CANNM033 */
                    CanNm_StopRepeatMsgTimer(InnerChannelRef);
                    CanNm_StopNMTimeoutTimer(InnerChannelRef);
                    CanNm_StopWaitBusSleepTimer(InnerChannelRef);
                    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
                    InnerChannelRef->remoteSleepInd = FALSE;
                    CanNm_StopRemoteSleepIndTimer(InnerChannelRef);
                    #endif/* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */
                    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                    #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
                    CanNm_StopMsgTimeoutTimer(InnerChannelRef);
                    #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
                    CanNm_StopMsgCycleTimer(InnerChannelRef);
                    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                    /* @req CANNM085 */
                    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                    memset(InnerChannelRef->txPduData, 0x0u, CANNM_DEFAULT_PDU_LEN);
                    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                    memset(InnerChannelRef->rxPduData, 0x0u, CANNM_DEFAULT_PDU_LEN);

                    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                    /* @req CANNM025 */
                    #if (STD_ON == CANNM_USER_DATA_ENABLED)
                    userDataLen = CanNm_GetUserDataLen(ConfigChannelRef);
                    userDataOffset = CanNm_GetUserDataBytePos(ConfigChannelRef);
                    userDataPtr = &(InnerChannelRef->txPduData[userDataOffset]);
                    memset(userDataPtr, CANNM_DEFAULT_USER_DATA, userDataLen);
                    #endif /* (STD_ON == CANNM_USER_DATA_ENABLED) */
                    #if (STD_ON == CANNM_NODE_ID_ENABLED)
                    if (ConfigChannelRef->pduNidPosition != CANNM_PDU_OFF)
                    {
                        InnerChannelRef->txPduData[ConfigChannelRef->pduNidPosition] = ConfigChannelRef->nodeId;
                    }
                    #endif /* (STD_ON == CANNM_NODE_ID_ENABLED) */
                    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                }
                else
                {
                    /* @req CANNM141, @req CANNM204 */
                    InnerChannelRef->nmState = NM_STATE_UNINIT;
                }
            }
            CanNm_InitFlag = TRUE;
        }
    }

    return;
}


/*************************************************************************/
/*
 * Brief               This service Passive startup of the AUTOSAR CAN NM.
 *                     It triggers the transition from Bus-Sleep Mode to
 *                     the Network Mode in Repeat Message State.
 * ServiceId           0x01
 * Sync/Async          Asynchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Passive startup of network management
 *                     has failed
 *                     NM_E_NOT_EXECUTED: Passive startup of network
 *                     management is currently not executed
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_PassiveStartUp
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
           P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
           P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

           if (innerChannelRef->nmMode == NM_MODE_BUS_SLEEP)
           {
               CanNm_BusSleepToRepeatMsg(configChannelRef, innerChannelRef);
               return NM_E_OK;
           }
           else
           {
               /* @req CANNM147 */
               return NM_E_NOT_EXECUTED;
           }
        }
        else
        {
            /* @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            /* @req CANNM019, @req CANNM192 */
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_PASSIVESTARTUP_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM018, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_PASSIVESTARTUP_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

}


#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/*************************************************************************/
/*
 * Brief               This service Request the network, since ECU needs
 *                     to communicate on the bus. Network state shall be
 *                     changed to 'requested'.
 * ServiceId           0x02
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Requesting of network has failed
 * PreCondition        CANNM_PASSIVE_MODE_ENABLED is not defined
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_NetworkRequest
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            /* @req CANNM104 */
            if (innerChannelRef->netRequest == TRUE)
            {
                return NM_E_OK;
            }
            
            innerChannelRef->netRequest = TRUE;
            switch(innerChannelRef->nmMode)
            {
                case NM_MODE_BUS_SLEEP:
                    innerChannelRef->immediateTransmissions = configChannelRef->immediateTransmissions;
                    CanNm_BusSleepToRepeatMsg(configChannelRef, innerChannelRef);
                    break;

                case NM_MODE_PREPARE_BUS_SLEEP:
                    /* @req CANNM095 */
                    innerChannelRef->immediateTransmissions = configChannelRef->immediateTransmissions;
                    CanNm_PrepareBusSleepToRepeatMsg(configChannelRef, innerChannelRef);
                    break;

                case NM_MODE_NETWORK:
                    if(innerChannelRef->nmState == NM_STATE_READY_SLEEP)
                    {
                        /* @req CANNM110 */
                        CanNm_ReadySleepToNormalOperation(configChannelRef, innerChannelRef);
                    }
                    break;

                default:
                    break;
           }
       }
       else
       {
            /* @req CANNM018, @req CANNM019, @req CANNM192 */
            #if(STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_NETWORKREQUEST_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;
       }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if(STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_NETWORKREQUEST_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}


/*************************************************************************/
/*
 * Brief               This service release the network, since ECU doesn't
 *                     have to communicate on the bus. Network state shall
 *                     be changed to 'released'.
 * ServiceId           0x03
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Releasing of network has failed
 * PreCondition        CANNM_PASSIVE_MODE_ENABLED is not defined
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_NetworkRelease
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];
            #endif

           /* @req CANNM105 */
           innerChannelRef->netRequest = FALSE;

           if (innerChannelRef->nmMode == NM_MODE_NETWORK)
           {
                if (NM_STATE_NORMAL_OPERATION == innerChannelRef->nmState)
                {
                    /* @req CANNM118 */
                    CanNm_NormalOperationToReadySleep(
                    #if (STD_ON == CANNM_STATE_CHANGE_IND_ENABLED)
                    configChannelRef, 
                    #endif
                    innerChannelRef);
                }
                else if (NM_STATE_READY_SLEEP == innerChannelRef->nmState)
                {
                    return NM_E_NOT_OK;
                }
                else
                {
                    /* Nothing to do */
                }
           }
           else
           {
               return NM_E_NOT_OK;
           }
        }
        else
        {
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_NETWORKRELEASE_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;
         }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_NETWORKRELEASE_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */


/* @reqCANNM168 */
#if (STD_ON == CANNM_COM_CONTROL_ENABLED)
/*************************************************************************/
/*
 * Brief               This service disable the NM PDU transmission ability.
 * ServiceId           0x0C
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Disabling of NM PDU transmission ability has failed
 *                     NM_E_NOT_EXECUTED: Disabling of NM PDU transmission
 *                     ability is not executed.
 * PreCondition        CANNM_COM_CONTROL_ENABLED is defined
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_DisableCommunication
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
   
            if (innerChannelRef->nmMode == NM_MODE_NETWORK)
            {
                /* @req CANNM170 */
                innerChannelRef->txEnable = FALSE;
                /* @req CANNM174 */
                CanNm_StopNMTimeoutTimer(innerChannelRef);
                #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
                /* @req CANNM175 */
                CanNm_StopRemoteSleepIndTimer(innerChannelRef);
                #endif/* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */
                #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                /* @req CANNM051 */
                CanNm_StopMsgCycleTimer(innerChannelRef);
                #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
                /* @req CANNM068 */
                CanNm_StopMsgTimeoutTimer(innerChannelRef);
                #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
                #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
            }
            else
            {
                /* @req CANNM172 */
                return NM_E_NOT_EXECUTED;
            }
        }
        else
        {
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_DISABLECOMMUNICATION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_DISABLECOMMUNICATION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}


/*************************************************************************/
/*
 * Brief               This service enable the NM PDU transmission ability.
 * ServiceId           0x0D
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Enabling of NM PDU transmission ability has failed;
 *                     NM_E_NOT_EXECUTED: Enabling of NM PDU transmission
 *                     ability is not executed.
 * PreCondition        CANNM_COM_CONTROL_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_EnableCommunication
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            if (TRUE == innerChannelRef->txEnable)
            {
                /* @req CANNM177 */
                return NM_E_NOT_EXECUTED;
            }
            else
            {
                /* @req CANNM176 */
                innerChannelRef->txEnable = TRUE;
                #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                /* @req CANNM178 */
                CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleOffset), innerChannelRef);
                #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                /* @req CANNM179 */
                CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelRef);
                #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
                /* @req CANNM180 */
                CanNm_StartRemoteSleepIndTimer(configChannelRef, innerChannelRef);
                #endif/* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */
            }
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_ENABLECOMMUNICATION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_ENABLECOMMUNICATION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* (STD_ON == CANNM_COM_CONTROL_ENABLED) */


/* @req CANNM159,@req CANNM015,@req CANNM031*/
#if (STD_ON == CANNM_USER_DATA_ENABLED)
#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/*************************************************************************/
/*
 * Brief               This service set user data for NM messages transmitted
 *                     next on the bus.
 * ServiceId           0x04
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 *                     nmUserDataPtr:Pointer where the user data for the next
 *                     transmitted NM message shall be copied from;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Setting of user data has failed;
 * PreCondition        CANNM_USER_DATA_ENABLED is defined;
 *                     CANNM_PASSIVE_MODE_ENABLED is not defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_SetUserData
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2CONST(uint8, AUTOMATIC, CANNM_APPL_DATA) nmUserDataPtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmUserDataPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_SETUSERDATA_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            uint8 userDataOffset = 0x0u;
            uint8 userDataLen = 0x0u;
            uint8 *destDataPtr = NULL_PTR;

            userDataLen = CanNm_GetUserDataLen(configChannelRef);
            userDataOffset = CanNm_GetUserDataBytePos(configChannelRef);
            destDataPtr = &(innerChannelRef->txPduData[userDataOffset]);

            memcpy(destDataPtr, nmUserDataPtr, userDataLen);
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_SETUSERDATA_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_SETUSERDATA_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

/* @req CANNM160 */
/*************************************************************************/
/*
 * Brief               This service get user data out of the most recently
 *                     received NM message.
 * ServiceId           0x05
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of user data has failed;
 * PreCondition        CANNM_USER_DATA_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetUserData
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmUserDataPtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmUserDataPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETUSERDATA_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            uint8 userDataOffset = 0x0u;
            uint8 userDataLen = 0x0u;
            uint8 *sourceDataPtr = NULL_PTR;

            userDataLen = CanNm_GetUserDataLen(configChannelRef);
            userDataOffset = CanNm_GetUserDataBytePos(configChannelRef);
            sourceDataPtr = &(innerChannelRef->rxPduData[userDataOffset]);

            memcpy(nmUserDataPtr, sourceDataPtr, userDataLen);
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETUSERDATA_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETUSERDATA_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* (STD_ON == CANNM_USER_DATA_ENABLED) */

#if(STD_ON == CANNM_NODE_ID_ENABLED)
/*************************************************************************/
/*
 * Brief               This service get node identifier out of the most
 *                     recently received NM PDU.
 * ServiceId           0x06
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier out of the
 *                     most recently received NM PDU shall be copied to.
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of the node identifier out of
 *                     the most recently received NM PDU has failed;
 * PreCondition        CANNM_NODE_ID_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetNodeIdentifier
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmNodeIdPtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmNodeIdPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETNODEIDENTIFIER_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            if(CANNM_PDU_OFF != configChannelRef->pduNidPosition)
            {
                /* @req CANNM132 */
                *nmNodeIdPtr = innerChannelRef->rxPduData[configChannelRef->pduNidPosition];
            }
            else
            {
                return NM_E_NOT_OK;
            }
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETNODEIDENTIFIER_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETNODEIDENTIFIER_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/*************************************************************************/
/*
 * Brief               This service get node identifier configured for the
 *                     local node.
 * ServiceId           0x07
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier of the
 *                     local node shall be copied to.
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of the node identifier of the
 *                     local node has failed;
 * PreCondition        CANNM_NODE_ID_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetLocalNodeIdentifier
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmNodeIdPtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmNodeIdPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETLOCALNODEIDENTIFIER_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            /* @req CANNM133 */
            *nmNodeIdPtr = configChannelRef->nodeId;
        }
        else
        {
            /* @req CANNM019,@req CANNM192,@req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETLOCALNODEIDENTIFIER_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETLOCALNODEIDENTIFIER_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
#endif /* (STD_ON == CANNM_NODE_ID_ENABLED) */

/* @req CANNM136 */
#if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
/*************************************************************************/
/*
 * Brief               This service Set Repeat Message Request Bit for NM
 *                     messages transmitted next on the bus.
 * ServiceId           0x08
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Setting of Repeat Message Request Bit has
 *                     failed;
 *                     NM_E_NOT_EXECUTED: Repeat Message Request is currently
 *                     not executed.
 * PreCondition        CANNM_NODE_DETECTION_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_RepeatMessageRequest
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;
    boolean txCbvPos = CANNM_PDU_OFF;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            if (configChannelRef->pduCbvPosition != CANNM_PDU_OFF)
            {
                if (configChannelRef->pduNidPosition == CANNM_PDU_OFF)
                {
                    txCbvPos = CANNM_PDU_BYTE_0;
                }
                else
                {
                    /* TOOL makes sure that CBV and NId are in different position */
                    txCbvPos = configChannelRef->pduCbvPosition;
                }
            }

            if (innerChannelRef->nmState == NM_STATE_READY_SLEEP)
            {
                /* @req CANNM113, @req CANNM121 */
                innerChannelRef->txPduData[txCbvPos] |= CANNNM_REPEAT_MESSAGE_REQUEST_CBV_MASK;
                /* @req CANNM112 */
                CanNm_ReadySleepToRepeatMsg(configChannelRef, innerChannelRef);
            }
            else if (innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION)
            {
                /* @req CANNM113, @req CANNM121 */
                innerChannelRef->txPduData[txCbvPos] |= CANNNM_REPEAT_MESSAGE_REQUEST_CBV_MASK;
                /* @req CANNM120 */
                CanNm_NormalOperationToRepeatMsg(configChannelRef, innerChannelRef);
            }
            else
            {
                /* @req CANNM137 */
                return NM_E_NOT_EXECUTED;
            }

        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_REPEATMESSAGEREQUEST_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_REPEATMESSAGEREQUEST_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */


/* @req CANNM139 */
#if ((STD_ON == CANNM_NODE_ID_ENABLED) || (STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_USER_DATA_ENABLED))
/*************************************************************************/
/*
 * Brief               Get the whole PDU data out of the most recently received NM message.
 * ServiceId           0x0a
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     nmPduDataPtr:Pointer where NM PDU shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of NM PDU data has failed;
 * PreCondition        CANNM_NODE_ID_ENABLED is defined;
 *                     CANNM_NODE_DETECTION_ENABLED is defined;
 *                     CANNM_USER_DATA_ENABLED is defined.
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetPduData
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmPduDataPtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmPduDataPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETPDUDATA_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            /* @req CANNM138 */
            memcpy(nmPduDataPtr, innerChannelRef->rxPduData, configChannelRef->pduLength);
        }
        else
        {
            /* @req CANNM019, @reqCANNM192, @reqCANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETPDUDATA_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @reqCANNM191, @reqCANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETPDUDATA_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* ((STD_ON == CANNM_NODE_ID_ENABLED) || (STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_USER_DATA_ENABLED)) */


/*************************************************************************/
/*
 * Brief               Returns the state and the mode of the network management.
 * ServiceId           0x0b
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     nmStatePtr:Pointer where state of the network management
 *                     shall be copied to;
 *                     nmModePtr:Pointer where the mode of the network management
 *                     shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of NM state has failed;
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
/* @req CANNM091 */
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetState
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmStatePtr,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmModePtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmStatePtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETSTATE_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    if (NULL_PTR == nmModePtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETSTATE_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];

            *nmStatePtr = (uint8)innerChannelRef->nmState;
            *nmModePtr = (uint8)innerChannelRef->nmMode;
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETSTATE_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETSTATE_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}

/* @req CANNM184,@req CANNM185 */
#if ((STD_ON == CANNM_BUS_SYNCHRONIZATION_ENABLED) && (STD_OFF == CANNM_PASSIVE_MODE_ENABLED))
/*************************************************************************/
/*
 * Brief               Request bus synchronization.
 * ServiceId           0xc0
 * Sync/Async          Asynchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Requesting of bus synchronization has failed;
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_RequestBusSynchronization
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            if ((TRUE == innerChannelRef->txEnable) &&
               (innerChannelRef->nmMode != NM_MODE_BUS_SLEEP) &&
               (innerChannelRef->nmMode != NM_MODE_PREPARE_BUS_SLEEP))
             {
                 /* @req CANNM130, @req CANNM186 */
                 CanNm_Transmit(configChannelRef, innerChannelRef);
             }
             else
             {
                 /* @req CANNM181, @req CANNM187 */
                 return NM_E_NOT_EXECUTED;
             }
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_REQUESTBUSSYNCHRONIZATION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_REQUESTBUSSYNCHRONIZATION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* ((STD_ON == CANNM_BUS_SYNCHRONIZATION_ENABLED) && (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)) */


/* @req CANNM153 */
#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
/*************************************************************************/
/*
 * Brief               Check if remote sleep indication takes place or not.
 * ServiceId           0xd0
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     nmRemoteSleepIndPtr:Pointer where check result of remote
 *                     sleep indication shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Requesting of bus synchronization has failed;
 *                     NM_E_NOT_EXECUTED: Checking of Remote Sleep Indication is
 *                     currently not executed.
 * PreCondition        CANNM_REMOTE_SLEEP_IND_ENABLED is defined.
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_CheckRemoteSleepIndication
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(boolean, AUTOMATIC, CANNM_APPL_DATA) nmRemoteSleepIndPtr
)
{
    uint8 nmIndex = 0;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == nmRemoteSleepIndPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_CHECKREMOTESLEEPINDICATION_SERVICEID, CANNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];

            if ((innerChannelRef->nmState == NM_STATE_READY_SLEEP) || (innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION) )
            {
                *nmRemoteSleepIndPtr = innerChannelRef->remoteSleepInd;
            }
            else
            {
                /* @req CANNM154 */
                return NM_E_NOT_EXECUTED;
            }
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_CHECKREMOTESLEEPINDICATION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            /* @req CANNM189 */
            return NM_E_NOT_OK;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_CHECKREMOTESLEEPINDICATION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        /* @req CANNM189 */
        return NM_E_NOT_OK;
    }

    return NM_E_OK;
}
#endif /* ( STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */


#if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
/*************************************************************************/
/*
 * Brief               This service confirms a previous successfully processed
 *                     CAN transmit request.
 * ServiceId           0x0F
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      canNmRxPduId:Identification of the network through PDU-ID;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        CANNM_IMMEDIATE_TXCONF_ENABLED is not defined.
 * CallByAPI           Lower layer
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_TxConfirmation
(
    PduIdType canNmTxPduId
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindPduId(canNmTxPduId, TRUE, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            if (innerChannelRef->nmMode == NM_MODE_NETWORK)
            {
                /* @req CANNM099 */
                CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelRef);
                #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                /* @req CANNM065 */
                CanNm_StopMsgTimeoutTimer(innerChannelRef);
                #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
                if ((TRUE == configChannelRef->busLoadReductionActive) && (innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION))
                {
                    CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgCycleTime), innerChannelRef);
                }
                #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */
                #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
            }
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_TXCONFIRMATION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_TXCONFIRMATION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */
    }

    return;
}
#endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */


/*************************************************************************/
/*
 * Brief               This service  indicates a successful reception of a
 *                     received NM message to the CanNm after passing all
 *                     filters and validation checks.
 * ServiceId           0x10
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      canNmRxPduId:Identification of the network through PDU-ID;
 *                     PduInfoPtr:Contains the length (SduLength) of the received
 *                     I-PDU and a pointer to a buffer (SduDataPtr) containing
 *                     the I-PDU.
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_RxIndication
(
    PduIdType canNmRxPduId,
    P2CONST(PduInfoType, AUTOMATIC, CANNM_APPL_DATA) PduInfoPtr
)
{
    uint8 nmIndex = 0;
    boolean repeatMsgBit = FALSE;

    /* @req CANNM016 */
    #if (STD_ON == CANNM_DEV_ERROR_DETECT)
    if (NULL_PTR == PduInfoPtr)
    {
        /* @req CANNM019 */
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_RXINDICATION_SERVICEID, CANNM_E_NULL_POINTER);
        return;
    }
    #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindPduId(canNmRxPduId, FALSE, &nmIndex))           //liaoyonggang xuigai
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            /* @req CANNM035 */
            memcpy(innerChannelRef->rxPduData, PduInfoPtr->SduDataPtr, (uint8)(PduInfoPtr->SduLength));
            if(configChannelRef->pduCbvPosition != CANNM_PDU_OFF)
            {
                repeatMsgBit = innerChannelRef->rxPduData[configChannelRef->pduCbvPosition] & CANNNM_REPEAT_MESSAGE_REQUEST_CBV_MASK;
            }

            switch(innerChannelRef->nmMode)
            {
                case NM_MODE_NETWORK:
                    /* @req CANNM098 */
                    CanNm_StartNMTimeoutTimer(configChannelRef, innerChannelRef);
                    /* @req CANNM069 */
                    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
                    if ((innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION) && (TRUE == innerChannelRef->busLoadEn))
                    {
                        CanNm_StartMsgCycleTimer((uint16)(configChannelRef->msgReducedtime), innerChannelRef);
                    }
                    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */
                    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
                    if ((innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION) || (innerChannelRef->nmState == NM_STATE_READY_SLEEP))
                    {
                        if (TRUE == innerChannelRef->txEnable)
                        {
                            CanNm_StartRemoteSleepIndTimer(configChannelRef, innerChannelRef);
                        }

                        if(TRUE == innerChannelRef->remoteSleepInd)
                        {
                            innerChannelRef->remoteSleepInd = FALSE;
                            /*@req CANNM151*/
                            Nm_RemoteSleepCancelation(configChannelRef->ChannelId);
                        }
                    }
                    #endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

                    if (TRUE == repeatMsgBit)
                    {
                        /* @req CANNM014 */
                        #if (STD_ON == CANNM_REPEAT_MSG_IND_ENABLED)
                        // Nm_RepeatMessageIndication(configChannelRef->ChannelId);
                        #endif /* (STD_ON == CANNM_REPEAT_MSG_IND_ENABLED) */

                        if (innerChannelRef->nmState == NM_STATE_REPEAT_MESSAGE)
                        {
                            /* EE标准规范-CAN总线网络管理需求规范_V1.0.pdf NM5 */
                            CanNm_RepeatMsgToRepeatMsg(configChannelRef, innerChannelRef);
                        }
                        else if (innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION)
                        {
                            /* @req CANNM119 */
                            CanNm_NormalOperationToRepeatMsg(configChannelRef, innerChannelRef);
                        }
                        else if (innerChannelRef->nmState == NM_STATE_READY_SLEEP)
                        {
                            /* @req CANNM111 */
                            CanNm_ReadySleepToRepeatMsg(configChannelRef, innerChannelRef);
                        }
                        else if (innerChannelRef->nmState == NM_STATE_PREPARE_BUS_SLEEP)
                        {
                            /* @req CANNM097 */
                            CanNm_PrepareBusSleepToRepeatMsg(configChannelRef, innerChannelRef);
                        }
                        else if (innerChannelRef->nmState == NM_STATE_BUS_SLEEP)
                        {
                            /* @req CANNM096 */
                            CanNm_BusSleepToRepeatMsg(configChannelRef, innerChannelRef);
                        }
                        else
                        {
                            /* Nothing to do */
                        }
                    }
                    break;

                case NM_MODE_PREPARE_BUS_SLEEP:
                    /* @req CANNM095 */
                    CanNm_PrepareBusSleepToRepeatMsg( configChannelRef, innerChannelRef);
                    break;

                case NM_MODE_BUS_SLEEP:
                    /* @req CANNM127 */
                    ComM_Nm_NetworkStartIndication(Nm_Channels[configChannelRef->ChannelId].NmComMChannel);
                    CanNm_BusSleepToRepeatMsg(configChannelRef,innerChannelRef);
                    // CanNm_PassiveStartUp(configChannelRef->ChannelId);
                    break;

                default :
                    break;
            }
            /* @req CANNM037, @req CANNM164, @req CANNM165 */
            #if (STD_ON == CANNM_PDU_RX_INDICATION_ENABLED)
                Nm_PduRxIndication(configChannelRef->ChannelId);
            #endif /* (STD_ON == CANNM_PDU_RX_INDICATION_ENABLED) */
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_RXINDICATION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

            return;
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_RXINDICATION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        return;
    }

    return;
}


/*************************************************************************/
/*
 * Brief               Main function of the CanNm which processes the algorithm.
 * ServiceId           0x13
 * Sync/Async          None
 * Reentrancy          None
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_MainFunction
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
)
{
    uint8 nmIndex = 0;

    if (TRUE == CanNm_InitFlag)
    {
        if (TRUE == CanNm_FindChannelIndex(nmChannelHandle, &nmIndex))
        {
            P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[nmIndex];
            P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[nmIndex];

            if (innerChannelRef->nmMode == NM_MODE_NETWORK)
            {
                if (TRUE == innerChannelRef->txEnable)
                {
                    CanNm_CheckNmTimeoutTimer(configChannelRef, innerChannelRef);
                    #if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
                    /* @req CANNM150 */
                    if (innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION)
                    {
                        CanNm_CheckRemoteSleepIndTimer(configChannelRef, innerChannelRef);
                    }
                    #endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

                    /* @req CANNM108 */
                    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
                    if ((innerChannelRef->nmState == NM_STATE_NORMAL_OPERATION) || (innerChannelRef->nmState == NM_STATE_REPEAT_MESSAGE ))
                    {
                        CanNm_CheckMsgCycleTimer(configChannelRef, innerChannelRef);
                    }
                    #if(STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
                    CanNm_CheckMsgTxTimer(configChannelRef, innerChannelRef);
                    #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
                    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
                }
                if (innerChannelRef->nmState == NM_STATE_REPEAT_MESSAGE)
                {
                    CanNm_CheckRepeatMsgTimer(configChannelRef, innerChannelRef);
                }
           }
           else if (innerChannelRef->nmMode == NM_MODE_PREPARE_BUS_SLEEP)
           {
               /* @req CANNM115 */
               CanNm_CheckWaitBusSleepTimer(configChannelRef, innerChannelRef);   // liaoyonggangxiugai
           }
           else
           {
               /* Nothing to do */
           }
        }
        else
        {
            /* @req CANNM019, @req CANNM192, @req CANNM016 */
            #if (STD_ON == CANNM_DEV_ERROR_DETECT)
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_MAINFUNCTION_SERVICEID, CANNM_E_INVALID_CHANNEL);
            #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */
            return;  
        }
    }
    else
    {
        /* @req CANNM039, @req CANNM019, @req CANNM191, @req CANNM016 */
        #if (STD_ON == CANNM_DEV_ERROR_DETECT)
        Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_MAINFUNCTION_SERVICEID, CANNM_E_NO_INIT);
        #endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

        return;
    }

    return;
}


/*************************************************************************/
/*
 * Brief               Main function of the CanNm which processes the algorithm.
 * ServiceId           0x13
 * Sync/Async          None
 * Reentrancy          None
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
void CanNmPrepareBusSleepToBusSleep(void)
{
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelRef = &CanNm_ChannelRuntime[0];
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef = &CanNm_ChannelConfigData[0];

    innerChannelRef->wbsTimer.remainWbsTTime = 0x0u;
    innerChannelRef->wbsTimer.started = FALSE;

    /* @req CANNM088 */
    CanNm_PrepareBusSleepToBusSleep(configChannelRef, innerChannelRef);
}

/*************************************************************************/
/*
 * Brief               Function of the CanNm Userdata 1 which processes the algorithm.
 * ServiceId           0x13
 * Sync/Async          None
 * Reentrancy          None
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
STATIC FUNC(void, CANNM_CODE)
CanNm_ProcessUserdataByte1
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    if (NM_STATE_REPEAT_MESSAGE == innerChannelConf->nmState)
    {
        innerChannelConf->txPduData[3] = (1);
    }
    else
    {
        innerChannelConf->txPduData[3] = (0);
    }

    return;
}

/*************************************************************************/
/*
 * Brief               Function of the CanNm Userdata 0 which processes the algorithm.
 * ServiceId           0x13
 * Sync/Async          None
 * Reentrancy          None
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
STATIC FUNC(void, CANNM_CODE)
CanNm_ProcessUserdataByte0
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf)
{
    BackupRamInfo *backupRamInfo = NULL;
    backupRamInfo                = (BackupRamInfo *)GetBackupRamInfo();

    switch (backupRamInfo->mcuWakeupSource)
    {
    case TBOX_WAKEUP_CAN1:
    case TBOX_WAKEUP_CAN2:
    case TBOX_WAKEUP_CAN3:
    case TBOX_WAKEUP_CAN4:
        if (WORK_STATUS_ACTIVE == g_commonInfo.accStatus)
        {
            innerChannelConf->txPduData[2] = (2);
        }
        else if (g_canNetStatus == 0x00)
        {
            innerChannelConf->txPduData[2] = (1);
        }
        else
        {
            innerChannelConf->txPduData[2] = (0);
        }
        break;
    case TBOX_WAKEUP_BPLUS:
    case TBOX_WAKEUP_ACC:
        if (WORK_STATUS_ACTIVE == g_commonInfo.accStatus)
        {
            innerChannelConf->txPduData[2] = (2);
        }
        else if (g_canNetStatus == 0x00)
        {
            innerChannelConf->txPduData[2] = (1);
        }
        else
        {
            innerChannelConf->txPduData[2] = (0);
        }
        break;
    default:
        innerChannelConf->txPduData[2] = (0);
        break;
    }

    return;
}

/*************************************************************************/
/*
 * Brief               Function of the CanNm Userdata 2 which processes the algorithm.
 * ServiceId           0x13
 * Sync/Async          None
 * Reentrancy          None
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
STATIC FUNC(void, CANNM_CODE)
CanNm_ProcessUserdataByte2
(
    P2CONST(CanNm_ChannelConfigType, AUTOMATIC, CANNM_CONST) configChannelRef,
    P2VAR(CanNm_InnerChannelType, AUTOMATIC, CANNM_VAR) innerChannelConf
)
{
    innerChannelConf->txPduData[4] = (0);
    innerChannelConf->txPduData[5] = (0);
    innerChannelConf->txPduData[6] = (0);
    // innerChannelConf->txPduData[7] = (0);
    innerChannelConf->txPduData[7] = g_canNetStatus;
    return;
}