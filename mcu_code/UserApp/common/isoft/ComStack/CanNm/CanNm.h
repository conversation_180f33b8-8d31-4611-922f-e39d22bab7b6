/*============================================================================*/
/*  Copyright (C) 2015-2020, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <CanNm.h>
 *  @brief      <CanNm>
 *
 *  <Compiler: CodeWarrior2.8 MCU:MPC5634>
 *
 *  <AUTHOR>
 *  @date       <2016-->
 */
/*============================================================================*/

#ifndef CANNM_H
#define CANNM_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       2016      SherryShen   Initial version
 *
 */
/*============================================================================*/

/*======[I N C L U D E S]====================================================*/
#include "NmStack_Types.h"
#include "ComStack_Types.h"
#include "CanNm_Cfg.h"

#if (STD_ON == CANNM_DEM_ERROR_DETECT)
/* @req CANNM082 */
#include "Dem.h"
#endif /* (STD_ON == CANNM_DEM_ERROR_DETECT) */

#if (STD_ON == CANNM_DEV_ERROR_DETECT)
/* @req CANNM082 */
#include "Det.h"
#endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

/* @req CANNM021 */
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define CANNM_MODULE_ID            29U
#define CANNM_H_VENDOR_ID          62U
#define CANNM_H_AR_MAJOR_VERSION   3U
#define CANNM_H_AR_MINOR_VERSION   2U
#define CANNM_H_AR_PATCH_VERSION   0U
#define CANNM_H_SW_MAJOR_VERSION   1U
#define CANNM_H_SW_MINOR_VERSION   0U
#define CANNM_H_SW_PATCH_VERSION   0U


// /* States of the network management state machine */
// typedef enum 
// {
//     NM_STATE_UNINIT,
//     NM_STATE_BUS_SLEEP,
//     NM_STATE_PREPARE_BUS_SLEEP,
//     NM_STATE_READY_SLEEP,
//     NM_STATE_NORMAL_OPERATION,
//     NM_STATE_REPEAT_MESSAGE,
//     NM_STATE_SYNCHRONIZE
// }Nm_StateType;

// /* Return type for NM functions. Derived from Std_ReturnType. */
// typedef enum 
// {
//     NM_E_OK,
//     NM_E_NOT_OK,
//     NM_E_NOT_EXECUTED
// }Nm_ReturnType;

// /* Operational modes of the network management */
// typedef enum
// {
//     NM_MODE_BUS_SLEEP,
//     NM_MODE_PREPARE_BUS_SLEEP,
//     NM_MODE_SYNCHRONIZE,
//     NM_MODE_NETWORK
// }Nm_ModeType;

/* BusNm Type */
typedef enum
{
    NM_BUSNM_CANNM,
    NM_BUSNM_FRNM,
    NM_BUSNM_LINNM,
    NM_BUSNM_OSEKNM,
    NM_BUSNM_UNDEF
}Nm_BusNmType;


/* Define byte position of CBV/NID in the NM PDU */
typedef enum
{
    CANNM_PDU_BYTE_0 = 0x0u,                   /* Byte 0 is used */
    CANNM_PDU_BYTE_1 = 0x1u,                   /* Byte 1 is used*/
    CANNM_PDU_OFF    = 0xFFu                   /* Cbv/Nid is not used */
}CanNm_PduBytePositionType;

/* configuration of the bus channe */
typedef struct
{
    const Nm_BusNmType          NmBusType;
    const NetworkHandleType     NmComMChannel;
} Nm_ChannelType;

typedef struct
{
    #if (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    CONST(boolean, CANNM_CONST) busLoadReductionActive;     /* Defines if bus load reduction for the respective NM channel is active or not. */
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */
    
    CONST(boolean, CANNM_CONST) channelActive;               /* Indicates whether a particular NM-channel shall be initialized (TRUE) or not. */
    
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    CONST(TickType, CANNM_CONST)  msgCycleOffset;              /* Time offset in the periodic transmission node. */
    
    CONST(TickType, CANNM_CONST)  msgCycleTime;                /* Period of a NM-message in seconds. */
    
    #if(STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED)
    CONST(TickType, CANNM_CONST)  msgReducedtime;              /* Node specific bus cycle time in the periodic transmission mode with bus load reduction. */
    #endif /* (STD_ON == CANNM_BUS_LOAD_REDUCTION_ENABLED) */
    
    #if (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED)
    CONST(TickType, CANNM_CONST)  msgTimeoutTime;              /* Transmission Timeout of NM-message. */
    #endif /* (STD_OFF == CANNM_IMMEDIATE_TXCONF_ENABLED) */
    
    #if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
    CONST(uint8, CANNM_CONST)   nodeId;                      /* Node identifier of local node. */
    #endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
    
    #if (STD_ON == CANNM_NODE_ID_ENABLED)
    CONST(CanNm_PduBytePositionType, CANNM_CONST) pduNidPosition;/* Defines the position of the source node identifier within the NM PDU. */
    #endif /* (STD_ON == CANNM_NODE_ID_ENABLED) */
    
    CONST(CanNm_PduBytePositionType, CANNM_CONST) pduCbvPosition;/* Defines the position of the control bit vector within the NM PDU. */
    
    CONST(uint8, CANNM_CONST) pduLength;                     /* Defines the length of the NM PDU. */
    
    #if(STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
    CONST(TickType, CANNM_CONST)  remoteSleepIndTime;          /* Timeout for Remote Sleep Indication. */
    #endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */
    
    CONST(TickType, CANNM_CONST)  repeatMsgTime;              /* Timeout for Repeat Message State. */
    
    CONST(TickType, CANNM_CONST)  timeoutTime;                 /* Network Timeout for NM-Messages. */
    
    CONST(uint8, CANNM_CONST)     userDataLength;              /* Defines the length of the user data contained in the NM PDU. */
    
    CONST(TickType, CANNM_CONST)  waitBusSleepTime;            /* Timeout for bus calm down phase. */
    
    CONST(NetworkHandleType, CANNM_CONST) ChannelId;         /* Channel identifier configured for the respective AUTOSAR NM cluster. */
    
    #if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
    CONST(PduIdType, CANNM_CONST)  TxPduId;                   /* Tx PduId */
    #endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

    CONST(TickType, CANNM_CONST)  immediateCycleTime;         /* immediateCycleTime */
    CONST(uint8, CANNM_CONST)     immediateTransmissions;     /* immediateTransmissions */
}CanNm_ChannelConfigType;


/* CanNm configuration */
typedef struct
{
    P2CONST(NetworkHandleType, AUTOMATIC, CANNM_CONST) canNmNetworkHandle;
}CanNm_ConfigType;

/* PC supported */
#if (CANNM_VARIANT_CFG == CANNM_VARIANT_PC )
extern CONST(NetworkHandleType,CANNM_CONST) CanNm_NetworkHandle[CANNM_NUMBER_OF_CHANNELS];
extern CONST(CanNm_ChannelConfigType,CANNM_CONST) CanNm_ChannelConfigData[CANNM_NUMBER_OF_CHANNELS];
#endif /* (CANNM_VARIANT_CFG == CANNM_VARIANT_PC ) */
/*=======[M A C R O S]========================================================*/
#define CANNM_INSTANCE_ID                   0x0u

#if(STD_ON == CANNM_DEV_ERROR_DETECT)
#define CANNM_INIT_SERVICEID                            0x0u
#define CANNM_PASSIVESTARTUP_SERVICEID                  0x1u
#define CANNM_NETWORKREQUEST_SERVICEID                  0x2u
#define CANNM_NETWORKRELEASE_SERVICEID                  0x3u
#define CANNM_DISABLECOMMUNICATION_SERVICEID            0xcu
#define CANNM_ENABLECOMMUNICATION_SERVICEID             0xdu
#define CANNM_SETUSERDATA_SERVICEID                     0x4u
#define CANNM_GETUSERDATA_SERVICEID                     0x5u
#define CANNM_GETNODEIDENTIFIER_SERVICEID               0x6u
#define CANNM_GETLOCALNODEIDENTIFIER_SERVICEID          0x7u
#define CANNM_REPEATMESSAGEREQUEST_SERVICEID            0x8u
#define CANNM_GETPDUDATA_SERVICEID                      0xau
#define CANNM_GETSTATE_SERVICEID                        0xbu
#define CANNM_GETVERSIONINFO_SERVICEID                  0xf1u
#define CANNM_REQUESTBUSSYNCHRONIZATION_SERVICEID       0xc0u
#define CANNM_CHECKREMOTESLEEPINDICATION_SERVICEID      0xd0u
#define CANNM_TXCONFIRMATION_SERVICEID                  0x0fu
#define CANNM_RXINDICATION_SERVICEID                    0x10u
#define CANNM_MAINFUNCTION_SERVICEID                    0x13u

#define CANNM_E_NO_INIT                                 0x1u
#define CANNM_E_INVALID_CHANNEL                         0x2u
#define CANNM_E_DEV_NETWORK_TIMEOUT                     0x11u
#define CANNM_E_NULL_POINTER                            0x12u
#endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/

#if (STD_ON == CANNM_VERSION_INFO_API)
/*************************************************************************/
/*
 * Brief               This service returns the version information of
 *                     this module
 * ServiceId           0xF1
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     Versioninfo: Pointer to where to store the version
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
/* @req 8.3.14 */
#if (STD_ON == CANNM_DEV_ERROR_DETECT)
#define CanNm_GetVersionInfo(VersionInfo) \
    do{\
        if (NULL_PTR == (VersionInfo))\
        { \
            Det_ReportError(CANNM_MODULE_ID, CANNM_INSTANCE_ID, CANNM_GETVERSION_ID, CANNM_E_PARAM_POINTER);\
        }\
        else\
        {\
            (VersionInfo)->vendorID = CANNM_H_VENDOR_ID; \
            (VersionInfo)->moduleID = CANNM_MODULE_ID; \
            (VersionInfo)->instanceID = 0u; \
            (VersionInfo)->sw_major_version = CANNM_H_SW_MAJOR_VERSION; \
            (VersionInfo)->sw_minor_version = CANNM_H_SW_MINOR_VERSION; \
            (VersionInfo)->sw_patch_version = CANNM_H_SW_PATCH_VERSION; \
        }\
    }while(0)
#else
#define CanNm_GetVersionInfo(VersionInfo) \
    do{\
        (VersionInfo)->vendorID = CANNM_H_VENDOR_ID; \
        (VersionInfo)->moduleID = CANNM_MODULE_ID; \
        (VersionInfo)->instanceID = 0u; \
        (VersionInfo)->sw_major_version = CANNM_H_SW_MAJOR_VERSION; \
        (VersionInfo)->sw_minor_version = CANNM_H_SW_MINOR_VERSION; \
        (VersionInfo)->sw_patch_version = CANNM_H_SW_PATCH_VERSION; \
    }while(0)
#endif /* (STD_ON == CANNM_DEV_ERROR_DETECT) */
#endif /* STD_ON == CANNM_VERSION_INFO_API */

/*************************************************************************/
/*
 * Brief               This service Initialize the complete CanNm module.
 * ServiceId           0x00
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      cannmConfigPtr:Pointer to a selected configuration structure ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_Init
(
    CONSTP2CONST(CanNm_ConfigType, AUTOMATIC, CANNM_CONST_PBCFG) cannmConfigPtr
);


/*************************************************************************/
/*
 * Brief               This service Passive startup of the AUTOSAR CAN NM.
 *                     It triggers the transition from Bus-Sleep Mode to
 *                     the Network Mode in Repeat Message State.
 * ServiceId           0x01
 * Sync/Async          Asynchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Passive startup of network management
 *                     has failed
 *                     NM_E_NOT_EXECUTED: Passive startup of network
 *                     management is currently not executed
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_PassiveStartUp
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);


#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/*************************************************************************/
/*
 * Brief               This service Request the network, since ECU needs
 *                     to communicate on the bus. Network state shall be
 *                     changed to 'requested'.
 * ServiceId           0x02
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Requesting of network has failed
 * PreCondition        CANNM_PASSIVE_MODE_ENABLED is not defined
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_NetworkRequest
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);


/*************************************************************************/
/*
 * Brief               This service release the network, since ECU doesn't
 *                     have to communicate on the bus. Network state shall
 *                     be changed to 'released'.
 * ServiceId           0x03
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Releasing of network has failed
 * PreCondition        CANNM_PASSIVE_MODE_ENABLED is not defined
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_NetworkRelease
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */


#if (STD_ON == CANNM_COM_CONTROL_ENABLED)
/*************************************************************************/
/*
 * Brief               This service disable the NM PDU transmission ability.
 * ServiceId           0x0C
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Disabling of NM PDU transmission ability has failed
 *                     NM_E_NOT_EXECUTED: Disabling of NM PDU transmission
 *                     ability is not executed.
 * PreCondition        CANNM_COM_CONTROL_ENABLED is defined
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_DisableCommunication
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);


/*************************************************************************/
/*
 * Brief               This service enable the NM PDU transmission ability.
 * ServiceId           0x0D
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error
 *                     NM_E_NOT_OK: Enabling of NM PDU transmission ability has failed;
 *                     NM_E_NOT_EXECUTED: Enabling of NM PDU transmission
 *                     ability is not executed.
 * PreCondition        CANNM_COM_CONTROL_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_EnableCommunication
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);
#endif /* (STD_ON == CANNM_COM_CONTROL_ENABLED) */


#if (STD_ON == CANNM_USER_DATA_ENABLED)
#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/*************************************************************************/
/*
 * Brief               This service set user data for NM messages transmitted
 *                     next on the bus.
 * ServiceId           0x04
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 *                     nmUserDataPtr:Pointer where the user data for the next
 *                     transmitted NM message shall be copied from;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Setting of user data has failed;
 * PreCondition        CANNM_USER_DATA_ENABLED is defined;
 *                     CANNM_PASSIVE_MODE_ENABLED is not defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_SetUserData
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2CONST(uint8, AUTOMATIC, CANNM_APPL_DATA) nmUserDataPtr
);
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */

/*************************************************************************/
/*
 * Brief               This service get user data out of the most recently
 *                     received NM message.
 * ServiceId           0x05
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     nmUserDataPtr: Pointer where user data out of the
 *                     most recently received NM message shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of user data has failed;
 * PreCondition        CANNM_USER_DATA_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetUserData
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmUserDataPtr
);
#endif /* (STD_ON == CANNM_USER_DATA_ENABLED) */

#if (STD_ON == CANNM_NODE_ID_ENABLED)
/*************************************************************************/
/*
 * Brief               This service get node identifier out of the most
 *                     recently received NM PDU.
 * ServiceId           0x06
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier of the
 *                     local node shall be copied to.
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of the node identifier of the
 *                     local node has failed;
 * PreCondition        CANNM_NODE_ID_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetNodeIdentifier
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmNodeIdPtr
);

#if (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)
/*************************************************************************/
/*
 * Brief               This service get node identifier configured for the
 *                     local node.
 * ServiceId           0x07
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier of the
 *                     local node shall be copied to.
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of the node identifier of the
 *                     local node has failed;
 * PreCondition        CANNM_NODE_ID_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetLocalNodeIdentifier
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmNodeIdPtr
);
#endif /* (STD_OFF == CANNM_PASSIVE_MODE_ENABLED) */
#endif /* (STD_ON == CANNM_NODE_ID_ENABLED) */

/* @ReqCANNM136 */
#if (STD_ON == CANNM_NODE_DETECTION_ENABLED)
/*************************************************************************/
/*
 * Brief               This service Set Repeat Message Request Bit for NM
 *                     messages transmitted next on the bus.
 * ServiceId           0x08
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel ;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Setting of Repeat Message Request Bit has
 *                     failed;
 *                     NM_E_NOT_EXECUTED: Repeat Message Request is currently
 *                     not executed.
 * PreCondition        CANNM_NODE_DETECTION_ENABLED is defined;
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_RepeatMessageRequest
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);
#endif /* (STD_ON == CANNM_NODE_DETECTION_ENABLED) */


/* @ReqCANNM139 */
#if ((STD_ON == CANNM_NODE_ID_ENABLED) || (STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_USER_DATA_ENABLED))
/*************************************************************************/
/*
 * Brief               Get the whole PDU data out of the most recently received NM message.
 * ServiceId           0x0a
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     nmPduDataPtr:Pointer where NM PDU shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of NM PDU data has failed;
 * PreCondition        CANNM_NODE_ID_ENABLED is defined;
 *                     CANNM_NODE_DETECTION_ENABLED is defined;
 *                     CANNM_USER_DATA_ENABLED is defined.
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetPduData
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmPduDataPtr
);
#endif /* ((STD_ON == CANNM_NODE_ID_ENABLED) || (STD_ON == CANNM_NODE_DETECTION_ENABLED) || (STD_ON == CANNM_USER_DATA_ENABLED)) */

/*************************************************************************/
/*
 * Brief               Returns the state and the mode of the network management.
 * ServiceId           0x0b
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     nmStatePtr:Pointer where state of the network management
 *                     shall be copied to;
 *                     nmModePtr:Pointer where the mode of the network management
 *                     shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Getting of NM state has failed;
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_GetState
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmStatePtr,
    CONSTP2VAR(uint8, AUTOMATIC, CANNM_APPL_DATA) nmModePtr
);

#if ((STD_ON == CANNM_BUS_SYNCHRONIZATION_ENABLED) && (STD_OFF == CANNM_PASSIVE_MODE_ENABLED))
/*************************************************************************/
/*
 * Brief               Request bus synchronization.
 * ServiceId           0xc0
 * Sync/Async          Asynchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Requesting of bus synchronization has failed;
 * PreCondition        None
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_RequestBusSynchronization
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);
#endif /* ((STD_ON == CANNM_BUS_SYNCHRONIZATION_ENABLED) && (STD_OFF == CANNM_PASSIVE_MODE_ENABLED)) */

#if (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED)
/*************************************************************************/
/*
 * Brief               Check if remote sleep indication takes place or not.
 * ServiceId           0xd0
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel;
 * Param-Name[out]     nmRemoteSleepIndPtr:Pointer where check result of remote
 *                     sleep indication shall be copied to;
 * Param-Name[in/out]  None
 * Return              NM_E_OK: No error;
 *                     NM_E_NOT_OK: Requesting of bus synchronization has failed;
 *                     NM_E_NOT_EXECUTED: Checking of Remote Sleep Indication is
 *                     currently not executed.
 * PreCondition        CANNM_REMOTE_SLEEP_IND_ENABLED is defined.
 * CallByAPI           RTE
 */
/*************************************************************************/
FUNC(Nm_ReturnType, CANNM_CODE)
CanNm_CheckRemoteSleepIndication
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle,
    CONSTP2VAR(boolean, AUTOMATIC, CANNM_APPL_DATA) nmRemoteSleepIndPtr
);
#endif /* (STD_ON == CANNM_REMOTE_SLEEP_IND_ENABLED) */

/*************************************************************************/
/*
 * Brief               Main function of the CanNm which processes the algorithm.
 * ServiceId           0x13
 * Sync/Async          None
 * Reentrancy          None
 * Param-Name[in]      None
 * Param-Name[out]
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Lower layer
 */
/*************************************************************************/
FUNC(void, CANNM_CODE)
CanNm_MainFunction
(
    CONST(NetworkHandleType, AUTOMATIC) nmChannelHandle
);

void CanNmPrepareBusSleepToBusSleep(void);


#endif /* CANNM_H */
