/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <OsekNm.h>
 *  @brief      <OsekNm>
 *
 *  <Compiler: >
 *
 *  <AUTHOR>
 *  @date       <2013-11-15>
 */
/*============================================================================*/
#ifdef CAN_ENBALE_OSEKNM

#ifndef OSEKNM_H
#define OSEKNM_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *      V1.0.0          20131115   Huanyu.zhao      Initial version
 *
 *
 */
/*============================================================================*/

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define OSEKNM_MODULE_ID            31U
#define OSEKNM_H_VENDOR_ID          62U
#define OSEKNM_INSTANCE_ID          0U
#define OSEKNM_H_AR_MAJOR_VERSION   2U
#define OSEKNM_H_AR_MINOR_VERSION   5U
#define OSEKNM_H_AR_PATCH_VERSION   3U
#define OSEKNM_H_SW_MAJOR_VERSION   1U
#define OSEKNM_H_SW_MINOR_VERSION   0U
#define OSEKNM_H_SW_PATCH_VERSION   0U

/*=======[I N C L U D E S]====================================================*/
#include "ComStack_Types.h"
#include "NmStack_Types.h"
#include "OsekNm_Cfg.h"
//#include "appqueue.h"
#include "AppTask.h"


/*=======[M A C R O S]========================================================*/
#if (STD_ON == OSEKNM_DEV_ERROR_DETECT)
#define OSEKNM_INIT_ID              ((uint8)0x00U)
#define OSEKNM_PASSIVESTARTUP_ID    ((uint8)0x01U)
#define OSEKNM_NETWORKREQUEST_ID    ((uint8)0x02U)
#define OSEKNM_NETWORKRELEASE_ID    ((uint8)0x03U)
#define OSEKNM_DISABLECOMMUNICATION_ID  ((uint8)0x0cU)
#define OSEKNM_ENABLECOMMUNICATION_ID   ((uint8)0x0dU)
#define OSEKNM_SETUSERDATA_ID           ((uint8)0x04U)
#define OSEKNM_GETUSERDATA_ID           ((uint8)0x05U)
#define OSEKNM_GETNODEIDENTIFIER_ID     ((uint8)0x06U)
#define OSEKNM_GETLOCALNODEIDENTIFIER_ID    ((uint8)0x07U)
#define OSEKNM_REPEATMESSAGEREQUEST_ID      ((uint8)0x08U)
#define OSEKNM_GETPDUDATA_ID                ((uint8)0x0aU)
#define OSEKNM_GETSTATE_ID                  ((uint8)0x0bU)
#define OSEKNM_GETVERSIONINFO_ID            ((uint8)0xf1U)
#define OSEKNM_REQUESTBUSSYNCHRONIZATION_ID ((uint8)0xc0U)
#define OSEKNM_CHECKREMOTESLEEPINDICATION_ID    ((uint8)0xd0U)
#define OSEKNM_GETCONFIG_ID                 ((uint8)0xe0U)
#define OSEKNM_CMPCONFIG_ID                 ((uint8)0xf0U)
#define OSEKNM_GETSTATUS_ID                 ((uint8)0xf2U)
#define OSEKNM_CMPSTATUS_ID                 ((uint8)0xf3U)

#define OSEKNM_BUSOFF_ID                    ((uint8)0x15U)
#define OSEKNM_BUSOFFRECOVERY_ID                    ((uint8)0x16U)
#define OSEKNM_TXCONFIRMATION_ID            ((uint8)0x0fU)
#define OSEKNM_RXINDICATION_ID              ((uint8)0x10U)
#define OSEKNM_MAINFUNCTION_ID              ((uint8)0x13U)

#define OSEKNM_E_NO_INIT            ((uint8)0x01U)
#define OSEKNM_E_INVALID_CHANNEL    ((uint8)0x02U)
#define OSEKNM_E_NULL_POINTER       ((uint8)0x12U)
#define OSEKNM_E_INVALID_PARA   ((uint8)0x13U)

#endif /* #if (STD_ON == OSEKNM_DEV_ERROR_DETECT) */


/*=======[T Y P E   D E F I N I T I O N S]====================================*/
/* Enumeration Interrupt or Polling mode */
typedef enum 
{
    OSEKNM_PROCESS_TYPE_INTERRUPT,
    OSEKNM_PROCESS_TYPE_POLLING
} OsekNm_ProcessType;

typedef P2FUNC(void, OSEKNM_APPL_CODE, OsekNm_RingDataNotifyType)(void);
typedef P2FUNC(void, OSEKNM_APPL_CODE, OsekNm_LimpHomeNotifyType)(boolean param);

typedef struct
{
    uint16_least    TTyp;
    uint16_least    TMax;
    uint16_least    TError;
    uint16_least    TTx;
    uint16_least    TWbs;
    #if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
    uint16_least TLimpErr;
    #endif
    #if(STD_ON == OSEKNM_BUSLOADREDUCTIONENABLED)
    uint16_least    MsgReducedTime;
    #endif 
    #if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED)
    uint16_least    MsgTimeoutTime;
    #endif
    PduIdType TxPduId;
    uint8 NodeId;
    #if(STD_ON == OSEKNM_USER_DATA_ENABLED)
    uint8   UserDataLength;
    #endif
    NetworkHandleType RefSm;
    uint8   TxLimitCfg;
    uint8   RxLimitCfg;
    OsekNm_ProcessType      TxProcessing;
    OsekNm_ProcessType      RxProcessing;
    OsekNm_RingDataNotifyType RingDataNotify;
    OsekNm_LimpHomeNotifyType LimpHomeNotify;
}OsekNm_ChannelConfigType;

/* NodeId Min bit 0,nodeId Second Min Bit1...*/
typedef uint8 OsekNm_ConfigRefType;

typedef uint16 OsekNm_NetWorkStatusType;

/* NM Config Type */
typedef enum
{
    OSEKNM_CFG_KIND_NORMAL,
    OSEKNM_CFG_KIND_LIMPHOME
}OsekNm_ConfigKindNameType;

typedef uint8 OsekNm_EventType;

typedef uint16 OsekNm_StatusType;

/*=======[E X T E R N A L   D A T A]==========================================*/
/* pointer to OsekNm_ConfigData array */
#define OSEKNM_START_SEC_CONST_UNSPECIFIED
#include "OsekNm_MemMap.h"
extern CONST(OsekNm_ChannelConfigType, OSEKNM_CONST) OsekNm_ConfigData[OSEKNM_NUMBER_OF_CHANNELS];
#define OSEKNM_STOP_SEC_CONST_UNSPECIFIED
#include "OsekNm_MemMap.h"


/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
/*************************************************************************/
/*
 * Brief               Initialize the complete OsekNm module.
 * ServiceId           0x00
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      oseknmConfigPtr: Pointer to a selected configuration structure
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_Init(void);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Passive startup of the OSEK NM
 * ServiceId           0x01
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE) 
OsekNm_PassiveStartUp(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Request the network, since ECU needs to communicate on the bus.
 * ServiceId           0x02
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_NetworkRequest(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Release the network, since ECU doesn't have to communicate on the bus.
 * ServiceId           0x03
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_NetworkRelease(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Disable the NM PDU transmission ability due to a ISO14229 
 *              Communication Control (28hex) service.
 * ServiceId           0x0c
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_COM_CONTROL_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_DisableCommunication(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Enable the NM PDU transmission ability due to a ISO14229 
 *              Communication Control (28hex) service.
 * ServiceId           0x0d
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_EnableCommunication(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_COM_CONTROL_ENABLED) */

/*************************************************************************/
/*
 * Brief               Set user data for NM messages transmitted next on the bus.  
 * ServiceId           0x04
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[in]      nmUserDataPtr: Pointer where the user data for the 
 *                  next transmitted NM message shall be copied from
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_USER_DATA_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_SetUserData(NetworkHandleType nmChannelHandle,
CONSTP2CONST(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmUserDataPtr);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get user data out of the most recently received NM message.  
 * ServiceId           0x05
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[in]      nmUserDataPtr: Pointer where user data out of the most recently
 *                  received NM message shall be copied to                  
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetUserData(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmUserDataPtr);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_USER_DATA_ENABLED) */

/*************************************************************************/
/*
 * Brief               Get node identifier out of the most recently received NM PDU.  
 * ServiceId           0x06
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier out of the most recently
 *                      received NM PDU shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetNodeIdentifier(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmNodeIdPtr);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get node identifier configured for the local node.  
 * ServiceId           0x07
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier of the local node 
 *                                  shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetLocalNodeIdentifier(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmNodeIdPtr);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Set Repeat Message Request Bit for NMmessages transmitted next on the bus.  
 * ServiceId           0x08
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]     
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_RepeatMessageRequest(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get the whole PDU data out of the most recently received NM message.  
 * ServiceId           0x0a
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant 
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]     nmPduDataPtr:Pointer where NM PDU shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetPduData(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmPduDataPtr);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Returns the state and the mode of the network management.  
 * ServiceId           0x0b
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant 
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]     nmStatePtr:Pointer where state of the network management 
 *                  shall be copied to
 * Param-Name[out]     nmModePtr:Pointer where the mode of the network management 
 *                  shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetState(NetworkHandleType nmChannelHandle,
P2VAR(Nm_StateType, AUTOMATIC, OSEKNM_APPL_DATA) nmStatePtr,
P2VAR(Nm_ModeType, AUTOMATIC, OSEKNM_APPL_DATA) nmModePtr);


#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Request bus synchronization..  
 * ServiceId           0xc0
 * Sync/Async          Asynchronous
 * Reentrancy          Non Reentrant 
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]    
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_BUS_SYNCHRONIZATION_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_RequestBusSynchronization(NetworkHandleType  nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_BUS_SYNCHRONIZATION_ENABLED) */
/*************************************************************************/
/*
 * Brief               Check if remote sleep indication takes place or not.  
 * ServiceId           0x08
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel                    
 * Param-Name[out]     nmRemoteSleepIndPtr:Pointer where check result of remote sleep
 *                  indication shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_CheckRemoteSleepIndication(NetworkHandleType nmChannelHandle,
P2VAR(boolean, AUTOMATIC, OSEKNM_APPL_DATA) nmRemoteSleepIndPtr);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */
/*************************************************************************/
/*
 * Brief               This service provides the actual configuration of the kind specified 
 *              by <ConfigKind>.
 * ServiceId           0xe0
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[in]      pudConfig: Kind of configuration
 * Param-Name[out]     udConfigKind:Configuration inquired
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetConfig(NetworkHandleType nmChannelHandle,
P2VAR(OsekNm_ConfigRefType, AUTOMATIC, OSEKNM_VAR) pudConfig,
OsekNm_ConfigKindNameType udConfigKind);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This service provides the current status of the network. 
 * ServiceId           0xf2
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]      pudNetworksStatus: Requested Stauts of the node
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetStatus(NetworkHandleType nmChannelHandle,
P2VAR(OsekNm_NetWorkStatusType, AUTOMATIC, OSEKNM_VAR) pudNetworksStatus);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Main function of the OsekNm which processes the algorithm
 * ServiceId           
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_MainFunction(void);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#if (STD_ON == OSEKNM_VERSION_INFO_API)
/*************************************************************************/
/*
 * Brief               This service returns the version information of 
 *                     this module
 * ServiceId           0xF1 
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     Versioninfo: Pointer to where to store the version
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
#if (STD_ON == OSEKNM_DEV_ERROR_DETECT)
#define OsekNm_GetVersionInfo(VersionInfo) \
    do{\
        if (NULL_PTR == (VersionInfo))\
        { \
            Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETVERSIONINFO_ID, OSEKNM_E_NULL_POINTER);\
        }\
        else\
        {\
            (VersionInfo)->vendorID = OSEKNM_H_VENDOR_ID; \
            (VersionInfo)->moduleID = OSEKNM_MODULE_ID; \
            (VersionInfo)->instanceID = 0u; \
            (VersionInfo)->sw_major_version = OSEKNM_H_SW_MAJOR_VERSION; \
            (VersionInfo)->sw_minor_version = OSEKNM_H_SW_MINOR_VERSION; \
            (VersionInfo)->sw_patch_version = OSEKNM_H_SW_PATCH_VERSION; \
        }\
    }while(0)  
#else 
#define OsekNm_GetVersionInfo(VersionInfo) \
    do{\
          (VersionInfo)->vendorID = OSEKNM_H_VENDOR_ID; \
          (VersionInfo)->moduleID = OSEKNM_MODULE_ID; \
          (VersionInfo)->instanceID = 0u; \
          (VersionInfo)->sw_major_version = OSEKNM_H_SW_MAJOR_VERSION; \
          (VersionInfo)->sw_minor_version = OSEKNM_H_SW_MINOR_VERSION; \
          (VersionInfo)->sw_patch_version = OSEKNM_H_SW_PATCH_VERSION; \
    }while(0)  
#endif /* #if (STD_ON == OSEKNM_DEV_ERROR_DETECT) */
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif/* STD_ON == OSEKNM_VERSION_INFO_API */

Nm_StateType OsekNm_GetCurrentState(void);
UINT16 OsekNm_GetCurrentStatus(void);


#endif/* #ifndef OSEKNM_H */

#endif /* CAN_ENBALE_OSEKNM */

/*=======[E N D   O F   F I L E]==============================================*/

