/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <OsekNM_Cbk.h>
 *  @brief      <OsekNM>
 *
 *  <Compiler: >
 *
 *  <AUTHOR>
 *  @date       <2013-11-15>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *      V1.0.0       20131115   Huanyu.zhao      Initial version
 *
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#ifdef CAN_ENBALE_OSEKNM
#ifndef OSEKNM_CBK_H
#define OSEKNM_CBK_H

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define OSEKNM_CBK_H_VENDOR_ID                  62U
#define OSEKNM_CBK_H_AR_MAJOR_VERSION    3U
#define OSEKNM_CBK_H_AR_MINOR_VERSION    1U
#define OSEKNM_CBK_H_AR_PATCH_VERSION     0U
#define OSEKNM_CBK_H_SW_MAJOR_VERSION    1U
#define OSEKNM_CBK_H_SW_MINOR_VERSION    0U
#define OSEKNM_CBK_H_SW_PATCH_VERSION    1U

/*=======[I N C L U D E S]====================================================*/
#include "OsekNm.h"


/*=======[T Y P E   D E F I N I T I O N S]====================================*/

/*=======[F U N C T I O N   I M P L E M E N T A T V O N S]====================*/
/*************************************************************************/
/*
 * Brief               Indication that bus off event has occurred.
 * ServiceId           0x15
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]       nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_BusOff(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Indication that bus off event try Recovery.
 * ServiceId           0x15
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]       nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_BusOffRecovery(NetworkHandleType nmChannelHandle);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               This service confirms a previous successfully processed CAN transmit request.
 * ServiceId           0x0f
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not within the same Channel)
 * Param-Name[in]       OsekNmTxPduId: Identification ofthe network through PDU-ID
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_TxConfirmation(PduIdType OsekNmTxPduId);

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED) */

/*************************************************************************/
/*
 * Brief               This service indicates a successful reception of a received NM message 
 *              to the OsekNm after passing all filters and validation checks.
 * ServiceId           0x10
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not within the same Channel)
 * Param-Name[in]       OsekNmTxPduId: Identification ofthe network through PDU-ID
 * Param-Name[in]       PduInfoPtr: Contains the length (SduLength) of the received I-PDU 
 *                  and a pointer to a buffer (SduDataPtr) containing the I-PDU.
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_RxIndication(PduIdType OsekNmRxPduId,
P2CONST(PduInfoType, AUTOMATIC, AUTOMATIC) PduInfoPtr);


#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"


#endif /* #ifndef OSEKNM_CBK_H */

#endif /* CAN_ENBALE_OSEKNM */
/*=======[E N D   O F   F I L E]==============================================*/
