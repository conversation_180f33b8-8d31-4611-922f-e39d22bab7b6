/*============================================================================*/
/*  Copyright (C) 2009-2013, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <OsekNm.c>
 *  @brief      <OsekNm>
 *
 *  <Compiler: >
 *
 *  <AUTHOR>
 *  @date       <2013-11-15>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *      V1.0.0          20131115   Huanyu.zhao      Initial version
 *      V1.0.1         20140506   wbn               Add SchM
 */



/*============================================================================*/

#ifdef CAN_ENBALE_OSEKNM
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define OSEKNM_C_AR_MAJOR_VERSION   2U
#define OSEKNM_C_AR_MINOR_VERSION   5U
#define OSEKNM_C_AR_PATCH_VERSION   3U
#define OSEKNM_C_SW_MAJOR_VERSION   1U
#define OSEKNM_C_SW_MINOR_VERSION   0U
#define OSEKNM_C_SW_PATCH_VERSION   1U



/*=======[I N C L U D E S]====================================================*/
#include "FreeRTOS.h"
#include "OsekNm.h"
#include "Nm_Cbk.h"
#include "CanIf.h"
#include "SchM_OsekNm.h"
#include "LogApi.h"
#include "CanApi.h"
extern CommonInfo g_commonInfo;

#if (STD_ON == OSEKNM_DEM_ERROR_DETECT)
#include "Dem.h"
#endif

#if (STD_ON == OSEKNM_DEV_ERROR_DETECT)
#include "Det.h"
#endif
extern boolean flag;
/*=======[V E R S I O N  C H E C K]===========================================*/
/* OsekNm.h head file check */
#if(OSEKNM_C_AR_MAJOR_VERSION != OSEKNM_H_AR_MAJOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Major Version"
#endif

#if(OSEKNM_C_AR_MINOR_VERSION != OSEKNM_H_AR_MINOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Minor Version"
#endif

#if(OSEKNM_C_AR_PATCH_VERSION != OSEKNM_H_AR_PATCH_VERSION)
    #error "OsekNm.c:Mismatch in Specification Patch Version"
#endif

#if(OSEKNM_C_SW_MAJOR_VERSION != OSEKNM_H_SW_MAJOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Major Version"
#endif

#if(OSEKNM_C_SW_MINOR_VERSION != OSEKNM_H_SW_MINOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Minor Version"
#endif

/* Nm_Cbk.h */
#if(1U != NM_CBK_H_AR_MAJOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Major Version"
#endif
#if(1U != NM_CBK_H_AR_MINOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Major Version"
#endif

/* CanIf.h */
#if(3U != CANIF_H_AR_MAJOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Major Version"
#endif

#if(2U != CANIF_H_AR_MINOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Minor Version"
#endif

#if(0U != CANIF_H_AR_PATCH_VERSION)
    #error "OsekNm.c:Mismatch in Specification Patch Version"
#endif

#if(1U != CANIF_H_SW_MAJOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Major Version"
#endif

#if(0U != CANIF_H_SW_MINOR_VERSION)
    #error "OsekNm.c:Mismatch in Specification Minor Version"
#endif
/*=======[M A C R O S]========================================================*/

#define OSEKNM_DATA_SIZE    6u
#define OSEKNM_TX_BUFFER_SIZE  9u
#define OSEKNM_RX_BUFFER_SIZE   10u

/* OSEKNM_MAX_NODE_BYTE = (OSEKNM_MAX_NODE_ID+7)/8 */
#define OSEKNM_MAX_NODE_BYTE 32u


#define OSEKNM_STATUS_MASK 0x01FFu
#define OSEKNM_STATUS_PRESENTCFG_STABLE    0x0001u
#define OSEKNM_STATUS_ERROR               0x0002u
/* default */
#define OSEKNM_STATUS_ACTIVE                    0x0004u
#define OSEKNM_STATUS_OFF                          0x0008u
#define OSEKNM_STATUS_LIMPHOME                0x0010u
#define OSEKNM_STATUS_BUSSLEEP                 0x0020u
#define OSEKNM_STATUS_TWBS                        0x0040u
/* default */
#define OSEKNM_STATUS_RINGDATA_NOTALLOWED   0x0080u
/* default */
#define OSEKNM_STATUS_NETWORKRLEASED    0x0100u

#define OSEKNM_STATUS_MERKER_STABLE      0x0200u
#define OSEKNM_STATUS_MERKER_LIMPHOME    0x0400u
#define OSEKNM_STATUS_REMOTE_SLEEPIND    0x0800u
#define OSEKNM_STATUS_REMOTE_SLEEPCHK    0x1000u


#define OSEKNM_EVENT_REQUEST 0x01u
#define OSEKNM_EVENT_TXFLAG 0x02u
#define OSEKNM_EVENT_RXIND  0x04u
#define OSEKNM_EVENT_TXCONF 0x08u

/* NMPDU encode index*/
#define OSEKNM_PDU_DESTID 0x00u
#define OSEKNM_PDU_OPCODE 0x01u
#define OSEKNM_PDU_RINGDATA 0x02u
#define OSEKNM_PDU_RINGDATA_LEN 0x08u
#define OSEKNM_PDU_SRCID 0x09u

/* OsekNm module State */
typedef enum
{
    OSEKNM_UNINIT,
    OSEKNM_INIT
}OsekNm_InitStateType;

typedef P2FUNC(void, OSEKNM_APPL_CODE, OsekNm_CbkFuncType) (NetworkHandleType nmChannelHandle);


typedef struct
{
    OsekNm_CbkFuncType cbk;
    uint16_least timer;
}OsekNm_TimerType;

/*=======[I N T E R N A L   D A T A]==========================================*/
#define OSEKNM_START_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
#include "OsekNm_MemMap.h"
/* OsekNm init states */
/*@req <OSEKNM190> */
#if (STD_ON == OSEKNM_DEV_ERROR_DETECT )
STATIC VAR(OsekNm_InitStateType, OSEKNM_VAR_POWER_ON_INIT) OsekNm_InitState = OSEKNM_UNINIT;
#endif /* #if (STD_ON == OSEKNM_DEV_ERROR_DETECT ) */

#define OSEKNM_STOP_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
#include "OsekNm_MemMap.h"

typedef struct 
{
    OsekNm_TimerType tStd;
    OsekNm_TimerType tTx;

    OsekNm_StatusType status;
    uint8 txCnt;
    uint8 rxCnt;

    OsekNm_ConfigRefType cfgNormal[OSEKNM_MAX_NODE_BYTE];
    OsekNm_ConfigRefType cfgLimpHome[OSEKNM_MAX_NODE_BYTE];

    Nm_StateType state;
    Nm_ModeType mode;

    OsekNm_EventType event;

    /* Tx NMPDU: DestId[Byte0], Opcode[Byte1], RingData[Byte2-7], RingDatalen[Byte8]*/
    uint8 txBuf[OSEKNM_TX_BUFFER_SIZE];

    /* Rx NMPDU: DestId[Byte0], Opcode[Byte1], RingData[Byte2-7], RingDatalen[Byte8], SrcId[Byte9]*/
    uint8 rxBuf[OSEKNM_RX_BUFFER_SIZE];

    #if(OSEKNM_RX_INTERRUPT == STD_ON)
    uint8 rxTempBuf[OSEKNM_RX_BUFFER_SIZE];
    #endif /* #if(OSEKNM_RX_INTERRUPT == STD_ON) */
}OsekNm_ChannelType;

#define OSEKNM_START_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
#include "OsekNm_MemMap.h"
VAR(OsekNm_ChannelType, OSEKNM_VAR) OsekNm_ChannelData[OSEKNM_NUMBER_OF_CHANNELS];

#define OSEKNM_STOP_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
#include "OsekNm_MemMap.h"
/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
/* define for get config data */
#define OSEKNM_CFG_NODE_ID(chId)    OsekNm_ConfigData[chId].NodeId
#define OSEKNM_CFG_CHANNEL_ID(chId) OsekNm_ConfigData[chId].ChannelIdRef
#define OSEKNM_CFG_CANSM_CHANNEL_ID(chId)   OsekNm_ConfigData[chId].RefSm
#define OSEKNM_CFG_TX_PDU_ID(chId)  OsekNm_ConfigData[chId].TxPduId

#define OSEKNM_CFG_TTYP(chId)   OsekNm_ConfigData[chId].TTyp
#define OSEKNM_CFG_TMAX(chId)   OsekNm_ConfigData[chId].TMax
#define OSEKNM_CFG_TERROR(chId) OsekNm_ConfigData[chId].TError
#define OSEKNM_CFG_TWBS(chId)   OsekNm_ConfigData[chId].TWbs
#if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
#define OSEKNM_CFG_TLIMPERR(chId)   OsekNm_ConfigData[chId].TLimpErr
#endif
#define OSEKNM_CFG_TTX(chId)    OsekNm_ConfigData[chId].TTx
#define OSEKNM_CFG_TX_LIMIT(chId)   OsekNm_ConfigData[chId].TxLimitCfg
#define OSEKNM_CFG_RX_LIMIT(chId)   OsekNm_ConfigData[chId].RxLimitCfg
#if(STD_ON == OSEKNM_BUSLOADREDUCTIONENABLED)
#define OSEKNM_CFG_TTYPOFFSET(chId) OsekNm_ConfigData[chId].MsgReducedTime
#endif
#if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED)
#define OSEKNM_CFG_TX_WAIT_CONF(chId)   OsekNm_ConfigData[chId].MsgTimeoutTime
#endif
#if(STD_ON == OSEKNM_USER_DATA_ENABLED)
#define OSEKNM_CFG_TX_DATA_LEN(chId) OsekNm_ConfigData[chId].UserDataLength
#endif

#define OSEKNM_CFG_RX_PROCESSING(chId)   OsekNm_ConfigData[chId].RxProcessing
#define OSEKNM_CFG_TX_PROCESSING(chId)   OsekNm_ConfigData[chId].TxProcessing

#define OSEKNM_CFG_RINGDATA_IND(chId)   OsekNm_ConfigData[chId].RingDataNotify

#define OSEKNM_CFG_LIMPHOME_IND(chId)   OsekNm_ConfigData[chId].LimpHomeNotify

/* NmIf Channel id Convert to OsekNm channel id */
#define OSEKNM_GET_CHANNEL_ID(chId) (chId - OSEKNM_CHANNEL_ID_OFFSET)

/* OsekNm channel id Convert to NmIf Channel id  */
#define OSEKNM_GET_CHANNEL_ID_OF_NMIF(chId) (chId + OSEKNM_CHANNEL_ID_OFFSET)

/* Timer */
#define OSEKNM_TSTD_START_TTYP(chId, tout) \
            do  \
            {   \
                OsekNm_ChannelData[chId].tStd.timer = tout; \
                OsekNm_ChannelData[chId].tStd.cbk = &OsekNm_TTyp;   \
            }while(0)

#define OSEKNM_TSTD_START_TMAX(chId, cbkfun)    \
        do  \
        {   \
            OsekNm_ChannelData[chId].tStd.timer = OSEKNM_CFG_TMAX(chId);    \
            OsekNm_ChannelData[chId].tStd.cbk = cbkfun; \
        }while(0)
#define OSEKNM_TSTD_START_TERROR(chId)  \
        do  \
        {   \
            OsekNm_ChannelData[chId].tStd.timer = OSEKNM_CFG_TERROR(chId);  \
            OsekNm_ChannelData[chId].tStd.cbk = &OsekNm_TError; \
        }while(0)
#define OSEKNM_TSTD_START_TWBS(chId)    \
        do  \
        {   \
            OsekNm_ChannelData[chId].tStd.timer = OSEKNM_CFG_TWBS(chId);    \
            OsekNm_ChannelData[chId].tStd.cbk = &OsekNm_BusSleepEnter;  \
        }while(0)


#define OSEKNM_TSTD_STOP(chId)  OsekNm_ChannelData[chId].tStd.timer = 0u


#define OSEKNM_TTX_START(chId, tout, cbkfun)    \
        do  \
        {   \
            OsekNm_ChannelData[chId].tTx.timer = tout;  \
            OsekNm_ChannelData[chId].tTx.cbk = cbkfun;  \
        }while(0)


#define OSEKNM_TTX_STOP(chId) OsekNm_ChannelData[chId].tTx.timer = 0u

/* Get Rx Pdu ID */
#define OSEKNM_RX_PDUID(pduId)  (pduId & 0xFFu)

/* Get Rx channel ID */
#define OSEKNM_RX_PDUID_CHANNELID(pduId)    ((pduId>>8u) & 0xFFu)

/* Nm Message */
#define OSEKNM_RXMSG_TYPE(chId)     OsekNm_ChannelData[chId].rxBuf[OSEKNM_PDU_OPCODE]
#define OSEKNM_RXMSG_DESTID(chId)   OsekNm_ChannelData[chId].rxBuf[OSEKNM_PDU_DESTID]
#define OSEKNM_RXMSG_SRCID(chId)    OsekNm_ChannelData[chId].rxBuf[OSEKNM_PDU_SRCID]

#define OSEKNM_LOGSUCCESSOR_SET(chId, val)  \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF); \
                OsekNm_ChannelData[chId].txBuf[OSEKNM_PDU_DESTID] = (val); \
                SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF); \
            }while(0)
            
#define OSEKNM_LOGSUCCESSORR_GET(chId)  OsekNm_ChannelData[chId].txBuf[OSEKNM_PDU_DESTID]
#define OSEKNM_TXMSG_SET_TYPE(chId, val) \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF); \
                OsekNm_ChannelData[chId].txBuf[OSEKNM_PDU_OPCODE] = (val); \
                SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF); \
            }while(0)
            
#define OSEKNM_TXMSG_TYPE(chId)     OsekNm_ChannelData[chId].txBuf[OSEKNM_PDU_OPCODE]

/* Status */
#define OSEKNM_STATUS_SET(chId, val) \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_STATUS); \
                OsekNm_ChannelData[chId].status |= (val); \
                SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_STATUS); \
            }while(0)

#define OSEKNM_STATUS_GET(chId) OsekNm_ChannelData[chId].status

#define OSEKNM_STATUS_CLR(chId, val) \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_STATUS); \
                OsekNm_ChannelData[chId].status &=  ~(val); \
                SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_STATUS); \
            }while(0)

/* Event */
#define OSEKNM_EVENT_GET(chId)  OsekNm_ChannelData[chId].event

#define OSEKNM_EVENT_SET(chId, val) \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_EVENT); \
                OsekNm_ChannelData[chId].event |= (val); \
                SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_EVENT); \
            }while(0)

#define OSEKNM_EVENT_CLR(chId, val)  \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_EVENT); \
                OsekNm_ChannelData[chId].event &= ~(val); \
                SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_EVENT); \
            }while(0)
            
#define OSEKNM_CLEAR_RINGDATA(chId) \
            do{ \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF); \
                OsekNm_ChannelData[chId].txBuf[OSEKNM_PDU_RINGDATA_LEN] = 0u; \
                SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF); \
            }while(0)
            
/* ConfigRef */
#define OSEKNM_CFGREF_SET(p, n)  ((p)[(n)>>3u] |=  (1u << ((n) & 7u)))
#define OSEKNM_CFGREF_CLR(p, n)  ((p)[(n)>>3u] &= ~(1u << ((n) & 7u)))
#define OSEKNM_CFGREF_ISSET(p, n) ((p)[(n)>>3u] & (1u << ((n) & 7u)))

#define OSEKNM_CFGREF_ADD_LIMPHOME(chId, nId)
#define OSEKNM_CFGREF_ADD_NORMAL(chId, nId)

/* Get State */
#define OSEKNM_CURSTATE_GET(chId) OsekNm_ChannelData[chId].state


#define OSEKNM_CURSTATE_SET(chId, st) OsekNm_ChannelData[chId].state = st

#define OSEKNM_CURMODE_GET(chId) OsekNm_ChannelData[chId].mode
#define OSEKNM_CURMODE_SET(chId, md) OsekNm_ChannelData[chId].mode = md

#define OSEKNM_TXCNT_INCREASE(chId) OsekNm_ChannelData[chId].txCnt++
#define OSEKNM_TXCNT_RESET(chId)    OsekNm_ChannelData[chId].txCnt = 0u
#define OSEKNM_TXCNT_GET(chId)  OsekNm_ChannelData[chId].txCnt

#define OSEKNM_RXCNT_INCREASE(chId) OsekNm_ChannelData[chId].rxCnt++
#define OSEKNM_RXCNT_RESET(chId) OsekNm_ChannelData[chId].rxCnt = 0u
#define OSEKNM_RXCNT_GET(chId) OsekNm_ChannelData[chId].rxCnt

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_BusSleepEnter(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_ResetEnter(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_NormalEnter(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_NormalStandard(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_NormalStandardRingMsg(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TTyp(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_RemoteSleepCancel(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_LimpHomeEnter(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_LimpHomeStandard(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TError(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TLimpErr(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_ReadySleepEnter(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_PrepareBusSleepEnter(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_ReqProcess(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_RxIndProcess(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TxConfProcess(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(boolean, OSEKNM_CODE)
OsekNm_IsSkipped(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_DetermineLogicalSuccessor(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TxTimeout(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_SendMessage(NetworkHandleType nmChannelHandle);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(uint8, OSEKNM_CODE)
OsekNm_GetMsgType(uint8 OpCode);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(boolean , OSEKNM_CODE)
OsekNm_IsSleepIndSet(uint8 OpCode);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(boolean , OSEKNM_CODE)
OsekNm_IsSleepAckSet(uint8 OpCode);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/* Memcpy and Memset for OsekNm Module  */
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_Memcpy
(
    P2VAR(uint8, AUTOMATIC, AUTOMATIC) dest,
    P2CONST(uint8, AUTOMATIC, AUTOMATIC) src,
    uint8 count
);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_Memset
(
        P2VAR(uint8, AUTOMATIC, AUTOMATIC) s,
        const uint8 c,
        uint8 n
);
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
/*************************************************************************/
/*
 * Brief               Initialize the complete OsekNm module.
 * ServiceId           0x00
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      oseknmConfigPtr: Pointer to a selected configuration structure
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(void, OSEKNM_CODE)
OsekNm_Init(void)
{
    NetworkHandleType chId = 0u;

    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    OsekNm_InitState = OSEKNM_INIT;
    #endif

    for(chId=0u; chId < OSEKNM_NUMBER_OF_CHANNELS; chId++)
    {
        /* Initialize runtime structture */
        OsekNm_ChannelData[chId].tStd.timer = 0u;
        OsekNm_ChannelData[chId].tStd.cbk = NULL_PTR;

        OsekNm_ChannelData[chId].tTx.timer = 0u;
        OsekNm_ChannelData[chId].tTx.cbk = NULL_PTR;

        OsekNm_ChannelData[chId].status = (OSEKNM_STATUS_ACTIVE\
            |OSEKNM_STATUS_RINGDATA_NOTALLOWED|OSEKNM_STATUS_NETWORKRLEASED);
        OSEKNM_TXCNT_RESET(chId);
        OSEKNM_RXCNT_RESET(chId);
        OsekNm_Memset(OsekNm_ChannelData[chId].cfgNormal, 0x0u, OSEKNM_MAX_NODE_BYTE);
        OsekNm_Memset(OsekNm_ChannelData[chId].cfgLimpHome, 0x0u, OSEKNM_MAX_NODE_BYTE);

        OsekNm_ChannelData[chId].mode = NM_MODE_BUS_SLEEP;
        OsekNm_ChannelData[chId].event = 0u;
        OsekNm_Memset(&OsekNm_ChannelData[chId].txBuf[OSEKNM_PDU_DESTID], 0u, OSEKNM_TX_BUFFER_SIZE);
        OsekNm_Memset(&OsekNm_ChannelData[chId].rxBuf[OSEKNM_PDU_DESTID], 0u, OSEKNM_RX_BUFFER_SIZE);

        #if(STD_ON == OSEKNM_RX_INTERRUPT)
        OsekNm_Memset(&OsekNm_ChannelData[chId].rxTempBuf[OSEKNM_PDU_DESTID], 0u, OSEKNM_RX_BUFFER_SIZE);
        #endif /* #if(STD_ON == OSEKNM_RX_INTERRUPT) */

        /*@req <OSEKNM191> */
        OSEKNM_CURSTATE_SET(chId, NM_STATE_BUS_SLEEP);
        OsekNm_BusSleepEnter(chId);
    }

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Passive startup of the OSEK NM
 * ServiceId           0x01
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_PassiveStartUp(NetworkHandleType nmChannelHandle)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_PASSIVESTARTUP_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_PASSIVESTARTUP_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    if(NM_STATE_BUS_SLEEP != OSEKNM_CURSTATE_GET(nmChannelId))
    {
        ret = NM_E_NOT_EXECUTED;
    }
    else
    {
        /*@req <OSEKNM133>  <OSEKNM134>*/
        /* the Bus-Sleep Mode shall be left and the Network Mode shall be entered; */
        OSEKNM_EVENT_SET(nmChannelId, OSEKNM_EVENT_REQUEST);
        ret = NM_E_OK;
    }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Request the network, since ECU needs to communicate on the bus.
 * ServiceId           0x02
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_NetworkRequest(NetworkHandleType nmChannelHandle)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_NETWORKREQUEST_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_NETWORKREQUEST_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    if((OSEKNM_STATUS_NETWORKRLEASED & OSEKNM_STATUS_GET(nmChannelId)) != OSEKNM_STATUS_NETWORKRLEASED)
    {
        ret =  NM_E_OK;
    }
    else
    {
        OSEKNM_STATUS_CLR(nmChannelId, OSEKNM_STATUS_NETWORKRLEASED);
        OSEKNM_EVENT_SET(nmChannelId, OSEKNM_EVENT_REQUEST);
        ret =  NM_E_OK;
    }

    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Release the network, since ECU doesn't have to communicate on the bus.
 * ServiceId           0x03
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_NetworkRelease(NetworkHandleType nmChannelHandle)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_NETWORKRELEASE_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_NETWORKRELEASE_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    if((OSEKNM_STATUS_NETWORKRLEASED & OSEKNM_STATUS_GET(nmChannelId)) == OSEKNM_STATUS_NETWORKRLEASED)
    {
        ret =  NM_E_OK;
    }

    else
    {
        OSEKNM_STATUS_SET(nmChannelId, OSEKNM_STATUS_NETWORKRLEASED);
        if((OSEKNM_EVENT_REQUEST & OSEKNM_EVENT_GET(nmChannelId)) == OSEKNM_EVENT_REQUEST)
        {
            /* request without done, clear request */
            OSEKNM_EVENT_CLR(nmChannelId, OSEKNM_EVENT_REQUEST);
        }
        else
        {
            OSEKNM_EVENT_SET(nmChannelId, OSEKNM_EVENT_REQUEST);
        }
        ret = NM_E_OK;
    }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Disable the NM PDU transmission ability due to a ISO14229
 *              Communication Control (28hex) service.
 * ServiceId           0x0c
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
/*@req <OSEKNM223> */
#if(STD_ON == OSEKNM_COM_CONTROL_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_DisableCommunication(NetworkHandleType nmChannelHandle)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_DISABLECOMMUNICATION_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_DISABLECOMMUNICATION_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
    #endif

    /*@req <OSEKNM227> */
    /* if the current mode is not NmAwake,*/
    if((OSEKNM_STATUS_ACTIVE & OSEKNM_STATUS_GET(nmChannelId)) != OSEKNM_STATUS_ACTIVE)
    {
        ret = NM_E_OK;
    }
    else
    {
        /*@req <OSEKNM225> */
        /* Enter NmPassive state */
        OSEKNM_STATUS_CLR(nmChannelId, OSEKNM_STATUS_ACTIVE);
        ret = NM_E_OK;
    }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Enable the NM PDU transmission ability due to a ISO14229
 *              Communication Control (28hex) service.
 * ServiceId           0x0d
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_EnableCommunication(NetworkHandleType nmChannelHandle)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_ENABLECOMMUNICATION_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_ENABLECOMMUNICATION_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */
    if((OSEKNM_STATUS_ACTIVE & OSEKNM_STATUS_GET(nmChannelId)) == OSEKNM_STATUS_ACTIVE)
    {
        ret = NM_E_OK;
    }
    else
    {
        /*@req <OSEKNM230> */
        /*  Set NmActive state */
        OSEKNM_STATUS_SET(nmChannelId, OSEKNM_STATUS_ACTIVE);
        ret = NM_E_OK;
    }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_COM_CONTROL_ENABLED) */

/*************************************************************************/
/*
 * Brief               Set user data for NM messages transmitted next on the bus.
 * ServiceId           0x04
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[in]      nmUserDataPtr: Pointer where the user data for the
 *                  next transmitted NM message shall be copied from
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_USER_DATA_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_SetUserData(NetworkHandleType nmChannelHandle,
CONSTP2CONST(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmUserDataPtr)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_SETUSERDATA_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_SETUSERDATA_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if(NULL_PTR == nmUserDataPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_SETUSERDATA_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif

    if((OSEKNM_STATUS_RINGDATA_NOTALLOWED & OSEKNM_STATUS_GET(nmChannelId)) == OSEKNM_STATUS_RINGDATA_NOTALLOWED)
    {
        ret = NM_E_NOT_OK;
    }
    else
    {
        SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
        /* Copy User data to NmPDU tx */
         OsekNm_Memcpy(&OsekNm_ChannelData[nmChannelId].txBuf[OSEKNM_PDU_RINGDATA], (const uint8 *)nmUserDataPtr, \
                OSEKNM_CFG_TX_DATA_LEN(nmChannelId));
         OsekNm_ChannelData[nmChannelId].txBuf[OSEKNM_PDU_RINGDATA_LEN] = OSEKNM_CFG_TX_DATA_LEN(nmChannelId);
         SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
         ret = NM_E_OK;
    }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get user data out of the most recently received NM message.
 * ServiceId           0x05
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[in]      nmUserDataPtr: Pointer where user data out of the most recently
 *                  received NM message shall be copied to
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetUserData(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmUserDataPtr)
{
    Nm_ReturnType ret = NM_E_NOT_OK;
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETUSERDATA_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETUSERDATA_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if(NULL_PTR == nmUserDataPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETUSERDATA_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    if((OSEKNM_STATUS_PRESENTCFG_STABLE & OSEKNM_STATUS_GET(nmChannelId)) != OSEKNM_STATUS_PRESENTCFG_STABLE)
    {
        ret = NM_E_NOT_OK;
    }
    else if(0u == OsekNm_ChannelData[nmChannelId].txBuf[OSEKNM_PDU_RINGDATA_LEN])
    {
        ret = NM_E_NOT_OK;
    }
    else
    {
        SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
        OsekNm_Memcpy(nmUserDataPtr, &OsekNm_ChannelData[nmChannelId].txBuf[OSEKNM_PDU_RINGDATA], \
             OSEKNM_CFG_TX_DATA_LEN(nmChannelId));
        SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);         
        ret = NM_E_OK;
    }

    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_USER_DATA_ENABLED) */

/*************************************************************************/
/*
 * Brief               Get node identifier out of the most recently received NM PDU.
 * ServiceId           0x06
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier out of the most recently
 *                      received NM PDU shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetNodeIdentifier(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmNodeIdPtr)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETNODEIDENTIFIER_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETNODEIDENTIFIER_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if(NULL_PTR == nmNodeIdPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETNODEIDENTIFIER_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */
    SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
    *nmNodeIdPtr = OsekNm_ChannelData[nmChannelId].rxBuf[OSEKNM_PDU_SRCID];
    SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get node identifier configured for the local node.
 * ServiceId           0x07
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     nmNodeIdPtr:Pointer where node identifier of the local node
 *                                  shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetLocalNodeIdentifier(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmNodeIdPtr)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETLOCALNODEIDENTIFIER_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETLOCALNODEIDENTIFIER_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if(NULL_PTR == nmNodeIdPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETLOCALNODEIDENTIFIER_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    *nmNodeIdPtr = OSEKNM_CFG_NODE_ID(nmChannelId);

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Set Repeat Message Request Bit for NMmessages transmitted next on the bus.
 * ServiceId           0x08
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_RepeatMessageRequest(NetworkHandleType nmChannelHandle)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_REPEATMESSAGEREQUEST_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_REPEATMESSAGEREQUEST_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Get the whole PDU data out of the most recently received NM message.
 * ServiceId           0x0a
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     nmPduDataPtr:Pointer where NM PDU shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetPduData(NetworkHandleType nmChannelHandle,
P2VAR(uint8, AUTOMATIC, OSEKNM_APPL_DATA) nmPduDataPtr)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETPDUDATA_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETPDUDATA_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if(NULL_PTR == nmPduDataPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETPDUDATA_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */
    SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
    OsekNm_Memcpy(nmPduDataPtr, &OsekNm_ChannelData[nmChannelId].rxBuf[OSEKNM_PDU_DESTID],
        (OsekNm_ChannelData[nmChannelId].rxBuf[OSEKNM_PDU_RINGDATA_LEN] + 2));
    SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
    return NM_E_OK;
}


#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Returns the state and the mode of the network management.
 * ServiceId           0x0b
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     nmStatePtr:Pointer where state of the network management
 *                  shall be copied to
 * Param-Name[out]     nmModePtr:Pointer where the mode of the network management
 *                  shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetState(NetworkHandleType nmChannelHandle,
P2VAR(Nm_StateType, AUTOMATIC, OSEKNM_APPL_DATA) nmStatePtr,
P2VAR(Nm_ModeType, AUTOMATIC, OSEKNM_APPL_DATA) nmModePtr)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETSTATE_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETSTATE_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if((NULL_PTR == nmStatePtr) || (NULL_PTR == nmModePtr))
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETSTATE_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    *nmStatePtr = OSEKNM_CURSTATE_GET(nmChannelId);
    *nmModePtr = OSEKNM_CURMODE_GET(nmChannelId);

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Request bus synchronization..
 * ServiceId           0xc0
 * Sync/Async          Asynchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_BUS_SYNCHRONIZATION_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_RequestBusSynchronization(NetworkHandleType nmChannelHandle)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_REQUESTBUSSYNCHRONIZATION_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_REQUESTBUSSYNCHRONIZATION_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif

/*************************************************************************/
/*
 * Brief               Check if remote sleep indication takes place or not.
 * ServiceId           0x08
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     nmRemoteSleepIndPtr:Pointer where check result of remote sleep
 *                  indication shall be copied to
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK,NM_E_NOT_EXECUTED
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_CheckRemoteSleepIndication(NetworkHandleType nmChannelHandle,
P2VAR(boolean, AUTOMATIC, OSEKNM_APPL_DATA) nmRemoteSleepIndPtr)
{
    NetworkHandleType nmChannelId = 0u;
       Nm_ReturnType ret = NM_E_NOT_OK;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_CHECKREMOTESLEEPINDICATION_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_CHECKREMOTESLEEPINDICATION_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }
        if(NULL_PTR == nmRemoteSleepIndPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_CHECKREMOTESLEEPINDICATION_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

       if((NM_MODE_BUS_SLEEP == OSEKNM_CURMODE_GET(nmChannelId))
            || (NM_MODE_PREPARE_BUS_SLEEP == OSEKNM_CURMODE_GET(nmChannelId))
            || (NM_STATE_REPEAT_MESSAGE == OSEKNM_CURSTATE_GET(nmChannelId)))
       {
            ret = NM_E_NOT_EXECUTED;
       }
       else
       {
            if((OSEKNM_STATUS_REMOTE_SLEEPIND & OSEKNM_STATUS_GET(nmChannelId)) == OSEKNM_STATUS_REMOTE_SLEEPIND)
            {
                *nmRemoteSleepIndPtr = TRUE;
            }
            else
            {
                *nmRemoteSleepIndPtr = FALSE;
            }
               ret = NM_E_OK;
       }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */

/*************************************************************************/
/*
 * Brief               This service provides the actual configuration of the kind specified
 *              by <ConfigKind>.
 * ServiceId           0xe0
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[in]      pudConfig: Kind of configuration
 * Param-Name[out]     udConfigKind:Configuration inquired
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetConfig(NetworkHandleType nmChannelHandle,
P2VAR(OsekNm_ConfigRefType, AUTOMATIC, OSEKNM_VAR) pudConfig,
OsekNm_ConfigKindNameType udConfigKind)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETCONFIG_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

     if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETCONFIG_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

     if(NULL_PTR == pudConfig)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETCONFIG_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
     if(udConfigKind > OSEKNM_CFG_KIND_LIMPHOME)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETCONFIG_ID, OSEKNM_E_INVALID_PARA);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    switch(udConfigKind)
    {
        case OSEKNM_CFG_KIND_NORMAL:
            OsekNm_Memcpy((uint8 *)pudConfig,
                    (const uint8 *)OsekNm_ChannelData[nmChannelId].cfgNormal,
                    OSEKNM_MAX_NODE_BYTE);
            break;

        case OSEKNM_CFG_KIND_LIMPHOME:
            OsekNm_Memcpy((uint8 *)pudConfig,
                    (const uint8 *)OsekNm_ChannelData[nmChannelId].cfgLimpHome,
                    OSEKNM_MAX_NODE_BYTE);
            break;

        default:
            break;
    }

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"


/*************************************************************************/
/*
 * Brief               This service provides the current status of the network.
 * ServiceId           0xf2
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]      pudNetworksStatus: Requested Stauts of the node
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Nm_ReturnType:NM_E_OK,NM_E_NOT_OK
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(Nm_ReturnType, OSEKNM_CODE)
OsekNm_GetStatus(NetworkHandleType nmChannelHandle,
P2VAR(OsekNm_NetWorkStatusType, AUTOMATIC, OSEKNM_VAR) pudNetworksStatus)
{
    NetworkHandleType nmChannelId = 0u;

    nmChannelId = OSEKNM_GET_CHANNEL_ID(nmChannelHandle);
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETSTATUS_ID, OSEKNM_E_NO_INIT);
        return NM_E_NOT_OK;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETSTATUS_ID, OSEKNM_E_INVALID_CHANNEL);
        return NM_E_NOT_OK;
    }

    if(NULL_PTR == pudNetworksStatus)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_GETSTATUS_ID, OSEKNM_E_NULL_POINTER);
        return NM_E_NOT_OK;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    *pudNetworksStatus = OSEKNM_STATUS_GET(nmChannelId);

    return NM_E_OK;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"



/*************************************************************************/
/*
 * Brief               This service confirms a previous successfully processed CAN transmit request.
 * ServiceId           0x0f
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not within the same Channel)
 * Param-Name[in]       OsekNmTxPduId: Identification ofthe network through PDU-ID
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_TxConfirmation(PduIdType OsekNmTxPduId)
{

    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_TXCONFIRMATION_ID, OSEKNM_E_NO_INIT);
        return;
    }

    if(OsekNmTxPduId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_TXCONFIRMATION_ID, OSEKNM_E_INVALID_CHANNEL);
        return;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    OSEKNM_EVENT_CLR(OsekNmTxPduId, OSEKNM_EVENT_TXFLAG);

    #if(STD_ON == OSEKNM_TX_INTERRUPT)
    if(OSEKNM_PROCESS_TYPE_INTERRUPT == OSEKNM_CFG_TX_PROCESSING(OsekNmTxPduId))
    {
        OSEKNM_EVENT_SET(OsekNmTxPduId, OSEKNM_EVENT_TXCONF);
    }
    #else
    {
        OsekNm_TxConfProcess(OsekNmTxPduId);
    }
    #endif /* #if(STD_ON == OSEKNM_TX_INTERRUPT) */

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED) */

/*************************************************************************/
/*
 * Brief               This service indicates a successful reception of a received NM message
 *              to the OsekNm after passing all filters and validation checks.
 * ServiceId           0x10
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant (but not within the same Channel)
 * Param-Name[in]       OsekNmRxPduId: Identification ofthe network through PDU-ID
 * Param-Name[in]       PduInfoPtr: Contains the length (SduLength) of the received I-PDU
 *                  and a pointer to a buffer (SduDataPtr) containing the I-PDU.
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/

#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_RxIndication(PduIdType OsekNmRxPduId,
P2CONST(PduInfoType, AUTOMATIC, AUTOMATIC) PduInfoPtr)
{
    NetworkHandleType nmChannelHandle = 0u;

    /* Get Rx channel ID */
    nmChannelHandle = OSEKNM_RX_PDUID_CHANNELID(OsekNmRxPduId);

    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_RXINDICATION_ID, OSEKNM_E_NO_INIT);
        return;
    }

    if(nmChannelHandle >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_RXINDICATION_ID, OSEKNM_E_INVALID_CHANNEL);
        return;
    }
    if(NULL_PTR == PduInfoPtr)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_RXINDICATION_ID, OSEKNM_E_NULL_POINTER);
        return;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    /* Ring msg && To me && event.TxFlag ==1 */
    if((OSEKNM_MSG_RING == PduInfoPtr->SduDataPtr[OSEKNM_PDU_OPCODE])
        && (OSEKNM_CFG_NODE_ID(nmChannelHandle) == PduInfoPtr->SduDataPtr[OSEKNM_PDU_DESTID])
        && ((OSEKNM_EVENT_TXFLAG & OSEKNM_EVENT_GET(nmChannelHandle)) == OSEKNM_EVENT_TXFLAG))
    {
        /* Implement is NULL */
        ;
    }
    else
    {
        #if(STD_ON == OSEKNM_RX_INTERRUPT)
        if(OSEKNM_PROCESS_TYPE_INTERRUPT == OSEKNM_CFG_RX_PROCESSING(nmChannelHandle))
        {
            SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
            /* Save SrcId to rxTmpBuf */
            OsekNm_ChannelData[nmChannelHandle].rxTempBuf[OSEKNM_PDU_SRCID] = (uint8)(OsekNmRxPduId&0xFF);
            /* Copy Data to rxTmpBuf */
            OsekNm_Memcpy(&OsekNm_ChannelData[nmChannelHandle].rxTempBuf[OSEKNM_PDU_DESTID], PduInfoPtr->SduDataPtr, \
                   PduInfoPtr->SduLength);
            OsekNm_ChannelData[nmChannelHandle].rxTempBuf[OSEKNM_PDU_RINGDATA_LEN] =  PduInfoPtr->SduLength -2;
            SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
            /* Set EventRxInd */
            OSEKNM_EVENT_SET(nmChannelHandle, OSEKNM_EVENT_RXIND);
        }
        else 
        #endif /* #if(STD_ON == OSEKNM_RX_INTERRUPT) */
        {
            SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
            /* Save SrcId to rxBuf */
            OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_SRCID] = (uint8)(OsekNmRxPduId&0xFF);
            /* Copy Data to rxBuf */
            OsekNm_Memcpy(&OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_DESTID], PduInfoPtr->SduDataPtr, \
                   PduInfoPtr->SduLength);
            OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_RINGDATA_LEN] = PduInfoPtr->SduLength - 2;
            OsekNm_RxIndProcess(nmChannelHandle);
            SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
        }
        
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Indication that bus off event has occurred.
 * ServiceId           0x15
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]       nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_BusOff(NetworkHandleType nmChannelHandle)
{
    NetworkHandleType nmChannelId = OSEKNM_NUMBER_OF_CHANNELS;
    NetworkHandleType chId = 0u;

    /* Find OsekNm channel Id */
    for(chId=0; chId < OSEKNM_NUMBER_OF_CHANNELS; chId++)
    {
        if(nmChannelHandle == OSEKNM_CFG_CANSM_CHANNEL_ID(chId))
        {
            nmChannelId = chId;
        }
    }

    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_BUSOFF_ID, OSEKNM_E_NO_INIT);
        return;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_BUSOFF_ID, OSEKNM_E_INVALID_CHANNEL);
        return;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    /* Set Status.Error =1 and Merker_Limphome = 0 */
    OSEKNM_STATUS_SET(nmChannelId, OSEKNM_STATUS_ERROR);
    OSEKNM_STATUS_CLR(nmChannelId, OSEKNM_STATUS_MERKER_LIMPHOME);
    /* Enter Limphome */
    OsekNm_LimpHomeEnter(nmChannelId);

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Indication that bus off event try Recovery.
 * ServiceId           0x15
 * Sync/Async          Asynchronous
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]       nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
extern FUNC(void, OSEKNM_CODE)
OsekNm_BusOffRecovery(NetworkHandleType nmChannelHandle)
{
    NetworkHandleType nmChannelId = OSEKNM_NUMBER_OF_CHANNELS;
    NetworkHandleType chId = 0u;

    /* Find OsekNm channel Id */
    for(chId = 0; chId < OSEKNM_NUMBER_OF_CHANNELS; chId++)
    {
        if(nmChannelHandle == OSEKNM_CFG_CANSM_CHANNEL_ID(chId))
        {
            nmChannelId = chId;
        }
    }
    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_BUSOFFRECOVERY_ID, OSEKNM_E_NO_INIT);
        return;
    }

    if(nmChannelId >= OSEKNM_NUMBER_OF_CHANNELS)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_BUSOFFRECOVERY_ID, OSEKNM_E_INVALID_CHANNEL);
        return;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */
    if((OSEKNM_STATUS_ERROR & OSEKNM_STATUS_GET(nmChannelId)) == OSEKNM_STATUS_ERROR)
    {
        /* Set Status.Error =0 */
        OSEKNM_STATUS_CLR(nmChannelId, OSEKNM_STATUS_ERROR);
        /* Enter OsekNm_TError */
        OsekNm_TError(nmChannelId);
    }

    return;
}

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               Main function of the OsekNm which processes the algorithm
 * ServiceId
 * Sync/Async          Synchronous
 * Reentrancy          Reentrant
 * Param-Name[in]      None
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None
 * PreCondition        None
 * CallByAPI           Up layer
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
FUNC(void, OSEKNM_CODE)
OsekNm_MainFunction(void)
{
    NetworkHandleType nmChannelHandle = 0u;

    #if(STD_ON == OSEKNM_DEV_ERROR_DETECT)
    if(OSEKNM_UNINIT == OsekNm_InitState)
    {
        Det_ReportError(OSEKNM_MODULE_ID, OSEKNM_INSTANCE_ID, OSEKNM_MAINFUNCTION_ID, OSEKNM_E_NO_INIT);
        return;
    }
    #endif /* #if(STD_ON == OSEKNM_DEV_ERROR_DETECT) */

    for(nmChannelHandle = 0u; nmChannelHandle < OSEKNM_NUMBER_OF_CHANNELS; nmChannelHandle++)
    {
        if(OsekNm_ChannelData[nmChannelHandle].tStd.timer > 0u)
        {
            OsekNm_ChannelData[nmChannelHandle].tStd.timer--;
            if(0 == OsekNm_ChannelData[nmChannelHandle].tStd.timer)
            {
                (OsekNm_ChannelData[nmChannelHandle].tStd.cbk)(nmChannelHandle);
            }
        }
        if(OsekNm_ChannelData[nmChannelHandle].tTx.timer > 0u)
        {
            OsekNm_ChannelData[nmChannelHandle].tTx.timer--;
            if(0 == OsekNm_ChannelData[nmChannelHandle].tTx.timer)
            {
                (OsekNm_ChannelData[nmChannelHandle].tTx.cbk)(nmChannelHandle);
            }
        }
        if((OSEKNM_EVENT_REQUEST & OSEKNM_EVENT_GET(nmChannelHandle)) == OSEKNM_EVENT_REQUEST)
        {
            OsekNm_ReqProcess(nmChannelHandle);
            OSEKNM_EVENT_CLR(nmChannelHandle, OSEKNM_EVENT_REQUEST);
        }
            #if(STD_ON == OSEKNM_RX_INTERRUPT)
        if((OSEKNM_EVENT_RXIND & OSEKNM_EVENT_GET(nmChannelHandle)) == OSEKNM_EVENT_RXIND)
        {
            SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);
            /* Copy Data From RxtempBuf to rxBuf */
            OsekNm_Memcpy(&OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_DESTID],
                                        &OsekNm_ChannelData[nmChannelHandle].rxTempBuf[OSEKNM_PDU_DESTID],
                                        OSEKNM_RX_BUFFER_SIZE);
            SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_RXBUF);                            
            OSEKNM_EVENT_CLR(nmChannelHandle, OSEKNM_EVENT_RXIND);
            OsekNm_RxIndProcess(nmChannelHandle);
        }
            #endif /* #if(STD_ON == OSEKNM_RX_INTERRUPT) */
            #if(STD_ON == OSEKNM_TX_INTERRUPT)
        if((OSEKNM_EVENT_TXCONF & OSEKNM_EVENT_GET(nmChannelHandle)) == OSEKNM_EVENT_TXCONF)
        {
            OsekNm_TxConfProcess(nmChannelHandle);
            OSEKNM_EVENT_CLR(nmChannelHandle, OSEKNM_EVENT_TXCONF);
        }
            #endif /* #if(STD_ON == OSEKNM_TX_INTERRUPT) */
    }
    return;
}

#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*======[I N T E R N A L  F U N C T I O N   I M P L E M E N T A T I O N S]====*/

/******************************************************************************/
/*
 * Brief               <Enter Bus-Sleep Mode>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_BusSleepEnter(NetworkHandleType nmChannelHandle)
{

    OSEKNM_RXCNT_RESET(nmChannelHandle);
    OSEKNM_TXCNT_RESET(nmChannelHandle);

    /*@req <OSEKNM131> */
    /* When Bus-Sleep Mode is entered, the NM shall notify the upper layer */
    if(NM_MODE_BUS_SLEEP != OSEKNM_CURMODE_GET(nmChannelHandle))
    {
        OSEKNM_CURMODE_SET(nmChannelHandle, NM_MODE_BUS_SLEEP);
        /*为了兼容蓝牙和TSP连接，T-Box没有真正进入睡眠，需要屏蔽这个函数，保持CAN数据接收后进入建环动作
            liaoyonggang 20190606 修改*/
        //Nm_BusSleepMode(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
        OsekNm_UserHandleEvenSet(HANDLE_REQUEST_CAN_DISABLE);
        #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED)
        Nm_StateChangeNotification(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle), OSEKNM_CURSTATE_GET(nmChannelHandle), NM_STATE_BUS_SLEEP);
        #endif /* #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED) */
        OSEKNM_CURSTATE_SET(nmChannelHandle, NM_STATE_BUS_SLEEP);        
    }

    OSEKNM_TSTD_STOP(nmChannelHandle);
    OSEKNM_TTX_STOP(nmChannelHandle);

    OSEKNM_STATUS_CLR(nmChannelHandle, (OSEKNM_STATUS_LIMPHOME|OSEKNM_STATUS_TWBS));
    OSEKNM_STATUS_SET(nmChannelHandle, OSEKNM_STATUS_BUSSLEEP|OSEKNM_STATUS_RINGDATA_NOTALLOWED);

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Enter NmReset State>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_ResetEnter(NetworkHandleType nmChannelHandle)
{
    /*  from Bus-Sleep Mode  */
    if(NM_MODE_NETWORK != OSEKNM_CURMODE_GET(nmChannelHandle))
    {
        OSEKNM_CURMODE_SET(nmChannelHandle, NM_MODE_NETWORK);
        /*@req <OSEKNM137> */
        Nm_NetworkMode(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
    }

    /*@req <OSEKNM138> */
    /* NmReset State is entered from NmLimphome, NMrxcount and NMtxcount shall be cleared */
    if((OSEKNM_STATUS_LIMPHOME & OSEKNM_STATUS_GET(nmChannelHandle)) == OSEKNM_STATUS_LIMPHOME)
    {
        if(NULL_PTR != OSEKNM_CFG_LIMPHOME_IND(nmChannelHandle))
        {
          OSEKNM_CFG_LIMPHOME_IND(nmChannelHandle)(FALSE);
        }
        #if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
        Dem_ReportErrorStatus(OSEKNM_E_LIMPHOME, DEM_EVENT_STATUS_PASSED);
        #endif /* #if(STD_ON == OSEKNM_DEM_ERROR_DETECT) */
        OSEKNM_RXCNT_RESET(nmChannelHandle);
        OSEKNM_TXCNT_RESET(nmChannelHandle);
    }

    #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle),
        OSEKNM_CURSTATE_GET(nmChannelHandle), NM_STATE_REPEAT_MESSAGE);
    #endif /* #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED) */

    OSEKNM_CURSTATE_SET(nmChannelHandle, NM_STATE_REPEAT_MESSAGE);

    /*@req <OSEKNM139> */
    /* Reset the system specific default configuration */
    OSEKNM_STATUS_CLR(nmChannelHandle, (OSEKNM_STATUS_OFF|OSEKNM_STATUS_LIMPHOME|OSEKNM_STATUS_TWBS|OSEKNM_STATUS_BUSSLEEP));
    OSEKNM_STATUS_CLR(nmChannelHandle, (OSEKNM_STATUS_PRESENTCFG_STABLE |OSEKNM_STATUS_MERKER_STABLE |OSEKNM_STATUS_MERKER_LIMPHOME));

    OSEKNM_STATUS_SET(nmChannelHandle, OSEKNM_STATUS_RINGDATA_NOTALLOWED);

    OSEKNM_TTX_STOP(nmChannelHandle);
    OSEKNM_TSTD_STOP(nmChannelHandle);

    /*@req <OSEKNM140> */
    /*  Initialize the NMPDU */
    OsekNm_Memset(OsekNm_ChannelData[nmChannelHandle].cfgNormal, 0x0u, OSEKNM_MAX_NODE_BYTE);
    OsekNm_Memset(OsekNm_ChannelData[nmChannelHandle].cfgLimpHome, 0x0u, OSEKNM_MAX_NODE_BYTE);

    SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
    OsekNm_Memset(&OsekNm_ChannelData[nmChannelHandle].txBuf[OSEKNM_PDU_DESTID], 0u, OSEKNM_TX_BUFFER_SIZE);
    SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
    
    OSEKNM_CFGREF_SET(OsekNm_ChannelData[nmChannelHandle].cfgNormal, OSEKNM_CFG_NODE_ID(nmChannelHandle));

    /*@req <OSEKNM141> */
    /*   Increment the reception counter */
    OSEKNM_RXCNT_INCREASE(nmChannelHandle);

    OSEKNM_LOGSUCCESSOR_SET(nmChannelHandle, OSEKNM_CFG_NODE_ID(nmChannelHandle));

    /*@req <OSEKNM142> */
    /* Send an alive message with a cleared bit sleep.ind and increment the transmission counter */
    if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
    {
        OsekNetworkStatusSet(0x01);
        OSEKNM_TXMSG_SET_TYPE(nmChannelHandle, OSEKNM_MSG_ALIVE);
        OSEKNM_TXCNT_INCREASE(nmChannelHandle);
        OsekNm_SendMessage(nmChannelHandle);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "osek nmsta 0x%02x\r\n", OsekNm_GetCurrentState());
    }

    /*@req <OSEKNM143> */
    /* NMtxcount > tx_limit or NMrxcount > rx_limit enter NMLimpHome */
    if((OSEKNM_TXCNT_GET(nmChannelHandle) > OSEKNM_CFG_TX_LIMIT(nmChannelHandle)) || \
        (OSEKNM_RXCNT_GET(nmChannelHandle) > OSEKNM_CFG_RX_LIMIT(nmChannelHandle)))
    {
        OsekNm_LimpHomeEnter(nmChannelHandle);
    }
    else
    {
        OsekNm_NormalEnter(nmChannelHandle);
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Enter Normal Status>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_NormalEnter(NetworkHandleType nmChannelHandle)
{
    /*@req <OSEKNM145> */
    /* When the NmNormal state is entered from NmReset state, */
    if(NM_STATE_REPEAT_MESSAGE == OSEKNM_CURSTATE_GET(nmChannelHandle))
    {
        /* the timer TTyp shall be started to transmit the 1st ring message */
         #if(STD_ON == OSEKNM_BUSLOADREDUCTIONENABLED)
        OSEKNM_TSTD_START_TTYP(nmChannelHandle, \
            (OSEKNM_CFG_TTYP(nmChannelHandle) + OSEKNM_CFG_TTYPOFFSET(nmChannelHandle)));
       #else
       OSEKNM_TSTD_START_TTYP(nmChannelHandle,OSEKNM_CFG_TTYP(nmChannelHandle));
       #endif /* #if(STD_ON == OSEKNM_BUSLOADREDUCTIONENABLED) */
    }

    #if(OSEKNM_STATE_CHANGE_IND_ENABLED == STD_ON)
    Nm_StateChangeNotification(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle),
    OSEKNM_CURSTATE_GET(nmChannelHandle),NM_STATE_NORMAL_OPERATION);
    #endif /* #if(OSEKNM_STATE_CHANGE_IND_ENABLED == STD_ON) */

    OSEKNM_CURSTATE_SET(nmChannelHandle,NM_STATE_NORMAL_OPERATION);

    #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
    OsekNm_RemoteSleepCancel(nmChannelHandle);
    #endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Normal Status called OsekNm_RxIndProcess>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <current state is NmNormal or NmNormal PrepSleep>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_NormalStandard(NetworkHandleType nmChannelHandle)
{
    /*@req <OSEKNM152> */
    /* clear NMrxcount, If any NM message is received */
    OSEKNM_RXCNT_RESET(nmChannelHandle);

    /*@req <OSEKNM149> */
    if(OSEKNM_MSG_LIMPHOME == OsekNm_GetMsgType(OSEKNM_RXMSG_TYPE(nmChannelHandle)))
    {
        /*  Detemine limphome configuration */
        OSEKNM_CFGREF_SET(OsekNm_ChannelData[nmChannelHandle].cfgLimpHome, OSEKNM_RXMSG_SRCID(nmChannelHandle));
    }
    else
    {
        /* Detemine actual configuration */
        OSEKNM_CFGREF_SET(OsekNm_ChannelData[nmChannelHandle].cfgNormal, OSEKNM_RXMSG_SRCID(nmChannelHandle));

        OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_RINGDATA_NOTALLOWED);

        /*@req <OSEKNM150> */
        /* when alive or ring message received */
        /* Detemine logical sucessor */
        OsekNm_DetermineLogicalSuccessor(nmChannelHandle);

        if(OSEKNM_MSG_ALIVE == OsekNm_GetMsgType(OSEKNM_RXMSG_TYPE(nmChannelHandle)))
        {
            /*@req <OSEKNM161> */
            OSEKNM_STATUS_CLR(nmChannelHandle, (OSEKNM_STATUS_PRESENTCFG_STABLE|OSEKNM_STATUS_MERKER_STABLE));
        }
        else
        {
            OsekNm_NormalStandardRingMsg(nmChannelHandle);
        }
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Normal Status Ring Msg>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <current state is NmNormal or NmNormal PrepSleep>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_NormalStandardRingMsg(NetworkHandleType nmChannelHandle)
{
     /* STOP Ttyp TMax */
    OSEKNM_TSTD_STOP(nmChannelHandle);
    /* pass the ring message delayed(Ttyp) (source=destination or destination=own station), */
    /* if there is no ring message on the bus */
    if((OSEKNM_RXMSG_DESTID(nmChannelHandle) == OSEKNM_RXMSG_SRCID(nmChannelHandle)) ||
            (OSEKNM_RXMSG_DESTID(nmChannelHandle) == OSEKNM_CFG_NODE_ID(nmChannelHandle)))
    {
            /* Copy Rx Ring Data to Tx Buf */
        SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);    
        if(OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_RINGDATA_LEN] > 0u)
        {
            OsekNm_Memcpy(&OsekNm_ChannelData[nmChannelHandle].txBuf[OSEKNM_PDU_RINGDATA],
                                        &OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_RINGDATA],
                                        OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_RINGDATA_LEN]);
            if(NULL_PTR != OSEKNM_CFG_RINGDATA_IND(nmChannelHandle))
            {
                OSEKNM_CFG_RINGDATA_IND(nmChannelHandle)();
            }
        }
        OsekNm_ChannelData[nmChannelHandle].txBuf[OSEKNM_PDU_RINGDATA_LEN] = OsekNm_ChannelData[nmChannelHandle].rxBuf[OSEKNM_PDU_RINGDATA_LEN];
        SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
        
        if((OSEKNM_STATUS_PRESENTCFG_STABLE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_PRESENTCFG_STABLE)
        {
            OSEKNM_STATUS_CLR(nmChannelHandle, OSEKNM_STATUS_RINGDATA_NOTALLOWED);
        }
        /* Start TTyp */
        OSEKNM_TSTD_START_TTYP(nmChannelHandle, OSEKNM_CFG_TTYP(nmChannelHandle));
    }
    else
    {
        /* Start TMax */
        /* Timeout(Tmax) at ring message entered NMReset */
        OSEKNM_TSTD_START_TMAX(nmChannelHandle, &OsekNm_ResetEnter);
        /* send an alive message, if skipped in logical ring */
        if(OsekNm_IsSkipped(nmChannelHandle))
        {
            /* Clear ring data length */
            OSEKNM_CLEAR_RINGDATA(nmChannelHandle);
            
            if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
            {
                if((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_NETWORKRLEASED)
                {
                    OSEKNM_TXMSG_SET_TYPE(nmChannelHandle,OSEKNM_MSG_ALIVE);
                }
                else
                {
                    OSEKNM_TXMSG_SET_TYPE(nmChannelHandle,OSEKNM_MSG_ALIVE_IND);
                }
                OsekNm_SendMessage(nmChannelHandle);
            }
        }
     }

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"


/******************************************************************************/
/*
 * Brief               <Tx TTyp Callback>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TTyp(NetworkHandleType nmChannelHandle)
{
    OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_RINGDATA_NOTALLOWED);
    if(NM_STATE_NORMAL_OPERATION == OSEKNM_CURSTATE_GET(nmChannelHandle))
    {
        /* Start TMax */
        OSEKNM_TSTD_START_TMAX(nmChannelHandle, &OsekNm_ResetEnter);
        if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
        {
            /* increment NMtxcount, if a NM message has to be transmitted */
            OSEKNM_TXCNT_INCREASE(nmChannelHandle);
            if((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_NETWORKRLEASED)
            {
                OSEKNM_TXMSG_SET_TYPE(nmChannelHandle,OSEKNM_MSG_RING_IND);
            }
            else
            {
                OSEKNM_TXMSG_SET_TYPE(nmChannelHandle,OSEKNM_MSG_RING);
            }
            OsekNm_SendMessage(nmChannelHandle);
        }
        if(OSEKNM_TXCNT_GET(nmChannelHandle) > OSEKNM_CFG_TX_LIMIT(nmChannelHandle))
        {
            OsekNm_LimpHomeEnter(nmChannelHandle);
        }
        else
        {
            if((OSEKNM_STATUS_MERKER_STABLE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_MERKER_STABLE)
            {
                OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_PRESENTCFG_STABLE);
                #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
                if((OSEKNM_STATUS_REMOTE_SLEEPCHK & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_REMOTE_SLEEPCHK)
                {
                    if((OSEKNM_STATUS_REMOTE_SLEEPIND & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_REMOTE_SLEEPIND)
                    {
                        OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_REMOTE_SLEEPIND);
                        Nm_RemoteSleepIndication(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
                    }
                }
                else
                {
                    OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_REMOTE_SLEEPCHK);
                }
                #endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */
            }
            else
            {
                OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_MERKER_STABLE);
            }
        }

    }
    else if(NM_STATE_READY_SLEEP == OSEKNM_CURSTATE_GET(nmChannelHandle))
    {
        if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
        {
            OSEKNM_TXMSG_SET_TYPE(nmChannelHandle,OSEKNM_MSG_RING_IND_ACK);
            OsekNm_SendMessage(nmChannelHandle);
        }
    }
    else
    {
        ;
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <RemoteSleepCancel>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_RemoteSleepCancel(NetworkHandleType nmChannelHandle)
{
    OSEKNM_STATUS_CLR(nmChannelHandle, OSEKNM_STATUS_REMOTE_SLEEPCHK);

    if((OSEKNM_STATUS_REMOTE_SLEEPIND & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_REMOTE_SLEEPIND)
    {
        OSEKNM_STATUS_CLR(nmChannelHandle, OSEKNM_STATUS_REMOTE_SLEEPIND);
        Nm_RemoteSleepCancelation(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */
/******************************************************************************/
/*
 * Brief               <Enter LimpHome Status>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_LimpHomeEnter(NetworkHandleType nmChannelHandle)
{
    OsekNm_Memset(OsekNm_ChannelData[nmChannelHandle].cfgNormal, 0x0u, OSEKNM_MAX_NODE_BYTE);
    OSEKNM_CFGREF_SET(OsekNm_ChannelData[nmChannelHandle].cfgLimpHome, OSEKNM_CFG_NODE_ID(nmChannelHandle));

    if(NM_MODE_NETWORK != OSEKNM_CURMODE_GET(nmChannelHandle))
    {
        OSEKNM_CURMODE_SET(nmChannelHandle, NM_MODE_NETWORK);
        Nm_NetworkMode(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
    }

    if(NM_STATE_NORMAL_OPERATION != OSEKNM_CURSTATE_GET(nmChannelHandle))
    {
        #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED)
        Nm_StateChangeNotification(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle),
            OSEKNM_CURSTATE_GET(nmChannelHandle),NM_STATE_NORMAL_OPERATION);
        #endif /* #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED) */
        OSEKNM_CURSTATE_SET(nmChannelHandle,NM_STATE_NORMAL_OPERATION);
    }

    OSEKNM_STATUS_CLR(nmChannelHandle, (OSEKNM_STATUS_PRESENTCFG_STABLE|OSEKNM_STATUS_OFF\
            |OSEKNM_STATUS_BUSSLEEP|OSEKNM_STATUS_TWBS));

    if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_LIMPHOME)
    {   

       OsekNetworkStatusSet(0x02);
        /* Start Ttx enter OsekNm_TLimpErr */
       #if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
       if(OSEKNM_CFG_TLIMPERR(nmChannelHandle) == 0u)
       {
            OsekNm_TLimpErr(nmChannelHandle);
       }
       else
       {
            OSEKNM_TTX_START(nmChannelHandle, OSEKNM_CFG_TLIMPERR(nmChannelHandle), &OsekNm_TLimpErr);
       }
       #endif /* #if(STD_ON == OSEKNM_DEM_ERROR_DETECT) */
       if(NULL_PTR != OSEKNM_CFG_LIMPHOME_IND(nmChannelHandle))
        {
            OSEKNM_CFG_LIMPHOME_IND(nmChannelHandle)(TRUE);
        }

        /* Clear ring data length */
        OSEKNM_CLEAR_RINGDATA(nmChannelHandle);
    }

    OSEKNM_STATUS_SET(nmChannelHandle, OSEKNM_STATUS_LIMPHOME|OSEKNM_STATUS_RINGDATA_NOTALLOWED);

    /*@req <OSEKNM164> */
    /* Start TError  to transmit the 1st limp home message*/
    if((OSEKNM_STATUS_ERROR & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_ERROR)
    {
        OSEKNM_TSTD_START_TERROR(nmChannelHandle);
    }
    else
    {
        OSEKNM_TSTD_STOP(nmChannelHandle);
    }

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Enter LimpHome Standard>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_LimpHomeStandard(NetworkHandleType nmChannelHandle)
{
    if(((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_NETWORKRLEASED)
            && (TRUE == OsekNm_IsSleepAckSet(OSEKNM_RXMSG_TYPE(nmChannelHandle))))
    {
            OsekNm_PrepareBusSleepEnter(nmChannelHandle);
    }
    else
    {
        if(((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_ACTIVE)
                 || (((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
                 && (OSEKNM_STATUS_MERKER_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_MERKER_LIMPHOME))
        {
            OsekNm_ResetEnter(nmChannelHandle);
        }

    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Tx TTyp Callback>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TError(NetworkHandleType nmChannelHandle)
{
    if((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_NETWORKRLEASED)
    {
           /* Start TError */
        OSEKNM_TSTD_START_TERROR(nmChannelHandle);
        if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
        {
            /* Send Limphome MSG */
            OSEKNM_TXMSG_SET_TYPE(nmChannelHandle,OSEKNM_MSG_LIMPHOME);
            OsekNm_SendMessage(nmChannelHandle);
        }
    }
    else
    {
        if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_ACTIVE)
        {
            /* Send Limphome MSG */
            OSEKNM_TXMSG_SET_TYPE(nmChannelHandle, OSEKNM_MSG_LIMPHOME_IND);
            OsekNm_SendMessage(nmChannelHandle);
        }
        OsekNm_ReadySleepEnter(nmChannelHandle);
    }

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <TLimpErr Callback>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TLimpErr(NetworkHandleType nmChannelHandle)
{
    Dem_ReportErrorStatus(OSEKNM_E_LIMPHOME, DEM_EVENT_STATUS_FAILED);
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
#endif /* #if(STD_ON == OSEKNM_DEM_ERROR_DETECT) */
/******************************************************************************/
/*
 * Brief               <ReadySleepEnter>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_ReadySleepEnter(NetworkHandleType nmChannelHandle)
{
    if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_LIMPHOME)
    {
       OSEKNM_TSTD_START_TMAX(nmChannelHandle, &OsekNm_PrepareBusSleepEnter);
     }
    /* NormalPrepSleep && LimpHomePrepSleep Enter */
    #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle),
        OSEKNM_CURSTATE_GET(nmChannelHandle), NM_STATE_READY_SLEEP);
    #endif /* #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED) */
    OSEKNM_CURSTATE_SET(nmChannelHandle, NM_STATE_READY_SLEEP);
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Enter PrePareBusSleep Mode>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_PrepareBusSleepEnter(NetworkHandleType nmChannelHandle)
{
    /* TwbsNormal && TwbsLimpHome Enter */
    OSEKNM_CURMODE_SET(nmChannelHandle, NM_MODE_PREPARE_BUS_SLEEP);
    Nm_PrepareBusSleepMode(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));

   #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED)
    Nm_StateChangeNotification(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle),
        OSEKNM_CURSTATE_GET(nmChannelHandle),NM_STATE_PREPARE_BUS_SLEEP);
    #endif /* #if(STD_ON == OSEKNM_STATE_CHANGE_IND_ENABLED) */

    OSEKNM_CURSTATE_SET(nmChannelHandle,NM_STATE_PREPARE_BUS_SLEEP);

    OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_TWBS);

    OSEKNM_TTX_STOP(nmChannelHandle);
    /*@req <OSEKNM182> */
    OSEKNM_TSTD_START_TWBS(nmChannelHandle);

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <ReqProcess>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_ReqProcess(NetworkHandleType nmChannelHandle)
{
    switch(OSEKNM_CURSTATE_GET(nmChannelHandle))
    {
        case NM_STATE_NORMAL_OPERATION:
            if((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_NETWORKRLEASED)
            {
                if((OSEKNM_STATUS_ACTIVE & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_ACTIVE)
                {
                    OsekNm_ReadySleepEnter(nmChannelHandle);
                }
            }
            break;
        case NM_STATE_PREPARE_BUS_SLEEP:
            if((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_NETWORKRLEASED)
            {
                if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_LIMPHOME)
                {
                    /* Enter Limphome */
                    OsekNm_LimpHomeEnter(nmChannelHandle);
                }
                else
                {
                    OsekNm_ResetEnter(nmChannelHandle);
                }
            }
            break;
        case NM_STATE_READY_SLEEP:
            if((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_NETWORKRLEASED)
            {
                if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_LIMPHOME)
                {
                    OsekNm_NormalEnter(nmChannelHandle);
                }
                else
                {
                    /* Enter Limphome */
                    OsekNm_LimpHomeEnter(nmChannelHandle);
                }
            }
            break;
        case NM_STATE_BUS_SLEEP:
            OsekNm_ResetEnter(nmChannelHandle);
            break;
        default:
            break;

    }

    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <RxIndProcess>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_RxIndProcess(NetworkHandleType nmChannelHandle)
{
    #if(STD_ON == OSEKNM_PDU_RX_INDICATION_ENABLED)
    Nm_PduRxIndication(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
    #endif /* #if(STD_ON == OSEKNM_PDU_RX_INDICATION_ENABLED) */
    switch(OSEKNM_CURSTATE_GET(nmChannelHandle))
    {
        case NM_STATE_NORMAL_OPERATION:
            if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_LIMPHOME)
            {
                OsekNm_LimpHomeStandard(nmChannelHandle);
            }
            else
            {
                OsekNm_NormalStandard(nmChannelHandle);
                #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED)
                if(((OSEKNM_MSG_RING == OsekNm_GetMsgType(OSEKNM_RXMSG_TYPE(nmChannelHandle))) \
                        && (FALSE == OsekNm_IsSleepIndSet(OSEKNM_RXMSG_TYPE(nmChannelHandle))))
                        || ((OSEKNM_STATUS_PRESENTCFG_STABLE& OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_PRESENTCFG_STABLE))
                {
                    OsekNm_RemoteSleepCancel(nmChannelHandle);
                }
                #endif /* #if(STD_ON == OSEKNM_REMOTE_SLEEP_IND_ENABLED) */
                if((TRUE == OsekNm_IsSleepAckSet(OSEKNM_RXMSG_TYPE(nmChannelHandle)))
                            && ((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_NETWORKRLEASED))
                {
                    OsekNm_PrepareBusSleepEnter(nmChannelHandle);
                }
             }
             break;
        case NM_STATE_PREPARE_BUS_SLEEP:
            if((FALSE == OsekNm_IsSleepIndSet(OSEKNM_RXMSG_TYPE(nmChannelHandle))))
            {
                if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_LIMPHOME)
                {
                    /* Enter Limphome */
                    OsekNm_LimpHomeEnter(nmChannelHandle);
                }
                else
                {
                    OsekNm_ResetEnter(nmChannelHandle);
                }
            }

            break;
        case NM_STATE_READY_SLEEP:
            if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_LIMPHOME)
            {
                if((FALSE == OsekNm_IsSleepIndSet(OSEKNM_RXMSG_TYPE(nmChannelHandle))))
                {
                    /* Enter Limphome */
                    OsekNm_LimpHomeEnter(nmChannelHandle);
                }
                OsekNm_LimpHomeStandard(nmChannelHandle);
            }
            else
            {
                if((FALSE == OsekNm_IsSleepIndSet(OSEKNM_RXMSG_TYPE(nmChannelHandle))))
                {
                    OsekNm_NormalEnter(nmChannelHandle);
                }
                OsekNm_NormalStandard(nmChannelHandle);
                if((TRUE == OsekNm_IsSleepAckSet(OSEKNM_RXMSG_TYPE(nmChannelHandle)))
                && ((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_NETWORKRLEASED))
                {
                    OsekNm_PrepareBusSleepEnter(nmChannelHandle);
                }
            }
            break;
        case NM_STATE_BUS_SLEEP:
            Nm_NetworkStartIndication(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
            break;
        default:
            break;

    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <TxConfProcess>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TxConfProcess(NetworkHandleType nmChannelHandle)
{
    if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_LIMPHOME)
    {
        if(OSEKNM_MSG_LIMPHOME == OsekNm_GetMsgType(OSEKNM_TXMSG_TYPE(nmChannelHandle)))
        {
            OSEKNM_STATUS_SET(nmChannelHandle,OSEKNM_STATUS_MERKER_LIMPHOME);
        }
    }
    else
    {
        /* Stop TTx  */
        OSEKNM_TTX_STOP(nmChannelHandle);
        /* clear NMtxcount is case of the transmission of any NM message */
        OSEKNM_TXCNT_RESET(nmChannelHandle);
        if(NM_STATE_NORMAL_OPERATION == OSEKNM_CURSTATE_GET(nmChannelHandle))
        {
            if(OSEKNM_MSG_RING == OsekNm_GetMsgType(OSEKNM_TXMSG_TYPE(nmChannelHandle)))
            {
                   /* Start TMax */
                OSEKNM_TSTD_START_TMAX(nmChannelHandle, &OsekNm_ResetEnter);
                if((TRUE == OsekNm_IsSleepIndSet(OSEKNM_TXMSG_TYPE(nmChannelHandle)))
                    && ((OSEKNM_STATUS_NETWORKRLEASED & OsekNm_ChannelData[nmChannelHandle].status) == OSEKNM_STATUS_NETWORKRLEASED))
                {
                    OsekNm_ReadySleepEnter(nmChannelHandle);
                }
            }

        }
        else if(NM_STATE_READY_SLEEP== OSEKNM_CURSTATE_GET(nmChannelHandle))
        {
            if(TRUE == OsekNm_IsSleepAckSet(OSEKNM_TXMSG_TYPE(nmChannelHandle)))
            {
                OsekNm_PrepareBusSleepEnter(nmChannelHandle);
            }
        }
        else
        {
            ;
        }
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Skipped in the logical ring>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              TRUE,FALSE
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(boolean, OSEKNM_CODE)
OsekNm_IsSkipped(NetworkHandleType nmChannelHandle)
{
    uint8 srcId = 0u;
    uint8 destId = 0u;
    uint8 recvId = 0u;
    boolean ret = FALSE;

    srcId = OSEKNM_RXMSG_SRCID(nmChannelHandle);
    destId = OSEKNM_RXMSG_DESTID(nmChannelHandle);
    recvId = OSEKNM_CFG_NODE_ID(nmChannelHandle);

    if(destId < recvId)
    {
        if((srcId >= destId) && (srcId < recvId))
        {
            /* DSR */
            ret = TRUE;
        }
    }
    else
    {
        if(srcId < destId)
        {
            if(srcId < recvId)
            {
                /* SRD */
                ret = TRUE;
            }
        }
        else
        {
            /* RDS */
            ret = TRUE;
        }
    }
    return ret;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"


/******************************************************************************/
/*
 * Brief               <DetermineLogSuccessor>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_DetermineLogicalSuccessor(NetworkHandleType nmChannelHandle)
{
    uint8 logSucessor = 0u;
    uint8 srcId = 0u;
    uint8 recvId = 0u;

    logSucessor = OSEKNM_LOGSUCCESSORR_GET(nmChannelHandle);
    srcId = OSEKNM_RXMSG_SRCID(nmChannelHandle);
    recvId = OSEKNM_CFG_NODE_ID(nmChannelHandle);

    if(logSucessor == recvId)
    {
        /* New logical successor */
        OSEKNM_LOGSUCCESSOR_SET(nmChannelHandle, srcId);
    }
    else
    {
        if(logSucessor < recvId)
        {
            if(srcId < logSucessor)
            {
                /* New logical successor */
                OSEKNM_LOGSUCCESSOR_SET(nmChannelHandle,srcId);
            }
            else
            {
                if(srcId >= recvId)
                {
                    /* New logical successor */
                    OSEKNM_LOGSUCCESSOR_SET(nmChannelHandle, srcId);
                }
            }
        }
        else
        {
            if((srcId < logSucessor) && (srcId >= recvId))
            {
                /* New logical successor */
                OSEKNM_LOGSUCCESSOR_SET(nmChannelHandle, srcId);
            }
        }
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <Tx Timeout Callback>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_TxTimeout(NetworkHandleType nmChannelHandle)
{
    OSEKNM_EVENT_CLR(nmChannelHandle, OSEKNM_EVENT_TXFLAG);
    Nm_TxTimeoutException(OSEKNM_GET_CHANNEL_ID_OF_NMIF(nmChannelHandle));
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"
/******************************************************************************/
/*
 * Brief               <SendMessage>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_SendMessage(NetworkHandleType nmChannelHandle)
{
    Std_ReturnType ret = E_NOT_OK;
    PduInfoType pduInfo;
    bool txEnableStatus = 0x00;

    txEnableStatus = ((g_commonInfo.udsCtrlNmPduStatus & 0x01) == 0x00);
    if(!txEnableStatus)
    {
        return;
    }

    SchM_Enter_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
    pduInfo.SduDataPtr = OsekNm_ChannelData[nmChannelHandle].txBuf;
    pduInfo.SduLength = 2u + OsekNm_ChannelData[nmChannelHandle].txBuf[OSEKNM_PDU_RINGDATA_LEN];
    /* Pending Pdu data */
    #if(STD_ON == OSEKNM_TX_PDU_PENDING_ENABLE)
    while(pduInfo.SduDataPtr[OSEKNM_PDU_RINGDATA_LEN] < OSEKNM_DATA_SIZE)
    {
        pduInfo.SduDataPtr[pduInfo.SduLength] = OSEKNM_TX_PDU_PENDING_VALUE;
        pduInfo.SduLength++;
        pduInfo.SduDataPtr[OSEKNM_PDU_RINGDATA_LEN]++;
    }
    #endif /* #if(STD_ON == OSEKNM_TX_PDU_PENDING_ENABLE) */
    ret = CanIf_Transmit(OSEKNM_CFG_TX_PDU_ID(nmChannelHandle), &pduInfo);
    SchM_Exit_OsekNm(OSEKNM_INSTANCE_ID, OSEKNM_AREA_TXBUF);
    if(E_OK != ret)
    {
        #if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
        Dem_ReportErrorStatus(OSEKNM_E_CANIF_TRANSMIT_ERROR, DEM_EVENT_STATUS_FAILED);
        #endif /* #if(STD_ON == OSEKNM_DEM_ERROR_DETECT) */
        if((0u < OSEKNM_CFG_TTX(nmChannelHandle)) &&
            (OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_LIMPHOME)
        {
            /* start Ttx to Retry SendMessage */
            /* repeat the transmission request(TTX) in case of a rejection from the CanIf, no effect to NMtxcount*/
            OSEKNM_TTX_START(nmChannelHandle,OSEKNM_CFG_TTX(nmChannelHandle), &OsekNm_SendMessage);
        }
    }
    else
    {
        #if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED)
        if(0u < OSEKNM_CFG_TX_WAIT_CONF(nmChannelHandle))
        {
            if((OSEKNM_STATUS_LIMPHOME & OsekNm_ChannelData[nmChannelHandle].status) != OSEKNM_STATUS_LIMPHOME)
            {
                /* Start Ttx to wait TxCont */
                OSEKNM_TTX_START(nmChannelHandle, OSEKNM_CFG_TX_WAIT_CONF(nmChannelHandle), &OsekNm_TxTimeout);
                /* Ring Message */
                if(OSEKNM_MSG_RING == OsekNm_GetMsgType(OSEKNM_TXMSG_TYPE(nmChannelHandle)))
                {
                     OSEKNM_EVENT_SET(nmChannelHandle, OSEKNM_EVENT_TXFLAG);
                }
            }
        }
        else
        #endif /* #if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED) */
        {
            OsekNm_TxConfProcess(nmChannelHandle);
        }
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <OsekNm_GetMsgType>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(uint8, OSEKNM_CODE)
OsekNm_GetMsgType(uint8 OpCode)
{
    uint8 res;

    switch(OpCode&OSEKNM_MSG_MASK)
    {
        case OSEKNM_MSG_ALIVE:
        case OSEKNM_MSG_ALIVE_IND:
            res = OSEKNM_MSG_ALIVE;
            break;

        case OSEKNM_MSG_LIMPHOME:
        case OSEKNM_MSG_LIMPHOME_IND:
            res = OSEKNM_MSG_LIMPHOME;
            break;
        default:
            res = OSEKNM_MSG_RING;
            break;
    }
    return res;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <OsekNm_IsSleepIndSet>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(boolean , OSEKNM_CODE)
OsekNm_IsSleepIndSet(uint8 OpCode)
{
    boolean res;

    switch(OpCode&OSEKNM_MSG_MASK)
    {
        case OSEKNM_MSG_ALIVE_IND:
        case OSEKNM_MSG_LIMPHOME_IND:
        case OSEKNM_MSG_RING_IND:
        case OSEKNM_MSG_RING_IND_ACK:
            res = TRUE;
            break;
        default:
            res = FALSE;
            break;
    }
    return res;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/******************************************************************************/
/*
 * Brief               <OsekNm_IsSleepAckSet>
 * Sync/Async          <Synchronous>
 * Reentrancy          Reentrant (but not for the same NM-Channel)
 * Param-Name[in]      nmChannelHandle: Identification of the NM-channel
 * Param-Name[out]     <description...>
 * Param-Name[in/out]  <description...>
 * Return              None
 * PreCondition        <description...>
 */
/******************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(boolean , OSEKNM_CODE)
OsekNm_IsSleepAckSet(uint8 OpCode)
{
    boolean res;
    
    switch(OpCode&OSEKNM_MSG_MASK)
    {
        case OSEKNM_MSG_RING_ACK:
        case OSEKNM_MSG_RING_IND_ACK:
            res = TRUE;
            break;
        default:
            res = FALSE;
            break;
    }
    return res;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"

/*************************************************************************/
/*
 * Brief               MISRA C 2004 forbid to use memcpy() lib,only used  to
 *                     copy data buffer of indirect address
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      *src:pointer to the source
 *                     count:copy how much length
 * Param-Name[out]     None
 * Param-Name[in/out]  *dest:pointer to the dest
 * Return              void
 * PreCondition        None
 * CallByAPI
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_Memcpy
(
    P2VAR(uint8, AUTOMATIC, AUTOMATIC) dest,
    P2CONST(uint8, AUTOMATIC, AUTOMATIC) src,
    uint8 count
)
{
    while(count > 0u)
    {
        *dest = *src;
        dest++;
        src++;
        count--;
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"



/*************************************************************************/
/*
 * Brief               MISRA C 2004 forbid to use memset() lib,only used  to
 *                     set data buffer of indirect address
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      c:the source which want to be set
 *                     n:copy how much length
 * Param-Name[out]     None
 * Param-Name[in/out]  s:pointer to the dest
 * Return              void
 * PreCondition        None
 * CallByAPI
 */
/*************************************************************************/
#define OSEKNM_START_SEC_CODE
#include "OsekNm_MemMap.h"
STATIC FUNC(void, OSEKNM_CODE)
OsekNm_Memset
(
    P2VAR(uint8, AUTOMATIC, AUTOMATIC) s,
    const uint8 c,
    uint8 n
)
{
    while(n > 0u)
    {
        *s = c;
        s++;
        n--;
    }
    return;
}
#define OSEKNM_STOP_SEC_CODE
#include "OsekNm_MemMap.h"


Nm_StateType OsekNm_GetCurrentState(void)
{
    Nm_StateType status = NM_STATE_UNINIT;

    status = OSEKNM_CURSTATE_GET(0);

    return status;
}


UINT16 OsekNm_GetCurrentStatus(void)
{
    return OSEKNM_STATUS_GET(0);
}

#endif /* CAN_ENBALE_OSEKNM */
/*=======[E N D   O F   F I L E]==============================================*/

