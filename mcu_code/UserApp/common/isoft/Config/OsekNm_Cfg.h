/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <OsekNm_Cfg.h>
 *  @brief      <>
 *  
 *  <MCU:MPC5644>
 *  
 *  <AUTHOR>
 *  @date       <2019-12-13 17:43:08>
 */
/*============================================================================*/

	
#ifdef CAN_ENABLE_OSEKNM
#ifndef OSEKNM_CFG_H
#define OSEKNM_CFG_H

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define OSEKNM_CFG_H_AR_MAJOR_VERSION    2
#define OSEKNM_CFG_H_AR_MINOR_VERSION    5
#define OSEKNM_CFG_H_AR_PATCH_VERSION    3
#define OSEKNM_CFG_H_SW_MAJOR_VERSION    1
#define OSEKNM_CFG_H_SW_MINOR_VERSION    0
#define OSEKNM_CFG_H_SW_PATCH_VERSION    0


/*=======[I N C L U D E S]====================================================*/
#include "ComStack_Types.h"

/*=======[M A C R O S]========================================================*/
/* define OSEKNM_VARIANT type */
#define	OSEKNM_VARIANT_PC	    VARIANT_PRE_COMPILE
#define	OSEKNM_VARIANT_LT	    VARIANT_LINK_TIME

/* define the Variant of the OsekNm Module */
#define OSEKNM_VARIANT_CFG	    OSEKNM_VARIANT_PC

/* OsekNmGlobalConfig */
#define OSEKNM_DEM_ERROR_DETECT STD_OFF
#define OSEKNM_DEV_ERROR_DETECT	STD_OFF
/*@req <OSEKNM903> */
#define OSEKNM_BUS_SYNCHRONIZATION_ENABLED	STD_OFF
#define OSEKNM_COM_CONTROL_ENABLED	        STD_ON
#define OSEKNM_IMMEDIATE_TXCONF_ENABLED	    STD_OFF

#define OSEKNM_NUMBER_OF_CHANNELS	        1
#define OSEKNM_CHANNEL_ID_OFFSET	        0

#define OSEKNM_PDU_RX_INDICATION_ENABLED	STD_ON
#define OSEKNM_REMOTE_SLEEP_IND_ENABLED	    STD_OFF
#define OSEKNM_REPEAT_MSG_IND_ENABLED	    STD_OFF
#define OSEKNM_STATE_CHANGE_IND_ENABLED	    STD_ON
#define OSEKNM_USER_DATA_ENABLED	        STD_ON
#define OSEKNM_VERSION_INFO_API	            STD_OFF

#define OSEKNM_BUSLOADREDUCTIONENABLED	    STD_OFF

#define OSEKNM_RX_INTERRUPT                 STD_ON
#define OSEKNM_TX_INTERRUPT                 STD_ON

#define OSEKNM_TX_PDU_PENDING_ENABLE	    STD_ON
/* Tx fill value */
#define OSEKNM_TX_PDU_PENDING_VALUE		    0x0u

/* message type define */
#define OSEKNM_MSG_MASK          0x37u

#define OSEKNM_MSG_RING          0x2u
#define OSEKNM_MSG_RING_IND	     0x12u
#define OSEKNM_MSG_RING_IND_ACK	 0x32u
#define OSEKNM_MSG_RING_ACK      0x22u

#define OSEKNM_MSG_ALIVE         0x1u
#define OSEKNM_MSG_ALIVE_IND     0x11u

#define OSEKNM_MSG_LIMPHOME      0x4u
#define OSEKNM_MSG_LIMPHOME_IND  0x14u

#endif /* end of OSEKNM_CFG_H */

#endif /* end of CAN_ENABLE_OSEKNM */
/*=======[E N D   O F   F I L E]==============================================*/
