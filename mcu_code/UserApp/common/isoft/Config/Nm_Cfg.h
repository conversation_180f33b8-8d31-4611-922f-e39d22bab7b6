/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Nm_Cfg.h>
 *  @brief      <>
 *  
 *  <MCU:MPC5644>
 *  
 *  <AUTHOR>
 *  @date       <2019-12-13 17:36:01>
 */
/*============================================================================*/

	
#ifndef NM_CFG_H_
#define NM_CFG_H_

/*=======[I N C L U D E S]====================================================*/
#include "Std_Types.h" 

/*=======[M A C R O S]========================================================*/
/* Pre-processor switch for enabling support of the Passive Mode */
#define NM_PASSIVE_MODE_ENABLED               STD_OFF

/* Pre-processor switch for enabling remote sleep indication support */
#define NM_REMOTE_SLEEP_IND_ENABLE            STD_ON

#define NM_REPEAT_MSG_IND_ENABLED			  STD_ON

/* Switch to inform if NM coordinator needs to support direct OSEK NM */
#define NM_COORDINATOR_SUPPORT_ENABLED        STD_ON

/* Number of rounds the coordinator shall keep a bus which runs AUTOSAR NM awake
 *  after all nodes including itself are ready to sleep */
#if (STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
#define NM_AUTOSAR_GATEWAY_ROUNDS             0
#endif

/* Pre-processor switch for enabling bus synchronization support */
#define NM_BUS_SYNCHRONIZATION_ENABLED        STD_OFF

/* Pre-processor switch for enabling the Communication Control support */
#define NM_COM_CONTROL_ENABLED                STD_ON

#if (STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
/* The period between successive calls to the Main Function of the NM Interface in seconds */
#define NM_CYCLETIME_MAIN_FUNCTION            0
#endif

/** @req Nm022 */
/* Pre-processor switch for enabling development error detection and notification */
#define NM_DEV_ERROR_DETECT                   STD_OFF

/* Pre-processor switch for enabling channel multiplicity support */
#define NM_MULTIPLE_CHANNELS_ENABLED          STD_OFF

/* Pre-processor switch for enabling the source node identifier */
#define NM_NODE_ID_ENABLED                    STD_ON

#if (STD_ON == NM_NODE_ID_ENABLED)
/* Pre-processor switch for enabling the Request Repeat Message Request support */
#define NM_NODE_DETECTION_ENABLED             STD_ON
#endif

/* Pre-processor channel numbers */
#define NM_NUMBER_OF_CHANNELS                 1

#if (STD_ON == NM_COORDINATOR_SUPPORT_ENABLED)
/* Number of rounds the coordinator shall keep a bus which runs OSEK NM awake 
 * after all nodes including itself are ready to sleep */
#define NM_OSEK_GATEWAY_ROUNDS                0
#endif

/* Pre-processor switch for enabling the PDU Rx Indication */
#define NM_PDU_RX_INDICATION_ENABLED          STD_ON

/* Pre-processor switch for enabling the CAN Network Management state change notification */
#define NM_STATE_CHANGE_IND_ENABLED           STD_ON

/* Pre-processor switch for enabling user data support */
#define NM_USER_DATA_ENABLED                  STD_ON

/* Pre-processor switch for enabling version info API support */
#define NM_VERSION_INFO_API                   STD_OFF

/* Pre-processor switch for enabling CanNm */
#define NM_BUSNM_CANNM_ENABLED                STD_ON       

/* Pre-processor switch for enabling FrNm */
#define NM_BUSNM_FRNM_ENABLED                 STD_OFF 

/* Pre-processor switch for enabling LinNm */
#define NM_BUSNM_LINNM_ENABLED                STD_OFF

/* Pre-processor switch for enabling OsekNm */
#define NM_BUSNM_OSEKNM_ENABLED               STD_OFF


#endif /* end of NM_CFG_H_*/
/*=======[E N D   O F   F I L E]==============================================*/
