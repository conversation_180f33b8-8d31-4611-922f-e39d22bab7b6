/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <CanIf_Cfg.c>
 *  @brief      <>
 *  
 *  <MCU:MPC5644>
 *  
 *  <AUTHOR>
 *  @date       <2019-12-13 17:36:00>
 */
/*============================================================================*/


/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/

#define CANIF_CFG_C_AR_MAJOR_VERSION  3
#define CANIF_CFG_C_AR_MINOR_VERSION  2
#define CANIF_CFG_C_AR_PATCH_VERSION  0
#define CANIF_CFG_C_SW_MAJOR_VERSION  1
#define CANIF_CFG_C_SW_MINOR_VERSION  0
#define CANIF_CFG_C_SW_PATCH_VERSION  1

/*=======[I N C L U D E S]====================================================*/
#include "Can_Cfg.h"
#include "CanIf.h"
#include "r_can.h"
#include "CanSM_Cbk.h"
#include "CanTp_Cbk.h"
#ifdef CAN_ENABLE_OSEKNM
#include "OsekNm_Cbk.h"
#else
#include "CanNm_Cbk.h"
#endif
/*=======[V E R S I O N  C H E C K]===========================================*/
#if(CANIF_CFG_C_AR_MAJOR_VERSION != CANIF_CFG_H_AR_MAJOR_VERSION)
    #error "CanIf.c:Mismatch in Specification Major Version"
#endif

#if(CANIF_CFG_C_AR_MINOR_VERSION != CANIF_CFG_H_AR_MINOR_VERSION)
    #error "CanIf.c:Mismatch in Specification Minor Version"
#endif

#if(CANIF_CFG_C_AR_PATCH_VERSION != CANIF_CFG_H_AR_PATCH_VERSION)
    #error "CanIf.c:Mismatch in Specification Patch Version"
#endif

#if(CANIF_CFG_C_SW_MAJOR_VERSION != CANIF_CFG_H_SW_MAJOR_VERSION)
    #error "CanIf.c:Mismatch in Specification Major Version"
#endif

#if(CANIF_CFG_C_SW_MINOR_VERSION != CANIF_CFG_H_SW_MINOR_VERSION)
    #error "CanIf.c:Mismatch in Specification Minor Version"
#endif

/*=======[E X T E R N A L   D A T A]==========================================*/

#define CANIF_START_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"
P2VAR(CanIf_RxPduBufferType,AUTOMATIC,CANIF_VAR) CanIf_RxBuffers;
#define CANIF_STOP_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIfUserRxFct,CANIF_CONST) CanIf_UserRxIndication[CANIF_USER_MAX_COUNT] =
{
    &CanNm_RxIndication,/*&CanNm_RxIndication,Enable only when CanNm include*/
    &CanTp_RxIndication,/*&CanTp_RxIndication,Enable only when CanTp include*/
    NULL_PTR,/*&PduR_CanIfRxIndication,Enable only when PduR include*/
    NULL_PTR,/**<&Xcp_CanIfRxIndication,XCP */
    #if STD_ON == CANSM_USE_OSEKNM
    &OsekNm_RxIndication
    #else
    NULL_PTR      /**<&OsekNm_RxIndication,OskeNm*/
    #endif

};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIfUserTxFct,CANIF_CONST) CanIf_UserTxConfirmation[CANIF_USER_MAX_COUNT] =
{
    &CanNm_TxConfirmation,/*&CanNm_TxConfirmation,Enable only when CanNm include*/
    &CanTp_TxConfirmation,/*&CanTp_TxConfirmation,Enable only when CanTp include*/
    NULL_PTR,/*&PduR_CanIfTxConfirmation,Enable only when PduR include*/
    NULL_PTR,/**<&Xcp_CanIfTxConfirmation,XCP */
	#ifdef CAN_ENABLE_OSEKNM
    &OsekNm_TxConfirmation       /**<&OsekNm_TxConfirmation,OsekNm*/
    #else
    NULL_PTR      /**<&OsekNm_TxConfirmation,OskeNm*/
    #endif
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"

/* contains the references  to the configuration setup of each underlying CAN driver.*/
#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIf_ControllerConfigType,CANIF_CONST)CanIf_ControllerConfiguration[CANIF_MAX_CONTROLLER] =
{
	{
	  /* Enables wakeup support and defines the source device of a wakeup event. */
	  CANIF_WAKEUP_SUPPORT_CONTROLLER,
	  
	  /* Logical handle of the underlying CAN controller  */
	  CAN_CONTROLLER_A,
	#if (STD_ON == CANIF_WAKEUP_VALIDATION)
	  /* CanIf WakeUp source,add by isoft */
	  0
	#endif
	  
	},
	{
	  /* Enables wakeup support and defines the source device of a wakeup event. */
	  CANIF_WAKEUP_SUPPORT_CONTROLLER,
	  
	  /* Logical handle of the underlying CAN controller  */
	  CAN_CONTROLLER_B,
	#if (STD_ON == CANIF_WAKEUP_VALIDATION)
	  /* CanIf WakeUp source,add by isoft */
	  0
	#endif
	  
	},
	{
	  /* Enables wakeup support and defines the source device of a wakeup event. */
	  CANIF_WAKEUP_SUPPORT_CONTROLLER,
	  
	  /* Logical handle of the underlying CAN controller  */
	  CAN_CONTROLLER_C,
	#if (STD_ON == CANIF_WAKEUP_VALIDATION)
	  /* CanIf WakeUp source,add by isoft */
	  0
	#endif
	  
	},
	{
	  /* Enables wakeup support and defines the source device of a wakeup event. */
	  CANIF_WAKEUP_SUPPORT_CONTROLLER,
	  
	  /* Logical handle of the underlying CAN controller  */
	  CAN_CONTROLLER_D,
	#if (STD_ON == CANIF_WAKEUP_VALIDATION)
	  /* CanIf WakeUp source,add by isoft */
	  0
	#endif
	  
	},
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"

/* Callout functions with respect to the upper layers */
#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIf_DispatchConfigType,CANIF_CONST) CanIf_DispatchConfigData = 
{
    /* Name of target BusOff notification services  */
	&CanSM_ControllerBusOff,/*&CanSM_ControllerBusOff,Enable only when CanSM include*/
	
    #if (STD_ON == CANIF_WAKEUP_VALIDATION)
	/*  Name of target wakeup notification services */
	NULL_PTR,/*&EcuM_SetWakeupEvent, Enable only when EcuM include */

	/* Name of target wakeup validation notification services */
	NULL_PTR /*&EcuM_ValidateWakeupEvent, Enable only when EcuM include */
    #endif 
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"


/* Configuration parameters for all the underlying CAN drivers*/
#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIf_DriverConfigType,CANIF_CONST) CanIf_DriverConfiguration =
{
    TRUE, /* CanIfBusoffNotification */
    TRUE, /* CanIfReceiveIndication */
    TRUE, /* CanIfTxConfirmation */
    FALSE  /* CanIfWakeupNotification */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"


/* contains configuration parameters for each hardware receive object */
#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIf_HrhConfigType,CANIF_CONST) CanIf_HrhConfigData[CANIF_NUMBER_OF_HRHUSED] =
{
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_A
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_C
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
	{
	    /* Define s the HRH type i.e, whether its a BasicCan or FullCan */
	    CANIF_FULL_CAN,

		/* perform software filtering */
		FALSE,
		
		/* Reference to controller Id to which the HRH belongs to */
		CAN_CONTROLLER_D
	},
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"


/* contains parameters related to each HTH */
#define CANIF_START_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIf_HthConfigType,CANIF_CONST) CanIf_HthConfigData[CANIF_NUMBER_OF_HTHUSED] =
{
	{
	    /*Transmission method of the corresponding HTH */
	    CANIF_FULL_CAN,

		/* Reference to controller Id to which the HTH belongs to */
		CAN_CONTROLLER_A,
		
		/* refers to a particular HTH object in the CAN Driver Module configuration*/
		1
	},
	{
	    /*Transmission method of the corresponding HTH */
	    CANIF_FULL_CAN,

		/* Reference to controller Id to which the HTH belongs to */
		CAN_CONTROLLER_B,
		
		/* refers to a particular HTH object in the CAN Driver Module configuration*/
		3
	},
	{
	    /*Transmission method of the corresponding HTH */
	    CANIF_FULL_CAN,

		/* Reference to controller Id to which the HTH belongs to */
		CAN_CONTROLLER_C,
		
		/* refers to a particular HTH object in the CAN Driver Module configuration*/
		5
	},
	{
	    /*Transmission method of the corresponding HTH */
	    CANIF_FULL_CAN,

		/* Reference to controller Id to which the HTH belongs to */
		CAN_CONTROLLER_A,
		
		/* refers to a particular HTH object in the CAN Driver Module configuration*/
		15
	},
	{
	    /*Transmission method of the corresponding HTH */
	    CANIF_FULL_CAN,

		/* Reference to controller Id to which the HTH belongs to */
		CAN_CONTROLLER_D,
		
		/* refers to a particular HTH object in the CAN Driver Module configuration*/
		16
	},
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
#include "CanIf_MemMap.h"


#define CANIF_START_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"
VAR(CanIf_NotifStatusType, CANIF_VAR) CanIf_TxNotifStatus[CANIF_MAX_NUMBER_OF_CANTXPDUIDS];
#define CANIF_STOP_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"
VAR(CanIf_NotifStatusType, CANIF_VAR) CanIf_RxNotifStatus[CANIF_MAX_NUMBER_OF_CANRXPDUIDS];
#define CANIF_STOP_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"

/* define dynamic tx pdu Canid */
#define CANIF_START_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"
P2VAR(Can_IdType,AUTOMATIC,CANIF_VAR) CanIf_DynamicTxPduCanIds;
#define CANIF_STOP_SEC_VAR_UNSPECIFIED
#include "CanIf_MemMap.h"


/* references to the configuration setup of each underlying CAN Driver */
#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_InitHohConfigType, CANIF_CONST_PBCFG) CanIf_InitHohConfig =
{  
    /* Selects the CAN Interface specific configuration setup */
    &Can_ControllerConfigData[0u]
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"


/* contains the configuration (parameters) of each transmit CAN L-PDU */
#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_TxPduConfigType, CANIF_CONST_PBCFG) CanIf_TxPduConfigData[CANIF_MAX_NUMBER_OF_CANTXPDUIDS] =
{
	{
		0x1,      /* CanIfCanTxPduIdCanId*/
		0,          /* CanIfCanTxPduIdDlc*/
		0,			/* CanIfCanTxPduIndex*/
		CANIF_PDU_TYPE_STATIC,  /* CanIfCanTxPduType*/
		0xFFFFu,    /* CanIfDynamicTxPduCanIdIndex*/
		FALSE,       /* CanIfReadTxPduNotifyStatus*/
        0xFF,       /* CanIfTxNotifyIndex*/
        CANIF_STANDARD_CAN,     /* CanIfTxPduIdCanIdType */
		CANIF_USER_TYPE_CAN_TP, /* CanIfTxUserType*/
		0        /* CanIfCanTxPduHthRef*/
    },
	{
		0x2,      /* CanIfCanTxPduIdCanId*/
		0,          /* CanIfCanTxPduIdDlc*/
		1,			/* CanIfCanTxPduIndex*/
		CANIF_PDU_TYPE_STATIC,  /* CanIfCanTxPduType*/
		0xFFFFu,    /* CanIfDynamicTxPduCanIdIndex*/
		FALSE,       /* CanIfReadTxPduNotifyStatus*/
        0xFF,       /* CanIfTxNotifyIndex*/
        CANIF_STANDARD_CAN,     /* CanIfTxPduIdCanIdType */
		CANIF_USER_TYPE_CAN_TP, /* CanIfTxUserType*/
		1        /* CanIfCanTxPduHthRef*/
    },
	{
		0x3,      /* CanIfCanTxPduIdCanId*/
		0,          /* CanIfCanTxPduIdDlc*/
		0,			/* CanIfCanTxPduIndex*/
		CANIF_PDU_TYPE_STATIC,  /* CanIfCanTxPduType*/
		0xFFFFu,    /* CanIfDynamicTxPduCanIdIndex*/
		FALSE,       /* CanIfReadTxPduNotifyStatus*/
        0xFF,       /* CanIfTxNotifyIndex*/
        CANIF_STANDARD_CAN,     /* CanIfTxPduIdCanIdType */
		CANIF_USER_TYPE_CAN_TP, /* CanIfTxUserType*/
		2        /* CanIfCanTxPduHthRef*/
    },
	{
		0x614,      /* CanIfCanTxPduIdCanId*/
		0,          /* CanIfCanTxPduIdDlc*/
		3,				/* CanIfCanTxPduIndex*/
		CANIF_PDU_TYPE_STATIC,  /* CanIfCanTxPduType*/
		0xFFFFu,    /* CanIfDynamicTxPduCanIdIndex*/
		FALSE,       /* CanIfReadTxPduNotifyStatus*/
        0xFF,       /* CanIfTxNotifyIndex*/
        CANIF_STANDARD_CAN,     /* CanIfTxPduIdCanIdType */
		CANIF_USER_TYPE_CAN_NM, /* CanIfTxUserType*/
		3        /* CanIfCanTxPduHthRef*/
    },
	{
		0x4,      /* CanIfCanTxPduIdCanId*/
		0,          /* CanIfCanTxPduIdDlc*/
		4,			/* CanIfCanTxPduIndex*/
		CANIF_PDU_TYPE_STATIC,  /* CanIfCanTxPduType*/
		0xFFFFu,    /* CanIfDynamicTxPduCanIdIndex*/
		FALSE,       /* CanIfReadTxPduNotifyStatus*/
        0xFF,       /* CanIfTxNotifyIndex*/
        CANIF_STANDARD_CAN,     /* CanIfTxPduIdCanIdType */
		CANIF_USER_TYPE_CAN_TP, /* CanIfTxUserType*/
		4        /* CanIfCanTxPduHthRef*/
    },
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"


/* contains the configuration (parameters) of each receive CAN L-PDU */
#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_RxPduConfigType, CANIF_CONST_PBCFG)CanIf_RxPduConfigData[CANIF_MAX_NUMBER_OF_CANRXPDUIDS] =
{
	{
		0x701,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_TP   /* CanIfRxUserType*/
	
    },
	{
		0x702,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x01,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_TP   /* CanIfRxUserType*/
	
    },
    {
		0x703,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x03,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_TP   /* CanIfRxUserType*/
	
    },
	{
		0x402,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x404,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x406,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x408,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x40a,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x410,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x412,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
	
    },
	{
		0x418,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x00,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_NM   /* CanIfRxUserType*/
    },
	{
		0x704,      /* CanIfCanRxPduCanId*/
		0,         /* CanIfCanRxPduDlc*/
		0x04,         /* CanIfCanRxPduId*/
		FALSE,          /* CanIfReadRxPduData*/
        0xFF,             /* CanIfRxBufferIndex */
		FALSE,         /* CanIfReadRxPduNotifyStatus*/	
		0xFF,             /* CanIfRxNotifyIndex */
		CANIF_STANDARD_CAN,      /* CanIfRxPduIdCanIdType*/
		CANIF_USER_TYPE_CAN_TP   /* CanIfRxUserType*/
	
    },
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"

/* define hth index ,every Hth used as BASIC CAN have a index cfg 
 * The Max size determined at link time, equal to CanIf_HthConfigData size.
 */
#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_HohIndexType, CANIF_CONST_PBCFG) CanIf_HthIndexCfg[CANIF_NUMBER_OF_HTHUSED] = 
{
    {
        0,/* start index */
        0 /* stop index */
    },
    {
        1,/* start index */
        1 /* stop index */
    },
    {
        2,/* start index */
        2 /* stop index */
    },
    {
        3,/* start index */
        3 /* stop index */
    },
    {
        4,/* start index */
        4 /* stop index */
    },
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"


/* every Hrh used as BASIC CAN and 'CanIfSoftwareFilterHrh' Configured TRUE have a RangeMask */
#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_HrhRangeMaskType, CANIF_CONST_PBCFG) CanIf_HrhRangeMaskConfig[CANIF_NUMBER_OF_HRHUSED] =
{
    {
        0x7ff, 
        0x701
    },
    {
        0x7ff, 
        0x702
    },
    {
        0x7ff, 
        0x703
    },
    {
        0x7ff,
        0x402
    },
    {
        0x7ff,
        0x404
    },
    {
        0x7ff,
        0x406
    },
    {
        0x7ff,
        0x408
    },
    {
        0x7ff,
        0x40a
    },
    {
        0x7ff,
        0x410
    },
    {
        0x7ff,
        0x412
    },
    {
        0x7ff,
        0x418
    },
    {
        0x7ff,
        0x704
    },
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"


#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_HohIndexType, CANIF_CONST_PBCFG) CanIf_HrhPduIndex[CANIF_NUMBER_OF_HRHUSED] = 
{
    {
        0,/* start index */
        0 /* stop index */
    },
    {
        1,/* start index */
        1 /* stop index */
    },
    {
        2,/* start index */
        2 /* stop index */
    },
    {
        3,/* start index */
        3 /* stop index */
    },
    {
        4,/* start index */
        4 /* stop index */
    },
    {
        5,/* start index */
        5 /* stop index */
    },
    {
        6,/* start index */
        6 /* stop index */
    },
    {
        7,/* start index */
        7 /* stop index */
    },
    {
        8,/* start index */
        8 /* stop index */
    },
    {
        9,/* start index */
        9 /* stop index */
    },
    {
        10,/* start index */
        10 /* stop index */
    },
    {
        11,/* start index */
        11 /* stop index */
    },
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"


/* each CanIfHrhConfig have a corresponding HrhIdConfig which contains the HRH */
#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(uint8, CANIF_CONST_PBCFG) CanIf_HrhIdConfig[CANIF_NUMBER_OF_HRHUSED] =
{
    0,
    2,
    4,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"

#define CANIF_START_CONST_PBCFG
#include "CanIf_MemMap.h"
CONST(CanIf_HrhFilterConfigType, CANIF_CONST_PBCFG) CanIf_HrhFilterRefCfg =
{
    &CanIf_HrhIdConfig[0u],
    &CanIf_HrhRangeMaskConfig[0u],
    &CanIf_HrhPduIndex[0u]
};
#define CANIF_STOP_CONST_PBCFG
#include "CanIf_MemMap.h"

/*=======[E N D   O F   F I L E]==============================================*/