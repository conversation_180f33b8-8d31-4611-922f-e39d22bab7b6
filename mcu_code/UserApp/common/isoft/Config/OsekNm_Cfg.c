/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <OsekNm_Cfg.c>
 *  @brief      <>
 *  
 *  <MCU:MPC5644>
 *  
 *  <AUTHOR>
 *  @date       <2019-12-13 17:43:08>
 */
/*============================================================================*/

	
#ifdef CAN_ENABLE_OSEKNM
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define OSEKNM_CFG_C_AR_MAJOR_VERSION    2
#define OSEKNM_CFG_C_AR_MINOR_VERSION    5
#define OSEKNM_CFG_C_AR_PATCH_VERSION    3
#define OSEKNM_CFG_C_SW_MAJOR_VERSION    1
#define OSEKNM_CFG_C_SW_MINOR_VERSION    0
#define OSEKNM_CFG_C_SW_PATCH_VERSION    0

/*=======[I N C L U D E S]====================================================*/
#include "OsekNm.h"

/*=======[V E R S I O N  C H E C K]===========================================*/

#if(OSEKNM_CFG_C_AR_MAJOR_VERSION != OSEKNM_CFG_H_AR_MAJOR_VERSION)
    #error "OsekNm_Cfg.c:Mismatch in Specification Major Version"
#endif

#if(OSEKNM_CFG_C_AR_MINOR_VERSION != OSEKNM_CFG_H_AR_MINOR_VERSION)
    #error "OsekNm_Cfg.c:Mismatch in Specification Minor Version"
#endif

#if(OSEKNM_CFG_C_AR_PATCH_VERSION != OSEKNM_CFG_H_AR_PATCH_VERSION)
    #error "OsekNm_Cfg.c:Mismatch in Specification Patch Version"
#endif

#if(OSEKNM_CFG_C_SW_MAJOR_VERSION != OSEKNM_CFG_H_SW_MAJOR_VERSION)
    #error "OsekNm_Cfg.c:Mismatch in Specification Major Version"
#endif

#if(OSEKNM_CFG_C_SW_MINOR_VERSION != OSEKNM_CFG_H_SW_MINOR_VERSION)
    #error "OsekNm_Cfg.c:Mismatch in Specification Minor Version"
#endif

/*=======[E X T E R N A L   D A T A]==========================================*/
/* VARIANT_PRE_COMPILE support */
#define OSEKNM_START_SEC_CONST_UNSPECIFIED
#include "OsekNm_MemMap.h"
CONST(OsekNm_ChannelConfigType, OSEKNM_CONST) OsekNm_ConfigData[OSEKNM_NUMBER_OF_CHANNELS] = 
{
	{
		10,	/* TTyp */
		25,	/* TMax Êµ¼ÊÅäÖÃ¼õÒ» */
		100,	/* TError */
		50,		/* TTx */
		500,	/* TWbs */
		#if(STD_ON == OSEKNM_DEM_ERROR_DETECT)
       	0,        /* TLimpErr */
        #endif
		#if(STD_ON == OSEKNM_BUSLOADREDUCTIONENABLED)
		0,		/* MsgReducedTime */
		#endif
		#if(STD_OFF == OSEKNM_IMMEDIATE_TXCONF_ENABLED)
		25,		/* MsgTimeoutTime  */
		#endif
		2,		/* TxPduId */
		20,	/* NodeId */
		#if(STD_ON == OSEKNM_USER_DATA_ENABLED)
		6,		/* UserDataLength */
		#endif
		0x0,	/* RefSm */
		8,		/* TxLimitCfg */
		4,		/* RxLimtCfg */
		OSEKNM_PROCESS_TYPE_INTERRUPT,           /* TxProcessing */
		OSEKNM_PROCESS_TYPE_POLLING,           /* RxProcessing */
		NULL_PTR,	/* RingDataNotify */
		NULL_PTR	/* LimpHomeNotify */
	},
};
#define OSEKNM_STOP_SEC_CONST_UNSPECIFIED
#include "OsekNm_MemMap.h"
#endif /* end of CAN_ENABLE_OSEKNM */
/*=======[E N D   O F   F I L E]==============================================*/
