/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <CanNm_Cfg.c>
 *  @brief      <>
 *  
 *  <MCU:TC1782>
 *  
 *  <AUTHOR>
 *  @date       <2016-08-31 14:17:38>
 */
/*============================================================================*/


#include "CanNm.h"
#include "Nm_Cfg.h"

CONST(CanNm_ChannelConfigType, CANNM_CONST) CanNm_ChannelConfigData[CANNM_NUMBER_OF_CHANNELS] =
{
    {
        #if(CANNM_BUS_LOAD_REDUCTION_ENABLED == STD_ON)
        .busLoadReductionActive=TRUE,               /* busLoadReductionActive */
        #endif  
        .channelActive=TRUE,                        /* channelActive */
      #if(CANNM_PASSIVE_MODE_ENABLED == STD_OFF)
        .msgCycleOffset=0u,                         /* msgCycleOffset */
        .msgCycleTime=500u,                         /* msgCycleTime */
        #if(CANNM_BUS_LOAD_REDUCTION_ENABLED == STD_ON)
        // .msgReducedtime=240u,                       /* msgReducedtime */
        .msgReducedtime=236u,                       /* msgReducedtime 3ms*/
        #endif
        #if (CANNM_IMMEDIATE_TXCONF_ENABLED == STD_OFF)
        .msgTimeoutTime=25u,                        /* msgTimeoutTime */
        #endif
        #if(CANNM_NODE_DETECTION_ENABLED == STD_ON)
        .nodeId=0x14u,                              /* nodeId*/
        #endif
      #endif
        #if(CANNM_NODE_ID_ENABLED == STD_ON)
        .pduNidPosition=CANNM_PDU_BYTE_0,           /* pduNidPosition */
        #endif
        .pduCbvPosition=CANNM_PDU_BYTE_1,           /* pduCbvPosition */
        .pduLength=8u,                              /* pduLength */
        #if(CANNM_REMOTE_SLEEP_IND_ENABLED== STD_ON)
        .remoteSleepIndTime=150u,                   /* remoteSleepIndTime */
        #endif   
        .repeatMsgTime=1600u,                       /* repeatMsgTime */
        .timeoutTime=3000u,                         /* timeoutTime */
        .userDataLength=6u,                         /* userDataLength */
        .waitBusSleepTime=2000u,                    /* waitBusSleepTime */
        .ChannelId=0u,                              /* ChannelId */
        #if (CANNM_PASSIVE_MODE_ENABLED == STD_OFF)
        .TxPduId=3u,                                /* TxPduId */
        #endif
        .immediateCycleTime=20u,                    /* immediateCycleTime Reducedtime 10ms */
        .immediateTransmissions=10u,                /* immediateTransmissions */
    },
};

CONST(NetworkHandleType, CANNM_CONST) CanNm_NetworkHandle[CANNM_NUMBER_OF_CHANNELS] =
{
    0x0u,        /* canNmNetworkHandle */
};

// CONST(Nm_ChannelType, CANNM_CONST) Nm_Channels[CANNM_NUMBER_OF_CHANNELS] = 
// {
//     { /* Channel_0 */
//         NM_BUSNM_CANNM,    /* Nm_BusNmType */
//         0                  /* NetworkHandleType */
//     },
// };

