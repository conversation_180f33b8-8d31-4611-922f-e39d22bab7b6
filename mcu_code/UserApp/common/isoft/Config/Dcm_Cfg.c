/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Dcm_Cfg.c>
 *  @brief      <>
 *  
 *  <MCU:MPC5644>
 *  
 *  <AUTHOR>
 *  @date       <2020-10-19 14:32:27>
 */
/*============================================================================*/


/******************************* references ************************************/
#include "Dcm_Types.h"
#include "Dcm_CfgType.h"
#include "Rte_Dcm.h"
#include "Dcm_Cfg.h"
#include "UDS.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "MemMap.h"
/**********************************************************************
 ***********************DcmGeneral Container***************************
 **********************************************************************/
CONST(Dcm_GeneralCfgType,DCM_CONST)Dcm_GeneralCfg =
{
    DCM_DEV_ERROR_DETECT,
    DCM_REQUEST_INDICATION_ENABLED,
    DCM_RESPOND_ALL_REQUEST,
    DCM_VERSION_INFO_API,
    10u
};


/**********************************************************************
 ***********************DSP Container**********************************
 *********************************************************************/
/************************************************
 ****DcmDspSecurityRow container(Multiplicity=0..31)****
 ************************************************/
STATIC  CONST(Dcm_DspSecurityRowType,DCM_CONST)Dcm_DspSecurityRow[2] =
{
    { /* ExtendSecurityLevel */
        1u,      /*DcmDspSecurityLevel*/
        4u,      /*DcmDspSecuritySeedSize*/
        4u,      /*DcmDspSecurityKeySize*/
        0u,      /*DcmDspSecurityADRSize*/
        4u,      /*DcmDspSecurityNumAttDelay*/
        0u,      /*DcmDspSecurityNumAttLock*/
        10000u,  /*DcmDspSecurityDelayTime,10s */
        10000u,       /*DcmDspSecurityDelayTimeOnBoot*/
        5000u,
        {UdsExtendCompKey,UdsExtendGetSeed}
    },
    { /* SecurityLevel_1 */
        0u,      /*DcmDspSecurityLevel*/
        4u,      /*DcmDspSecuritySeedSize*/
        4u,      /*DcmDspSecurityKeySize*/
        0u,      /*DcmDspSecurityADRSize*/
        4u,      /*DcmDspSecurityNumAttDelay*/
        0u,      /*DcmDspSecurityNumAttLock*/
        0u,  /*DcmDspSecurityDelayTime,10s */
        0u,       /*DcmDspSecurityDelayTimeOnBoot*/
        5000u,
        {NULL_PTR,NULL_PTR}
    }
};

/************************************************
 ****DcmDspSecurity container(Multiplicity=1)****
 ************************************************/
STATIC  CONST(Dcm_DspSecurityType,DCM_CONST)Dcm_DspSecurity =
{
    &Dcm_DspSecurityRow[0],
    2u
};

/************************************************
 ****DcmDspSessionRow container(Multiplicity=0..31)
 ************************************************/
STATIC  CONST(Dcm_DspSessionRowType,DCM_CONST)Dcm_DspSessionRow[2] =
{ 
    { /* DefaultSession */
        1u,
        50u,
        5000u
    },
    { /* ExtendSession */
        3u,
        50u,
        5000u
    }
};

/************************************************
 *******Dcm_DspSession container(Multiplicity=1)*
 ************************************************/
STATIC  CONST(Dcm_DspSessionType,DCM_CONST)Dcm_DspSession =
{
    &Dcm_DspSessionRow[0],
    2u,
};

/************************************************
 ******DcmDspDid container(Multiplicity=0..*)****
 ***********************************************/
/******************************************
 *DcmDspDidRead container configration
 *****************************************/
/*DID = DidRW*/
STATIC  CONST(uint8,DCM_CONST)Dcm_DidRW_Read_SecRefCfg[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_DidRW_Read_SesRefCfg[2] = {3,1};
STATIC  CONST(Dcm_DspDidReadType,DCM_CONST)Dcm_DidRW_ReadContainerCfg =
{
    2u,
    &Dcm_DidRW_Read_SecRefCfg[0],
    2u,
    &Dcm_DidRW_Read_SesRefCfg[0],
};
/*DID = DidR*/
STATIC  CONST(uint8,DCM_CONST)Dcm_DidR_Read_SecRefCfg[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_DidR_Read_SesRefCfg[2] = {3,1};
STATIC  CONST(Dcm_DspDidReadType,DCM_CONST)Dcm_DidR_ReadContainerCfg =
{
    2u,
    &Dcm_DidR_Read_SecRefCfg[0],
    2u,
    &Dcm_DidR_Read_SesRefCfg[0],
};
/*DID = DidRW2*/
STATIC  CONST(uint8,DCM_CONST)Dcm_DidRW_Read2_SecRefCfg[1] = {1};
STATIC  CONST(uint8,DCM_CONST)Dcm_DidRW_Read2_SesRefCfg[1] = {3};
STATIC  CONST(Dcm_DspDidReadType,DCM_CONST)Dcm_DidRW_Read2ContainerCfg =
        {
                1u,
                &Dcm_DidRW_Read2_SecRefCfg[0],
                1u,
                &Dcm_DidRW_Read2_SesRefCfg[0],
        };
/*******************************************
 *DcmDspDidWrite container configuration,
 which is in the DcmDspDidInfo container
 ******************************************/
/*DID = DidRW*/
STATIC  CONST(uint8,DCM_CONST)Dcm_DidRW_Write_SecRefCfg[1] = {1};
STATIC  CONST(uint8,DCM_CONST)Dcm_DidRW_Write_SesRefCfg[1] = {3};
STATIC  CONST(Dcm_DspDidWriteType,DCM_CONST)Dcm_DidRW_WriteContainerCfg=
{
    1u,
    &Dcm_DidRW_Write_SecRefCfg[0],
    1u,
    &Dcm_DidRW_Write_SesRefCfg[0],
};

/*******************************************
 *DcmDspDidControl container configuration,
 which is in the DcmDspDidInfo container
 ******************************************/ 



/*******************************************
 **DcmDspDidAccess container configration**
 ******************************************/ 
/*DID = DidRW*/
STATIC  CONST(Dcm_DspDidAccessType,DCM_CONST)Dcm_DidRW_AccessCfg =
{
    NULL_PTR,
    &Dcm_DidRW_ReadContainerCfg,
    &Dcm_DidRW_WriteContainerCfg
};
/*DID = DidRW2*/
STATIC  CONST(Dcm_DspDidAccessType,DCM_CONST)Dcm_DidRW2_AccessCfg =
{
    NULL_PTR,
    &Dcm_DidRW_Read2ContainerCfg,
    &Dcm_DidRW_WriteContainerCfg
};
/*DID = DidR*/
STATIC  CONST(Dcm_DspDidAccessType,DCM_CONST)Dcm_DidR_AccessCfg =
{
    NULL_PTR,
    &Dcm_DidR_ReadContainerCfg,
    NULL_PTR
};

/******************************************
 *DcmDspDidInfo container Configuration *****
 ******************************************/
STATIC  CONST(Dcm_DspDidInfoType,DCM_CONST)Dcm_DspDidInfoCfg[3] =
{
    { /* DidRW */
        FALSE,	/*true = DID can be dynamically defined, false = DID can not bedynamically defined*/
        TRUE,    /*true = datalength of the DID is fixed, false = datalength of the DID is variable*/
        0u,      /*If Scaling information service is available for this DID, it provides the size of the scaling information.*/
        &Dcm_DidRW_AccessCfg
    },
    { /* DidR */
        FALSE,	/*true = DID can be dynamically defined, false = DID can not bedynamically defined*/
        TRUE,    /*true = datalength of the DID is fixed, false = datalength of the DID is variable*/
        0u,      /*If Scaling information service is available for this DID, it provides the size of the scaling information.*/
        &Dcm_DidR_AccessCfg
    },
    { /* DidRW2 */
        FALSE,	/*true = DID can be dynamically defined, false = DID can not bedynamically defined*/
        TRUE,    /*true = datalength of the DID is fixed, false = datalength of the DID is variable*/
        0u,      /*If Scaling information service is available for this DID, it provides the size of the scaling information.*/
        &Dcm_DidRW2_AccessCfg
    }
};

/**********************************************
 *DcmDspDid container configration*************
 **********************************************/
STATIC  CONST(Dcm_DspDidType,DCM_CONST)Dcm_DspDidCfg[] =
{ 
    { /* VehicleManufacturerSparePartNumber */
        0xF187,
        10u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadVehicleManufacturerSparePartNumber,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* SoftwareId */
        0xF188,
        10u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSoftwareId,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* HardwareId */
            0xF191,
            10u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadHardwareId,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* SystemSupplierIdentifier */
        0xF18A,
        10u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSystemSupplierIdentifier,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* SystemSupplierEcuHardWareVersionNumber */
        0xF193,
        8u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSystemSupplierEcuHardWareVersionNumber,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* SystemSupplierEcuSoftWareVersionNumber */
        0xF195,
        8u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSystemSupplierEcuSoftWareVersionNumber,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* TboxSerialNumber */
        0xF18C, 
        20u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadTBoxSerialNumber,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* Vin */
        0xF190,         
        17u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadVin,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_WriteVin,
        0u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* SoftUpdateDate */
         0xF199,
         7u,            /*DcmDspDidSize*/
         NULL_PTR,
         NULL_PTR,
         NULL_PTR,
         NULL_PTR,
         NULL_PTR,
         Rte_ReadSoftUpdateDate,
         NULL_PTR,
         NULL_PTR,
         NULL_PTR,
         NULL_PTR,
         1u,            /*DcmDspDidInfo array subscript*/
         0u,
         NULL_PTR,
         0u,
         NULL_PTR
    },
    { /* SystemName */
        0xF197,         
        6u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSystemName,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* VehicleOfflineConfig */
            0xF110,
            8u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadVehicleOfflineConfig,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteVehicleOfflineConfig,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* TUID */
            0x1600,
            32u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadTUID,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* TBoxAuthStatus */
            0x1601,
            1,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadTBoxAuthStatus,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* SimNumber */
            0x1602,
            13,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadSimNumber,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* ICCID */
        0x1603,
        20u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadICCID,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        1u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
        { /* IMEI */
            0x1604,
            15u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadIMEI,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
        },
    { /* TboxCertificateID */
            0x1605,
            32u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadTboxCertificateID,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link1Addr */
            0x1606,
            32u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink1Addr,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink1Addr,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link1Port */
            0x1607,
            8u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink1Port,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink1Port,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link2Addr */
            0x1608,
            32u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink2Addr,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink2Addr,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link2Port */
            0x1609,
            8u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink2Port,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink2Port,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link3Addr */
            0x1610,
            32u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3Addr,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink3Addr,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link3Port */
            0x1611,
            8u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3Port,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink3Port,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* EcuSerialNumber 内部SN */
            0x1612,
            10u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadEcuSerialNumber,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* ThirdPartyLinkAddr */
            0x1613,
            32u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadThirdPartyLinkAddr,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteThirdPartyLinkAddr,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* ThirdPartyLinkPort */
            0x1614,
            8u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadThirdPartyLinkPort,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteThirdPartyLinkPort,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* ThirdPartyLinkPort */
            0x1615,
            4u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadWakeupTimeInterval,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteWakeupTimeInterval,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link3AddrUsername */
            0x1616,
            64u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3Username,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink3Username,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link3PortPassword */
            0x1617,
            64u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3Password,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink3Password,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* AutoRechargeUnderVoltage */
            0x1618,
            4u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadAutoRechargeUnderVoltage,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteAutoRechargeUnderVoltage,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link1SSLEnable */
            0x1619,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink1SSLEnable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink1SSLEnable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link2SSLEnable */
            0x1620,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink2SSLEnable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink2SSLEnable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* OfflineCheckFlag */
            0x1621,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadOfflineCheckFlag,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteOfflineCheckFlag,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* ACTimeoutMinutes */
            0x1622,
            4u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadACTimeoutMinutes,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteACTimeoutMinutes,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* TimedWakeupEnable */
            0x1623,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadTimedWakeupEnable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteTimedWakeupEnable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* NetworkWakeupEnable */
            0x1624,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadNetworkWakeupEnable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteNetworkWakeupEnable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* GlobalLogEnable */
            0x1625,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadGlobalLogEnable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteGlobalLogEnable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link1Enable */
            0x1626,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink1Enable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink1Enable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link2Enable */
            0x1627,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink2Enable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink2Enable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link3Enable */
            0x1628,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3Enable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink3Enable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* ThirdPartyLinkEnable */
            0x1629,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadThirdPartyLinkEnable,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteThirdPartyLinkEnable,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* CertUpdateTime */
            0x1630,
            7u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadCertUpdateTime,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* DataResendTestTime */
            0x1631,
            2u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadDataResendTestTime,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteDataResendTestTime,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* DataResendTestTime */
            0x1632,
            2u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLevel3AlarmTestTime,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLevel3AlarmTestTime,
            0u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    // { /* DataResendTestTime */
    //         0x1633,
    //         1u,            /*DcmDspDidSize*/
    //         NULL_PTR,
    //         NULL_PTR,
    //         NULL_PTR,
    //         NULL_PTR,
    //         NULL_PTR,
    //         Rte_ReadVehicleRestrictFlag,
    //         NULL_PTR,
    //         NULL_PTR,
    //         NULL_PTR,
    //         Rte_WriteVehicleRestrictFlag,
    //         0u,            /*DcmDspDidInfo array subscript*/
    //         0u,
    //         NULL_PTR,
    //         0u,
    //         NULL_PTR
    // },
    { /*  */
            0x1634,
            8u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3BiAuthPort,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_WriteLink3BiAuthPort,
            2u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
{ /* LockCarStatus */
        0x1636,
        1u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadLockCarStatusValue,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_WriteLockCarStatusValue,
        0u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
{ /* SpeedLimitStatus */
        0x1637,
        1u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSpeedLimitStatusValue,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_WriteSpeedLimitStatusValue,
        0u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* SpeedLimitValue */
        0x1635,
        1u,            /*DcmDspDidSize*/
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_ReadSpeedLimitValue,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        Rte_WriteSpeedLimitValue,
        0u,            /*DcmDspDidInfo array subscript*/
        0u,
        NULL_PTR,
        0u,
        NULL_PTR
    },
    { /* ECUBatteryVoltage */
            0xCF00,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadEcuBatteryVoltage,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* VehicleSpeed */
            0xCF01,
            2u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadVehicleSpeed,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Odometer */
            0xCF02,
            4u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadOdometer,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* LowPowerMode */
            0xCF03,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLowPowerMode,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* DateTime */
            0xCF04,
            6u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadDateTime,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* VehiclePowerMode */
            0xCF05,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadVehiclePowerMode,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* GearPosition */
            0xCF06,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadGearPosition,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* CellularSignalStrength */
            0x1650,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadCellularSignalStrength,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* GnssSignalStrength */
            0x1651,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadGnssSignalStrength,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* WifiSignalStrength */
            0x1652,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadWifiSignalStrength,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link1TcpStatus */
            0x1653,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink1TcpStatus,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link2TcpStatus */
            0x1654,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink2TcpStatus,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* Link3TcpStatus */
            0x1655,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadLink3TcpStatus,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* PowerManagementMode */
            0x1656,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadPowerManagementMode,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* MileageClearCount */
            0x1660,
            1u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadMileageClearCount,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* TboxSavesMileageValues */
            0x1661,
            4u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadTboxSavesMileageValues,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* PowerManagementMode */
            0x1670,
            20u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadRdsSoftwareIn,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* PowerManagementMode */
            0x1671,
            10u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadRdsSoftwareSupplierIdentifier,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
    { /* PowerManagementMode */
            0x1672,
            10u,            /*DcmDspDidSize*/
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            Rte_ReadRdsSoftwareOut,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            NULL_PTR,
            1u,            /*DcmDspDidInfo array subscript*/
            0u,
            NULL_PTR,
            0u,
            NULL_PTR
    },
};

/***********************************************
 ***DcmDspEcuReset container configration*******
 ***********************************************/
STATIC  CONST(Dcm_EcuResetType,DCM_CONST)Dcm_EcuResetPort[1]=
{
    Rte_EcuReset,
};

STATIC  CONST(uint8,DCM_CONST)Dcm_DspEcuReset_Rte_SoftEcuReset_RefSecCfg[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_DspEcuReset_Rte_SoftEcuReset_RefSesCfg[2] = {1,3};
STATIC  CONST(Dcm_DspEcuResetType,DCM_CONST)Dcm_DspEcuResetCfg[2]=
{
        { /*  */
        1u,    /*ResetType=hard reset */
        2u,
        &Dcm_DspEcuReset_Rte_SoftEcuReset_RefSecCfg[0],
        2u,
        &Dcm_DspEcuReset_Rte_SoftEcuReset_RefSesCfg[0],
        },
    { /*  */
        3u,    /*ResetType=Soft reset */
        2u,
        &Dcm_DspEcuReset_Rte_SoftEcuReset_RefSecCfg[0],
        2u,
        &Dcm_DspEcuReset_Rte_SoftEcuReset_RefSesCfg[0],
    }
};

/*************************************************
 ************DcmDspReadDTC container configration
 *************************************************/
CONST(uint8,DCM_CONST)Dcm_DspReadDTC_ReadDTCNum_RefSecCfg[2] = {0,1};
CONST(uint8,DCM_CONST)Dcm_DspReadDTC_ReadDTCList_RefSecCfg[2] = {0,1};
CONST(uint8,DCM_CONST)Dcm_DspReadDTC_ReadDTCSnapshot_RefSecCfg[2] = {0,1};
CONST(uint8,DCM_CONST)Dcm_DspReadDTC_ReadDTC_Supported_RefSecCfg[2] = {1,0};

CONST(Dcm_DspReadDTCRowType,DCM_CONST)Dcm_DspReadDTCRowCfg[4] =
{
    { /* ReadDTCNum */
        0x01,  /*subfunction: reportNumberOfDTCByStatusMask */
        TRUE,  /*TRUE =sub-function supported. FALSE = sub-function not supported*/
        2u,
        &Dcm_DspReadDTC_ReadDTCNum_RefSecCfg[0],
    },
    { /* ReadDTCList */
        0x02,  /*subfunction: reportNumberOfDTCByStatusMask */
        TRUE,  /*TRUE =sub-function supported. FALSE = sub-function not supported*/
        2u,
        &Dcm_DspReadDTC_ReadDTCList_RefSecCfg[0],
    },
    { /* ReadDTCList */
            0x04,  /*subfunction: reportNumberOfDTCByStatusMask */
            TRUE,  /*TRUE =sub-function supported. FALSE = sub-function not supported*/
            2u,
            &Dcm_DspReadDTC_ReadDTCSnapshot_RefSecCfg[0],
    },
    { /* ReadDTC_Supported */
        0x0A,  /*subfunction: reportNumberOfDTCByStatusMask */
        TRUE,  /*TRUE =sub-function supported. FALSE = sub-function not supported*/
        2u,
        &Dcm_DspReadDTC_ReadDTC_Supported_RefSecCfg[0],
    }
};

CONST(Dcm_DspReadDTCType,DCM_CONST)Dcm_DspReadDTCCfg =
{
    4u,
    &Dcm_DspReadDTCRowCfg[0]
};

/*************************************************
 *****DcmDspRoutine container configration********
 *************************************************/
/***********************************
 *DcmDspRoutineAuthorization container
 **********************************/

/***********************************
 *DcmDspRoutineStart container
 **********************************/

/***********************************
 *DcmDspRoutineStop container
 **********************************/

/***********************************
 *DcmDspRoutineRequestRes container configration
 **********************************/
/*In the the response of RequestResult,the size of the number of bytes for Optional record  */


/***********************************
 *DcmDspRoutine container configration
 **********************************/

/************************************************
 *******DcmDsp container configration(Multiplicity=1)**
 ************************************************/
CONST(Dcm_DspCfgType,DCM_CONST)Dcm_DspCfg =
{
    0, /*Indicates the maximum allowed DIDs in a single "ReadDataByIdentifier" request. If set to 0, then no limitation is applied. */
    65u,
    &Dcm_DspDidCfg[0],    /* Dids */
    3u,
    &Dcm_DspDidInfoCfg[0], /* Did Infos*/
    2u,
    &Dcm_DspEcuResetCfg[0],/* Resets */ 
    1u,
    &Dcm_EcuResetPort[0],   /* Resets Callback*/
    &Dcm_DspReadDTCCfg,        /* Read DTCs */
    DCM_DSP_ROUTINE_MAX_NUM,
    NULL,
    0,
    NULL,
    &Dcm_DspSecurity,  /* Security levels */
    &Dcm_DspSession,  /* Sessions */
};
/*****************************************************************************************
 ********************************* DSD container configration*****************************
 *****************************************************************************************/
/**********************************************************************/
/******UDS Service session and security configration******/
//STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service01_SecRef[2] = {0,1};
//STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service01_SesRef[2] = {1,3};
//STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service09_SecRef[2] = {0,1};
//STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service09_SesRef[2] = {3,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service10_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service10_SesRef[2] = {1,3};  
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service22_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service22_SesRef[2] = {3,1};  
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service2e_SecRef[1] = {1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service2e_SesRef[1] = {3};  
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service27_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service27_SesRef[1] = {3};  
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service11_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service11_SesRef[2] = {1,3};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service14_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service14_SesRef[2] = {3,1};  
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service28_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service28_SesRef[1] = {3};  
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service19_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service19_SesRef[2] = {1,3};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service3e_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service3e_SesRef[2] = {1,3};
//STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service31_SecRef[2] = {0,1};
//STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service31_SesRef[2] = {1,3};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service85_SecRef[2] = {0,1};
STATIC  CONST(uint8,DCM_CONST)Dcm_g_udsServiceTable_Service85_SesRef[1] = {3};  
/**********************************************************************/
STATIC CONST(Dcm_DsdServiceCfgType,DCM_CONST)Dcm_Dsd_g_udsServiceTable_ServiceTable_Service[DCM_UDS_SERVICE_NUM] =
{
//    { /* Service01 */
//        0x01u,
//        TRUE,
//        2u,
//        &Dcm_g_udsServiceTable_Service01_SecRef[0],
//        2u,
//        &Dcm_g_udsServiceTable_Service01_SesRef[0],
//        DCM_ADDRESSING_PHYANDFUNC,
//        DspInternal_OBD0x01,
//    },
//    { /* Service09 */
//        0x09u,
//        TRUE,
//        2u,
//        &Dcm_g_udsServiceTable_Service09_SecRef[0],
//        2u,
//        &Dcm_g_udsServiceTable_Service09_SesRef[0],
//        DCM_ADDRESSING_PHYANDFUNC,
//        DspInternal_OBD0x09,
//    },
    { /* Service10 */
        0x10u,
        TRUE,
        2u,
        &Dcm_g_udsServiceTable_Service10_SecRef[0],
        2u,
        &Dcm_g_udsServiceTable_Service10_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x10,
    },
    { /* Service22 */
        0x22u,
        FALSE,
        2u,
        &Dcm_g_udsServiceTable_Service22_SecRef[0],
        2u,
        &Dcm_g_udsServiceTable_Service22_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x22,
    },
    { /* Service2e */
        0x2Eu,
        FALSE,
        1u,
        &Dcm_g_udsServiceTable_Service2e_SecRef[0],
        1u,
        &Dcm_g_udsServiceTable_Service2e_SesRef[0],
        DCM_ADDRESSING_PHYSICAL,
        DspInternal_UDS0x2E,
    },
    { /* Service27 */
        0x27u,
        FALSE,
        2u,
        &Dcm_g_udsServiceTable_Service27_SecRef[0],
        1u,
        &Dcm_g_udsServiceTable_Service27_SesRef[0],
        DCM_ADDRESSING_PHYSICAL,
        DspInternal_UDS0x27,
    },
    { /* Service11 */
        0x11u,
        TRUE,
        2u,
        &Dcm_g_udsServiceTable_Service11_SecRef[0],
        2u,
        &Dcm_g_udsServiceTable_Service11_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x11,
    },
    { /* Service14 */
        0x14u,
        FALSE,
        2u,
        &Dcm_g_udsServiceTable_Service14_SecRef[0],
        2u,
        &Dcm_g_udsServiceTable_Service14_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x14,
    },
    { /* Service28 */
        0x28u,
        TRUE,
        2u,
        &Dcm_g_udsServiceTable_Service28_SecRef[0],
        1u,
        &Dcm_g_udsServiceTable_Service28_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x28,
    },
    { /* Service19 */
        0x19u,
        FALSE,
        2u,
        &Dcm_g_udsServiceTable_Service19_SecRef[0],
        2u,
        &Dcm_g_udsServiceTable_Service19_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x19,
    },
//    { /* Service31 */
//        0x31u,
//        TRUE,
//        2u,
//        &Dcm_g_udsServiceTable_Service31_SecRef[0],
//        2u,
//        &Dcm_g_udsServiceTable_Service31_SesRef[0],
//        DCM_ADDRESSING_PHYSICAL,
//        DspInternal_UDS0x31,
//    },
    { /* Service3e */
        0x3Eu,
        TRUE,
        2u,
        &Dcm_g_udsServiceTable_Service3e_SecRef[0],
        2u,
        &Dcm_g_udsServiceTable_Service3e_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x3E,
    },
    { /* Service85 */
        0x85u,
        TRUE,
        2u,
        &Dcm_g_udsServiceTable_Service85_SecRef[0],
        1u,
        &Dcm_g_udsServiceTable_Service85_SesRef[0],
        DCM_ADDRESSING_PHYANDFUNC,
        DspInternal_UDS0x85,
    }  
};

/**********************************************************************/
/*DCM Support Service Table(Multiplicity=1..256)*/
STATIC  CONST(Dcm_DsdServiceTableCfgType,DCM_CONST)Dcm_DsdServiceTable[DCM_SERVICE_TAB_NUM]=
{
    {
        0x0,
        &Dcm_Dsd_g_udsServiceTable_ServiceTable_Service[0],
        DCM_UDS_SERVICE_NUM	
    }
};

/**********************************************************************/
/*Dsd container(Multiplicity=1)*/
CONST(Dcm_DsdCfgType,DCM_CONST)Dcm_DsdCfg =
{
    &Dcm_DsdServiceTable[0],
    DCM_SERVICE_TAB_NUM
};

/*****************************************************************************************
 ********************************* DSL container configration*****************************
 *****************************************************************************************/
/*DcmDslBuffer container(Multiplicity=1..256)*/
STATIC  CONST(Dcm_DslBufferType,DCM_CONST)Dcm_DslBufferCfg[DCM_CHANNEL_NUM] =
{
    {
        0x0u, 
        255u,
        0u
    },
    {
        0x1u, 
        255u,
        255u
    },
    {
        0x2u, 
        255u,
        510u
    }
};

/***********************************/
/*DcmDslDiagResp container(Multiplicity=1)*/
STATIC  CONST(Dcm_DslDiagRespType,DCM_CONST)Dcm_DslDiagRespCfg =
{
    DCM_DSLDIAGRESP_FORCERESPENDEN,
    0u
};

/*****************************************************
 *DcmDslCallbackDCMRequestService port configration(Multiplicity=1..*)
 *****************************************************/
STATIC  CONST(Dcm_DslCallbackDCMRequestServiceType,DCM_CONST)Dcm_DslCallbackDCMRequestServiceCfg[1] =
{
    {
        StartProtocol,
        StopProtocol
    }
};

/*****************************************************
 *DcmDslSessionControl port configration(Multiplicity=1..*)*******
 ****************************************************/
STATIC  CONST(Dcm_DslSessionControlType,DCM_CONST)Dcm_DslSessionControlCfg[3]=
{
    {
        UdsChangeDefaultIndication,
        UdsGetDefaultSesChgPermission
    },
    {
        UdsChangeExtendIndication,
        UdsGetExtendSesChgPermission
    },
    {
        UdsProgrammingChangeIndication,
        UdsProgrammingGetSesChgPermission
    }
};

/*****************************************************
 *DcmDslServiceRequestIndication port configration(Multiplicity=0..*)*
 ****************************************************/
STATIC  CONST(Dcm_ServiceRequestIndicationType,DCM_CONST)Dcm_ServiceRequestIndicationCfg[1] =
{
    {
        UdsIndicationService
    }
};

/*****************************************************
 ****DcmDslProtocolTiming container(Multiplicity=1)***********
 ****************************************************/
CONST(Dcm_DslProtocolTimingRowType,DCM_CONST)Dcm_DslProtocolTimingRowCfg[1] =
{
    {
        50U,  	/*P2ServerMax*/
        0U,
        5000U,	/* P2StarServerMax */
        0U,
        5000U	/*S3Server*/
    }
};

CONST(Dcm_DslProtocolTimingType,DCM_CONST)Dcm_DslProtocolTimingCfg=
{
    &Dcm_DslProtocolTimingRowCfg[0],
    1u,
};

/******************************************************
 *************DcmDslConnection container***************
 *****************************************************/

/********************UDS protocal Connection configration*******************/
/*Connection1,Mainconnection,ProtocolRx configration(Multiplicity=1..*)*/
STATIC  CONST(Dcm_DslProtocolRxType,DCM_CONST)Dcm_TboxUdsProtocol_TboxUdsConnection_RxCfg[2]=
{
    {
        DCM_FUNCTIONAL,       /*DcmDslProtocolRxAddrType*/
        0x01u,              /*DcmDslProtocolRxPduId*/
    },
    {
        DCM_PHYSICAL,       /*DcmDslProtocolRxAddrType*/
        0x0u,              /*DcmDslProtocolRxPduId*/
    }
};

/*Connection1,Mainconnection,ProtocolTx configration(Multiplicity=1..*)*/
STATIC  CONST(Dcm_DslMainConnectionType,DCM_CONST) Dcm_TboxUdsProtocol_TboxUdsConnectionCfg =
{
    NULL_PTR,
    NULL_PTR,
    &Dcm_TboxUdsProtocol_TboxUdsConnection_RxCfg[0], /*pDcmDslProtocolRx*/
    2u,                     /*DcmDslProtocolRx_Num*/
    0x0u                  /*DcmDslProtocolTxPduId*/
};

/*Connection1 configration*/
STATIC  CONST(Dcm_DslConnectionType,DCM_CONST)Dcm_Dsl_TboxUdsProtocol_ConnectionCfg[1]=
{
    {
        &Dcm_TboxUdsProtocol_TboxUdsConnectionCfg,  /*pDcmDslMainConnection*/
        NULL_PTR,         /*pDcmDslPeriodicTransmission*/
        NULL_PTR          /*pDcmDslResponseOnEvent*/
    }
};
/*****************************************************
 ****Dcm_DslProtocolRow container configration(Multiplicity=1..*)*******
 ****************************************************/
STATIC  CONST(Dcm_DslProtocolRowType,DCM_CONST)Dcm_DslProtocolRowCfg[DCM_DSLPROTOCOLROW_NUM_MAX] =
{
    {
        DCM_UDS_ON_CAN,			/*DcmDslProtocolID*/
        FALSE,					/*DcmDslProtocolIsParallelExecutab*/
        0u,					/*DcmDslProtocolPreemptTimes*/
        0u,						/*DcmDslProtocolPriority*/
        DCM_PROTOCOL_TRAN_TYPE1,/*DcmDslProtocolTransType*/
        0x0,					/*DcmDslProtocolRxBufferID*/
        0x2,					/*DcmDslProtocolTxBufferID*/
        0x0, 					/*DcmDslServiceTableID*/
        &Dcm_DslProtocolTimingRowCfg[0],			    /*pDcmDslProtocolTimeLimit*/
        &Dcm_Dsl_TboxUdsProtocol_ConnectionCfg[0],/*DcmDslConnection*/
        1u,		/*Number of connection*/
    }
};

/*****************************************************
 *DcmDslProtocol container configration(Multiplicity=1)
 ****************************************************/
STATIC  CONST(Dcm_DslProtocolType,DCM_CONST)Dcm_DslProtocol =
{
    &Dcm_DslProtocolRowCfg[0],
    DCM_DSLPROTOCOLROW_NUM_MAX,
};

/*****************************************************
 ****************DcmDsl container configration*****
 ****************************************************/
CONST(Dcm_DslCfgType,DCM_CONST)Dcm_DslCfg =
{
    DCM_CHANNEL_NUM,				/*Number of Channel configration*/
    &Dcm_DslBufferCfg[0],

    1u,	/*Number of DslCallbackDCMRequestService port*/
    &Dcm_DslCallbackDCMRequestServiceCfg[0],
    1u,			/*Number of ServiceRequestIndication port*/
    &Dcm_ServiceRequestIndicationCfg[0],
    3u,			/*Number of SessionControl port*/
    &Dcm_DslSessionControlCfg[0], /*reference to SessionControl port configration*/

    &Dcm_DslDiagRespCfg,          /*reference to DcmDslDiagResp configration*/
    &Dcm_DslProtocol,             /*reference to DcmDslProtocol configration*/
    &Dcm_DslProtocolTimingCfg
};

#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "MemMap.h"