/*
 * NetworkManagement_Cfg.h
 * 描述：网络管理配置头文件，定义三种网络管理模式的宏
 * 作者：guoyuchen
 * 时间：2025.07.08
 */

#ifndef NETWORK_MANAGEMENT_CFG_H
#define NETWORK_MANAGEMENT_CFG_H

/*=======[网络管理模式配置]====================================================*/
/*
 * 网络管理模式选择：
 * 只能选择其中一种模式，不能同时定义多个
 */

/* 模式1：OSEK网络管理 */
// #define CAN_ENABLE_OSEKNM

/* 模式2：AUTOSAR CAN网络管理 */
// #define CAN_ENABLE_AUTOSAR_NM

/* 模式3：无网络管理 */
#define CAN_ENABLE_NO_NM

/*=======[网络管理模式验证]====================================================*/
/* 确保只定义了一种网络管理模式 */
#if defined(CAN_ENABLE_OSEKNM) && defined(CAN_ENABLE_AUTOSAR_NM)
    #error "Cannot enable both OSEK NM and AUTOSAR NM simultaneously"
#endif

#if defined(CAN_ENABLE_OSEKNM) && defined(CAN_ENABLE_NO_NM)
    #error "Cannot enable both OSEK NM and NO NM simultaneously"
#endif

#if defined(CAN_ENABLE_AUTOSAR_NM) && defined(CAN_ENABLE_NO_NM)
    #error "Cannot enable both AUTOSAR NM and NO NM simultaneously"
#endif

/* 确保至少定义了一种网络管理模式 */
#if !defined(CAN_ENABLE_OSEKNM) && !defined(CAN_ENABLE_AUTOSAR_NM) && !defined(CAN_ENABLE_NO_NM)
    #error "Must define one network management mode"
#endif

/*=======[网络管理功能开关]====================================================*/
#ifdef CAN_ENABLE_OSEKNM
    /* OSEK网络管理模式 */
    #define NETWORK_MANAGEMENT_ENABLED      1
    #define OSEK_NM_ENABLED                 1
    #define AUTOSAR_NM_ENABLED              0
    #define NO_NM_ENABLED                   0
#elif defined(CAN_ENABLE_AUTOSAR_NM)
    /* AUTOSAR网络管理模式 */
    #define NETWORK_MANAGEMENT_ENABLED      1
    #define OSEK_NM_ENABLED                 0
    #define AUTOSAR_NM_ENABLED              1
    #define NO_NM_ENABLED                   0
#elif defined(CAN_ENABLE_NO_NM)
    /* 无网络管理模式 */
    #define NETWORK_MANAGEMENT_ENABLED      0
    #define OSEK_NM_ENABLED                 0
    #define AUTOSAR_NM_ENABLED              0
    #define NO_NM_ENABLED                   1
#endif

/*=======[网络管理相关配置]====================================================*/
#ifdef CAN_ENABLE_OSEKNM
    /* OSEK NM相关配置 */
    #define NM_NETWORK_CHANNEL_ID           0x00
    #define NM_MESSAGE_ID_RANGE_START       0x600
    #define NM_MESSAGE_ID_RANGE_END         0x67F
#elif defined(CAN_ENABLE_AUTOSAR_NM)
    /* AUTOSAR NM相关配置 */
    #define NM_NETWORK_CHANNEL_ID           0x00
    #define NM_MESSAGE_ID_RANGE_START       0x600
    #define NM_MESSAGE_ID_RANGE_END         0x67F
#elif defined(CAN_ENABLE_NO_NM)
    /* 无网络管理模式下的配置 */
    #define NM_NETWORK_CHANNEL_ID           0x00
    #define NM_MESSAGE_ID_RANGE_START       0x600
    #define NM_MESSAGE_ID_RANGE_END         0x67F
    /* 在无网络管理模式下，这些ID范围仅用于应用报文识别 */
#endif

#endif /* NETWORK_MANAGEMENT_CFG_H */
