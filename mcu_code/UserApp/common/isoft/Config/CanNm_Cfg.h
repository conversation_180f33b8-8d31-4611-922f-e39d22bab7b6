/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <CanNm_Cfg.h>
 *  @brief      <>
 *  
 *  <MCU:TC1782>
 *  
 *  <AUTHOR>
 *  @date       <2016-08-31 14:17:37>
 */
/*============================================================================*/


#ifndef CANNM_CFG_H_
#define CANNM_CFG_H_

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       2016      SherryShen  Initial version
 */
/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define CANNM_CFG_H_AR_MAJOR_VERSION  3u
#define CANNM_CFG_H_AR_MINOR_VERSION  2u
#define CANNM_CFG_H_AR_PATCH_VERSION  0u
#define CANNM_CFG_H_SW_MAJOR_VERSION  1u
#define CANNM_CFG_H_SW_MINOR_VERSION  0u
#define CANNM_CFG_H_SW_PATCH_VERSION  0u

/*=======[I N C L U D E S]====================================================*/

/*=======[M A C R O S]========================================================*/
/* Config CanNM Module Variant */
#define CANNM_VARIANT_PC             VARIANT_PRE_COMPILE
#define CANNM_VARIANT_LT             VARIANT_LINK_TIME
#define CANNM_VARIANT_PB             VARIANT_POST_BUILD
#define CANNM_VARIANT_CFG            CANNM_VARIANT_PC      /* Variant Config */

#define CANNM_NUMBER_OF_CHANNELS            0x1u
#define CANNM_OS_COUNTER_ID                 0x0u

#define CANNM_DEV_ERROR_DETECT              STD_OFF        /* development error detection */
#define CANNM_DEM_ERROR_DETECT              STD_OFF        /* Diagnostic Event Manager */
#define CANNM_VERSION_INFO_API              STD_OFF        /* Com_VersionInfo */
#define CANNM_NODE_ID_ENABLED               STD_ON         /* source node identifier */
#define CANNM_PASSIVE_MODE_ENABLED          STD_OFF        /* support of the Passive Mode */

/*ReqCANNM163*/
#if(CANNM_PASSIVE_MODE_ENABLED == STD_ON)
	#define CANNM_BUS_LOAD_REDUCTION_ENABLED    STD_ON         /* busload reduction */
	#define CANNM_BUS_SYNCHRONIZATION_ENABLED   STD_OFF        /* bus synchronization */
	#define CANNM_NODE_DETECTION_ENABLED        STD_OFF        /* node detection */
	#define CANNM_REMOTE_SLEEP_IND_ENABLED      STD_OFF        /* remote sleep indication */
	#define CANNM_COM_CONTROL_ENABLED           STD_ON         /* Communication Control */
	#define CANNM_IMMEDIATE_RESTART_ENABLED     STD_OFF        /* asynchronous transmission of a NM PDU upon bus-communication request in Prepare-Bus-Sleep mode */
	#define CANNM_IMMEDIATE_TXCONF_ENABLED      STD_OFF        /* immediate tx confirmation */
#else
	#define CANNM_BUS_LOAD_REDUCTION_ENABLED    STD_ON        /* busload reduction or */
	#define CANNM_BUS_SYNCHRONIZATION_ENABLED   STD_ON        /* bus synchronization */
	
	#if (CANNM_NODE_ID_ENABLED == STD_ON)
		#define CANNM_NODE_DETECTION_ENABLED        STD_ON        /* node detection */
	#else
		#define CANNM_NODE_DETECTION_ENABLED        STD_OFF       /* node detection */
	#endif

	#define CANNM_REMOTE_SLEEP_IND_ENABLED      STD_ON        /* remote sleep indication */
	#define CANNM_COM_CONTROL_ENABLED           STD_ON        /* Communication Control */
	#define CANNM_IMMEDIATE_RESTART_ENABLED     STD_OFF        /* asynchronous transmission of a NM PDU upon bus-communication request in Prepare-Bus-Sleep mode */
	#define CANNM_IMMEDIATE_TXCONF_ENABLED      STD_OFF        /* immediate tx confirmation */
#endif

#define CANNM_PDU_RX_INDICATION_ENABLED     STD_ON        /* PDU Rx Indication */
#define CANNM_REPEAT_MSG_IND_ENABLED        STD_ON        /* When receiving a RepeatMessageRequest bit, enable or disable the notification */
#define CANNM_STATE_CHANGE_IND_ENABLED      STD_ON        /* CAN NM state change notification */
#define CANNM_USER_DATA_ENABLED             STD_ON        /* user data support */
#define CANNM_USER_CONTROL_ENABLED          STD_ON        /* user control support */

#endif /* CANNM_CFG_H_ */