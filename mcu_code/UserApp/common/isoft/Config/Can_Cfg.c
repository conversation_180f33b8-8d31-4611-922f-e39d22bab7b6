/*============================================================================*/
/*  Copyright (C) 2009-2014, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <Can_Cfg.c>
 *  @brief      <>
 *
 *  <MCU:MPC5644>
 *
 *  <AUTHOR>
 *  @date       <2019-06-25 18:38:30>
 */
/*============================================================================*/
#include "Can_Cfg.h"
#include "event.h"

/*=======[I N C L U D E S]====================================================*/

/*=======[V E R S I O N   I N F O R M A T I O N]===============================*/
#define CAN_CFG_C_AR_MAJOR_VERSION 2
#define CAN_CFG_C_AR_MINOR_VERSION 4
#define CAN_CFG_C_AR_PATCH_VERSION 0
#define CAN_CFG_C_SW_MAJOR_VERSION 1
#define CAN_CFG_C_SW_MINOR_VERSION 0
#define CAN_CFG_C_SW_PATCH_VERSION 0

/*=======[V E R S I O N  C H E C K]===========================================*/
#if (CAN_CFG_C_AR_MAJOR_VERSION != CAN_CFG_H_AR_MAJOR_VERSION)
#error "Can_Cfg.c:Mismatch in Specification Major Version"
#endif
#if (CAN_CFG_C_AR_MINOR_VERSION != CAN_CFG_H_AR_MINOR_VERSION)
#error "Can_Cfg.c:Mismatch in Specification Minor Version"
#endif
#if (CAN_CFG_C_AR_PATCH_VERSION != CAN_CFG_H_AR_PATCH_VERSION)
#error "Can_Cfg.c:Mismatch in Specification Patch Version"
#endif
#if (CAN_CFG_C_SW_MAJOR_VERSION != CAN_CFG_H_SW_MAJOR_VERSION)
#error "Can_Cfg.c:Mismatch in Specification Major Version"
#endif
#if (CAN_CFG_C_SW_MINOR_VERSION != CAN_CFG_H_SW_MINOR_VERSION)
#error "Can_Cfg.c:Mismatch in Specification Minor Version"
#endif

/*=======[E X T E R N A L   D A T A]==========================================*/

#define CAN_START_CONST_PBCFG
#include "Can_MemMap.h"
/* HRHs first,HTH next*/
/* Controller ID must be grouped together */
/* Can ID to ensure top priority IDs are first */
CONST(Can_HardwareObjectType, CAN_CONST_PBCFG)
Can_HardwareObjectConfigData[CAN_MAX_HARDWAREOBJECTS] =
    {
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x000,                   /* CanIdValue */
            .CanObjectId      = 0,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_A,        /* CanControllerRef */
            .CanFilterMask    = 0x80000000               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,     /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,     /* CanIdType*/
            .CanIdValue       = 0x1,                      /* CanIdValue */
            .CanObjectId      = 1,                        /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_TRANSMIT, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_A,         /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF                /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x000,                   /* CanIdValue */
            .CanObjectId      = 2,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_B,        /* CanControllerRef */
            .CanFilterMask    = 0x80000000               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,     /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,     /* CanIdType*/
            .CanIdValue       = 0x2,                      /* CanIdValue */
            .CanObjectId      = 3,                        /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_TRANSMIT, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_B,         /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF                /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x40a,                   /* CanIdValue */
            .CanObjectId      = 4,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x404,                   /* CanIdValue */
            .CanObjectId      = 5,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x406,                   /* CanIdValue */
            .CanObjectId      = 6,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x402,                   /* CanIdValue */
            .CanObjectId      = 7,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x410,                   /* CanIdValue */
            .CanObjectId      = 8,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x418,                   /* CanIdValue */
            .CanObjectId      = 9,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x408,                   /* CanIdValue */
            .CanObjectId      = 10,                      /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x412,                   /* CanIdValue */
            .CanObjectId      = 11,                      /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x000,                   /* CanIdValue */
            .CanObjectId      = 12,                      /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,        /* CanControllerRef */
            .CanFilterMask    = 0x80000000               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,     /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,     /* CanIdType*/
            .CanIdValue       = 0x414,                    /* CanIdValue */
            .CanObjectId      = 13,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_TRANSMIT, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,         /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF                /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,     /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,     /* CanIdType*/
            .CanIdValue       = 0x3,                      /* CanIdValue */
            .CanObjectId      = 14,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_TRANSMIT, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_C,         /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF                /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,    /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,    /* CanIdType*/
            .CanIdValue       = 0x000,                   /* CanIdValue */
            .CanObjectId      = 15,                      /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_RECEIVE, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_D,        /* CanControllerRef */
            .CanFilterMask    = 0x80000000               /* CanFilterMask */
        },
        {
            .CanHandleType    = CAN_HANDLE_TYPE_FULL,     /* CanHandleType */
            .CanIdType        = CAN_ID_TYPE_EXTENDED,     /* CanIdType*/
            .CanIdValue       = 0x4,                      /* CanIdValue */
            .CanObjectId      = 16,                       /* CanObjectId */
            .CanObjectType    = CAN_OBJECT_TYPE_TRANSMIT, /* CanObjectType */
            .CanControllerRef = CAN_CONTROLLER_D,         /* CanControllerRef */
            .CanFilterMask    = 0xFFFFFFFF                /* CanFilterMask */
        },
};

CONST(uint16, CAN_CONST_PBCFG)
Can_HohConfigData[CAN_MAX_HOHS] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};

CONST(Can_ControllerConfigType, CAN_CONST_PBCFG)
Can_ControllerConfigData[CAN_MAX_CONTROLLERS] =
    {
        {
            0x012b0001,
            /* CanCtrlValue--> BaudRate:500 Kbps */  // 0x033a0001
            0,          /* CanRxHwObjFirst */
            1,          /* CanRxHwObjCount */
            1,          /* CanTxHwObjFirst */
            1,          /* CanTxHwObjCount */
        },
        {
            0x012b0001, /* CanCtrlValue--> BaudRate:500 Kbps */
            2,          /* CanRxHwObjFirst */
            1,          /* CanRxHwObjCount */
            3,          /* CanTxHwObjFirst */
            1,          /* CanTxHwObjCount */
        },
        {
            0x012b0001, /* CanCtrlValue--> BaudRate:500 Kbps */
            4,          /* CanRxHwObjFirst */
            9,          /* CanRxHwObjCount */
            13,         /* CanTxHwObjFirst */
            2,          /* CanTxHwObjCount */
        },
        {
            0x012b0001, /* CanCtrlValue--> BaudRate:500 Kbps */
            15,         /* CanRxHwObjFirst */
            1,          /* CanRxHwObjCount */
            16,         /* CanTxHwObjFirst */
            1,          /* CanTxHwObjCount */
        },
};
#define CAN_STOP_CONST_PBCFG
#include "Can_MemMap.h"

#define CAN_START_SEC_CONST_UNSPECIFIED
#include "Can_MemMap.h"
CONST(Can_ControllerPCConfigType, CAN_CONST)
Can_ControllerPCConfigData[CAN_MAX_CONTROLLERS] =
    {
        {
            CAN_CONTROLLER_A,           /* CanControllerId */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanBusOffProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanRxProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanTxProcessing */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanWakeupProcessing */

        },
        {
            CAN_CONTROLLER_B,           /* CanControllerId */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanBusOffProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanRxProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanTxProcessing */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanWakeupProcessing */

        },
        {
            CAN_CONTROLLER_C,           /* CanControllerId */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanBusOffProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanRxProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanTxProcessing */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanWakeupProcessing */
        },
        {
            CAN_CONTROLLER_D,           /* CanControllerId */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanBusOffProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanRxProcessing */
            CAN_PROCESS_TYPE_POLLING,   /* CanTxProcessing */
            CAN_PROCESS_TYPE_INTERRUPT, /* CanWakeupProcessing */
        },

};
#define CAN_STOP_SEC_CONST_UNSPECIFIED
#include "Can_MemMap.h"

/*=======[E N D   O F   F I L E]==============================================*/