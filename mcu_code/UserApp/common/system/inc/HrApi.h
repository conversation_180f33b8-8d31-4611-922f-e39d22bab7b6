/*
      HrApi.h
描述：此文件主要是MCU硬件相关接口头文件
作者：廖勇刚
时间：2016.9.23
*/

#ifndef  _HR_API_H_
#define  _HR_API_H_

#include "AppTask.h"
#include "iodefine.h"
#include "pins_driver.h"

/************************函数接口***************************/
void McuSleepIntConfig(void);
void McuIntAdcA0I0Isr(void);
void McuIntAdcA0I1Isr(void);
void McuIntAdcA0ErrIsr(void);
void McuIntSensorOrRtcIsr(void);
void McuIntTestIsr(void);
void McuLpsDisable(void);
void McuHrDeviceInit(void);
void McuTaub0Init(void);
void GetCounterValue(UINT8 id,  UINT32 *osTickTime);
void GetElapsedCounterValue(UINT8 id, UINT32 *oldCurTickValue, UINT32 *elapsedTicks);

int McuSetGpioLevel(UINT8 index, UINT8 level);
UINT8 McuGetGpioLevel(UINT8 index);
int McuSetPinModule(UINT8 index, UINT8 module);
int McuSetPinIntConfig(UINT8 index, UINT8 config);

#endif
