/*
      NvApi.h
描述：此文件主要是MCU存储设计函数接口头文件
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _NV_API_H_
#define  _NV_API_H_

#include "AppTask.h"
#include "Dem.h"
#include "event.h"
#include "fee_config.h"
#include "status.h"
#include "DiagApi.h"

/************************宏定义***************************/
//云图FLASH相关定义，flash起始地址0x00100000，起始4K留做升级等使用,
#define FLASH_INST (0U)
#define FLASH_MIN_BYTE_ALIGNMENT                     8
#define FLASH_START_ADDR                             0x00100000   //flash的起始地址，nv使用的起始地址是0x101000，最前面的4K用做其他用途 查看手册page 7
#define ADDRESS_1k_ALIGNMENT                         0x400

#define USER_UPGRADE_STATE_ADDR                       FLASH_START_ADDR                   //升级标志存储地址
#define USER_SOFTRESET_COUNT_ADDR                    (FLASH_START_ADDR +  ADDRESS_1k_ALIGNMENT)  //软件重启次数存储地址
#define WAKEUP_SOURCE_ADDR                           (USER_SOFTRESET_COUNT_ADDR + 4)      //boot保存的系统启动源地址
#define BOOT_VERSION_ADDR                            (USER_SOFTRESET_COUNT_ADDR + 8)              //boot版本存储地址
#define USER_NV_INIT_INFO_ADDR                       (FLASH_START_ADDR +  (ADDRESS_1k_ALIGNMENT * 2))  //NV初始化信息存储地址

//NV属性定义
#define NV_VERSION                                0x0001
#define NV_MAGIC_DATA                             0x11111111
#define NV_MAX_TOTAL_LEN                          256

//代码固化DID信息
#define TBOX_SOFTWARE_ID                         "0000"   //F188 ECU软件编号 五菱使用的版本号
#define TBOX_SOFTWARE_ID_LEN                      10
#define TBOX_HARDWARE_ID                         "00"   //F191 ECU硬件编号
#define TBOX_HARDWARE_ID_LEN                      10
#define TBOX_SYSTEM_SUPPIER_IDENTIFIER           "7080001631"   //F18A 供应商代码
#define TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN        10
#define TBOX_NAME                                "TBOX"         //F197 ECU名称
#define TBOX_NAME_LEN                             10

#define TBOX_SOFT_VERSION_LEN                     8
#define TBOX_HARD_VERSION_LEN                     8
#define TBOX_BOOT_VERSION_LEN                     8

//DID结构体参数长度定义
#define TBOX_SPARE_PARTNUMBER_LEN                 10 // 客户零件号最大长度，表示客户零件号的最大字符数
#define TBOX_VIN_LEN                              17 // VIN码的长度，表示车辆识别码的字符数
#define TBOX_SOFTWARE_UPDATE_DATE_LEN             7  // 软件更新日期的长度，7位BCD格式的日期
#define VEHICLE_OFFLINE_CONFIG_LEN                8  // 车辆下线配置长度，表示车辆下线时配置的字节数

#define TUID_LEN                                  32  // TUID的长度，表示T-Box的唯一标识符，32字节ASCII码
#define TBOX_SERIAL_NUMBER_LEN                    20  // 序列号的长度，TUID后20字节
#define TBOX_SIM_NUMBER_LEN                       13  // SIM卡号的长度，表示SIM卡的数字字符数
#define TBOX_ICCID_LEN                            20  // ICCID号的长度，表示SIM卡的ICCID编号字符数
#define TBOX_IMEI_LEN                             15  // IMEI号的长度，表示手机或设备的国际移动设备身份码字符数
#define TBOX_CERT_LEN                             32  // TBOX证书编号的长度，表示TBOX数字证书的编号字符数
#define TBOX_LINK_LEN                             32  // 链路地址的长度，表示TBOX用于通信的链路地址长度
#define TBOX_LINK_PORT_LEN                        8   // 链路端口的长度，表示TBOX链路端口的字符数
#define TBOX_ID_LEN                               10  // TBOX内部SN的长度，表示TBOX设备的内部编号长度
#define TBOX_LINK_USERNAME_LEN                    64  // 链路用户名的长度，表示用于链路连接的用户名最大长度
#define TBOX_LINK_PASSWORD_LEN                    64  // 链路密码的长度，表示用于链路连接的密码最大长度
#define CERT_UPDATE_TIME_LEN                      7   // 证书更新时间的长度，表示更新证书的时间长度，7字节


#define NV_MAX_NUMBER                                    NV_ID_MAX + DTC_MAX_INDEX - 1   //最大FEE_CRT_CFG_NR_OF_BLOCKS

// NEW_TODO 无效Code，弃用，与业务相关，需处理
#define TBOX_ESK_LEN                                     0x10

/************************函数指针定义***************************/
typedef void (*pWriteNvCallBack)(void);

#define COPY_STRING_WITH_LIMIT(dst, src, limit) do { \
    size_t len = strlen(src);                       \
    len = (len > (limit)) ? (limit) : len;          \
    memcpy((dst), (src), len);                      \
} while(0)


/************************数据结构定义***************************/

typedef enum
{
    NV_NONE,
    NV_ID_CAR_INFO = 1,      //第一个永久性不改变
    NV_ID_STATIC_DID,
    NV_ID_STATIC_DID2,
    NV_ID_SELF_CONFIG,
    NV_ID_BLE_HARDWARE_PARE,
    NV_ID_BACKUP_RAM,
    NV_ID_DTC_INFO,          //因为FEE中定义的Block编号限制，这项必须放在最后定义
    NV_ID_MAX,               //开沃量产版中所有DTC存储在一个Block中，欧洲版需要加上DTC_MAX_INDEX包含所有DTC信息
}NvId;

typedef enum
{
    NV_NO_ERROR = 0,                   //执行正确
    NV_PARA_PONIT_NULL,                //输入参数指针为空
    NV_OUT_OF_RANGE_VALUE,             //输入参数超过已定义范围值
    NV_READ_CRC_NOT_MATCH,             //读取数据CRC校验不匹配
    NV_READ_FAIL_DEFAULT_VALUE,        //当前和备份数据区都失败，返回默认值
    NV_READ_FDL_FAIL,                  //从DATA FALSH读取数据失败
    NV_WRITE_FDL_FAIL,                 //写入DATA FALSH数据失败
    NV_WRITE_AFTER_COMPARE_FAIL,       //写入数据后比较数据不匹配
    NV_WRITE_BOTH_FAIL,                //当前和备份数据域都失败
    NV_WRITE_CURRENT_FAIL,             //当前数据域失败
    NV_WRITE_BACKUP_FAIL,              //备份数据域失败
    NV_INIT_WRITE_FAIL,                //MCU初始化有写数据失败
    NV_VERSION_CODE_LOW_NV,            //代码版本号小于NV版本号
    NV_VERSION_CODE_EQU_NV_PLUS_ONE,   //代码版本号等于NV版本号加一，正常NV升级数据和变更数据
    NV_VERSION_CODE_EQU_NV,            //代码版本号等于NV版本号， 正常启动
    NV_VERSION_CODE_HIGH_NV_PLUS_ONE,  //代码版本号大于NV版本号加一，非正常NV升级数据和变更数据
    NV_WRITE_TOHER_FAIL,               //写其他区域地址失败
}NvErrorCode;

typedef struct nvItemInfo
{
    NvId                id;               // NV ID
    uint16_t            nvValidLen;       // Nv保存数据有效数据长度，以UINT32字节长度为单位
    uint16_t            nvTotalLen;       // Nv保存数据总长度
    uint32_t*           pNvDefaultData;   // NV 代码默认数据指针
    uint32_t*           pNvData;          // Nv 保存的数据指针
    uint8_t             nvCrc;            // Nv 保存的CRC数据
    pWriteNvCallBack    WriteNvCallBack;  //Nv写入操作后需要执行的功能
}NvItemInfo;

#pragma pack(1)
typedef struct {
    uint8_t vehicleType : 8;     // 车型，0-7位 0~0xFF

    uint8_t powerType : 4;       // 动力类型，0-3位 0~3
    uint8_t leftRightDrive : 1;  // 左舵右舵，4位 0~1
    uint8_t seatCount : 2;       // 座位数，5-6位 0~3
    uint8_t reserved1 : 1;       // 保留位，7位

    uint8_t abs : 1;             // ABS，0位
    uint8_t esc : 1;             // ESC，1位
    uint8_t epb : 1;             // EPB，2位
    uint8_t ble : 1;             // BLE，3位
    uint8_t ptg : 1;             // PTG，4位
    uint8_t ccs : 1;             // CCS，5位
    uint8_t rrs : 1;             // RRS，6位
    uint8_t reserved2 : 1;       // 保留位，7位

    uint8_t tireSize : 3;        // 轮胎尺寸，0-2位 0~3
    uint8_t reserved3 : 5;       // 保留位，3-7位

    uint8_t batteryPackHeating : 1; // 电池包加热，0位
    uint8_t brakeFunctionConfig : 3; // 制动功能配置，1-3位 0~7
    uint8_t remoteSpeedLimit : 1;    // 远程限速，4位
    uint8_t remoteControl : 1;      // 远程控制，5位
    uint8_t keylessEntry : 1;       // 无钥匙进入，6位
    uint8_t exteriorMirror : 1;     // 外后视镜，7位

    uint8_t passengerAirbag : 1;    // 副驾安全气囊，0位
    uint8_t reversingCamera : 1;    // 倒车影像，1位
    uint8_t tirePressureModule : 1; // 胎压监测模块，2位
    uint8_t epsModeAdjust : 1;      // EPS模式调节，3位
    uint8_t reserved5 : 4;          // 保留位，4-7位

    uint8_t reserved6 : 8;          // 保留位，8位
    uint8_t reserved7 : 8;          // 保留位，8位
} VehicleConfigData;
#pragma pack()

#pragma pack(1)
typedef struct {

    uint8_t tboxId[TBOX_ID_LEN];                              // 1612 TBOX 内部SN 五菱不使用

    uint8_t tboxSparePartNumber[TBOX_SPARE_PARTNUMBER_LEN]; // F187  客户零件号
    uint8_t vinCode[TBOX_VIN_LEN];                        // F190  vin码
    uint8_t softUpdateDate[TBOX_SOFTWARE_UPDATE_DATE_LEN]; // F199  软件更新日期7位BCD
    uint8_t vehicleOfflineConfig[VEHICLE_OFFLINE_CONFIG_LEN]; // F110 车辆下线配置

    uint8_t tuid[TUID_LEN];                               // 1600  TUID，T-Box SN(TUID 后20字节)，32位ASCII
    uint8_t tboxAuthStatus;                               // 1601 TBOX登签认证状态
    uint8_t simNumber[TBOX_SIM_NUMBER_LEN];                   // 1602 SIM卡号
    uint8_t iccid[TBOX_ICCID_LEN];                        // 1603 ICCID号
    uint8_t imei[TBOX_IMEI_LEN];                          // 1604 imei

    uint32_t wakeupTimeInterval;                          // 1615 定时唤醒间隔，默认4h (单位秒)

    uint32_t AutoRechargeUnderVoltage;                    // 1618 智能补电欠压阈值 (单位mV)
    uint8_t link1SSLEnable;                               // 1619 第一链路SSL使能
    uint8_t link2SSLEnable;                               // 1620 第二链路SSL使能
    uint8_t OfflineCheckFlag;                             // 1621 是否触发下线检测
    uint32_t acTimeoutMinutes;                            // 1622 空调运行超时关闭时间 (单位秒)
    uint8_t timedWakeupEnable;                            // 1623 定时唤醒使能
    uint8_t networkWakeupEnable;                          // 1624 网络唤醒使能
    uint8_t globalLogEnable;                              // 1625 全局日志输出使能
    uint8_t link1Enable;                                  // 1626 第一链路使能
    uint8_t link2Enable;                                  // 1627 第二链路使能
    uint8_t link3Enable;                                  // 1628 第三链路使能
    uint8_t thirdPartyLinkEnable;                         // 1629 第三方平台链路使能
    uint8_t certUpdateTime[CERT_UPDATE_TIME_LEN];         // 1630 证书更新时间
    uint16_t dataResendTestTime;                          // 1631 数据补发测试时间
    uint16_t level3AlarmTestTime;                         // 1632 3级报警测试时间
    // uint8_t vehicleRestrictFlag;                          // 1633 限速或锁车标志，8字节中，第一位表示锁车，第二位表示限速
    uint8_t lockCarStatus;                                // 1636 锁车状态 保存是否锁车成功 00:无操作 01:需要锁车 02:需要解锁
    uint8_t speedLimitStatus;                             // 1637 限速状态 保存是否限速成功 00:无操作 01:需要限速 02:需要解限
    uint8_t speedLimitValue;                              // 1635 限速值 0~255
    uint8_t reserved[87];                                 /*内存对齐,四字节对齐*/
} CarInfo; //256字节
#pragma pack()
// StaticDidInfo
#pragma pack(1)
typedef struct {
    uint8_t tboxCertificateID[TBOX_CERT_LEN];             // 1605 TBOX 数字证书编号
    uint8_t link1Addr[TBOX_LINK_LEN];                     // 1606 TBOX 第1链路地址
    uint8_t link1Port[TBOX_LINK_PORT_LEN];                // 1607 TBOX 第1链路地址端口
    uint8_t link2Addr[TBOX_LINK_LEN];                     // 1608 TBOX 第2链路地址
    uint8_t link2Port[TBOX_LINK_PORT_LEN];                // 1609 TBOX 第2链路地址端口
    uint8_t link3Addr[TBOX_LINK_LEN];                     // 1610 TBOX 第3链路地址
    uint8_t link3Port[TBOX_LINK_PORT_LEN];                // 1611 TBOX 第3链路单项向认证端口
    uint8_t link3BiAuthPort[TBOX_LINK_PORT_LEN];          // 1634 TBOX 第3链路双向认证端口
    uint8_t reserved[96];                            /*内存对齐,四字节对齐.*/
} StaticDidInfo; //256字节
#pragma pack()
// StaticDid2Info
#pragma pack(1)
typedef struct {
    uint8_t thirdPartyLinkAddr[TBOX_LINK_LEN];            // 1613 第三方平台链路地址
    uint8_t thirdPartyLinkPort[TBOX_LINK_PORT_LEN];       // 1614 第三方平台链路地址
    uint8_t link3Username[TBOX_LINK_USERNAME_LEN];    // 1616 TBOX 第3链路登录名
    uint8_t link3Password[TBOX_LINK_PASSWORD_LEN];    // 1617 TBOX 第3链路登录密码
    uint8_t reserved[88];                            /*内存对齐,四字节对齐.*/
} StaticDid2Info;  //256字节
#pragma pack()

typedef struct {
    uint8_t tboxSoftwareId[TBOX_SOFTWARE_ID_LEN + 1];
    uint8_t tboxHardwareId[TBOX_HARDWARE_ID_LEN + 1];
    uint8_t tboxSystemSuppierIdentifier[TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN + 1];
    uint8_t tboxName[TBOX_NAME_LEN + 1];
    uint8_t tboxSoftVersion[TBOX_SOFT_VERSION_LEN + 1];
    uint8_t tboxHardVersion[TBOX_HARD_VERSION_LEN + 1];
    uint8_t tboxBootVersion[TBOX_BOOT_VERSION_LEN + 1];

    CarInfo *carInfo;
    StaticDidInfo *staticDidInfo;
    StaticDid2Info *staticDid2Info;
}TotalDidInfo;

#define RDS_SOFTWARE_IN_LEN  20  // OTAmaster、远程诊断软件版本号内部长度
#define RDS_SOFTWARE_OUT_LEN 10  // OTAmaster、远程诊断软件版本号外部长度
#define RDS_SOFTWARE_SUPPLIER_IDENTIFIER_LEN 10  // OTAmaster、远程诊断软件版本供应商标识长度

typedef struct {
    uint8_t ecuBatteryVoltage;       // 电池电压 (ECU供电电压), 1字节
    uint16_t vehicleSpeed;           // 车速, 2字节
    uint32_t odometer;               // 里程, 4字节
    uint8_t lowPowerMode;            // 低压电源模式, 1字节
    uint8_t dateTime[6];             // 日期和时间, 6字节
    uint8_t vehiclePowerMode;        // 整车动力模式, 1字节
    uint8_t gearPosition;            // 档位, 1字节
    uint8_t cellularSignalStrength;  // 移动网络信号强度, 1字节
    uint8_t gnssSignalStrength;      // GNSS信号强度, 1字节
    uint8_t wifiSignalStrength;      // WIFI信号强度, 1字节
    uint8_t link1TcpStatus;          // 第1链路TCP连接状态, 1字节
    uint8_t link2TcpStatus;          // 第2链路TCP连接状态, 1字节
    uint8_t link3TcpStatus;          // 第3链路TCP连接状态, 1字节
    uint8_t powerManagementMode;     // 电源管理工作模式, 1字节
    uint8_t mileageClearCount;       // 里程清零计数
    uint32_t tboxSaveMileageValues;  // TBOX保存里程值

    uint8_t rdsSoftwareIn[RDS_SOFTWARE_IN_LEN]; //OTAmaster、远程诊断软件版本号内部
    uint8_t rdsSoftwareOut[RDS_SOFTWARE_OUT_LEN]; //OTAmaster、远程诊断软件版本号外部
    uint8_t rdsSoftwareSupplierIdentifier[RDS_SOFTWARE_SUPPLIER_IDENTIFIER_LEN];
} DynamicDidInfo;






//故障码相关结构体
#pragma pack(1)
typedef struct
{
    uint8_t isNotNull;                  /*内存对齐,四字节对齐.*/
    uint8_t ecuBatteryVoltage;       // 电池电压 (ECU供电电压), 1字节
    uint16_t vehicleSpeed;           // 车速, 2字节
    uint32_t odometer;               // 里程, 4字节
    uint8_t lowPowerMode;            // 低压电源模式, 1字节
    uint8_t dateTime[6];             // 日期和时间, 6字节
    uint8_t vehiclePowerMode;        // 整车动力模式, 1字节
    uint8_t gearPosition;            // 档位, 1字节
    uint8_t reserved[3];
}DtcSnapshot;
#pragma pack()

#define DTC_SNAPSHOT_COUNT_MAX          10
//故障码相关结构体
#pragma pack(1)
typedef struct
{
    uint8_t status;
    uint8_t index;
    DtcSnapshot dtcSnapshot[DTC_SNAPSHOT_COUNT_MAX];
    uint8_t accCount;
    uint8_t reserved[5];                  /*内存对齐,四字节对齐.*/
}DtcType;                                /* 208*/
#pragma pack()

#pragma pack(1)
typedef struct
{
    DtcType dtc[DTC_MAX_INDEX];
}DtcInfo;
#pragma pack()                           /* 96，内存对齐云途fee32byte对齐*/




#pragma pack(1)
typedef struct
{
    uint8_t Mac[6];   //蓝牙信标的MAC地址信息保存
    uint8_t index;    //蓝牙信标的编号
} STBPara;
#pragma pack()

#pragma pack(1)
typedef struct
{
    uint8_t   boardMac[6];  /*T-Box板载蓝牙模块的MAC地址*/
    STBPara   STBPara[4];   /*T-Box的信标模块的信息.预设4个信标模块的信息保存空间,目前量产项目均仅使用了3个信标*/
    uint8_t   reserved[6];   /*T-Box的NV空间划分,需要为32的倍数,因此此处增加预留位.*/
}TboxBleKeyHardPara;        /* 固定40 */
#pragma pack()




#pragma pack(1)
typedef struct
{
    uint32_t   magicData;               // NV 魔术字 用于表示NV初始状态
    uint32_t   nvVersion;               // NV 版本号
}NvAttributeInfo; //长度需要8的倍数，
#pragma pack()

//TBOX 系统配置参数结构体
#pragma pack(1)
typedef struct
{
    uint8_t logMode;                //LOG配置模式
    uint8_t logLevel;               //LOG配置等级
    uint8_t retransmissionCount;    //失败重传次数
    uint8_t retransmissionTimeout;  //超时重传时间
} LogAndIpc;
#pragma pack()

#pragma pack(1)
typedef struct
{
    uint8_t SohCount;                //内置电池寿命记录
    uint8_t NoFullCount;             //内置电池一次充电时长达到最大时长时电压不到默认满电压的次数
    uint8_t batVoltageH;             //内置电池上一次充电时长达到最大时长时的电压(mV)高位
    uint8_t batVoltageL;             //内置电池上一次充电时长达到最大时长时的电压(mV)低位
} BatPara;
#pragma pack()
/*
 * @brief T-Box功能配置
 * @note    0000 0011 C7011060BAA
 * @note    0000 0101 C7011065BAA
 * @note    0000 1001 C7011070BAA
 * @note    0001 0001 C7011081BAA
 */
typedef enum
{
    TBOX_CFG_FLAG = 0x01,           /*bit0 配置标志位 0 未配置 1 已配置*/
    TBOX_CFG_WITH_NAV = 0x02,       /*bit1 惯导标志位 0 内置GPS 1 外置惯导 由T-Box的MCU和ARM通过0x802 IPC协议握手成功后更新至ARM中 发送时0表示外置 1表示内置*/
    TBOX_CFG_WITH_STB = 0x04,       /*bit2 蓝牙信标标志位 0 不使用蓝牙信标天线提供蓝牙定位能力 1 使用蓝牙信标天线提供蓝牙定位能力 新平台未使用这个，因此后续增加蓝牙信标的T-Box将此bit至1即可*/
    TBOX_CFG_WITH_SIMDIR = 0x08,    /*SIM卡套餐标志位 0 SIM卡套餐配置为全套餐(T-Box与大屏通过USB共享网络,大屏可通过T-Box上网) 1 SIM卡套餐为定向套餐(T-Box不与大屏共享网络,仅通过部分域名地址上网)*/
    TBOX_CFG_WITH_ECALL = 0x10,     /*ECALL标志位 0 不带ECALL 1 带ECALL*/
}TboxCfgType;

#pragma pack(1)
typedef struct
{
    LogAndIpc logAndIpc;    /*T-Box的MCU日志打印配置信息,占4Bytes*/
    BatPara   batPara;      /*T-Box的备用电池生命信息,占4Bytes*/
    uint8_t   tboxCfgType;  // TboxCfgType,每一个bit的详细解释见TboxCfgType 1
    uint8_t   STmin;        // UDS 多帧接收超时时间 ms 最大127ms
    uint8_t   reserved[22];   /*T-Box的NV空间划分,需要为4的倍数,且按32个字节对齐,因此此处增加预留位.*/
} TboxSelfConfigPara;       /*固定 32*/
#pragma pack()

typedef enum {
    DID_TBOX_CFG_TYPE               = 0x0101, // 内部 TboxCfgType，每一个bit的详细解释见 TboxCfgType
    DID_UDS_STMIN                   = 0x0102, // 内部UDS STmin 时间
    DID_TBOX_ID                     = 0x1612, // TBOX 内部SN 五菱不使用

    DID_TBOX_SPARE_PART_NUMBER      = 0xF187, // 客户零件号
    DID_TBOX_SERIAL_NUMBER          = 0xF18C, // 客户SN
    DID_TBOX_SOFTWARE_ID            = 0xF188, // ECU软件编号
    DID_TBOX_HARDWARE_ID            = 0xF191, // ECU硬件编号
    DID_TBOX_SUPPIER_IDENTIFIER     = 0xF18A, // 供应商代码
    DID_VIN_CODE                    = 0xF190, // VIN 码
    DID_TBOX_HARD_VERSION           = 0xF193, // 硬件版本号
    DID_TBOX_SOFT_VERSION           = 0xF195, // 软件版本号
    DID_TBOX_NAME                   = 0xF197, // ECU NAME
    DID_SOFT_UPDATE_DATE            = 0xF199, // 软件更新日期 7 位 BCD
    DID_VEHICLE_OFFLINE_CONFIG      = 0xF110, // 车辆下线配置

    DID_TUID                        = 0x1600, // TUID，T-Box SN，32 位 ASCII 后20字节是SN 对应0xF18C
    DID_TBOX_AUTH_STATUS            = 0x1601, // TBOX 登签认证状态
    DID_SIM_NUMBER                  = 0x1602, // SIM 卡号
    DID_ICCID                       = 0x1603, // ICCID 号
    DID_IMEI                        = 0x1604, // IMEI
    DID_TBOX_CERTIFICATE_ID         = 0x1605, // TBOX 数字证书编号
    DID_LINK1_ADDR                  = 0x1606, // TBOX 第 1 链路地址
    DID_LINK1_PORT                  = 0x1607, // TBOX 第 1 链路地址端口
    DID_LINK2_ADDR                  = 0x1608, // TBOX 第 2 链路地址
    DID_LINK2_PORT                  = 0x1609, // TBOX 第 2 链路地址端口
    DID_LINK3_ADDR                  = 0x1610, // TBOX 第 3 链路地址
    DID_LINK3_PORT                  = 0x1611, // TBOX 第 3 链路地址端口
    DID_THIRD_PARTY_LINK_ADDR       = 0x1613, // 第三方平台链路地址
    DID_THIRD_PARTY_LINK_PORT       = 0x1614, // 第三方平台链路地址
    DID_WAKEUP_TIME_INTERVAL        = 0x1615, // 定时唤醒间隔，默认 4h
    DID_LINK3_ADDR_USERNAME         = 0x1616, // TBOX 第 3 链路登录名
    DID_LINK3_PORT_PASSWORD         = 0x1617,  // TBOX 第 3 链路登录密码
    DID_AUTO_RECHARGE_UNDER_VOLTAGE = 0x1618, // 智能补电欠压阈值
    DID_LINK1_SSL_ENABLE            = 0x1619, // 第一链路 SSL 使能
    DID_LINK2_SSL_ENABLE            = 0x1620, // 第二链路 SSL 使能
    DID_OFFLINE_CHECK_FLAG          = 0x1621, // 是否触发下线检测
    DID_AC_TIMEOUT_MINUTES          = 0x1622, // 空调运行超时关闭时间（单位为分钟）
    DID_TIMED_WAKEUP_ENABLE         = 0x1623, // 定时唤醒使能
    DID_NETWORK_WAKEUP_ENABLE       = 0x1624, // 网络唤醒使能
    DID_GLOBAL_LOG_ENABLE           = 0x1625, // 全局日志输出使能
    DID_LINK1_ENABLE                = 0x1626, // 第一链路使能
    DID_LINK2_ENABLE                = 0x1627, // 第二链路使能
    DID_LINK3_ENABLE                = 0x1628, // 第三链路使能
    DID_THIRD_PARTY_LINK_ENABLE     = 0x1629, // 第三方平台链路使能
    DID_CERT_UPDATE_TIME            = 0x1630, // 证书更新时间
    DID_DATA_RESEND_TEST_TIME       = 0x1631, // 数据补发测试时间
    DID_LEVEL3_ALARM_TEST_TIME      = 0x1632, // 3 级报警测试时间
    // DID_VEHICLE_RESTRICT_FLAG       = 0x1633, // 限速或锁车标志 (已废弃)
    DID_LINK3_BI_AUTH_PORT          = 0x1634, // 第3链路双向认证端口
    DID_SPEED_LIMIT_VALUE           = 0x1635, // 限速值 0~255
    DID_LOCK_CAR_STATUS             = 0x1636, // 锁车状态 (00:无命令, 01:进入锁车, 02:退出锁车)
    DID_SPEED_LIMIT_STATUS          = 0x1637, // 限速状态 (00:无命令, 01:进入限速, 02:退出限速)

    //动态DID
    DID_ECU_BATTERY_VOLTAGE         = 0xCF00, // 电池电压 (ECU供电电压)
    DID_VEHICLE_SPEED               = 0xCF01, // 车速
    DID_ODOMETER                    = 0xCF02, // 里程
    DID_LOW_POWER_MODE              = 0xCF03, // 低压电源模式
    DID_DATE_AND_TIME               = 0xCF04, // 日期和时间
    DID_VEHICLE_POWER_MODE          = 0xCF05, // 整车动力模式
    DID_GEAR_POSITION               = 0xCF06, // 档位
    DID_CELLULAR_SIGNAL_STRENGTH    = 0x1650, // 移动网络信号强度
    DID_GNSS_SIGNAL_STRENGTH        = 0x1651, // GNSS信号强度
    DID_WIFI_SIGNAL_STRENGTH        = 0x1652, // WIFI信号强度
    DID_LINK1_TCP_STATUS            = 0x1653, // 第1链路TCP连接状态
    DID_LINK2_TCP_STATUS            = 0x1654, // 第2链路TCP连接状态
    DID_LINK3_TCP_STATUS            = 0x1655, // 第3链路TCP连接状态
    DID_POWER_MANAGEMENT_MODE       = 0x1656, // 电源管理工作模式
    DID_MILEAGE_CLEAR_COUNT         = 0x1660, // 里程清零计数
    DID_TBOX_SAVE_MILEAGE_VALUE     = 0x1661, // TBOX保存里程值
    DID_RDS_SOFTWARE_IN             = 0x1670, // 远程诊断软件版本号内部
    DID_RDS_SOFTWARE_OUT            = 0x1672, // 远程诊断软件版本号内部
    DID_RDS_SUPPIER_IDENTIFIER      = 0x1671, // 远程诊断软件版本号内部

    DID_CAR_INFO_DID                = 0xFFC0,
    DID_STATIC_DID                  = 0xFFC1,
    DID_STATIC2_DID                 = 0xFFC2,
    DID_DYNAMIC_DID                 = 0xFFC3,
    DID_TOTAL_DID                   = 0xFFCC,

    //控制类DID
    DID_CTRL_MCU_GPIO               = 0xFCC0,

} DidTypeInfo;


/************************函数接口***************************/
status_t EraseStorageRegion(uint32_t address);
status_t WriteDataToFlash(uint32_t address,uint32_t* data,uint16_t len);
uint32_t ReadValueOfFlashAddress(uint32_t address);

void NvMcuPowerOnInitNvData(void);
NvErrorCode NvApiReadData(uint16_t id, uint32_t *pNvData, uint16_t len);
NvErrorCode NvApiWriteData(uint16_t id, uint8_t *pNvData, uint16_t len);

int NvClearDTC(uint32 groupOfDTC, uint8 dtcType,uint8 memoryType);

CarInfo *GetCanInfo(void);
DtcInfo *GetDtcInfo(void);
StaticDidInfo *GetStaticDid(void);
StaticDid2Info *GetStaticDid2(void);
DynamicDidInfo *GetDynamicDid(void);
uint8_t GetGearPosition(void);
uint16_t GetVehicleSpeed(void);

bool GetDtcFaultStatus(DtcIndexType indexType);
uint8_t GetDtcStatus(DtcIndexType indexType);

BackupRamInfo *GetBackupRamInfo(void);
TboxSelfConfigPara *GetTboxSelfConfigData(void);
TboxBleKeyHardPara *GetTboxBleKeyHardPara(void);

int SetUdsSTmin(uint8_t timeMs);

void SetMcuDynamicDid(void);
void DcodeAndSetDynamicDid(uint8_t *data);
size_t EncodeTotalDid(uint8_t *buf);
int DecodeAndSetCarDid(CarInfo *info, uint8_t *buf);
int DecodeAndSetStaticDid(StaticDidInfo *info, uint8_t *buf);
int DecodeAndSetStaticDid2(StaticDid2Info *info, uint8_t *buf);
#endif

