#ifndef  _SYSTEM_API_H_
#define  _SYSTEM_API_H_


#include "event.h"
#include <stdbool.h>
#include <stdint.h>
#define PROJECT_INITIATE_YEAR             2024
#define PROJECT_INITIATE_MONTH            05


#define SYSTEM_TIME_START_YEAR              1970

#define POSITIONING_TIMESTAMP              1735689600  // 2025/01/01 00:00:00  +0

typedef enum
{
    /*网络时间,时间来源自外部，NTP或GPS或TSP*/
    NET_TYPE_TIME = 0x00,
    /*RTC时间,时间来源自RTC保存的时间信息*/
    RTC_TYPE_TIME,
    /*无效时间*/
    INVALID_TYPE_TIME,
    /*预留*/
    RESERVED_TYPE_TIME,
}TimeTypeEnum;

typedef enum
{
    /*无RTC唤醒请求*/
    RTC_WAKEUP_NONE = 0,
    /*设置RTC在指定minute时间后唤醒*/
    RTC_WAKEUP_MINUTE,
    /*设置RTC在指定hour时间后唤醒*/
    RTC_WAKEUP_HOUR,
    /*设置RTC在指定second时间后唤醒*/
    RTC_WAKEUP_SECOND,
    /*设置RTC在指定day时间后唤醒*/
    RTC_WAKEUP_DAY,
    /*设置RTC在指定hour,minute, second时间后唤醒*/
    RTC_WAKEUP_SPECIFIED,
    
}RtcWakeUpTypeEnum;

typedef struct
{
    TimeTypeEnum timeType;
    uint8_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
    bool  timeIsValid;
}TimeInfoStruct;

typedef struct
{
    uint16_t year;  //0~20xx
    uint8_t mon;    //1~12
    uint8_t day;    //1~31
    uint8_t hour;   //0~23
    uint8_t min;    //0~59
    uint8_t sec;    //0~59
    int8_t timeZone;
}SystemTime_s;

typedef struct
{
    /*T-Box当前时间*/
    TimeInfoStruct    currentTime;
    /*T-Box本次休眠后被唤醒的时间类型*/
    RtcWakeUpTypeEnum rtcWakeupType;
    /*T-Box本次休眠后被唤醒的的时间长度，仅当rtcWakeupType为RTC_WAKEUP_SECOND，RTC_WAKEUP_MINUTE， RTC_WAKEUP_HOUR时有效*/
    uint8_t             rtcWakeupTime;
    /*T-Box从唤醒或上电后已经工作的时间长度，ms单位*/
    uint32_t            executedTimeStamp;
}TboxSystemTimeStruct;

typedef enum
{
    CRC8,   /**< CRC-8 算法 */
    CRC16,  /**< CRC-16 算法 */
    CRC32   /**< CRC-32 算法 */
} CRC_Type_e;

/**
 * @brief CRC 参数结构体
 */
typedef struct
{
    uint8_t width;           /**< CRC 位宽（8, 16, 32） */
    uint32_t polynomial;     /**< CRC 多项式 */
    uint32_t init;           /**< 初始 CRC 值 */
    uint32_t final_xor;      /**< 最终异或值 */
    bool reflect_input;     /**< 是否反射输入字节 */
    bool reflect_output;    /**< 是否反射 CRC 输出 */
} CRC_Parameters;

uint32_t SystemCalCRC(const uint8_t *data, uint32_t length, const CRC_Parameters *params);
uint8_t SystemCalCrc8(const uint8_t *data, uint32_t length);
uint16_t SystemCalCrc16(const uint8_t *data, uint32_t length);
uint32_t SystemCalCrc32(const uint8_t *data, uint32_t length);
uint8_t BcdTo2bin(uint8_t val);
uint8_t Bin2Tobcd(uint8_t val);
TboxSystemTimeStruct *TboxSystemTimeInfoRead(void);
int TboxSystemTimeSet(uint16_t year, uint8_t month, uint8_t week, uint8_t day, uint8_t hour, uint8_t minute, uint8_t sec, TimeTypeEnum timeType);
int TboxSystemRtcWakeupSet(uint8_t day, uint8_t hour, uint8_t minute, uint8_t sec, RtcWakeUpTypeEnum rtcWakeUpType);
void SystemApiHexToStr(uint8_t *pSource, int32_t sourceLen, uint8_t *pDest);
void SystemApiStrToHex(uint8_t *pSource, int32_t sourceLen, uint8_t *pDest);
void CalcRtcTime(void);
int LocalToUnixTime(const SystemTime_s *localTime, uint32_t *unixTime);

void __DI(void);
void __EI(void);
#ifdef WINDOWS_SIM
void asm(void);
#endif

#endif

