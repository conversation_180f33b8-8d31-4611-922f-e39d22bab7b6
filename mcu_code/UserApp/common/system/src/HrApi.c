/*
      HrApi.c
      此文件主要是MCU硬件配置相关的函数接口:
描述：
作者：廖勇刚
时间：2016.9.23
*/
#include "gpio.h"
#include "HrApi.h"
#include "LogApi.h"
#include "pwm.h"
#include "event.h"


/************************外部全局变量****************************/
extern CommonInfo g_commonInfo;
extern UINT32     g_osCurrentTickTime;

#define POWER_ARM_PORT                (GPIOB)

#define POWER_ARM_PORT_INDEX          (11U)
#define LED5_PORT_INDEX               (14U)
#define POWER_ARM_PWRKEY_PORT_INDEX   (15U)

#define POWER_INDICATION_PORT         (GPIOA)
#define POWER_LED_PORT_INDEX          (1U)

/************************全局变量****************************/
GpioInfo g_gpioPowerOnInfoList[] = 
{
    /* gpioIndex                    gpioPowerConfig     gpioSleepConfig     gpioDeepSleepConfig gpioPowerLevel      gpioSleepLevel      gpioDeepSleepLevel  gpioFlag      gpioNumber gpioBase pctrlBase*/
    {MCU_GPIO_AIRBAG_INPUT,         PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 16, GPIOE, PCTRLE},//AIRBAG_INPUT
    {MCU_GPIO_REBOOT,               PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 15, GPIOE, PCTRLE},//REBOOT
    {MCU_GPIO_UART3_RX,             PCTRL_MUX_ALT5,     PCTRL_MUX_ALT5,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   11, GPIOE, PCTRLE},//MCU_UART3_TX
    {MCU_GPIO_UART3_TX,             PCTRL_MUX_ALT5,     PCTRL_MUX_ALT5,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   10, GPIOE, PCTRLE},//MCU_UART3_RX
    {MCU_GPIO_ARM_STATUS,           PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 13, GPIOE, PCTRLE},//T106_STATUS_MCU
    {MCU_GPIO_B_KEY_INPUT,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   5,  GPIOE, PCTRLE},//B_KEY_INPUT
    {MCU_GPIO_I_KEY_INPUT,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   4,  GPIOE, PCTRLE},//I_KEY_INPUT
    {MCU_GPIO_M_EXTAL,              PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 7,  GPIOB, PCTRLB},//MCU_EXTAL
    {MCU_GPIO_M_XTAL,               PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 6,  GPIOB, PCTRLB},//MCU_XTAL
    {MCU_GPIO_IO_FWD_IO,            PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   14, GPIOE, PCTRLE},//FWD_IO
    {MCU_GPIO_SOS_KEY_INPUT,        PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 3,  GPIOE, PCTRLE},//SOS_KEY_INPUT
    {MCU_GPIO_M_POWER_WIFI,         PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 12, GPIOE, PCTRLE},//MCU_POWER_WIFI
    {MCU_GPIO_M_POWER_GNSSBACKUP,   PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 17, GPIOD, PCTRLD},//MCU_POWER_GNSSbackup
    {MCU_GPIO_M_EXTAL32,            PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 16, GPIOD, PCTRLD},//MCU_EXTAL32
    {MCU_GPIO_M_XTAL32,             PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 15, GPIOD, PCTRLD},//MCU_XTAL32
    {MCU_GPIO_ACC,                  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 9,  GPIOE, PCTRLE},//ACC_INPUT
    {MCU_GPIO_M_RST_GNSS,           PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 14, GPIOD, PCTRLD},//MCU_RST_GNSS
    {MCU_GPIO_ARM_NET_STATUS_MCU,   PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 13, GPIOD, PCTRLD},//T106_NET_STATUS_MCU
    {MCU_GPIO_BLE_STATUS_INT,       PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 8,  GPIOE, PCTRLE},//BLE_OUTPUT_INT
    {MCU_GPIO_WAKEUP_ARM,           PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 5,  GPIOB, PCTRLB},//MCU_WAKEUP_T106
    {MCU_GPIO_WHEEL_TICK_IO,        PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   4,  GPIOB, PCTRLB},//WHEEL_TICK_IO
    {MCU_GPIO_UART2_TX,             PCTRL_MUX_ALT4,     PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 3,  GPIOC, PCTRLC},//MCU_UART2_RX
    {MCU_GPIO_UART2_RX,             PCTRL_MUX_ALT4,     PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 2,  GPIOC, PCTRLC},//MCU_UART2_TX
    {MCU_GPIO_4G_LED_RED,           PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   7,  GPIOD, PCTRLD},//LED1
    {MCU_GPIO_4G_LED_GREEN,         PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   6,  GPIOD, PCTRLD},//LED2
    {MCU_GPIO_RTC_INT,              PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 5,  GPIOD, PCTRLD},//RTC_INT
    {MCU_GPIO_BLE_WAKEUP_CTL,       PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_HIGH,   GPIO_FLAG_UNCHANGE, 12, GPIOD, PCTRLD},//BLE_WAKEUP_CTL
    {MCU_GPIO_BLE_RST_CTL,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 11, GPIOD, PCTRLD},//BLE_RST_CTL
    {MCU_GPIO_GNSS_ANT_SW,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 10, GPIOD, PCTRLD},//GNSS_ANT_SW
    {MCU_GPIO_CAN3_TX,              PCTRL_MUX_ALT4,     PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   1,  GPIOC, PCTRLC},//CAN3_TX
    {MCU_GPIO_CAN3_RX,              PCTRL_MUX_ALT4,     PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   0,  GPIOC, PCTRLC},//CAN3_RX
    {MCU_GPIO_RLIN21_TX,            PCTRL_MUX_ALT2,     PCTRL_MUX_ALT2,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   9,  GPIOD, PCTRLD},//RLIN21_TX
    {MCU_GPIO_RLIN21_RX,            PCTRL_MUX_ALT2,     PCTRL_MUX_ALT2,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   8,  GPIOD, PCTRLD},//RLIN21_RX
    {MCU_GPIO_BAT_VOLTAGE_DET,      PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 17, GPIOC, PCTRLC},//BAT_VOLTAGE_DET
    {MCU_GPIO_MIC_VOLTAGE_DET,      PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 16, GPIOC, PCTRLC},//MIC_VOLTAGE_DET
    {MCU_GPIO_BAT_NTC_DET,          PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 15, GPIOC, PCTRLC},//BAT_NTC_DET
    {MCU_GPIO_RUN_VOLTAGE_DET,      PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 14, GPIOC, PCTRLC},//RUN_VOLTAGE_DET
    {MCU_GPIO_M_TRANSLATOR_EN,      PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  GPIO_OUTPUT_LOW,    GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_HIGH,   GPIO_FLAG_UNCHANGE, 3,  GPIOB, PCTRLB},//MCU_TRANSLATOR_EN
    {MCU_GPIO_GNSS_ANT_OPEN,        PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   2,  GPIOB, PCTRLB},//GNSS_ANT_OPEN
    {MCU_GPIO_GNSS_ANT_SHORT,       PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   13, GPIOC, PCTRLC},//GNSS_ANT_SHORT
    {MCU_GPIO_MAN_ANT_SW,           PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 12, GPIOC, PCTRLC},//MAN_ANT_SW
    {MCU_GPIO_INFO_ARM_IO,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 11, GPIOC, PCTRLC},//MCU_INFO_T106_IO
    {MCU_GPIO_BAT_CHARGE_EN,        PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 10, GPIOC, PCTRLC},//BAT_CHARGE_EN
    {MCU_GPIO_SENSOR_INT2,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 1,  GPIOB, PCTRLB},//Sensor_INT2
    {MCU_GPIO_SENSOR_INT1,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 0,  GPIOB, PCTRLB},//Sensor_INT1
    {MCU_GPIO_CAN3_STB,             PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_HIGH,   GPIO_FLAG_UNCHANGE, 9,  GPIOC, PCTRLC},//CAN3_STB
    {MCU_GPIO_CAN5_STB,             PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_HIGH,   GPIO_FLAG_UNCHANGE, 8,  GPIOC, PCTRLC},//CAN4_STB
    {MCU_GPIO_I2C_SCL,              PCTRL_MUX_ALT5,     PCTRL_MUX_ALT5,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   7,  GPIOA, PCTRLA},//MCU_I2C_SCL
    {MCU_GPIO_I2C_SDA,              PCTRL_MUX_ALT5,     PCTRL_MUX_ALT5,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   6,  GPIOA, PCTRLA},//MCU_I2C_SDA
    {MCU_GPIO_EN_VCC8V,             PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 7,  GPIOE, PCTRLE},//MCU_EN_VCC8V
    {MCU_GPIO_HANDSHAKE_LED_GREEN,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_CHANGE,   17, GPIOA, PCTRLA},//LED3
    {MCU_GPIO_HANDSHAKE_LED_RED,    PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_CHANGE,   17, GPIOB, PCTRLB},//LED4
    {MCU_GPIO_BATTERY_CUTOFF,       PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   16, GPIOB, PCTRLB},//BATTERY_CUTOFF
    {MCU_GPIO_M_PWR_ONOFF,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 15, GPIOB, PCTRLB},//MCUGPIO_OUT_T106_PWRKEY
    {MCU_GPIO_GPS_LED_GREEN,        PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_CHANGE,   14, GPIOB, PCTRLB},//LED5
    {MCU_GPIO_CAN2_TX,              PCTRL_MUX_ALT4,     PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   13, GPIOB, PCTRLB},//CAN2_TX
    {MCU_GPIO_CAN2_RX,              PCTRL_MUX_ALT4,     PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 12, GPIOB, PCTRLB},//CAN2_RX
    {MCU_GPIO_GPS_LED_RED,          PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_CHANGE,   4,  GPIOD, PCTRLD},//LED6
    {MCU_GPIO_CAN5_TX,              PCTRL_MUX_ALT5,     PCTRL_MUX_ALT5,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   3,  GPIOD, PCTRLD},//CAN4_TX
    {MCU_GPIO_CAN5_RX,              PCTRL_MUX_ALT5,     PCTRL_MUX_ALT5,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   2,  GPIOD, PCTRLD},//CAN4_RX
    {MCU_GPIO_CAN2_STB,             PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_HIGH,   GPIO_FLAG_UNCHANGE, 3,  GPIOA, PCTRLA},//CAN2_STB
    {MCU_GPIO_CAN1_STB,             PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_HIGH,   GPIO_FLAG_UNCHANGE, 2,  GPIOA, PCTRLA},//CAN1_STB
    {MCU_GPIO_4V1_PWR_SW,           PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 11, GPIOB, PCTRLB},//4V1_PWR_SW
    {MCU_GPIO_M_ECALL_BTN_BL,       PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 10, GPIOB, PCTRLB},//MCU_ECALL_BTN_BL
    {MCU_GPIO_LTE_ANT_SHORT,        PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 9,  GPIOB, PCTRLB},//LTE_ANT_SHORT
    {MCU_GPIO_LTE_ANT_OPEN,         PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 8,  GPIOB, PCTRLB},//LTE_ANT_OPEN
    {MCU_GPIO_3V3_PWR_SW,           PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_HIGH,   GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 1,  GPIOA, PCTRLA},//3V3_PWR_SW
    {MCU_GPIO_MUTE_OUTPUT,          PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_CHANGE,   0,  GPIOA, PCTRLA},//MUTE_OUTPUT
    {MCU_GPIO_CAN1_TX,              PCTRL_MUX_ALT3,     PCTRL_MUX_ALT3,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_CHANGE,   7,  GPIOC, PCTRLC},//CAN1_TX
    {MCU_GPIO_CAN1_RX,              PCTRL_MUX_ALT3,     PCTRL_MUX_ALT3,     PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 6,  GPIOC, PCTRLC},//CAN1_RX
    {MCU_GPIO_ACC_OUT_CTL,          PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 16, GPIOA, PCTRLA},//ACC_OUT_CTL
    {MCU_GPIO_M_SOS_LED_RED,        PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 15, GPIOA, PCTRLA},//MCU_SOS_LED_RED
    {MCU_GPIO_RLIN21_SLP_N,         PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_HIGH,   GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 6,  GPIOE, PCTRLE},//RLIN21_SLP_N
    {MCU_GPIO_SCHG_STA_INPUT,       PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 2,  GPIOE, PCTRLE},//SCHG_STA_INPUT
    {MCU_GPIO_M_SOS_LED_GREEN,      PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 14, GPIOA, PCTRLA},//MCU_SOS_LED_GREEN
    {MCU_GPIO_M_SPI_CS,             PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 13, GPIOA, PCTRLA},//MCU_SPI_CS
    {MCU_GPIO_M_SPI_CLK,            PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 12, GPIOA, PCTRLA},//MCU_SPI_CLK
    {MCU_GPIO_M_SPI_MISO,           PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 11, GPIOA, PCTRLA},//MCU_SPI_MISO
    {MCU_GPIO_M_SPI_MOSI,           PCTRL_MUX_ALT4,     PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 10, GPIOA, PCTRLA},//MCU_SPI_MOSI
    {MCU_GPIO_FCHG_STA_INPUT,       PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 1,  GPIOE, PCTRLE},//FCHG_STA_INPUT
    {MCU_GPIO_M_WAKEUP_OUT,         PCTRL_MUX_AS_GPIO,  PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 0,  GPIOE, PCTRLE},//T106_WAKEUP_MCU
    {MCU_GPIO_ARM_SHDN_N,           PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 5,  GPIOC, PCTRLC},//MCUGPIO_OUT_T106_SHDN_N
    {MCU_GPIO_M_SWDCLK,             PCTRL_MUX_ALT7,     PCTRL_MUX_ALT7,     PCTRL_MUX_ALT7,     GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 4,  GPIOC, PCTRLC},//MCU_SWDCLK
    {MCU_GPIO_ARM_RESET_MCU,        PCTRL_MUX_ALT7,     PCTRL_MUX_ALT7,     PCTRL_MUX_ALT7,     GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 5,  GPIOA, PCTRLA},//T106_RESET_MCU
    {MCU_GPIO_M_SWDIO,              PCTRL_MUX_ALT7,     PCTRL_MUX_ALT7,     PCTRL_MUX_ALT7,     GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_DEFAULT_LEVEL, GPIO_FLAG_UNCHANGE, 4,  GPIOA, PCTRLA},//MCU_SWDIO
    {MCU_GPIO_ARM_RESET,            PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_DEFAULT_LEVEL, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_CHANGE,   9,  GPIOA, PCTRLA},//MCUGPIO_OUT_T106_RESET
    {MCU_GPIO_M_PRTRG_GNSS,         PCTRL_MUX_AS_GPIO,  PCTRL_PIN_DISABLED, PCTRL_PIN_DISABLED, GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_OUTPUT_LOW,    GPIO_FLAG_UNCHANGE, 8,  GPIOA, PCTRLA},//MCU_PRTRG_GNSS
};

/*************************************************
函数名称: McuIntAdcA0I0Isr
函数功能: IntAdcA0I0中断回调函数,主要读取B+的电压值
          AP0_0: B+睡眠模式下ADC值
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/23
*************************************************/
void McuIntAdcA0I0Isr(void)
{

}

/*************************************************
函数名称: McuIntAdcA0I1Isr
函数功能: IntAdcA0I1中断回调函数
          AP0_1: B+正常工作模式下ADC值
          AP0_2: 内置电池电压ADC值
          AP0_3: 内置电池温度ADC值
          AP0_4: ICALL按键ADC值
          AP0_5: BCALL按键ADC值
          AP0_6: ECALL按键ADC值
          AP0_7: ANALOG1_IN的ADC值
          AP0_8: ANALOG2_IN的ADC值
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/23
*************************************************/
void McuIntAdcA0I1Isr(void)
{

}

/*************************************************
函数名称: McuIntAdcA0ErrIsr
函数功能: IntAdcA0Err中断回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/23
*************************************************/
void McuIntAdcA0ErrIsr(void)
{

}

/*************************************************
函数名称: McuIntTestIsr
函数功能: 测试中断回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/23
*************************************************/
void McuIntTestIsr(void)
{

}

/*************************************************
函数名称: McuSleepIntConfig
函数功能: MCU睡眠前中断配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/29
*************************************************/
void McuSleepIntConfig(void)
{

}

/*************************************************
函数名称: McuPowerOnGpioLevelConfig
函数功能: MCU开机电平配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/29
*************************************************/
static void McuPowerOnGpioLevelConfig(void)
{
    //MCU开机保证BATTERY_CUTOFF为高电平，防止B+掉电切换不到内置电池
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BATTERY_CUTOFF], GPIO_OUTPUT_HIGH);
    //输出高电平时，关闭电平转换芯片，默认输出低电平,为SPI的电平转换芯片供电
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_TRANSLATOR_EN], GPIO_OUTPUT_LOW);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_RLIN21_SLP_N], GPIO_OUTPUT_HIGH);
}

/*************************************************
函数名称: McuSetGpioLevel
函数功能: 设置GPIO输出电平
输入参数: index - 序号(GpioIndex)，level - 电平(GpioLevel)
输出参数: 无
函数返回类型值：0-成功，-1-失败
编写者: zhengyong
编写日期 :2024/04/25
*************************************************/
int McuSetGpioLevel(UINT8 index, UINT8 level)
{
    if(index >= MCU_MAX_NUM || level > GPIO_OUTPUT_HIGH)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Index or level error for set gpio:%d, level:%d\r\n", index, level);
        return -1;
    }

    GpioSetOutputLevel(&g_gpioPowerOnInfoList[index], level);
    return 0;
}

/*************************************************
函数名称: McuSetGpioLevel
函数功能: 获取GPIO输入电平
输入参数: index - 序号(GpioIndex)
输出参数: 无
函数返回类型值：电平高低(GpioLevel)
编写者: zhengyong
编写日期 :2024/04/25
*************************************************/
UINT8 McuGetGpioLevel(UINT8 index)
{
    GpioLevel level = 0;

    if(index >= MCU_MAX_NUM)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Index error for get gpio:%d\r\n", index);
        return -1;
    }

    GpioReadInputLevel(&g_gpioPowerOnInfoList[index], &level);
    return level;
}

/*************************************************
函数名称: McuSetPinModule
函数功能: 设置PIN脚模式
输入参数: index - 序号(GpioIndex)，module - 模式(port_mux_t)
输出参数: 无
函数返回类型值：0-成功，-1-失败
编写者: zhengyong
编写日期 :2024/04/25
*************************************************/
int McuSetPinModule(UINT8 index, UINT8 module)
{
    if(index >= MCU_MAX_NUM || module > PCTRL_MUX_ALT7)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Index or module error for gpio:%d\r\n", index, module);
        return -1;
    }

    PINS_DRV_SetMuxModeSel(g_gpioPowerOnInfoList[index].pctrlBase, g_gpioPowerOnInfoList[index].gpioNumber, module);
    return 0;
}

/*************************************************
函数名称: McuSetPinIntConfig
函数功能: 设置PIN脚中断模式
输入参数: index - 序号(GpioIndex)，config - 模式(gpio_interrupt_config_t)
输出参数: 无
函数返回类型值：0-成功，-1-失败
编写者: zhengyong
编写日期 :2024/04/25
*************************************************/
int McuSetPinIntConfig(UINT8 index, UINT8 config)
{
    if(index >= MCU_MAX_NUM || config > PCTRL_INT_LOGIC_ONE)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Index or module error for gpio:%d\r\n", index, config);
        return -1;
    }


    PINS_DRV_SetPinIntSel(g_gpioPowerOnInfoList[index].gpioBase, g_gpioPowerOnInfoList[index].gpioNumber, config);
    return 0;
}

/*************************************************
函数名称: McuLpsDisable
函数功能: Lps不使能
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/21
*************************************************/
void McuLpsDisable(void)
{
}

/*************************************************
函数名称: GetCounterValue
函数功能: 获取当前TICK值
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/06
*************************************************/
void GetCounterValue(UINT8 id,  UINT32 *osTickTime)
{
     *osTickTime = g_osCurrentTickTime;    
}

/*************************************************
函数名称: GetElapsedCounterValue
函数功能: 计算TICK差值
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/06
*************************************************/
void GetElapsedCounterValue(UINT8 id, UINT32 *oldCurTickValue, UINT32 *elapsedTicks)
{
    if(g_osCurrentTickTime >= *oldCurTickValue)
    {
        *elapsedTicks = g_osCurrentTickTime - *oldCurTickValue;
    }
    else
    {
        *elapsedTicks = g_osCurrentTickTime + 0xffffffff - *oldCurTickValue;
    }
    
    *oldCurTickValue = g_osCurrentTickTime;
}

/*************************************************
函数名称: McuTaju0Init
函数功能: MCU Taju0定时器初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/14
*************************************************/
void McuTaub0Init(void)
{

}

/*************************************************
函数名称: McuTaub0PwmInputInit
函数功能: MCU输入捕捉初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/10/11
*************************************************/
void McuTaub0PwmInputInit(void)
{

}

/*************************************************
函数名称: McuTaub0PwmInputStart
函数功能: MCU输入捕捉功能启动
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/10/11
*************************************************/
void McuTaub0PwmInputStart(void)
{

}

/*************************************************
函数名称: McuHrDeviceInit
函数功能: Mcu硬件设备初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/14
*************************************************/
void McuHrDeviceInit(void)
{
#ifndef WINDOWS_SIM
    McuPowerOnGpioLevelConfig();
    PwmInit();
#endif
}
