#include "SystemApi.h"
#include "NvApi.h"
#include "rtc.h"
#include "rtc_driver.h"
#include "power_manager.h"
#include <stdlib.h>
#include <ctype.h>
#include "LogApi.h"
#include "Platform_Types.h"
#include "PmApi.h"

/********************外部全局变量**********************/
extern BackupRamInfo        g_backupRamInfo;

/********************静态全局变量**********************/
static TboxSystemTimeStruct g_TboxSystemTime;


/*************************************************
函数名称: TboxSystemTimeStruct *TboxSystemTimeInfoRead(void)
函数功能: 获取系统当前时间信息
输入参数: NULL
输出参数: NULL
函数返回类型值：TboxSystemTimeStruct
编写者: zxl
编写日期 :2020/04/13
*************************************************/
TboxSystemTimeStruct *TboxSystemTimeInfoRead(void)
{
    return &g_TboxSystemTime;
}

/*************************************************
函数名称: BcdTo2bin
函数功能: BCD码转二进制
输入参数: BCD码字符
输出参数: UINT8
函数返回类型值：二进制数
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
UINT8 BcdTo2bin(UINT8 val)
{
    return (val & 0x0f) + (val >> 4) * 10;
}

/*************************************************
函数名称: BinTo2bcd
函数功能: 二进制转BCD码
输入参数: 二进制数据
输出参数: UINT8 
函数返回类型值：BCD码数据
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
UINT8 Bin2Tobcd(UINT8 val)
{
    return ((val / 10) << 4) + val % 10;
}

/*************************************************
函数名称: TboxSystemTimeSet
函数功能: 设置系统时间
输入参数: 年、月、日、时、分、秒，类型为RTC_TYPE_TIME时，年为千年，如2020年，为NET_TYPE_TIME时，年为20
输出参数: 
函数返回类型值：无
编写者: zxl
编写日期 :2020/04/13
*************************************************/
int TboxSystemTimeSet(UINT16 year, UINT8 month, UINT8 week, UINT8 day, UINT8 hour, UINT8 minute, UINT8 sec, TimeTypeEnum timeType)
{
    static UINT8 netTimeCalced = 0;
    UINT8 tempDec = PROJECT_INITIATE_YEAR % 100;
    RtcTime rtcTime;
    TboxSystemTimeStruct *pTimeStruct = TboxSystemTimeInfoRead();

    if (NULL == pTimeStruct)
    {
        return -1;
    }

    rtcTime.year    = (timeType == NET_TYPE_TIME) ? year + 2000 : year;
    rtcTime.month   = month;
    rtcTime.weekday = week;
    rtcTime.day     = day;
    rtcTime.hour    = hour;
    rtcTime.minute  = minute;
    rtcTime.second  = sec;

    if (timeType == NET_TYPE_TIME)
    {
        netTimeCalced = 1;
        pTimeStruct->currentTime.timeIsValid = TRUE;
        RtcSetDateTime(rtcTime);
    }
    else if (timeType == RTC_TYPE_TIME)
    {
        pTimeStruct->currentTime.timeIsValid = (year >= tempDec) ? TRUE : FALSE;

        if ((TBOX_WAKEUP_BPLUS == g_backupRamInfo.mcuWakeupSource) && (netTimeCalced == 0))
        {
            pTimeStruct->currentTime.timeIsValid = FALSE;
        }
    }

    pTimeStruct->currentTime.year   = rtcTime.year - 2000;
    pTimeStruct->currentTime.month  = rtcTime.month;
    pTimeStruct->currentTime.day    = rtcTime.day;
    pTimeStruct->currentTime.hour   = rtcTime.hour;
    pTimeStruct->currentTime.minute = rtcTime.minute;
    pTimeStruct->currentTime.second = rtcTime.second;
    pTimeStruct->currentTime.timeType = timeType;

    SystemApiLogPrintf(LOG_INFO_OUTPUT,
                       "Date Set: %02d-%02d-%02d %02d:%02d:%02d, type:%d, Valid:%d\r\n",
                       pTimeStruct->currentTime.year,
                       pTimeStruct->currentTime.month,
                       pTimeStruct->currentTime.day,
                       pTimeStruct->currentTime.hour,
                       pTimeStruct->currentTime.minute,
                       pTimeStruct->currentTime.second,
                       pTimeStruct->currentTime.timeType,
                       pTimeStruct->currentTime.timeIsValid);

    return 0;
}

/*************************************************
函数名称: RtcAlarmCallback
函数功能: 唤醒回调，暂时只打印日志
输入参数: callbackParam-未使用
输出参数:
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/03/12
*************************************************/
void RtcAlarmCallback(void *callbackParam)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Got rtc alarm callback\r\n");
    SetWakeupSource(TBOX_WAKEUP_RTC);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Wake up mcu system\r\n");
}

/*************************************************
函数名称: TboxSystemRtcWakeupSet
函数功能: 设置T-Box的RTC唤醒时间
输入参数: 
输出参数:
函数返回类型值：无
编写者: zxl
编写日期 :2020/04/13
*************************************************/
int TboxSystemRtcWakeupSet(UINT8 day, UINT8 hour, UINT8 minute, UINT8 sec, RtcWakeUpTypeEnum rtcWakeUpType)
{
    boolean clearRtc = FALSE;
    int   ret = 0x00;
    UINT8 tempBuf[6] = {0x80};
    TboxSystemTimeStruct *pTimeStruct = NULL;
#ifdef USE_INTERNAL_RTC
    static rtc_alarm_config_t rtcAlarmConfig = {0};
#endif

    pTimeStruct = TboxSystemTimeInfoRead();
    if(NULL == pTimeStruct)
    {
        return -1;
    }

    pTimeStruct->rtcWakeupType = rtcWakeUpType;

    switch(pTimeStruct->rtcWakeupType)
    {
        /*无定时唤醒功能，可以清除RTC唤醒寄存器值*/
        case RTC_WAKEUP_NONE:
        {
            rtcAlarmConfig.alarmTime.year = pTimeStruct->currentTime.year + 2000 + 20;
            rtcAlarmConfig.alarmTime.month = pTimeStruct->currentTime.month;
            rtcAlarmConfig.alarmTime.day = pTimeStruct->currentTime.day;
            rtcAlarmConfig.alarmTime.hour = pTimeStruct->currentTime.hour;
            rtcAlarmConfig.alarmTime.minutes = pTimeStruct->currentTime.minute;
            rtcAlarmConfig.alarmTime.seconds = pTimeStruct->currentTime.second;
            rtcAlarmConfig.alarmIntEnable = false;
            rtcAlarmConfig.rtcAlarmCallback = RtcAlarmCallback;
            ret = RTC_DRV_ConfigureAlarmInt(0, &rtcAlarmConfig);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC WakeUp Time Clear.ret = %d \r\n",ret);
            break;
        }
    #if 0 //这几个计算方式是有问题，翻转的情形没有考虑全，目前也没有使用，为避免后续误用，暂时屏蔽
        case RTC_WAKEUP_SECOND:
        {
            memset(tempBuf, 0x80, sizeof(tempBuf));
            tempBuf[0] = PCF85063_REG_AS;
            tempBuf[1] = (pTimeStruct->currentTime.second + sec)%60;
            tempBuf[2] = (pTimeStruct->currentTime.minute + ((pTimeStruct->currentTime.second + sec)/60))%60;
            tempBuf[3] = (pTimeStruct->currentTime.hour + (pTimeStruct->currentTime.minute + (pTimeStruct->currentTime.second + sec)/60)/60)%24;
            tempBuf[4] = (pTimeStruct->currentTime.day);

            tempBuf[1] = Bin2Tobcd(tempBuf[1]);
            tempBuf[2] = Bin2Tobcd(tempBuf[2]);
            tempBuf[3] = Bin2Tobcd(tempBuf[3]);
            tempBuf[4] = Bin2Tobcd(tempBuf[4]);
            RtcWriteRegister(tempBuf, sizeof(tempBuf));
            break;
        }
        case RTC_WAKEUP_MINUTE:
        {
            memset(tempBuf, 0x80, sizeof(tempBuf));
            tempBuf[0] = PCF85063_REG_AS;
            tempBuf[1] = (pTimeStruct->currentTime.second)%60;
            tempBuf[2] = (pTimeStruct->currentTime.minute + minute)%60;
            tempBuf[3] = (pTimeStruct->currentTime.hour + (pTimeStruct->currentTime.minute + minute)/60)%24;
            tempBuf[4] = (pTimeStruct->currentTime.day);

            tempBuf[1] = Bin2Tobcd(tempBuf[1]);
            tempBuf[2] = Bin2Tobcd(tempBuf[2]);
            tempBuf[3] = Bin2Tobcd(tempBuf[3]);
            tempBuf[4] = Bin2Tobcd(tempBuf[4]);
            RtcWriteRegister(tempBuf, sizeof(tempBuf));
            break;
        }
        case RTC_WAKEUP_HOUR:
        {
            memset(tempBuf, 0x80, sizeof(tempBuf));
            tempBuf[0] = PCF85063_REG_AS;
            tempBuf[1] = (pTimeStruct->currentTime.second)%60;
            tempBuf[2] = (pTimeStruct->currentTime.minute)%60;
            tempBuf[3] = (pTimeStruct->currentTime.hour + hour)%24;
            tempBuf[4] = (pTimeStruct->currentTime.day);

            tempBuf[1] = Bin2Tobcd(tempBuf[1]);
            tempBuf[2] = Bin2Tobcd(tempBuf[2]);
            tempBuf[3] = Bin2Tobcd(tempBuf[3]);
            tempBuf[4] = Bin2Tobcd(tempBuf[4]);
            RtcWriteRegister(tempBuf, sizeof(tempBuf));
            break;
        }
        case RTC_WAKEUP_DAY:
        {
            memset(tempBuf, 0x80, sizeof(tempBuf));
            tempBuf[0] = PCF85063_REG_AS;
            tempBuf[1] = (pTimeStruct->currentTime.second)%60;
            tempBuf[2] = (pTimeStruct->currentTime.minute)%60;
            tempBuf[3] = (pTimeStruct->currentTime.hour)%24;
            tempBuf[4] = pTimeStruct->currentTime.day;

            tempBuf[1] = Bin2Tobcd(tempBuf[1]);
            tempBuf[2] = Bin2Tobcd(tempBuf[2]);
            tempBuf[3] = Bin2Tobcd(tempBuf[3]);
            tempBuf[4] = Bin2Tobcd(tempBuf[4]);
            RtcWriteRegister(tempBuf, sizeof(tempBuf));
            break;
        }
    #endif
        case RTC_WAKEUP_SPECIFIED:
        {
        #ifdef USE_INTERNAL_RTC
            if((0x00 == day) && (0x00 == hour) && (0x00 == minute) && (0x00 == sec))
            {
                rtcAlarmConfig.alarmTime.year = pTimeStruct->currentTime.year + 2000 + 20;
                rtcAlarmConfig.alarmTime.month = pTimeStruct->currentTime.month;
                rtcAlarmConfig.alarmTime.day = pTimeStruct->currentTime.day;
                rtcAlarmConfig.alarmTime.hour = pTimeStruct->currentTime.hour;
                rtcAlarmConfig.alarmTime.minutes = pTimeStruct->currentTime.minute;
                rtcAlarmConfig.alarmTime.seconds = pTimeStruct->currentTime.second;
                rtcAlarmConfig.alarmIntEnable = false;
                rtcAlarmConfig.rtcAlarmCallback = RtcAlarmCallback;
                ret = RTC_DRV_ConfigureAlarmInt(0, &rtcAlarmConfig);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC WakeUp Time Clear.ret = %d \r\n",ret);
            }
            else if((day <= 31) && (hour <= 23) && (minute <= 59) && (sec <= 59))
            {
                rtcAlarmConfig.alarmTime.year = pTimeStruct->currentTime.year + 2000;
                rtcAlarmConfig.alarmTime.month = pTimeStruct->currentTime.month;
                rtcAlarmConfig.alarmTime.day = day;
                if(rtcAlarmConfig.alarmTime.day < pTimeStruct->currentTime.day) //如果小于当天，表示下一个月
                {
                    rtcAlarmConfig.alarmTime.month++;
                    if(rtcAlarmConfig.alarmTime.month > 12)
                    {
                        rtcAlarmConfig.alarmTime.month = 1;
                        rtcAlarmConfig.alarmTime.year++;
                    }
                }
                rtcAlarmConfig.alarmTime.hour = hour;
                rtcAlarmConfig.alarmTime.minutes = minute;
                rtcAlarmConfig.alarmTime.seconds = sec;
                rtcAlarmConfig.alarmIntEnable = true;
                rtcAlarmConfig.rtcAlarmCallback = RtcAlarmCallback;
                ret = RTC_DRV_ConfigureAlarmInt(0, &rtcAlarmConfig);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC WakeUp Time Set %d-%02d-%02d %02d:%02d:%02d, Type:%d, ret=%d\r\n", 
                        rtcAlarmConfig.alarmTime.year, rtcAlarmConfig.alarmTime.month, rtcAlarmConfig.alarmTime.day,
                        rtcAlarmConfig.alarmTime.hour, rtcAlarmConfig.alarmTime.minutes, rtcAlarmConfig.alarmTime.seconds, 
                        rtcWakeUpType, ret);
            }
            else
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC WakeUp Time Set was called with Invalid param. Day=%d, Hour=%d, Minute=%d, Second=%d. \r\n", day, hour, minute, sec);
            }
        #else
            memset(tempBuf, 0x80, sizeof(tempBuf));
            if(day == 0x80 && hour == 0x80 && minute == 0x80 && sec == 0x80)
            {
                tempBuf[0] = PCF85063_REG_AS;
                tempBuf[1] = tempBuf[1];
                tempBuf[2] = tempBuf[2];
                tempBuf[3] = tempBuf[3];
                tempBuf[4] = tempBuf[4];
                clearRtc = TRUE;
            }
            else
            {
                tempBuf[0] = PCF85063_REG_AS;
                tempBuf[1] = sec%60;
                tempBuf[2] = minute%60;
                tempBuf[3] = hour%24;
                tempBuf[4] = day;
                tempBuf[1] = Bin2Tobcd(tempBuf[1]);
                tempBuf[2] = Bin2Tobcd(tempBuf[2]);
                tempBuf[3] = Bin2Tobcd(tempBuf[3]);
                tempBuf[4] = Bin2Tobcd(tempBuf[4]);
            }

            RtcWriteRegister(tempBuf, sizeof(tempBuf));
        #endif
            break;
        }
        default:
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Rtc WakeUp Set inValid, type:%d\r\n", rtcWakeUpType);
            ret = -1;
            break;
        }
    }

    if(clearRtc == TRUE)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC WakeUp Time Clear\r\n");
    }
    else
    {
    #ifndef USE_INTERNAL_RTC
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC WakeUp Time Set xx-xx-%02d %02d:%02d:%02d, Type:%d\r\n",  ((0x80 == tempBuf[3])?pTimeStruct->currentTime.day:    tempBuf[4]),  \
                                                                                                           ((0x80 == tempBuf[3])?pTimeStruct->currentTime.hour :  tempBuf[3]),  \
                                                                                                           ((0x80 == tempBuf[2])?pTimeStruct->currentTime.minute: tempBuf[2]), \
                                                                                                           ((0x80 == tempBuf[1])?pTimeStruct->currentTime.second: tempBuf[1]), \
                                                                                                           rtcWakeUpType);
    #endif
    }
#ifndef USE_INTERNAL_RTC
    memset(tempBuf, 0x00, 2);
    tempBuf[0] = PCF85063_REG_CTRL2;
    tempBuf[1] = 0x80;
    RtcWriteRegister(tempBuf, 2);
#endif
    return ret;
}

uint8_t str2Hex(uint8_t data1, uint8_t data2)
{
    char hex[] = "00";
    uint8_t ret = 0;

    if(((('0' <= data1) && ('9' >= data1)) || 
        (('a' <= data1) && ('f' >= data1)) || 
        (('A' <= data1) && ('F' >= data1))) && 
       ((('0' <= data2) && ('9' >= data2)) || 
        (('a' <= data2) && ('f' >= data2)) || 
        (('A' <= data2) && ('F' >= data2))))
    {
        hex[0] = data1;
        hex[1] = data2;
        ret = (uint8_t)strtol(hex, NULL, 16);
    }
    return ret;
}

/*************************************************
函数名称: SystemApiStrToHex
函数功能: 字符串转换为16进制
输入参数: unsigned char *pSource
          int sourceLen
          unsigned char *pDest
输出参数: 无
函数返回类型值：无
编写者: 
编写日期 :
*************************************************/
void SystemApiStrToHex(uint8_t *pSource, int32_t sourceLen, uint8_t *pDest)
{
    uint8_t h1, h2;
    uint8_t s1, s2;
    int32_t i;

    if ((NULL == pSource) || (NULL == pDest))
    {
        return;
    }

    for (i = 0; i < sourceLen; i++)
    {
        h1 = pSource[2 * i];
        h2 = pSource[2 * i + 1];

        s1 = toupper(h1) - 0x30;
        if (s1 > 9)
            s1 -= 7;

        s2 = toupper(h2) - 0x30;
        if (s2 > 9)
            s2 -= 7;

        pDest[i] = s1 * 16 + s2;
    }
}

/*************************************************
函数名称: SystemApiHexToStr
函数功能: 16进制转换为字符串
输入参数: unsigned char *pSource
          int sourceLen
          unsigned char *pDest
输出参数: 无
函数返回类型值：无
编写者: 
编写日期 :
*************************************************/
void SystemApiHexToStr(uint8_t *pSource, int32_t sourceLen, uint8_t *pDest)
{
    uint8_t ddl, ddh;
    int32_t i;

    if ((NULL == pSource) || (NULL == pDest))
    {
        return;
    }
    
    for (i = 0; i < sourceLen; i++)
    {
        ddh = 48 + pSource[i] / 16;
        ddl = 48 + pSource[i] % 16;
        if (ddh > 57) ddh = ddh + 7;
        if (ddl > 57) ddl = ddl + 7;
        pDest[i * 2] = ddh;
        pDest[i * 2 + 1] = ddl;
    }

    pDest[sourceLen * 2] = '\0';
}

static uint32_t reflect(uint32_t data, uint8_t n_bits)
{
    uint32_t reflection = 0;
    for (uint8_t i = 0; i < n_bits; i++)
    {
        if (data & 0x01)
        {
            reflection |= (1UL << ((n_bits - 1) - i));
        }
        data >>= 1;
    }
    return reflection;
}

/**
 * @brief 根据提供的参数计算 CRC 值
 *
 * @param data 数据缓冲区指针
 * @param length 数据长度
 * @param params CRC 参数结构体指针
 * @return uint32_t 计算得到的 CRC 值
 */
uint32_t SystemCalCRC(const uint8_t *data, uint32_t length, const CRC_Parameters *params)
{
    uint32_t crc = params->init;

    for (uint32_t i = 0; i < length; i++)
    {
        uint8_t byte = data[i];

        // 反射输入字节（如果需要）
        if (params->reflect_input)
        {
            byte = (uint8_t) reflect(byte, 8);
        }

        if (params->width <= 8)
        {
            crc ^= byte;
        }
        else
        {
            crc ^= ((uint32_t) byte << (params->width - 8));
        }

        for (uint8_t bit = 0; bit < 8; bit++)
        {
            if (crc & (1UL << (params->width - 1)))
            {
                crc = (crc << 1) ^ params->polynomial;
            }
            else
            {
                crc = (crc << 1);
            }
        }
    }

    // 反射输出 CRC（如果需要）
    if (params->reflect_output)
    {
        crc = reflect(crc, params->width);
    }

    crc ^= params->final_xor;

    // 掩码到 CRC 位宽
    if (params->width < 32)
    {
        crc &= ((1UL << params->width) - 1);
    }
    else
    {
        crc &= 0xFFFFFFFF;
    }

    return crc;
}

uint8_t SystemCalCrc8(const uint8_t *data, uint32_t length)
{
    CRC_Parameters params = {
        .width = 8,
        .polynomial = 0x07,
        .init = 0,
        .final_xor = 0x00,
        .reflect_input = false,
        .reflect_output = false
    };

    return (uint8_t) SystemCalCRC(data, length, &params);
}

uint16_t SystemCalCrc16(const uint8_t *data, uint32_t length)
{
    CRC_Parameters params = {
        .width = 16,
        .polynomial = 0x8005,
        .init = 0x0000,
        .final_xor = 0x0000,
        .reflect_input = true,
        .reflect_output = true
    };

    return (uint16_t) SystemCalCRC(data, length, &params);
}

uint32_t SystemCalCrc32(const uint8_t *data, uint32_t length)
{
    CRC_Parameters params = {
        .width = 32,
        .polynomial = 0x04C11DB7,
        .init = 0xFFFFFFFF,
        .final_xor = 0xFFFFFFFF,
        .reflect_input = true,
        .reflect_output = true
    };

    return SystemCalCRC(data, length, &params);
}

/*************************************************
函数名称: UnixToLocalTime
函数功能: 时间戳转换成标准时间
输入参数: unixTime - 时间戳，自1970经过的秒数
输出参数: localTime - 转换后的标准时间
函数返回类型值：MCU_OK - 转换成功，MCU_PARA_ERROR - 参数有误
编写者: zhengyong
编写日期 :2020/12/22
*************************************************/
int UnixToLocalTime(uint32_t unixTime, SystemTime_s *localTime)
{
    uint8_t days[12] = {31,28,31,30,31,30,31,31,30,31,30,31}; //12个月
    uint32_t pass4Year = 0;
    uint32_t yearHour = 0;
    uint32_t fourYearHour = 0;

    if(NULL == localTime)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Para null when trans time to local\r\n");
        return -2;
    }

    localTime->timeZone = 8;

    //加上时区，计算秒
    unixTime = unixTime + (int32_t)localTime->timeZone * 3600;
    localTime->sec = (uint8_t)(unixTime % 60);
    unixTime /= 60;

    //计算分
    localTime->min = (uint8_t)(unixTime % 60);
    unixTime /= 60;

    //计算过去多少个4年
    fourYearHour = (365 * 4 + 1) * 24;
    pass4Year = unixTime / fourYearHour;
    unixTime %= fourYearHour;
    localTime->year = (pass4Year << 2) + 1970;

    //校正闰年影响的年份，计算一年中剩下的小时数
    while(true)
    {
        //计算一年中的小时数
        yearHour = 365 * 24;
        if((localTime->year & 0x03) == 0 && ((localTime->year % 100) != 0 || (localTime->year % 400) == 0))
        {
            yearHour += 24;
        }

        //如果剩余的小时数小于一年的小时数，退出循环
        if(unixTime < yearHour)
        {
            break;
        }

        //否则加一年，继续计算
        localTime->year++;
        unixTime -= yearHour;
    }

    //计算小时数
    localTime->hour = (uint8_t)(unixTime % 24);
    //剩余天数
    unixTime /= 24;

    //校正闰年误差
    if((localTime->year & 0x03) == 0 && ((localTime->year % 100) != 0 || (localTime->year % 400) == 0))
    {
        days[1] = 29;
    }

    for(localTime->mon = 0; days[localTime->mon] <= unixTime; localTime->mon++)
    {
        unixTime -= days[localTime->mon];
    }
    localTime->mon++;
    localTime->day = (uint8_t)unixTime + 1; //每月从1号开始，而不是0号

    return 0;
}

/*************************************************
函数名称: LocalToUnixTime
函数功能: 标准时间转换成时间戳
输入参数: localTime - 标准时间
输出参数: unixTime - 转换后的时间戳，自1970经过的秒数
函数返回类型值：MCU_OK - 转换成功，MCU_PARA_ERROR - 参数有误
编写者: zhengyong
编写日期 :2020/12/22
*************************************************/
int LocalToUnixTime(const SystemTime_s *localTime, uint32_t *unixTime)
{
    uint8_t days[12] = {31,28,31,30,31,30,31,31,30,31,30,31}; //12个月
    uint32_t totalDays = 0;
    uint32_t i = 0;

    if(NULL == localTime || NULL == unixTime)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Para null when trans time to unix\r\n");
        return -2;
    }

    //先按平年计算过去年数对应的天数
    totalDays = (uint32_t)(localTime->year - SYSTEM_TIME_START_YEAR) * 365;
    //补充闰年天数
    for(i = SYSTEM_TIME_START_YEAR; i < localTime->year; i++)
    {
        if((i & 0x03) == 0 && ((i % 100) != 0 || (i % 400) == 0))
        {
            totalDays++;
        }
    }

    //计算当年天数，当年如果是闰年，2月加1天
    if((localTime->year & 0x03) == 0 && ((localTime->year % 100) != 0 || (localTime->year % 400) == 0))
    {
        days[1] = 29;
    }
    for(i = 1; i < localTime->mon; i++)
    {
        totalDays += days[i - 1];
    }
    totalDays += localTime->day - 1;

    //计算总时间
    *unixTime = ((totalDays * 24 + localTime->hour - localTime->timeZone) * 60 + localTime->min) * 60 + localTime->sec;
    return 0;
}


//to define Disable Interrupt and Enable Interrupt for the system
void __DI(void)
{
    portDISABLE_INTERRUPTS();
}

void __EI(void)
{
    portENABLE_INTERRUPTS();
}

#ifdef _WINDOWS_SIM
void asm(void)
{
    //empty function definition to calm compiler
}
#endif

void __STSR(void)
{

}