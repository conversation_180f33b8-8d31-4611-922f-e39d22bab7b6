/*
      NvApi.c
      此文件主要是MCU存储设计函数接口,包括NV数据初始化，NV数据读接口，NV数据写接口函数等
      注意点:
      1:RH850 DATA FLASH擦除后数据是随机数，不是0x00或0xff,只能通过是否空白字节来查询。
      2:NV读和写都是32位地址对齐，非32位地址传入后FDL会返回失败
      3:写入数据之前必须先要擦除所在的数据块
      4:读取之前必须是写入过的数据块，不然会返回失败。
描述：
作者：廖勇刚
时间：2016.7.5
*/
#include "LogApi.h"
#include "event.h"
#define EEELIB_INTDEF

#include  <string.h>
#include <stdio.h>
#include "NvApi.h"
#include "fee_config.h"
#include "interrupt_manager.h"
#include "flash_driver.h"
#include "DiagApi.h"
#include "SystemApi.h"
#include "BatApi.h"
#include "CanApi.h"


/************************全局变量****************************/
NvAttributeInfo     g_nvAttributeInfo             = {0};

DtcInfo             g_dtcInfo                     = {0};

TboxSelfConfigPara  g_nvTboxSelfConfigData        = {0};

StaticDidInfo       g_staticDid                   = {0};

StaticDid2Info       g_staticDid2                 = {0};

CarInfo             g_carInfo                     = {0};

TboxBleKeyHardPara  g_tboxBleKeyHardPara          = {0};

BackupRamInfo       g_backupRamInfo               = {0};

DynamicDidInfo      g_dynamicDid                  = {0};

TotalDidInfo        g_totalDidInfo                = {0};

//FEE status declaration
MemIf_StatusType FeeRstStatus = MEMIF_UNINIT;

NvItemInfo g_nvInfoMap[NV_MAX_NUMBER] = 
{
        {NV_NONE,                 0,                          0,                          NULL, NULL,                                 0, NULL},
        {NV_ID_CAR_INFO,          sizeof(CarInfo),            sizeof(CarInfo),            NULL, (uint32_t *) &g_carInfo,              0, NULL},
        {NV_ID_STATIC_DID,        sizeof(StaticDidInfo),      sizeof(StaticDidInfo),      NULL, (uint32_t *) &g_staticDid,            0, NULL},
        {NV_ID_STATIC_DID2,       sizeof(StaticDid2Info),     sizeof(StaticDid2Info),     NULL, (uint32_t *) &g_staticDid2,           0, NULL},
        {NV_ID_SELF_CONFIG,       sizeof(TboxSelfConfigPara), sizeof(TboxSelfConfigPara), NULL, (uint32_t *) &g_nvTboxSelfConfigData, 0, NULL},
        {NV_ID_BLE_HARDWARE_PARE, sizeof(TboxBleKeyHardPara), sizeof(TboxBleKeyHardPara), NULL, (uint32_t *) &g_tboxBleKeyHardPara,   0, NULL},
        {NV_ID_BACKUP_RAM,        sizeof(BackupRamInfo),      sizeof(BackupRamInfo),      NULL, (uint32_t *) &g_backupRamInfo,        0, NULL},
        {NV_ID_DTC_INFO +
         DTC_CAN_BUS_OFF_INDEX,                   sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_CAN_BUS_OFF_INDEX],                   0, NULL},
        {NV_ID_DTC_INFO +
         DTC_EXT_POWER_VOL_LOW_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_EXT_POWER_VOL_LOW_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_EXT_POWER_VOL_HIGH_INDEX,            sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_EXT_POWER_VOL_HIGH_INDEX],            0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_HECU_INDEX,            sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_HECU_INDEX],            0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_BCM_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_BCM_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_BMS_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_BMS_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_MCU_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_MCU_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_DCDC_INDEX,            sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_DCDC_INDEX],            0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_ABS_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_ABS_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_IC_INDEX,              sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_IC_INDEX],              0, NULL},
        {NV_ID_DTC_INFO +
         DTC_LOST_COM_WITH_APU_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_LOST_COM_WITH_APU_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_GNSS_MODULE_FAULT_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_GNSS_MODULE_FAULT_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_REMOTE_COM_FAULT_INDEX,              sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_REMOTE_COM_FAULT_INDEX],              0, NULL},
        {NV_ID_DTC_INFO +
         DTC_SIM_CARD_FAULT_INDEX,                sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_SIM_CARD_FAULT_INDEX],                0, NULL},
        {NV_ID_DTC_INFO +
         DTC_NETWORK_MODULE_FAILURE_INDEX,        sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_NETWORK_MODULE_FAILURE_INDEX],        0, NULL},
        {NV_ID_DTC_INFO +
         DTC_GNSS_ANT_OPEN_INDEX,                 sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_GNSS_ANT_OPEN_INDEX],                 0, NULL},
        {NV_ID_DTC_INFO +
         DTC_GNSS_ANT_SHORT_INDEX,                sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_GNSS_ANT_SHORT_INDEX],                0, NULL},
        {NV_ID_DTC_INFO +
         DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX, sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX], 0, NULL},
        {NV_ID_DTC_INFO +
         DTC_EMMC_STORAGE_CHIP_FAULT_INDEX,       sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_EMMC_STORAGE_CHIP_FAULT_INDEX],       0, NULL},
        {NV_ID_DTC_INFO +
         DTC_EMMC_STORAGE_FULL_INDEX,             sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_EMMC_STORAGE_FULL_INDEX],             0, NULL},
        {NV_ID_DTC_INFO +
         DTC_NETWORK_MODULE_FLASH_FULL_INDEX,     sizeof(DtcType), sizeof(DtcType), NULL, (uint32_t *) &g_dtcInfo.dtc[DTC_NETWORK_MODULE_FLASH_FULL_INDEX],     0, NULL},
};

uint32 g_dtcValueList[] = {
        DTC_CAN_BUS_OFF_VALUE,        // Info CAN Bus Off
        DTC_EXT_POWER_VOL_LOW_VALUE,        //供电电压电量过低
        DTC_EXT_POWER_VOL_HIGH_VALUE,        //供电电压电量过高
        DTC_LOST_COM_WITH_HECU_VALUE,        // Lost communication with HECU
        DTC_LOST_COM_WITH_BCM_VALUE,         // Lost communication with BCM
        DTC_LOST_COM_WITH_BMS_VALUE,         // Lost communication with BMS
        DTC_LOST_COM_WITH_MCU_VALUE,         // Lost communication with MCU
        DTC_LOST_COM_WITH_DCDC_VALUE,         // Lost communication with DCDC
        DTC_LOST_COM_WITH_ABS_VALUE,         // Lost communication with ABS
        DTC_LOST_COM_WITH_IC_VALUE,          // Lost communication with IC
        DTC_LOST_COM_WITH_APU_VALUE,         // Lost communication with APU
        DTC_GNSS_MODULE_FAULT_VALUE,           // 定位模块故障
        DTC_REMOTE_COM_FAULT_VALUE,           // 远程通信故障
        DTC_SIM_CARD_FAULT_VALUE,           // SIM卡不可用
        DTC_NETWORK_MODULE_FAILURE_VALUE,           // 联网模块（模组）故障、或者联网天线损坏、开路、短路
        DTC_GNSS_ANT_OPEN_VALUE,        // GPS antenna open
        DTC_GNSS_ANT_SHORT_VALUE,        // GPS antenna short to GND
        DTC_INTERNAL_BATTERY_DISCONNECTED_VALUE,        // 内部备用电池未连接
        DTC_EMMC_STORAGE_CHIP_FAULT_VALUE,             // eMMC存储芯片异常
        DTC_EMMC_STORAGE_FULL_VALUE,                   // eMMC存储空间满
        DTC_NETWORK_MODULE_FLASH_FULL_VALUE,          // 通信模块内部FLASH存储空间满
};

/*
    擦除存储区域 1K
*/
status_t EraseStorageRegion(uint32_t address)
{
    status_t status = STATUS_SUCCESS;
    uint8_t i = 10;

    do{
        INT_SYS_DisableIRQGlobal();
        status = FLASH_DRV_EraseSector(FLASH_INST, address, ADDRESS_1k_ALIGNMENT); //先擦
        INT_SYS_EnableIRQGlobal();

    }while(( status !=STATUS_SUCCESS )&&( i-- ));

    return status;
}

/*
    向存储区域写入数据
    data 写入的数组的字节
    len 为8的倍数
*/
status_t WriteDataToFlash(uint32_t address,uint32_t* data,uint16_t len)
{
    uint16_t Num = 0;
    status_t status = STATUS_ERROR;

    if((len / 8 == 0)||(data == NULL))
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "The Parameter of WriteUpgradeStatusToFlash is Error\r\n");
        return STATUS_ERROR;
    }
    Num = len / 8;
    if(len % FLASH_MIN_BYTE_ALIGNMENT)
    {
        Num = Num + 1;
    }
    INT_SYS_DisableIRQGlobal();
    status = FLASH_DRV_Program(FLASH_INST, address, FLASH_MIN_BYTE_ALIGNMENT * Num, data);
    INT_SYS_EnableIRQGlobal();
    return status;
}

/*
    读取指定flash地址存储的数据
*/
uint32_t ReadValueOfFlashAddress(uint32_t address)
{
    return  *(uint32_t *)(address);
}


static int WaitForFeeIdle(void)
{
    MemIf_StatusType feeStatus = MEMIF_UNINIT;
    do
    {
        Fee_MainFunction();
        feeStatus = Fee_GetStatus();
    } while (feeStatus != MEMIF_IDLE);

    return Fee_GetJobResult();
}
/*************************************************
函数名称: NvApiReadData
函数功能: 从云途芯片的D-FLASH读取指定的ID数据
输入参数: Block ID，读数据指针, 读取长度
输出参数: 读取结果
函数返回类型值：
            NV_NO_ERROR---执行正确
            NV_PARA_PONIT_NULL---输入参数指针为空
            NV_OUT_OF_RANGE_VALUE---输入参数超过已定义范围值
            NV_READ_FAIL_DEFAULT_VALUE---当前和备份数据区域都读取失败后返回代码默认值
编写者: liaoyonggang
编写日期 :2016/07/11
修改日期 :2016/07/15 增加备份区域读取成功后回写当前区域
*************************************************/
NvErrorCode NvApiReadData(uint16_t id, uint32_t *pNvData, uint16_t len)
{
    NvErrorCode errorCode = NV_NO_ERROR;
    Std_ReturnType readResult = E_OK;

    if(NULL == pNvData)
    {
        errorCode = NV_PARA_PONIT_NULL;
        return errorCode;
    }

    if(NV_MAX_NUMBER <= id || NV_NONE == id)
    {
        errorCode = NV_OUT_OF_RANGE_VALUE;
        return errorCode; 
    }

    readResult = Fee_Read(id, 0, (uint8_t *)pNvData, len);
    if(E_OK != readResult)
    {
        errorCode = NV_READ_FDL_FAIL;
        return(errorCode);
    }

    readResult = WaitForFeeIdle();
    if(E_OK != readResult)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "NV read the FEE is failed,id = %u, readResult = %u\r\n",id, readResult);
        memcpy(pNvData, g_nvInfoMap[id].pNvDefaultData, len);
        errorCode = NV_READ_FAIL_DEFAULT_VALUE;
    }
    return(errorCode);
}

/*************************************************
函数名称: NvApiWriteData
函数功能: 从云途芯片的D-FLASH写入指定的ID数据
输入参数: Block ID，写入数据指针，数据验证长度
输出参数: 读取结果
函数返回类型值：
            NV_NO_ERROR---执行正确
            NV_PARA_PONIT_NULL---输入参数指针为空
            NV_OUT_OF_RANGE_VALUE---输入参数超过已定义范围值
            NV_WRITE_CURRENT_FAIL---当前数据区域写入失败
编写者: liaoyonggang
编写日期 :2016/07/11
*************************************************/
NvErrorCode NvApiWriteData(uint16_t id, uint8_t *pNvData, uint16_t len)
{
    Std_ReturnType writeResult = E_NOT_OK;
    Std_ReturnType ReadResult = E_NOT_OK;
    uint8_t readData[NV_MAX_TOTAL_LEN] = {0};

    SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "NV write FEE is id = %u len = %u\r\n",id,len);

    if(NULL == pNvData)
    {
        return NV_PARA_PONIT_NULL;
    }

    if(NV_MAX_NUMBER <= id || NV_NONE == id)
    {
        return NV_OUT_OF_RANGE_VALUE;
    }

    memcpy(g_nvInfoMap[id].pNvData, (uint8_t*)pNvData, len);
    writeResult = Fee_Write(id, (uint8_t*)pNvData);
    if(E_OK != writeResult)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "NV write FEE is failed id = %u writeResult = %u\r\n",id,writeResult);
        return NV_WRITE_FDL_FAIL;
    }

    writeResult = WaitForFeeIdle();
    if(E_OK != writeResult)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "NV write FEE is failed id = %u writeResult = %u\r\n",id,writeResult);
        return NV_WRITE_CURRENT_FAIL;
    }

    if(len > NV_MAX_TOTAL_LEN)
    {
        len = NV_MAX_TOTAL_LEN;
    }

    ReadResult = Fee_Read(id, 0, (UINT8*)readData, len);
    if(E_OK != ReadResult)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "NV read the write FEE result failed,id = %u ReadResult = %u\r\n",id,ReadResult);
    }

    ReadResult = WaitForFeeIdle();
    if(E_OK != ReadResult)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "NV read the write FEE result failed,id = %u ReadResult = %u\r\n",id,ReadResult);
    }

    if(memcmp(pNvData, readData, len) != 0)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Nv Write FEE Data id=%d verify failed.\r\n", id);
    }


    if(NULL != g_nvInfoMap[id].WriteNvCallBack)
    {
        g_nvInfoMap[id].WriteNvCallBack();
    }
    return NV_NO_ERROR;
}
/*************************************************
函数名称: InitDidTotalTable
函数功能: 初始化DID总表数据
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: lfc
编写日期 :2024/12/03
*************************************************/
void InitDidTotalTable(void)
{
    char buf[30] = {0};

    g_totalDidInfo.carInfo = &g_carInfo;
    g_totalDidInfo.staticDidInfo = &g_staticDid;
    g_totalDidInfo.staticDid2Info = &g_staticDid2;

    COPY_STRING_WITH_LIMIT(g_totalDidInfo.tboxSoftwareId, TBOX_SOFTWARE_ID, TBOX_SOFTWARE_ID_LEN);
    COPY_STRING_WITH_LIMIT(g_totalDidInfo.tboxHardwareId, TBOX_HARDWARE_ID, TBOX_HARDWARE_ID_LEN);
    COPY_STRING_WITH_LIMIT(g_totalDidInfo.tboxSystemSuppierIdentifier, TBOX_SYSTEM_SUPPIER_IDENTIFIER, TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN);
    COPY_STRING_WITH_LIMIT(g_totalDidInfo.tboxName, TBOX_NAME, TBOX_NAME_LEN);

    snprintf(buf, TBOX_SOFT_VERSION_LEN + 1, "1.0.%u.%u", MCU_SOFT_MAIN_VERSION, MCU_SOFT_SUB_VERSION);
    COPY_STRING_WITH_LIMIT(g_totalDidInfo.tboxSoftVersion, buf, TBOX_SOFT_VERSION_LEN);
    snprintf((char *)g_totalDidInfo.tboxHardVersion, TBOX_HARD_VERSION_LEN + 1, "1.0.%u.%u", MCU_HR_MAIN_VERSION, MCU_HR_SUB_VERSION);
    uint32_t bootVersion = ReadValueOfFlashAddress(BOOT_VERSION_ADDR);
    uint8_t mainVersion = (bootVersion >> 8) & 0xff;
    uint8_t subVersion  =  bootVersion       & 0xff;
    snprintf((char *)g_totalDidInfo.tboxBootVersion, TBOX_HARD_VERSION_LEN + 1, "%u.%u", mainVersion, subVersion);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "SysVersion: boot:%s, soft:%s, hard:%s\r\n",g_totalDidInfo.tboxBootVersion,g_totalDidInfo.tboxSoftVersion,g_totalDidInfo.tboxHardVersion);
}

/*************************************************
函数名称: NvDefaultDataInit
函数功能: 初始化默认数据
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: lfc
编写日期 :2024/12/03
*************************************************/
void NvDefaultDataInit(void)
{
    uint32_t ret = 0;

    g_carInfo.wakeupTimeInterval = 4 * 60 * 60; //4小时
    g_carInfo.AutoRechargeUnderVoltage = 12200; //12.2V
    g_carInfo.link1SSLEnable = 0x01; //默认使能
    g_carInfo.link2SSLEnable = 0x01; //默认使能
    g_carInfo.acTimeoutMinutes = 10 * 60; //默认10分钟
    g_carInfo.timedWakeupEnable = 0x01; //默认使能
    g_carInfo.networkWakeupEnable = 0x01; //默认使能
    g_carInfo.globalLogEnable = 0x01; //默认使能
    g_carInfo.link1Enable = 0x01; //默认使能
    g_carInfo.link2Enable = 0x01; //默认使能
    g_carInfo.link3Enable = 0x01; //默认使能

    g_nvTboxSelfConfigData.logAndIpc.logMode = LOG_ARM_SAVE_OUTPUT;
    g_nvTboxSelfConfigData.logAndIpc.logLevel = LOG_INFO_OUTPUT + LOG_WARING_OUTPUT + LOG_ERROR_OUTPUT;
    g_nvTboxSelfConfigData.logAndIpc.retransmissionCount = 2;
    g_nvTboxSelfConfigData.logAndIpc.retransmissionTimeout = 4;

    g_nvTboxSelfConfigData.STmin = 20; // 默认20ms

    for(int nvId = NV_ID_CAR_INFO; nvId < NV_MAX_NUMBER; nvId++)
    {
        ret |= NvApiWriteData(nvId,  (uint8_t*)g_nvInfoMap[nvId].pNvData, g_nvInfoMap[nvId].nvValidLen);
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Nv Default Data Init.ret = %d\r\n",ret);
}
/*************************************************
函数名称: ReadNvAttributeInfo
函数功能: 读取NV信息，包含魔术字和版本
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: lfc
编写日期 :2024/12/12
*************************************************/
void ReadNvAttributeInfo(void)
{
    g_nvAttributeInfo.magicData = ReadValueOfFlashAddress(USER_NV_INIT_INFO_ADDR);
    g_nvAttributeInfo.nvVersion = ReadValueOfFlashAddress(USER_NV_INIT_INFO_ADDR + 4);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Nv Info Data magicData = 0x%X, nvVersion = %u\r\n", g_nvAttributeInfo.magicData, g_nvAttributeInfo.nvVersion);
}
/*************************************************
函数名称: NvAttributeInfoInit
函数功能: 初始化魔术字和版本
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: lfc
编写日期 :2024/12/12
*************************************************/
void NvAttributeInfoInit(void)
{
    uint32_t i = 10;
    uint32_t magicData = NV_MAGIC_DATA;

    g_nvAttributeInfo.magicData = NV_MAGIC_DATA;
    g_nvAttributeInfo.nvVersion = NV_VERSION;

    do{
        EraseStorageRegion(USER_NV_INIT_INFO_ADDR);
        WriteDataToFlash(USER_NV_INIT_INFO_ADDR, (uint32_t *)&g_nvAttributeInfo, sizeof(g_nvAttributeInfo));
        magicData = ReadValueOfFlashAddress(USER_NV_INIT_INFO_ADDR);                                          //读出来检查
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Writ Addr 0x%X to 0x%X %s\r\n", USER_NV_INIT_INFO_ADDR, NV_MAGIC_DATA, g_nvAttributeInfo.magicData == magicData ? "Success" : "Failed");
    }while((magicData != NV_MAGIC_DATA) && (i--));
}

/*************************************************
函数名称: CheckNvDataNeedInit
函数功能: 读取数据后检查是否初始化数据
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: lfc
编写日期 :2024/12/03
*************************************************/
bool CheckNvDataNeedInit(void)
{
    ReadNvAttributeInfo();
    if (g_nvAttributeInfo.magicData != NV_MAGIC_DATA)
    {
        return true;
    }
    return false;
}

/*************************************************
函数名称: NvMcuPowerOnReadNvData
函数功能: MCU开机读取DATA FALSH里的NV数据项
输入参数: 无
输出参数: 读取结果
函数返回类型值：
            NV_NO_ERROR---执行正确
            NV_PARA_PONIT_NULL---输入参数指针为空
            NV_OUT_OF_RANGE_VALUE---输入参数超过已定义范围值
            NV_READ_FAIL_DEFAULT_VALUE---当前和备份数据区域都读取失败后返回代码默认值
编写者: liaoyonggang
编写日期 :2016/07/11
*************************************************/
NvErrorCode NvMcuPowerOnReadNvData(void)
{
    NvErrorCode errorCode = NV_NO_ERROR;

    for(int nvId = NV_ID_CAR_INFO; nvId < NV_MAX_NUMBER; nvId++)
    {
        errorCode |= NvApiReadData(nvId, g_nvInfoMap[nvId].pNvData, g_nvInfoMap[nvId].nvValidLen);
    }

    g_backupRamInfo.lastResetARMTime = 0;

    if (g_nvTboxSelfConfigData.STmin == 0)
    {
        g_nvTboxSelfConfigData.STmin = 20;
        NvApiWriteData(NV_ID_SELF_CONFIG, (uint8_t *) &g_nvTboxSelfConfigData, sizeof(g_nvTboxSelfConfigData));
    }
    return errorCode;
}

/*************************************************
函数名称: NvMcuPowerOnInitNvData
函数功能: MCU开机后DATA FLASH后NV数据初始化
输入参数: 无
输出参数: 读取结果
函数返回类型值：
            NV_NO_ERROR---执行正确
            NV_PARA_PONIT_NULL---输入参数指针为空
            NV_OUT_OF_RANGE_VALUE---输入参数超过已定义范围值
            NV_WRITE_BOTH_FAIL---当前和备份数据区域都写入失败
            NV_WRITE_BACKUP_FAIL---备份数据区域写入失败
            NV_WRITE_CURRENT_FAIL---当前数据区域写入失败
            NV_VERSION_CODE_LOW_NV---代码版本号小于NV版本号
            NV_VERSION_CODE_EQU_NV_PLUS_ONE---代码版本号等于NV版本号加一，正常NV升级数据和变更数据
            NV_VERSION_CODE_EQU_NV---代码版本号等于NV版本号， 正常启动
            NV_VERSION_CODE_HIGH_NV_PLUS_ONE---代码版本号大于NV版本号加一，非正常NV升级数据和变更数据
编写者: liaoyonggang
编写日期 :2016/07/11
*************************************************/
void NvMcuPowerOnInitNvData(void)
{    

    Fee_Init(&FEEGenConfig);
    do
    {
        Fee_MainFunction();
        FeeRstStatus = Fee_GetStatus();
    } while (MEMIF_IDLE != FeeRstStatus);

    if (MEMIF_JOB_OK != Fee_GetJobResult())
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "The Fee initialization is failed\n");
    }

    if (CheckNvDataNeedInit())
    {
        NvDefaultDataInit();
        NvAttributeInfoInit();
    }
    else
    {
        NvMcuPowerOnReadNvData();
    }

    InitDidTotalTable();

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "init NV data ok !\n");
}
/*************************************************
函数名称: NvClearDTC
函数功能: 清除DTC
输入参数: 无
输出参数: groupOfDTC DTC值，dtcType memoryType
函数返回类型值: int
编写者: lfc
编写日期 :2025/2/25
*************************************************/
int NvClearDTC(uint32 groupOfDTC, uint8 dtcType,uint8 memoryType)
{

    int ret = 0;
    DtcInfo  *dtcInfo = GetDtcInfo();

    SetCheckDtcEnable(false);
    int dtcIndex = 0;
    for(dtcIndex = 0; dtcIndex < DTC_MAX_INDEX; dtcIndex++)
    {
        if(groupOfDTC == g_dtcValueList[dtcIndex] || (groupOfDTC & DEM_DTC_GROUP_ALL_DTCS) == DEM_DTC_GROUP_ALL_DTCS)
        {
            memset(&dtcInfo->dtc[dtcIndex],0x00, sizeof(DtcType));
            ret |= NvApiWriteData(NV_ID_DTC_INFO + dtcIndex, (uint8 *)&dtcInfo->dtc[dtcIndex], sizeof(DtcType));
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Clear DTC: 0x%06X. ret: %d\r\n", g_dtcValueList[dtcIndex], ret);
        }
    }

    DiagDtcDeviceTableInit();
    SetCheckDtcEnable(true);

    return ret;
}

/*************************************************
函数名称: GetCanInfo
函数功能: 获取 g_carInfo 指针
输入参数: 无
输出参数: g_carInfo
函数返回类型值: CarInfo *
编写者: xiazhichuan
编写日期 :2024/9/25
*************************************************/
CarInfo *GetCanInfo(void)
{
    return &g_carInfo; 
}


/*************************************************
函数名称: GetStaticDid
函数功能: 获取 g_staticDid 指针
输入参数: 无
输出参数: g_staticDid
函数返回类型值: StaticDidInfo *
编写者: xiazhichuan
编写日期 :2024/9/25
*************************************************/
StaticDidInfo *GetStaticDid(void)
{
    return &g_staticDid; 
}

/*************************************************
函数名称: GetStaticDid2
函数功能: 获取 g_staticDid2 指针
输入参数: 无
输出参数: g_staticDid2
函数返回类型值: StaticDid2Info *
编写者: lfc
编写日期 :2024/11/27
*************************************************/
StaticDid2Info *GetStaticDid2(void)
{
    return &g_staticDid2;
}
/*************************************************
函数名称: GetDynamicDid
函数功能: 获取 g_dynamicDid 指针
输入参数: 无
输出参数: g_dynamicDid
函数返回类型值: DynamicDidInfo *
编写者: lfc
编写日期 :2024/11/27
*************************************************/
DynamicDidInfo *GetDynamicDid(void)
{
    return &g_dynamicDid;
}

uint8_t GetGearPosition(void)
{
    return g_dynamicDid.gearPosition;
}
uint16_t GetVehicleSpeed(void)
{
    return g_dynamicDid.vehicleSpeed;
}

/*************************************************
函数名称: GetBackupRamInfo
函数功能: 获取 g_backupRamInfo 指针
输入参数: 无
输出参数: g_backupRamInfo
函数返回类型值: BackupRamInfo *
编写者: xiazhichuan
编写日期 :2024/9/25
*************************************************/
BackupRamInfo *GetBackupRamInfo(void)
{
    return &g_backupRamInfo;
}


/*************************************************
函数名称: GetDtcInfo
函数功能: 获取 g_dtcInfo 指针
输入参数: 无
输出参数: g_dtcInfo
函数返回类型值: DtcInfo *
编写者: xiazhichuan
编写日期 :2024/9/25
*************************************************/
DtcInfo *GetDtcInfo(void)
{
    return &g_dtcInfo;
}


/*************************************************
函数名称: GetTboxSelfConfigData
函数功能: 获取 g_nvTboxSelfConfigData 指针
输入参数: 无
输出参数: g_nvTboxSelfConfigData
函数返回类型值: TboxSelfConfigPara *
编写者: xiazhichuan
编写日期 :2024/9/25
*************************************************/
TboxSelfConfigPara *GetTboxSelfConfigData(void)
{
    return &g_nvTboxSelfConfigData;
}
/*************************************************
函数名称: SetUdsSTmin
函数功能: 设置UDS STmin
输入参数: 无
输出参数:
函数返回类型值:
编写者: lfc
编写日期 :2025/2/10
*************************************************/
int SetUdsSTmin(uint8_t timeMs)
{
    if (timeMs > 127)
    {
        timeMs = 127;
    }
    if (timeMs == 0)
    {
        timeMs = 20;
    }
    g_nvTboxSelfConfigData.STmin = timeMs;
    int errorCode = NvApiWriteData(NV_ID_SELF_CONFIG, (uint8_t *) &g_nvTboxSelfConfigData, sizeof(g_nvTboxSelfConfigData));
    if(NV_NO_ERROR != errorCode)
    {
        return MCU_ERROR;
    }
    return MCU_OK;
}

/*************************************************
函数名称: GetTboxBleKeyHardPara
函数功能: 获取 g_tboxBleKeyHardPara 指针
输入参数: 无
输出参数: g_tboxBleKeyHardPara
函数返回类型值: TboxBleKeyHardPara *
编写者: xiazhichuan
编写日期 :2024/9/25
*************************************************/
TboxBleKeyHardPara *GetTboxBleKeyHardPara(void)
{
    return &g_tboxBleKeyHardPara;
}

/*************************************************
函数名称: GetDtcFaultStatus
函数功能: 获取 指定DTC故障状态
输入参数: indexType dtc下标
输出参数: 无
函数返回类型值: bool ,true:有故障 false:无故障
编写者: lfc
编写日期 :2024/12/13
*************************************************/
bool GetDtcFaultStatus(DtcIndexType indexType)
{
    if (indexType >= DTC_MAX_INDEX)
    {
        return DTC_STATUS_FAULT;
    }
    return DTC_STATUS_FAULT == (g_dtcInfo.dtc[indexType].status & 0x01);
}
/*************************************************
函数名称: GetDtcStatus
函数功能: 获取 指定DTC状态
输入参数: indexType dtc下标
输出参数: 无
函数返回类型值: DtcStatus
编写者: lfc
编写日期 :2024/12/13
*************************************************/
uint8_t GetDtcStatus(DtcIndexType indexType)
{
    if (indexType >= DTC_MAX_INDEX)
    {
        return DTC_STATUS_FAULT;
    }
    return g_dtcInfo.dtc[indexType].status;
}
/*************************************************
函数名称: DcodeAndSetDynamicDidSetMcuDynamicDid
函数功能: 设置MCU部分的动态DID，需要1秒调用一次
输入参数: 无
输出参数:
函数返回类型值:
编写者: lfc
编写日期 :2024/11/28
*************************************************/
void SetMcuDynamicDid(void)
{
    TboxSystemTimeStruct *pTimeStruct = TboxSystemTimeInfoRead();

    g_dynamicDid.ecuBatteryVoltage = ReadBpPlusValue() / 100;

    g_dynamicDid.dateTime[0]  =  pTimeStruct->currentTime.second;
    g_dynamicDid.dateTime[1]  =  pTimeStruct->currentTime.minute;
    g_dynamicDid.dateTime[2]  =  pTimeStruct->currentTime.hour;
    g_dynamicDid.dateTime[3]  =  pTimeStruct->currentTime.day;
    g_dynamicDid.dateTime[4]  =  pTimeStruct->currentTime.month;
    g_dynamicDid.dateTime[5]  =  pTimeStruct->currentTime.year - 10;
}

/*************************************************
函数名称: DcodeAndSetDynamicDid
函数功能: 接收Arm 传递来的部分动态did信息，
输入参数: 无
输出参数:
函数返回类型值:
编写者: lfc
编写日期 :2024/11/28
*************************************************/
void DcodeAndSetDynamicDid(uint8_t *data)
{
    if (!data)
    {
        return; // 空指针检查
    }

    uint8_t *p = data; // 指向数据的指针

    g_dynamicDid.vehicleSpeed = (uint16_t)((p[0] << 8) | p[1]); // 大端解码
    p += 2;

    g_dynamicDid.odometer = (uint32_t)((p[0] << 24) | (p[1] << 16) | (p[2] << 8) | p[3]); // 大端解码
    p += 4;

    g_dynamicDid.lowPowerMode = *p++;

    g_dynamicDid.vehiclePowerMode = *p++;
    g_dynamicDid.gearPosition = *p++;
    g_dynamicDid.cellularSignalStrength = *p++;
    g_dynamicDid.gnssSignalStrength = *p++;
    g_dynamicDid.wifiSignalStrength = *p++;
    g_dynamicDid.link1TcpStatus = *p++;
    g_dynamicDid.link2TcpStatus = *p++;
    g_dynamicDid.link3TcpStatus = *p++;
    g_dynamicDid.powerManagementMode = *p++;
    g_dynamicDid.mileageClearCount = *p++;
    g_dynamicDid.tboxSaveMileageValues = (uint32_t)((p[0] << 24) | (p[1] << 16) | (p[2] << 8) | p[3]); // 大端解码
}

int DecodeAndSetCarDid(CarInfo *info, uint8_t *buf)
{
    if (!buf)
    {
        return MCU_ERROR;
    }

    uint8_t *p = buf;

    p += TBOX_ID_LEN;

    p += TBOX_SPARE_PARTNUMBER_LEN;

    memcpy(info->vinCode, p, TBOX_VIN_LEN);
    p += TBOX_VIN_LEN;

    p += TBOX_SOFTWARE_UPDATE_DATE_LEN;

    memcpy(info->vehicleOfflineConfig, p, VEHICLE_OFFLINE_CONFIG_LEN);
    p += VEHICLE_OFFLINE_CONFIG_LEN;

    p += TUID_LEN;

//    info->tboxAuthStatus = *p++;
    p ++;

    p += TBOX_SIM_NUMBER_LEN;
    p += TBOX_ICCID_LEN;
    p += TBOX_IMEI_LEN;

    info->wakeupTimeInterval = ((uint32_t)p[0] << 24) |
                               ((uint32_t)p[1] << 16) |
                               ((uint32_t)p[2] << 8) |
                               (uint32_t)p[3];
    p += 4;

    info->AutoRechargeUnderVoltage = ((uint32_t)p[0] << 24) |
                                     ((uint32_t)p[1] << 16) |
                                     ((uint32_t)p[2] << 8) |
                                     (uint32_t)p[3];
    p += 4;

    info->link1SSLEnable = *p++;
    info->link2SSLEnable = *p++;
    info->OfflineCheckFlag = *p++;

    info->acTimeoutMinutes = ((uint32_t)p[0] << 24) |
                             ((uint32_t)p[1] << 16) |
                             ((uint32_t)p[2] << 8) |
                             (uint32_t)p[3];
    p += 4;

    info->timedWakeupEnable = *p++;
    info->networkWakeupEnable = *p++;
    info->globalLogEnable = *p++;
    info->link1Enable = *p++;
    info->link2Enable = *p++;
    info->link3Enable = *p++;
    info->thirdPartyLinkEnable = *p++;

    p += CERT_UPDATE_TIME_LEN;

    info->dataResendTestTime = ((uint16_t)p[0] << 8) |
                               (uint16_t)p[1];
    p += 2;

    info->level3AlarmTestTime = ((uint16_t)p[0] << 8) |
                                (uint16_t)p[1];
    p += 2;

    // info->vehicleRestrictFlag = *p++;
    info->lockCarStatus = *p++;
    info->speedLimitStatus = *p++;
    info->speedLimitValue = *p++;

    return MCU_OK;
}



size_t EncodeCarDid(CarInfo *info, uint8_t *buf)
{
    if (!buf || !info)
    {
        return 0;
    }
    uint8_t *p = buf;

    memcpy(p, info->tboxId, TBOX_ID_LEN);
    p += TBOX_ID_LEN;

    memcpy(p, info->tboxSparePartNumber, TBOX_SPARE_PARTNUMBER_LEN);
    p += TBOX_SPARE_PARTNUMBER_LEN;

    memcpy(p, info->vinCode, TBOX_VIN_LEN);
    p += TBOX_VIN_LEN;

    memcpy(p, info->softUpdateDate, TBOX_SOFTWARE_UPDATE_DATE_LEN);
    p += TBOX_SOFTWARE_UPDATE_DATE_LEN;

    memcpy(p, info->vehicleOfflineConfig, VEHICLE_OFFLINE_CONFIG_LEN);
    p += VEHICLE_OFFLINE_CONFIG_LEN;

    memcpy(p, info->tuid, TUID_LEN);
    p += TUID_LEN;

    *p++ = info->tboxAuthStatus;

    memcpy(p, info->simNumber, TBOX_SIM_NUMBER_LEN);
    p += TBOX_SIM_NUMBER_LEN;

    memcpy(p, info->iccid, TBOX_ICCID_LEN);
    p += TBOX_ICCID_LEN;

    memcpy(p, info->imei, TBOX_IMEI_LEN);
    p += TBOX_IMEI_LEN;

    *p++ = (uint8_t)(info->wakeupTimeInterval >> 24);
    *p++ = (uint8_t)(info->wakeupTimeInterval >> 16);
    *p++ = (uint8_t)(info->wakeupTimeInterval >> 8);
    *p++ = (uint8_t)(info->wakeupTimeInterval);

    *p++ = (uint8_t)(info->AutoRechargeUnderVoltage >> 24);
    *p++ = (uint8_t)(info->AutoRechargeUnderVoltage >> 16);
    *p++ = (uint8_t)(info->AutoRechargeUnderVoltage >> 8);
    *p++ = (uint8_t)(info->AutoRechargeUnderVoltage);

    *p++ = info->link1SSLEnable;
    *p++ = info->link2SSLEnable;
    *p++ = info->OfflineCheckFlag;

    *p++ = (uint8_t)(info->acTimeoutMinutes >> 24);
    *p++ = (uint8_t)(info->acTimeoutMinutes >> 16);
    *p++ = (uint8_t)(info->acTimeoutMinutes >> 8);
    *p++ = (uint8_t)(info->acTimeoutMinutes);

    *p++ = info->timedWakeupEnable;
    *p++ = info->networkWakeupEnable;
    *p++ = info->globalLogEnable;
    *p++ = info->link1Enable;
    *p++ = info->link2Enable;
    *p++ = info->link3Enable;
    *p++ = info->thirdPartyLinkEnable;

    memcpy(p, info->certUpdateTime, CERT_UPDATE_TIME_LEN);
    p += CERT_UPDATE_TIME_LEN;

    *p++ = (uint8_t)(info->dataResendTestTime >> 8);
    *p++ = (uint8_t)(info->dataResendTestTime);

    *p++ = (uint8_t)(info->level3AlarmTestTime >> 8);
    *p++ = (uint8_t)(info->level3AlarmTestTime);

    // *p++ = info->vehicleRestrictFlag;
    *p++ = info->lockCarStatus;
    *p++ = info->speedLimitStatus;
    *p++ = info->speedLimitValue;
    return (size_t)(p - buf);
}

int DecodeAndSetStaticDid(StaticDidInfo *info, uint8_t *buf)
{
    if (!buf || !info) {
        return MCU_ERROR;
    }

    uint8_t *p = buf;

    // 解码 tboxCertificateID
    p += TBOX_CERT_LEN;

    // 解码 link1Addr
    memcpy(info->link1Addr, p, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 解码 link1Port
    memcpy(info->link1Port, p, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 解码 link2Addr
    memcpy(info->link2Addr, p, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 解码 link2Port
    memcpy(info->link2Port, p, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 解码 link3Addr
    memcpy(info->link3Addr, p, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 解码 link3Port
    memcpy(info->link3Port, p, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 解码 link3BiAuthPort
    memcpy(info->link3BiAuthPort, p, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    return MCU_OK; // 返回解码后的字节数
}

size_t EncodeStaticDid(StaticDidInfo *info, uint8_t *buf)
{
    if (!info || !buf) {
        return 0;
    }

    uint8_t *p = buf;

    // 编码 tboxCertificateID
    memcpy(p, info->tboxCertificateID, TBOX_CERT_LEN);
    p += TBOX_CERT_LEN;

    // 编码 link1Addr
    memcpy(p, info->link1Addr, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 编码 link1Port
    memcpy(p, info->link1Port, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 编码 link2Addr
    memcpy(p, info->link2Addr, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 编码 link2Port
    memcpy(p, info->link2Port, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 编码 link3Addr
    memcpy(p, info->link3Addr, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 编码 link3Port
    memcpy(p, info->link3Port, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 编码 link3BiAuthPort
    memcpy(p, info->link3BiAuthPort, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    return (size_t)(p - buf); // 返回编码后的字节数
}

int DecodeAndSetStaticDid2(StaticDid2Info *info, uint8_t *buf)
{
    if (!buf || !info) {
        return MCU_ERROR;
    }

    uint8_t *p = buf;

    // 解码 thirdPartyLinkAddr
    memcpy(info->thirdPartyLinkAddr, p, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 解码 thirdPartyLinkPort
    memcpy(info->thirdPartyLinkPort, p, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 解码 link3Username
    memcpy(info->link3Username, p, TBOX_LINK_USERNAME_LEN);
    p += TBOX_LINK_USERNAME_LEN;

    // 解码 link3Password
    memcpy(info->link3Password, p, TBOX_LINK_PASSWORD_LEN);
    p += TBOX_LINK_PASSWORD_LEN;

    return MCU_OK; // 返回解码后的字节数
}

size_t EncodeStaticDid2(const StaticDid2Info *info, uint8_t *buf)
{
    if (!info || !buf) {
        return 0;
    }

    uint8_t *p = buf;

    // 编码 thirdPartyLinkAddr
    memcpy(p, info->thirdPartyLinkAddr, TBOX_LINK_LEN);
    p += TBOX_LINK_LEN;

    // 编码 thirdPartyLinkPort
    memcpy(p, info->thirdPartyLinkPort, TBOX_LINK_PORT_LEN);
    p += TBOX_LINK_PORT_LEN;

    // 编码 link3Username
    memcpy(p, info->link3Username, TBOX_LINK_USERNAME_LEN);
    p += TBOX_LINK_USERNAME_LEN;

    // 编码 link3Password
    memcpy(p, info->link3Password, TBOX_LINK_PASSWORD_LEN);
    p += TBOX_LINK_PASSWORD_LEN;

    return (size_t)(p - buf); // 返回编码后的字节数
}

/*************************************************
函数名称: EncodeTotalDid
函数功能: 编码全部did信息，通过IPC发送给arm
输入参数:
输出参数:
函数返回类型值:
编写者: lfc
编写日期 :2024/11/28
*************************************************/
size_t EncodeTotalDid(uint8_t *buf)
{
    if (!buf) {
        return 0;
    }
    TotalDidInfo *info = &g_totalDidInfo;
    uint8_t *p = buf;

    // 编码简单数组部分
    memcpy(p, info->tboxSoftwareId, TBOX_SOFTWARE_ID_LEN);
    p += TBOX_SOFTWARE_ID_LEN;

    memcpy(p, info->tboxHardwareId, TBOX_HARDWARE_ID_LEN);
    p += TBOX_HARDWARE_ID_LEN;

    memcpy(p, info->tboxSystemSuppierIdentifier, TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN);
    p += TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN;

    memcpy(p, info->tboxName, TBOX_NAME_LEN);
    p += TBOX_NAME_LEN;

    memcpy(p, info->tboxSoftVersion, TBOX_SOFT_VERSION_LEN);
    p += TBOX_SOFT_VERSION_LEN;

    memcpy(p, info->tboxHardVersion, TBOX_HARD_VERSION_LEN);
    p += TBOX_HARD_VERSION_LEN;

    // 编码指针部分
    if (info->carInfo)
    {
        p += EncodeCarDid(info->carInfo, p);
    }

    if (info->staticDidInfo)
    {
        p += EncodeStaticDid(info->staticDidInfo, p);
    }

    if (info->staticDid2Info)
    {
        p += EncodeStaticDid2(info->staticDid2Info, p);
    }

    return (size_t)(p - buf); // 返回已编码的字节数
}



