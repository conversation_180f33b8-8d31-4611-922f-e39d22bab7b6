/*******************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only
* intended for use with Renesas products. No other uses are authorized. This
* software is owned by Renesas Electronics Corporation and is protected under
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND RENESAS MAKES NO WARRANTIES REGARDING
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT
* LIMITED TO WAR<PERSON>NTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE
* AND NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software
* and to discontinue the availability of this software. By using this software,
* you agree to the additional terms and conditions found by accessing the
* following link:
* http://www.renesas.com/disclaimer
* (c) 2014 Renesas Electronics Corporation All rights reserved.
*******************************************************************************/


/******************************************************************************
* File Name     : r_adc.c
* Version       : 1.0
* Device(s)     : R7F701035xAFP RH850/F1L
* Tool-Chain    : CubeSuite+(V2.01.00)
* Description   : This file is a sample of the A/D converter (ADC).
* Operation     : -
*******************************************************************************
*******************************************************************************
* History       : DD.MM.YYYY Version Description
*               : 20.03.2014 1.00    First Release
******************************************************************************/

/******************************************************************************
Includes <System Includes> , �gProject Includes�h
******************************************************************************/
#include    "FreeRTOS.h"
#include    "r_typedefs.h"
#include    "iodefine.h"


/******************************************************************************
Imported global variables and functions (from other files)
******************************************************************************/


/******************************************************************************
Macro definitions
******************************************************************************/


/******************************************************************************
Exported global variables and functions (to be accessed by other files)
******************************************************************************/
        void        R_ADC_Init( void );


/******************************************************************************
Private global variables and functions
******************************************************************************/


/******************************************************************************
* Function Name : void R_ADC_Init( void )
* Description   : This function initialize ADCA0.
* Argument      : none
* Return Value  : none
******************************************************************************/
void R_ADC_Init( void )
{
    /* Set interrupt flags */
    MKADCA0I0              = 1U;   //ADCA0 scan group 1 (SG1) end interrupt
    RFADCA0I0              = 0U;   //ADCA0 scan group 1 (SG1) end interrupt
    TBADCA0I0              = 0U;   //ADCA0 scan group 1 (SG1) end interrupt

    MKADCA0I1              = 1U;   //ADCA0 scan group 2 (SG2) end interrupt
    RFADCA0I1              = 0U;   //ADCA0 scan group 2 (SG2) end interrupt
    TBADCA0I1              = 0U;   //ADCA0 scan group 2 (SG2) end interrupt

    MKADCA0I2              = 1U;   //ADCA0 scan group 3 (SG3) end interrupt
    RFADCA0I2              = 0U;   //ADCA0 scan group 3 (SG3) end interrupt
    TBADCA0I2              = 0U;   //ADCA0 scan group 3 (SG3) end interrupt

    MKADCA0ERR             = 1U;   //ADCA0 error interrupt
    RFADCA0ERR             = 0U;   //ADCA0 error interrupt
    TBADCA0ERR             = 0U;   //ADCA0 error interrupt


    /* Control the virtual channel
    ADCAnVCRj  - Virtual Channel Register j
    b31:b15                - Reserved set to 0
    b15          MPXE      - MPX Enable                                     - The use of MPX is prohibited.
    b14:b12      MPXV      - These bits are used to set the MPX value to be
                             transferred to an external analog multiplexer. - Unused. Set to 000'b. 
    b11:b10                - Reserved set to 0
    b 9          CNVCLS    - A/D Conversion Type Select for Self-Diagnosis  - Unused. Set to 0'b.
    b 8          ADIE      - A/D Conversion End Interrupt Enable            - A scan group x end interrupt (NT_SGx)
                                                                              is not generated when A/D conversion
                                                                              for virtual channel j ends in SGx.
                                                                              Set to 0'b.
    b 7:b 6      ULS       - Upper Limit/Lower Limit Table Select           - Upper limit and lower limit are checked
                                                                              for ADCAnULLMTBR0.Unused. Set to 01'b.
    b 5:b 0      GCTRL     - Physical Channel Select                        - Select ANI000 - ANI007 */
    ADCA0.VCR00.UINT32     = 0x00000040UL;                                   /* �Ա� ADCAnULLMTBR0  */
    ADCA0.VCR01.UINT32     = 0x00000041UL;
    ADCA0.VCR02.UINT32     = 0x00000042UL;
    ADCA0.VCR03.UINT32     = 0x00000043UL;
    ADCA0.VCR04.UINT32     = 0x00000044UL;
    ADCA0.VCR05.UINT32     = 0x00000045UL;
    ADCA0.VCR06.UINT32     = 0x00000046UL;
    ADCA0.VCR07.UINT32     = 0x00000047UL;


    /* Common control the ADCAn
    ADCAnADCR  - A/D Control Register
    b31:b 8                - Reserved set to 0
    b 7          DGON      - Self-Diagnostic Voltage Standby Control        - The self-diagnostic voltage circuit is
                                                                              turned off. Set to 0'b.
    b 6                    - Reserved set to 0
    b 5          CRAC      - Alignment Control                              - The results of conversion to WDDR and
                                                                              ADCAnDRj are stored rightaligned.
                                                                              Set to 0'b.
    b 4          CTYP      - 12/10 Bit Select Mode                          - 12-bit mode. Set to 0'b.
    b 3:b 2                - Reserved set to 0
    b 1:b 0      SUSMTD    - Suspend Mode Select                            - Synchronous suspend when a higher-priority
                                                                              SG or SVSTOP interrupt. Set to 0'b. */
    ADCA0.ADCR.UINT32      = 0x00000000UL;                                   /* ����12λ */


    /* Controls the sampling time for SG4 (PWM-Diag) and SG1 to SG3.
    ADCAnSMPCR - Sampling Control Register
    b31:b 8                - Reserved set to 0
    b 7:b 0      SMPT      - These bits are used to set the sampling time
                             (the number of cycles).                        - 18 cycles (ADCACLK = 8 MHz to 32 MHz) */
    ADCA0.SMPCR.UINT32     = 0x00000012UL;                                  // 18???????


    /* Set safety control.
    ADCAnSFTCR - Safety Control Register
    b31:b 5                - Reserved set to 0
    b 4          RDCLRE    - Read & Clear Enable                            - ADCAnPWDDR/ADCAnDRj and ADCAnPWDDIR /
                                                                              ADCAnDIRj are not cleared when 
                                                                              ADCAnPWDDR/ADCAnDRj or ADCAnPWDDIR / 
                                                                              ADCAnDIRj is read. Set to 0'b.
    b 3          ULEIE     - A/D Error Interrupt (INT_ADE) Enable on
                             Upper/Lower Limit Error Detection              - Enabled. Set to 1'b.
    b 2          OWEIE     - A/D Error Interrupt (INT_ADE) Enable on
                             Overwrite Error Detection                      - Enabled. Set to 1'b.
    b 1:b 0                - Reserved set to 0  */
    ADCA0.SFTCR.UINT32     = 0x0000000CUL;                                 


    /* Set the upper limit / lower limit error detection threshold.
    ADCAnULLMTBR0 - Upper Limit/Lower Limit Table Registers 0
    b31:b20      ULMTB     - Upper Limit Table                              - Upper limit = 0FFFH
    b19:b16                - Reserved set to 0
    b15:b 4      LLMTB     - Lower Limit Table                              - Lower limit = 0800H
    b 3:b 0                - Reserved set to 0 */
    ADCA0.ULLMTBR0.UINT32  = 0xFFF08000UL;                                 


    /* Set A/D conversion trigger (hardware trigger) of SGx
    ADCAnSGTSELx - Scan Group x Start Trigger Control Register x
    b31:b 9                - Reserved set to 0
    b 8          LLMTB     - T1SEL8                                         - Unused. Set to 0'b.
    b 7          LLMTB     - T1SEL7                                         - Unused. Set to 0'b.
    b 6          LLMTB     - T1SEL6                                         - Unused. Set to 0'b.
    b 5          LLMTB     - T1SEL5                                         - Unused. Set to 0'b.
    b 4          LLMTB     - T1SEL4                                         - Unused. Set to 0'b.
    b 3          LLMTB     - T1SEL3                                         - Unused. Set to 0'b.
    b 2          LLMTB     - T1SEL2                                         - Unused. Set to 0'b.
    b 1          LLMTB     - T1SEL1                                         - Unused. Set to 0'b. Use INTTAUJ0I3. Set to 1'b.
    b 0          LLMTB     - T1SEL0                                         - Unused. Set to 0'b. */
    ADCA0.SGTSEL1.UINT32   = 0x00000002UL;                                 
    //ADCA0.SGTSEL2.UINT32   = 0x00000002UL;                                 
    //ADCA0.SGTSEL3.UINT32   = 0x00000002UL;                                  


    /* Set A/D conversion trigger (hardware trigger) of SGx
    ADCAnSGCRx - Scan Group x Control Register
    b31:b 6                - Reserved set to 0
    b 5          SCANMD    - Scan Mode                                      - Multicycle scan mode
    b 4          ADIE      - Scan End Interrupt Enable                      - INT_SGx is output when the scan for
                                                                              SGx ends. Set to 1'b.
    b 3:b 2      SCT       - Channel Repeat Times Select                    - The selected number of channel repeat
                                                                              times is one. Set to 00'b.
    b 1                    - Reserved set to 0
    b 0          TRGMD     - Trigger Mode                                   - SGx_TRG start trigger or hold complete
                                                                              trigger A/B is selected for the trigger
                                                                              input to SGx. Set to 1'b. */
    ADCA0.SGCR1.UINT32     = 0x00000011UL;                                   
    //ADCA0.SGCR2.UINT32     = 0x00000011UL;
    //ADCA0.SGCR3.UINT32     = 0x00000011UL;


    /* Set the start pointer of the virtual channel
    ADCAnSGVCSPx - Scan Group x Start Virtual Channel Pointer
    b31:b 6                - Reserved set to 0
    b 5:b 0      VCSP      - Start Virtual Channel Pointer                  - set ADCA0VCR00      */
    ADCA0.SGVCSP1.UINT32   = 0x00000000UL;              //SG1
    /* Set the end pointer of the virtual channel
    ADCAnSGVCEPx - Scan Group x End Virtual Channel Pointer
    b31:b 6                - Reserved set to 0
    b 5:b 0      VCEP      - End Virtual Channel Pointer                    - set ADCA0VCR02      */
    ADCA0.SGVCEP1.UINT32   = 0x00000007UL;              //SG1
    /* Set the scan number of times in the scan mode for a multi-cycle
    ADCAnSGMCYCRx - Scan Group x Multicycle Register
    b31:b 2                - Reserved set to 0
    b 1:b 0      MCYC      - Multicycle Number Specification                - Number of scans = 1 */
    ADCA0.SGMCYCR1.UINT32  = 0x00000000UL;             //SG1????

#if 0
    /* Set the start pointer of the virtual channel
    ADCAnSGVCSPx - Scan Group x Start Virtual Channel Pointer
    b31:b 6                - Reserved set to 0
    b 5:b 0      VCSP      - Start Virtual Channel Pointer                  - set ADCA0VCR03      */
    ADCA0.SGVCSP2.UINT32   = 0x00000003UL;           //SG2???????
    /* Set the end pointer of the virtual channel
    ADCAnSGVCEPx - Scan Group x End Virtual Channel Pointer
    b31:b 6                - Reserved set to 0
    b 5:b 0      VCEP      - End Virtual Channel Pointer                    - set ADCA0VCR04      */
    ADCA0.SGVCEP2.UINT32   = 0x00000004UL;           //SG2???????
    /* Set the scan number of times in the scan mode for a multi-cycle
    ADCAnSGMCYCRx - Scan Group x Multicycle Register
    b31:b 2                - Reserved set to 0
    b 1:b 0      MCYC      - Multicycle Number Specification                - Number of scans = 1 */
    ADCA0.SGMCYCR2.UINT32  = 0x00000000UL;           //SG2????


    /* Set the start pointer of the virtual channel
    ADCAnSGVCSPx - Scan Group x Start Virtual Channel Pointer
    b31:b 6                - Reserved set to 0
    b 5:b 0      VCSP      - Start Virtual Channel Pointer                  - set ADCA0VCR05      */
    ADCA0.SGVCSP3.UINT32   = 0x00000005UL;          //SG3???????
    /* Set the end pointer of the virtual channel
    ADCAnSGVCEPx - Scan Group x End Virtual Channel Pointer
    b31:b 6                - Reserved set to 0
    b 5:b 0      VCEP      - End Virtual Channel Pointer                    - set ADCA0VCR07      */
    ADCA0.SGVCEP3.UINT32   = 0x00000007UL;          //SG3???????
    /* Set the scan number of times in the scan mode for a multi-cycle
    ADCAnSGMCYCRx - Scan Group x Multicycle Register
    b31:b 2                - Reserved set to 0
    b 1:b 0      MCYC      - Multicycle Number Specification                - Number of scans = 1 */
    ADCA0.SGMCYCR3.UINT32  = 0x00000000UL;         //SG3???????
#endif

    MKADCA0I0              = 0U;                             /* Enable INTADCA0I0 interrupt       */
   // MKADCA0I1              = 0U;                             /* Enable INTADCA0I1 interrupt       */
   // MKADCA0I2              = 0U;                             /* Enable INTADCA0I2 interrupt       */
    MKADCA0ERR             = 0U;                             /* Enable INTADCA0ERR interrupt      */
    
}


void Adc0Init(void)
{

    MKADCA0I0              = 1U;
    RFADCA0I0              = 0U;
    TBADCA0I0              = 1U;

    MKADCA0ERR             = 1U;
    RFADCA0ERR             = 0U;
    TBADCA0ERR             = 1U;


    ADCA0.VCR00.UINT32     = 0x00000040UL;

    ADCA0.ADCR.UINT32      = 0x00000000UL;

    ADCA0.SMPCR.UINT32     = 0x00000012UL;

    ADCA0.SFTCR.UINT32     = 0x00000008UL;

    ADCA0.ULLMTBR0.UINT32  = 0xBFF08000UL; 

    ADCA0.SGTSEL1.UINT32   = 0x00000010UL;

    ADCA0.SGCR1.UINT32     = 0x00000011UL;

    ADCA0.SGVCSP1.UINT32   = 0x00000000UL;

    ADCA0.SGVCEP1.UINT32   = 0x00000000UL;

    ADCA0.SGMCYCR1.UINT32  = 0x00000000UL;

    MKADCA0ERR             = 0U; 

}

