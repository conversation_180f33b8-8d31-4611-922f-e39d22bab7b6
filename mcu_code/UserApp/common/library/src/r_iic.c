/*
      R_iic.c
描述：此文件包括IIC初始化，I2C发送， I2C接收，以及中断回调函数
作者：廖勇刚
时间：2016.9.28
*/

#include    "FreeRTOS.h"
#include    "r_typedefs.h"
#include    "iodefine.h"
#include    "r_iic.h"
//#include    "task.h"




/************************全局变量****************************/
I2cRam   g_i2cRam;




/*************************************************
函数名称: McuI2cInit
函数功能: I2C初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/08
*************************************************/
void McuI2cInit(void)
{
    UINT32   value;
    return; //TODO，驱动待对接 暂时屏蔽，下同
    //屏蔽I2C 传输空中断
    MKRIIC0TI           = 1U;
    RFRIIC0TI           = 0U;
    TBRIIC0TI           = 0U;

    //屏蔽I2C 传输结束中断
    MKRIIC0TEI          = 1U;
    RFRIIC0TEI          = 0U;
    TBRIIC0TEI          = 0U;

    //屏蔽I2C 接收完成中断
    MKRIIC0RI           = 1U;
    RFRIIC0RI           = 0U;
    TBRIIC0RI           = 0U;

    //屏蔽I2C 通讯错误中断
    MKRIIC0EE           = 1U;
    RFRIIC0EE           = 0U;
    TBRIIC0EE           = 0U;

    //不使能I2C 接口
    RIIC0.CR1.UINT32    &= 0xFFFFFF7FUL;

    //初始化I2C复位或内部复位
    RIIC0.CR1.UINT32    |= 0x00000040UL;

    //使能I2C接口
    RIIC0.CR1.UINT32    |= 0x00000080UL;

    //内部MR1分频为PCLK/16
    RIIC0.MR1.UINT32    |= 0x00000000UL;  

    //配置I2C 波特率为10K
    RIIC0.BRH.UINT32    = 0xF6U; 
    RIIC0.BRL.UINT32    = 0xF9U;

    //内部MR2 使能低和高电平计数
    RIIC0.MR2.UINT32    = 0x00000006UL;

    //设置ACKBT位为1
    RIIC0.MR3.UINT32    = 0x00000010UL;

    //NACK接收使能为1
    RIIC0.FER.UINT32    = 0x00000010UL;
    
    //传输数据空 传输结束 接收完成 停止位检测中断使能
    RIIC0.IER.UINT32    = 0x000000E8UL;
    
    //配置为开漏输出
    do
    {
        value  = PODC0 | 0x00001800UL;
        PPCMD0          = 0xA5U;
        PODC0          = value;
        PODC0          = (UINT32)(~value);
        PODC0          = value;
    } while ( PPROTS0 != 0x00000000UL);

    //双向模式启用
    PBDC0              |= 0x00001800UL;

    //配置为清除I2C复位或内部复位
    RIIC0.CR1.UINT32    &= 0xFFFFFFBFUL;

    //使能I2C 传输空中断
    MKRIIC0TI           = 0U;

    //使能I2C 传输结束中断
    MKRIIC0TEI          = 0U;

    //使能I2C 接收完成中断
    MKRIIC0RI           = 0U;

    //使能通讯错误中断
    MKRIIC0EE           = 0U;
    
}

/*************************************************
函数名称: McuI2cMasterSend
函数功能: I2C主发送函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/08
*************************************************/
void McuI2cMasterSend(UINT8 slaveAddr, UINT32 len, UINT8 *buf)
{
    //Richard: to do: change to the new code 
    /*
    UINT32 count = 0;
    return;
    //等待I2C忙状态
    do
    {
        count++;
    }while ((RIIC0.CR2.UINT32 & 0x00000080UL) && (0x20000 >= count));

    if(0x20000 == count)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "iic send 1 time out");
        return;
    }

    g_i2cRam.slaveAddr = slaveAddr;
    g_i2cRam.state = ADDR_W_STATE;
    g_i2cRam.txBuf = buf;
    g_i2cRam.txLen = len;
    g_i2cRam.txCount = 0;

    //请求发送开始位
    RIIC0.CR2.UINT32    |= 0x00000002UL;

    do
    {
        count++;
    }while ((g_i2cRam.state != IDLE_STATE) && (0x20000 >= count));

    if(0x20000 == count)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "iic send time out");
    }
     */
}

/*************************************************
函数名称: McuI2cMasterSend
函数功能: I2C主发送函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/08
*************************************************/
void McuI2cMasterReceive(UINT32 len, UINT8 *buf)
{ 
    UINT32 count = 0;
    return;
    if(RTC_SLAVE_ADDR == g_i2cRam.slaveAddr)
    {
        //等待I2C忙状态
        do
        {
            count++;
        }while ((RIIC0.CR2.UINT32 & 0x00000080UL) && (0x20000 >= count));

        if(0x20000 == count)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "iic rx 1 time out");
            return;
        }
    }
    
    g_i2cRam.state = ADDR_R_STATE;
    g_i2cRam.rxBuf = buf;
    g_i2cRam.rxLen = len;
    g_i2cRam.rxCount = 0;

    if(RTC_SLAVE_ADDR == g_i2cRam.slaveAddr)
    {
        //请求发送开始位
        RIIC0.CR2.UINT32    |= 0x00000002UL;
    }
    else
    {
        //请求发送重新开始位
        RIIC0.CR2.UINT32    |= 0x00000004UL;
    }
}

/*************************************************
函数名称: McuI2cTransEmptyIsr
函数功能: I2C发送数据缓存数据空中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void McuI2cTransEmptyIsr(void)
{
    return;
    if (ADDR_W_STATE == g_i2cRam.state)
    {
        RIIC0.DRT.UINT32    = (UINT32)g_i2cRam.slaveAddr + IIC_DIR_W;
        g_i2cRam.state      = DATA_SEND_STATE;
    }
    else if (ADDR_R_STATE == g_i2cRam.state)
    {
        RIIC0.DRT.UINT32    = (uint32_t)g_i2cRam.slaveAddr + IIC_DIR_R;      
        g_i2cRam.state      = DATA_RECV_STATE;
    }
    else
    {
        /* NULL */
    }
}

/*************************************************
函数名称: McuI2cTransEndIsr
函数功能: I2C发送数据缓存数据结束中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void McuI2cTransEndIsr(void)
{
    return;
    if ( DATA_SEND_STATE == g_i2cRam.state)
    {
        if ((RIIC0.SR2.UINT32 & 0x00000010UL) || ( g_i2cRam.txCount == g_i2cRam.txLen) )
        {

            //清除传输完成标志
            RIIC0.SR2.UINT32    &= 0xFFFFFFBFUL;

            //清除停止位检测标志
            RIIC0.SR2.UINT32    &= 0xFFFFFFF7UL;

            if(RTC_SLAVE_ADDR == g_i2cRam.slaveAddr)
            {
                //请求发送停止位
                RIIC0.CR2.UINT32    |= 0x00000008UL;
            }
            
            g_i2cRam.state = DATA_SEND_END_STATE;
        }
        else
        {
            RIIC0.DRT.UINT32    = *g_i2cRam.txBuf++;                      
            g_i2cRam.txCount++;
        }
    }
}

/*************************************************
函数名称: McuI2cRecvEndIsr
函数功能: I2C接收数据结束中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void McuI2cRecvEndIsr(void)
{
    UINT32  temp = 0x00;
    return;
    temp = temp;

    if (DATA_RECV_STATE == g_i2cRam.state)
    {
        if ( RIIC0.SR2.UINT32 & 0x00000010UL)                              
        {
            //清除停止位检测标志
            RIIC0.SR2.UINT32    &= 0xFFFFFFF7UL;

            //请求发送一个停止位
            RIIC0.CR2.UINT32    |= 0x00000008UL;
 
            temp  = RIIC0.DRR.UINT32;
    
            if (2UL <= g_i2cRam.rxLen)
            {
                g_i2cRam.state = DATA_RECV_END_STATE_2;
            }
            else
            {
                g_i2cRam.state = DATA_RECV_END_STATE;
            }
        }
        else
        {
            if (2UL >= g_i2cRam.rxLen)
            {
                //设置等待WAIT
                RIIC0.MR3.UINT32    |= 0x00000040UL;
                if (2U == g_i2cRam.rxLen)
                {
                    temp  = RIIC0.DRR.UINT32;
                    g_i2cRam.state = DATA_RECV_STATE_2;
                }
                else
                {
                    //NACK 写使能
                    RIIC0.MR3.UINT32    |= 0x00000010UL;

                    //配置第8个上升边沿RDRF标志，ACK应答位
                    RIIC0.MR3.UINT32    |= 0x00000028UL;
 
                    temp  = RIIC0.DRR.UINT32;
                    g_i2cRam.state = DATA_RECV_STATE_3;
                }
            }
            else
            {
                temp  = RIIC0.DRR.UINT32;
                g_i2cRam.state = DATA_RECV_STATE_4;
            }
        }
    }
    else if (DATA_RECV_STATE_2 == g_i2cRam.state)
    {
        //配置NACK写使能
        RIIC0.MR3.UINT32    |= 0x00000010UL;

        //配置第8个上升边沿RDRF标志，ACK应答位
        RIIC0.MR3.UINT32    |= 0x00000028UL;
     
        *g_i2cRam.rxBuf++ = RIIC0.DRR.UINT32;
        g_i2cRam.rxCount++;
        g_i2cRam.state    = DATA_RECV_STATE_3;
    }
    else if (DATA_RECV_STATE_3 == g_i2cRam.state )
    {    
        //清除停止位检测标志
        RIIC0.SR2.UINT32    &= 0xFFFFFFF7UL;

        //请求发送停止位
        RIIC0.CR2.UINT32    |= 0x00000008UL;
 
        *g_i2cRam.rxBuf++ = RIIC0.DRR.UINT32;                          
        g_i2cRam.rxCount++;
    
        //NACK 写使能
        RIIC0.MR3.UINT32    |= 0x00000010UL;                              
      
        //写ACKBT使能应答
        RIIC0.MR3.UINT32    |= 0x00000008UL;
      
        //释放WAIT
        RIIC0.MR3.UINT32    &= 0xFFFFFFBFUL;
        g_i2cRam.state   = DATA_RECV_END_STATE_2;
     }
     else if (DATA_RECV_STATE_4 == g_i2cRam.state)
     {
         if ( g_i2cRam.rxLen == (g_i2cRam.rxCount + 1UL))
         {

             //清除停止位检测标志
             RIIC0.SR2.UINT32    &= 0xFFFFFFF7UL;
    
             //请求发送停止位
             RIIC0.CR2.UINT32    |= 0x00000008UL;
    
             *g_i2cRam.rxBuf++ = RIIC0.DRR.UINT32; 
             g_i2cRam.rxCount++;
  
             //等待WAIT释放
             RIIC0.MR3.UINT32    &= 0xFFFFFFBFUL;
  
             g_i2cRam.state   = DATA_RECV_END_STATE;
         }
         else if (g_i2cRam.rxLen== (g_i2cRam.rxCount+ 2UL))
         {
             //NACK 写使能
             RIIC0.MR3.UINT32    |= 0x00000010UL;
    
             //配置ACKWP为1
             RIIC0.MR3.UINT32    |= 0x00000008UL;
  
             *g_i2cRam.rxBuf++ = RIIC0.DRR.UINT32;
             g_i2cRam.rxCount++;
         }
         else if ( g_i2cRam.rxLen == (g_i2cRam.rxCount + 3UL) )
         {
             //设置WAIT为1
             RIIC0.MR3.UINT32    |= 0x00000040UL;
      
             *g_i2cRam.rxBuf++ = RIIC0.DRR.UINT32;
             g_i2cRam.rxCount++;
         } 
         else 
         {
             *g_i2cRam.rxBuf++ = RIIC0.DRR.UINT32;
             g_i2cRam.rxCount++;
         }
     }
}

/*************************************************
函数名称: McuI2cCommunErrIsr
函数功能: I2C接收通讯错误中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void McuI2cCommunErrIsr(void)
{
    UINT32 status = 0x00;
    return;
    status = status;

    //SystemApiLogPrintf(LOG_ERROR_OUTPUT, "I2c Commun Err Isr\r\n");
    if(RIIC0.SR2.UINT32 & 0x00000008UL)                                
    {
        if (g_i2cRam.state == DATA_RECV_END_STATE_2)
        {
            //设置ACKWP为1
            RIIC0.MR3.UINT32    |= 0x00000010UL;

            //设置RDRFS为0，设置ACKBT为0
            RIIC0.MR3.UINT32    &= 0xFFFFFFD7UL;
        }

        status = RIIC0.SR2.UINT32;
        
        //清除RIICnNACKF标志
        RIIC0.SR2.UINT32    &= 0xFFFFFFEFUL;

        //清除RIICnSTOP为0
        RIIC0.SR2.UINT32    &= 0xFFFFFFF7UL;

        g_i2cRam.state      = IDLE_STATE;
    }
}
