/*
      R_wdt.c
描述：此文件包括看门狗初始化函数、看门狗喂狗接口
作者：廖勇刚
时间：2016.9.29
*/
#include    "r_wdt.h"





/*************************************************
函数名称: McuClearWatchdog
函数功能: 清除看门狗
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/29
*************************************************/
void McuClearWatchdog(void)
{
     //配置看门狗计数值
     //WDTA0.EVAC = ( 0xAC - WDTA0.REF ); 
}

/*************************************************
函数名称: McuWatchdogInit
函数功能: 初始化看门狗配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/29
*************************************************/
void McuWatchdogInit(void)
{
    /*
    //配置WDT时钟
    do
    {
        PROTCMD0                  = 0x000000A5UL;
        CKSC_AWDTAD_CTL           = 0x00000001UL;
        CKSC_AWDTAD_CTL           = ~0x00000001UL;
        CKSC_AWDTAD_CTL           = 0x00000001UL;
    } while (PROTS0 != 0x00000000UL);
    while ( CKSC_AWDTAD_ACT != 0x00000001UL )
    {

    }

    //配置2*13 / WDTATCKI  复位模式  开启周期100
    WDTA0.MD                      = 0x47U;    
    McuClearWatchdog();
    */
}


