/*
      pwm.c
描述：此文件包括Pwm配置，PWM开始函数，PWM设置函数，PWM停止函数
作者：廖勇刚
时间：2016.9.13
*/
#include "pwm.h"
#include "gpio.h"
#include "event.h"
#include "Platform_Types.h"

extern GpioInfo g_gpioPowerOnInfoList[];

/*************************************************
函数名称: PwmInit
函数功能: pwm初始化配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/26
*************************************************/
void PwmInit(void)
{
    /*
    //设置PWMCLK0 = PCLK/(2*(PWBA0BRS0[10:0]))
    PWBA0.BRS0 = 50;

    //开始PWMCLK0触发
    PWBA0.TS = 0x01;

    //初始化通道选择时钟源
    PWGA0.CTL = 0x00;   
    PWGA8.CTL = 0x00;
    PWGA9.CTL = 0x00;
    PWGA20.CTL = 0x00;
    PWGA21.CTL = 0x00;
    PWGA32.CTL = 0x00;
    PWGA33.CTL = 0x00;
    PWGA34.CTL = 0x00;
    PWGA35.CTL = 0x00;

    //初始化通道第一次延时计数
    PWGA0.CSDR = 0x00;
    PWGA8.CSDR = 0x00;
    PWGA9.CSDR = 0x00;
    PWGA20.CSDR = 0x00;
    PWGA21.CSDR = 0x00;
    PWGA32.CSDR = 0x00;
    PWGA33.CSDR = 0x00;
    PWGA34.CSDR = 0x00;
    PWGA35.CSDR = 0x00;
    */
}

/*************************************************
函数名称: PwmChannelInit
函数功能: pwm通道初始化配置
输入参数: 通道号  占空比
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/26
*************************************************/
void PwmChannelInit(UINT8 channel, UINT8 duty)
{
    //  float pwmDuty = 0;
     
    //  pwmDuty = PWM_DUTY_CYCLE*duty;
     
     switch(channel)
     {
         case PWM_LED_CHANNEL_RED_POWER:
         {
            // PWGA8.CRDR = pwmDuty; 
             //PWGA8.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_GREEN_POWER:
         {
            // PWGA9.CRDR = pwmDuty; 
            // PWGA9.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_RED_4G:
         {
            // PWGA20.CRDR = pwmDuty; 
             //PWGA20.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_GREEN_4G:
         {
             //PWGA21.CRDR = pwmDuty; 
             //PWGA21.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_RED_GNSS:
         {
            // PWGA33.CRDR = pwmDuty; 
            // PWGA33.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_GREEN_GNSS:
         {
            // PWGA34.CRDR = pwmDuty; 
            // PWGA34.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_9:
         {
            // PWGA32.CRDR = pwmDuty; 
            // PWGA32.RDT = 1;
             break;
         }
         case PWM_LED_CHANNEL_GPS_PHY:
         {
            // PWGA35.CRDR = pwmDuty; 
            // PWGA35.RDT = 1;
             break;
         }
         default:
         {
             break;
         }
     }
}

/*************************************************
函数名称: PwmChannelStart
函数功能: pwm通道开始
输入参数: 通道号
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/03/17
*************************************************/
void PwmChannelStart(UINT8 channel)
{
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[channel], GPIO_OUTPUT_HIGH);
}

/*************************************************
函数名称: PwmChannelStop
函数功能: pwm通道关闭
输入参数: 通道号
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/03/17
*************************************************/
void PwmChannelStop(UINT8 channel)
{
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[channel], GPIO_OUTPUT_LOW);
}


/*************************************************
函数名称: SpeedPwmInit
函数功能: 车速PWM初始化
输入参数: uint16_t speed 车速 km/h
输出参数: 无
函数返回类型值：无
编写者: liujunjie
编写日期 :2019/03/07
*************************************************/
void SpeedPwmInit(uint16_t speed)
{
    // TODO: 修复寄存器操作 2024/3/15 14:36
    return;
    UINT8 tempRSF = 0;
    float32 regData = 0;
    if(300 < speed)
    {
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "speed %d > 300\r\n", speed); 
        return;
    }

    if(0 == speed)
    {
        regData = 0;
    }
    else
    {
        regData = 40000000/((float32)speed*2*4096*1.47);
        regData = regData + 0.5; //四舍五入
    }
    

    //tempRSF = PWGA3.RSF;
    if(0 != tempRSF)
    {
        return;
    }

    //关闭PWM
    //SLPWGA0 &= ~(1 << 3);
    //P10 &= ~(1 << 3);

    if(0 != speed)
    {
        //选择时钟
        //设置PWMCLK1 = PCLK/(2*(PWBA0BRS0[10:0]))
        //speed*4 = PCLK/(2*4096*(PWBA0BRS0[10:0]))
        //PWBA0.BRS1 = (unsigned short)regData;
        //开始PWMCLK1触发
        //PWBA0.TS = 0x02;
        //选择时钟源
        //PWGA3.CTL = 0x01;
        //配置占空比为50%
        //PWGA3.CSDR = 0;
        //PWGA3.CRDR = 2048; 
        //重写申请
        //PWGA3.RDT = 1;

        //开启PWM
        //SLPWGA0 |= (1 << 3);
        //P10 |=  (1 << 3);
    }
}
