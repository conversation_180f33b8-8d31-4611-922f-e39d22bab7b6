/*******************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only
* intended for use with Renesas products. No other uses are authorized. This
* software is owned by Renesas Electronics Corporation and is protected under
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND RENESAS MAKES NO WARRANTIES REGARDING
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT
* LIMITED TO WAR<PERSON>NTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE
* AND NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software
* and to discontinue the availability of this software. By using this software,
* you agree to the additional terms and conditions found by accessing the
* following link:
* http://www.renesas.com/disclaimer
* (c) 2014 Renesas Electronics Corporation All rights reserved.
*******************************************************************************/


/******************************************************************************
* File Name     : r_tauj.c
* Version       : 1.0
* Device(s)     : R7F701035xAFP RH850/F1L
* Tool-Chain    : CubeSuite+(V2.01.00)
* Description   : This file is a sample of the TAUJ.
* Operation     : -
*******************************************************************************
*******************************************************************************
* History       : DD.MM.YYYY Version Description
*               : 20.03.2014 1.00    First Release
******************************************************************************/

/******************************************************************************
Includes <System Includes> , �gProject Includes�h
******************************************************************************/
#include    "FreeRTOS.h"
#include    "r_typedefs.h"
#include    "iodefine.h"


/******************************************************************************
Imported global variables and functions (from other files)
******************************************************************************/


/******************************************************************************
Macro definitions
******************************************************************************/


/******************************************************************************
Exported global variables and functions (to be accessed by other files)
******************************************************************************/
        void        R_TAUJ_Init( void );
        void        R_TAUJ_Start( void );


/******************************************************************************
Private global variables and functions
******************************************************************************/



/******************************************************************************
* Function Name : void R_TAUJ_Init( void )
* Description   : This function initialize TAUJ (ch.3).
* Argument      : none
* Return Value  : none
******************************************************************************/
void R_TAUJ_Init( void )
{
    uint32_t   reg32_value;

    /* Source Clock Setting for C_AWO_TAUJ
    CKSC_ATAUJS_CTL - C_AWO_TAUJ Source Clock Selection Register
    b31:b 2                       - Reserved set to 0
    b 1:b 0           ATAUJSCSID  - Source Clock Setting for C_AWO_TAUJ  - HS IntOSC fRH (8MHz)                */
    do
    {
        reg32_value               = 0x00000001UL;
        PROTCMD0                  = 0x000000A5UL;    /* Protection release the CKSC_ATAUJS_CTL register.       */
        CKSC_ATAUJS_CTL           = reg32_value;
        CKSC_ATAUJS_CTL           = ~reg32_value;
        CKSC_ATAUJS_CTL           = reg32_value;
    } while ( PROTS0 != 0x00000000UL );
    while( CKSC_ATAUJS_ACT != reg32_value )
    {
        /* Waiting for CKSC_ATAUJS_CTL to set. */
    }


    /* Clock Divider Setting for C_AWO_TAUJ
    CKSC_ATAUJD_CTL - C_AWO_TAUJ Divided Clock Selection Register
    b31:b 2                       - Reserved set to 0
    b 1:b 0           ATAUJDCSID  - Clock Divider Setting for C_AWO_TAUJ - CKSC_ATAUJS_CTL selection / 1       */
    do
    {
        reg32_value               = 0x00000001UL;
        PROTCMD0                  = 0x000000A5UL;    /* Protection release the CKSC_ATAUJD_CTL register.       */
        CKSC_ATAUJD_CTL           = reg32_value;
        CKSC_ATAUJD_CTL           = ~reg32_value;
        CKSC_ATAUJD_CTL           = reg32_value;
    } while ( PROTS0 != 0x00000000UL );
    while( CKSC_ATAUJD_ACT != reg32_value )
    {
        /* Waiting for CKSC_ATAUJD_CTL to set. */
    }


    /* Set interrupt flags */
    MKTAUJ0I3                     = 1U;       // ??TAUJ0??3 
    RFTAUJ0I3                     = 0U;       // ??TAUJ0??3
    TBTAUJ0I3                     = 0U;       // ??TAUJ0??3


    /* Specifies the CK3 , CK2 , CK1 and CK0 clock  
    TAUJnTPS        - TAUJn Prescaler Clock Select Register
    b15:b12           TAUJnPRS3   - Specifies CK3_PRE clock                      - PCLK / 1
    b11:b 8           TAUJnPRS2   - Specifies CK2_PRE clock                      - PCLK / 1
    b 7:b 4           TAUJnPRS1   - Specifies CK1_PRE clock                      - PCLK / 1
    b 3:b 0           TAUJnPRS0   - Specifies CK0_PRE clock                      - PCLK / 4 */
    TAUJ0.TPS                     = 0x0000U;         /* HS IntOsc fRH(8MHz)   2                                */


    /* Data register for the capture/compare value ???????
    TAUJnCDRm       - TAUJn Channel Data Register
    b31:b 0           TAUJnCDR    - Data register for capture/compare values     - 100us                       */
    TAUJ0.CDR3                    = 800UL - 1UL;     /* INTTAUJnIm cycle = count clock cycle * (TAUJnCDRm + 1) */


    /* Controls master channel operation  
    TAUJnCMORm      - TAUJn Channel Mode OS Register
    b15:b14           TAUJnCKS    - Selects an operation clock.                  - Selected Operation CK0  ???????(CK0)
    b13:b12           TAUJnCCS0   - Selects a count clock for TAUJnCNTm counter. - Set to 00'b  ??TAUJnCKS??
    b11               TAUJnMAS    - Specifies whether the channel is a master
                                    channe or slave channel during synchronous
                                    channel operation.                           - Set to 0'b   ??Slave
    b10:b 8           TAUJnSTS    - Selects an external start trigger.           - Set to 000'b ??????
    b 7:b 6           TAUJnCOS    - Specifies the timing for updating capture
                                    register TAUJnCDRm and overflow flag
                                    TAUJnCSRm.TAUJnOVF of channel m.             - Set to 00'b.
    b 5                           - Reserved set to 0
    b 4:b 1           TAUJnMD     - Specifies an operating mode.                 - Interval timer mode. Set to 0000'b
    b 0               TAUJnMD0    - Specifies whether INTTAUJnIm is generated
                                    at the beginning of count operation (when
                                    a start trigger is entered) or not.          - INTTAUDnIm is not generated.
                                                                                   Set to 0'b */
    TAUJ0.CMOR3                   = 0x0000U;

    /* Specifies the type of valid edge detection used for the TAUJTTINm input.
    TAUJnCMURm      - TAUJn Channel Mode User Register
    b 7:b 2                       - Reserved set to 0
    b 1:b 0           TAUJnTIS    - Specifies a valid edge of TAUJTTINm input
                                    signal.                                      - Unused. Set to 00'b. */
    TAUJ0.CMUR3                   = 0x00U;


    /* TAUJnTOE.TAUJnTOEm is set to 0 because the channel output mode is not used by this samples. */
    TAUJ0.TOE                     &= 0xF7U;          


    /* Unused TAUJnRDE, TAUJnRDM registers in interval timer function. */
    /* These registers set "0". */
    TAUJ0.RDE                     &= 0xF7U;         
    TAUJ0.RDM                     &= 0xF7U;         


    MKTAUJ0I3                     = 0U;              /* Enable INTTAUJ0I3 interrupt   */
}

void Tauj0Init(void)
{
    /* Set interrupt flags */
    MKTAUJ0I0                     = 1U;
    RFTAUJ0I0                     = 0U;
    TBTAUJ0I0                     = 1U;

    //standy TAUJ0 ʱ�����ò�ֹͣ
    CKSC_ATAUJD_STPM              = 0x00000003UL;

    TAUJ0.TPS                     = 0x0000U;    
    
    TAUJ0.CDR0                    = 8000UL - 1UL;

    TAUJ0.CMOR0                   = 0x0000U;

    TAUJ0.CMUR0                   = 0x00U;

    TAUJ0.TOE                     &= 0xFEU;

    TAUJ0.RDE                     &= 0xFEU;

    TAUJ0.RDM                     &= 0xFEU;

}

void Tauj0Start(void)
{
    TAUJ0.TS                       = 0x01U;
}

/******************************************************************************
* Function Name : void R_TAUJ_Start( void )
* Description   : This function starts TAUJ (ch.3).
* Argument      : none
* Return Value  : none
******************************************************************************/
void R_TAUJ_Start( void )
{
    /* Enables the counter for each channel
    TAUJnTS         - TAUJn Channel Start Trigger Register
    b 7:b 0           TAUJnTSm    - Enables the counter operation of channel m.  - Enables the ch.3 counter */
    TAUJ0.TS                      = 0x08U;           
}

