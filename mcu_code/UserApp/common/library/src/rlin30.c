/*
      Rlin30.c
描述：此文件包括UART30 、UART32初始化函数、发送数据接口
作者：廖勇刚
时间：2016.9.28
*/
#include <string.h>
#include "rlin30.h"
#include "LogApi.h"
#include "event.h"
#include "CanApi.h"
#include "BtApi.h"
#include "CanMsgApi.h"
#include "linflexd_uart_config.h"

/************************外部全局变量****************************/
extern CommonInfo g_commonInfo;
extern UINT8      g_txTempBuf[];
extern UINT8      g_BtRxData[BT_RX_BUFF_SIZE];
extern UINT16     g_BtRxDataCount;

/************************全局变量****************************/
UINT8 g_canRxData[CAN_PACKAGE_DATA_LEN] = {0};
UINT8 g_ipcRxData[IPC_MAX_SIZE_LEN]     = {0};

static  boolean g_txComplete = true;

/*************************************************
函数名称: McuUartIPCInit
函数功能: 初始化UART3串口配置寄存器(IPC双核通讯)
输入参数: dataCallback-数据接收回调;
          errorCallback错误接收回调
输出参数: 无
函数返回类型值：无
编写者: ZhengYong/RichardChen
编写日期 :2024/04/17
*************************************************/
void McuUartIPCInit(uart_callback_t dataCallback, uart_callback_t errorCallback)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC Uart Init was called.\r\n");

    LINFlexD_UART_DRV_InstallRxCallback(UART_PORT_IPC, dataCallback, NULL);
    LINFlexD_UART_DRV_InstallErrorCallback(UART_PORT_IPC, errorCallback, NULL);
    LINFlexD_UART_DRV_ReceiveData(UART_PORT_IPC, g_ipcRxData, sizeof(g_ipcRxData));
}

/*************************************************
函数名称: McuUartIPCReInit
函数功能: 重新初始化IPC UART串口
输入参数: dataCallback-数据接收回调;
          errorCallback错误接收回调
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/30
*************************************************/
void McuUartIPCReInit(uart_callback_t dataCallback, uart_callback_t errorCallback)
{
    LINFlexD_UART_DRV_Deinit(UART_PORT_IPC);
    //LINFlexD_UART_DRV_Init(UART_PORT_IPC, &linflexd3_uart_config_921600bps_State, &linflexd3_uart_config_921600bps);
    LINFlexD_UART_DRV_Init(UART_PORT_IPC, &linflexd3_uart_config_576000bps_State, &linflexd3_uart_config_576000bps);

    LINFlexD_UART_DRV_InstallRxCallback(UART_PORT_IPC, dataCallback, NULL);
    LINFlexD_UART_DRV_InstallErrorCallback(UART_PORT_IPC, errorCallback, NULL);

    LINFlexD_UART_DRV_ReceiveData(UART_PORT_IPC, g_ipcRxData, sizeof(g_ipcRxData));
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC Uart Re-Init was called.\r\n");
}

/*************************************************
函数名称: McuUartBLEInit
函数功能: 初始化UART2串口配置寄存器(与BLE的通讯)
输入参数: dataCallback-数据接收回调;
          errorCallback错误接收回调
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void McuUartBLEInit(uart_callback_t dataCallback, uart_callback_t errorCallback)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Uart Init was called.\r\n");

    LINFlexD_UART_DRV_InstallRxCallback(UART_PORT_BLE, dataCallback, NULL);
    LINFlexD_UART_DRV_InstallErrorCallback(UART_PORT_BLE, errorCallback, NULL);

    LINFlexD_UART_DRV_ReceiveData(UART_PORT_BLE, &g_BtRxData[0], sizeof(uint8_t));
}

/*************************************************
函数名称: McuUartBLEReInit
函数功能: 重新初始化BLE UART串口配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/30
*************************************************/
void McuUartBLEReInit(uart_callback_t dataCallback, uart_callback_t errorCallback)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Uart ReInit was called.\r\n");

    LINFlexD_UART_DRV_ReceiveData(UART_PORT_BLE, &g_BtRxData[0], sizeof(uint8_t));
}

/*************************************************
函数名称: McuUartIPCRxData
函数功能: 触发接收数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/03/19
*************************************************/
void McuUartIPCRxData(void)
{
    LINFlexD_UART_DRV_ReceiveData(UART_PORT_IPC, g_ipcRxData, sizeof(g_ipcRxData));
}

/*************************************************
函数名称: McuUartIPCSetRxBuff
函数功能: 触发接收数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/04/24
*************************************************/
void McuUartIPCSetRxBuff(void)
{
    LINFlexD_UART_DRV_SetRxBuffer(UART_PORT_IPC, g_ipcRxData, sizeof(g_ipcRxData));
}

/*************************************************
函数名称: McuUartBLERxData
函数功能: 触发接收数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void McuUartBLERxData(UINT16 index)
{
    LINFlexD_UART_DRV_ReceiveData(UART_PORT_BLE, &g_BtRxData[index], sizeof(uint8_t));
}

/*************************************************
函数名称: McuUartBLESetRxBuff
函数功能: 触发接收数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/23
*************************************************/
void McuUartBLESetRxBuff(UINT16 index)
{
    LINFlexD_UART_DRV_SetRxBuffer(UART_PORT_BLE, &g_BtRxData[index], sizeof(uint8_t));
}

/*************************************************
函数名称: UartIPCSendData
函数功能: 通过UART3串口发送数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: ZhengYong
编写日期 :2024/03/19
修改者: RichardChen
修改日期 :2024/04/17
*************************************************/
void UartIPCSendData(UINT16 len, UINT8 *buf)
{
    //waiting at most 1000 ms for one sending action
    UINT32 timeout = 1000;
    /*
    while(false == g_txComplete)
    {
        OSIF_TimeDelay(1);
        timeout --;
        if(0 == timeout)
        {
            //等待超时后退出,防止任务被卡住
            g_txComplete = true;
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Uart IPC send data timeout 1 seconde error,abort previous sending.\r\n");
            LINFlexD_UART_DRV_AbortSendingData(UART_PORT_IPC);
        }
    }
    //发送数据前先设置标志,数据发送完成后在回调函数中清除标志
    g_txComplete = false;
    LINFlexD_UART_DRV_SendData(UART_PORT_IPC, buf, len);
    */

    LINFlexD_UART_DRV_SendDataBlocking(UART_PORT_IPC, buf, len, timeout);
}

/*************************************************
函数名称: UartBLESendData
函数功能: 通过UART2串口发送数据
输入参数: len-buffer以byte为单位的数据长度；
          buf-发送数据的buffer
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void UartBLESendData(UINT16 len, UINT8 *buf)
{
    LINFlexD_UART_DRV_SendDataBlocking(UART_PORT_BLE, buf, len, BLE_SEND_TIMEOUT);
}

uint32_t UartGetRecvLen(uint8_t uartNum)
{
    uint32_t remainDataLen = 0;

    if(uartNum != 0 && uartNum != 3)
    {
        return 0;
    }

    LINFlexD_UART_DRV_GetReceiveStatus(uartNum, &remainDataLen);
    return remainDataLen;
}

/*************************************************
函数名称: SendDataIPCCallback
函数功能: Uart发送结果的回调处理函数
输入参数: LINFLexDState-Uart发送的状态;
         event-发送的事件;
         userData-发送的数据
输出参数:
函数返回类型值：
编写： RichardChen
编写日期 :2024/04/17
*************************************************/
void SendDataIPCCallback(void *LINFLexDState, uart_event_t event, void *userData)
{
    //(void)LINFLexDState;
    linflexd_uart_state_t * uartState = (linflexd_uart_state_t*)LINFLexDState;
    //just print the first data in case of error or timeout
    //UINT8 sendData = 0;

    if (UART_EVENT_TX_EMPTY == event)
    {
        g_txComplete = true;
    }
    else if(UART_EVENT_ERROR == event)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "IPC data Tx Callback error event=UART_EVENT_ERROR. state=%d\r\n", uartState->receiveStatus);
    }
    else if(UART_EVENT_TIMEOUT == event)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "IPC data Tx Callback error event=UART_EVENT_TIMEOUT. state=%d\r\n", uartState->receiveStatus);
    }
}

/*************************************************
函数名称: SendDataBLECallback
函数功能: Uart发送结果的回调处理函数
输入参数: LINFLexDState-Uart发送的状态;
         event-发送的事件;
         userData-发送的数据
输出参数:
函数返回类型值：
编写： RichardChen
编写日期 :2024/04/17
*************************************************/
void SendDataBLECallback(void *LINFLexDState, uart_event_t event, void *userData)
{
    linflexd_uart_state_t * uartState = (linflexd_uart_state_t*)LINFLexDState;

    if (UART_EVENT_TX_EMPTY == event)
    {
        g_txComplete = true;
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "BLE data Tx Callback event=UART_EVENT_TX_EMPTY. state=%d\r\n", uartState->receiveStatus);
    }
    else if(UART_EVENT_ERROR == event)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "BLE data Tx Callback error event=UART_EVENT_ERROR. state=%d\r\n", uartState->receiveStatus);
    }
    else if(UART_EVENT_TIMEOUT == event)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "BLE data Tx Callback error event=UART_EVENT_TIMEOUT. state=%d\r\n", uartState->receiveStatus);
    }
}
