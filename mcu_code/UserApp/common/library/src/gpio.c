/*
      gpio.c
描述：此文件包括gpio 功能管脚配置，输出高低电平和重新初始化GPIO
作者：廖勇刚
时间：2016.9.13
*/
#include "gpio.h"
#include "pins_driver.h"
#include "Platform_Types.h"

//#include "pins_driver.h"

/*************************************************
函数名称: GpioInitPortMode
函数功能: GPIO初始化端口配置
输入参数: GPIO初始状态， GPIO枚举端口
输出参数: BOOL类型
函数返回类型值：
              TRUE---配置成功
              FALSE---配置失败
编写者: liaoyonggang
编写日期 :2016/09/13
*************************************************/
bool  GpioInitPortMode(GpioStatus status, GpioInfo  *gpioInfo)
{
    //Richard:to do the real GPIO init if needed
    /*
    UINT8 gpioConfig = 0;
       
    
    if((NULL == gpioInfo)||(GPIO_STATUS_SLEEP < status))
    {
        return FALSE;
    }

    if(GPIO_STATUS_POWER == status)
    {
        gpioConfig = gpioInfo->gpioPowerConfig;
    }
    else
    {
        if(GPIO_FLAG_UNCHANGE == gpioInfo->gpioFlag)
        {
            return TRUE; 
        }
        else
        {
            gpioConfig = gpioInfo->gpioSleepConfig;
        }
    }

    switch(gpioConfig)
    {
        case GPIO_PORT_INPUT:
        {
            *(gpioInfo->pmReg)   |=  (1 <<  gpioInfo->gpioNumber);
            *(gpioInfo->pmcReg)  &=  ~(1 << gpioInfo->gpioNumber); 
            *(gpioInfo->pibcReg) |=  (1 <<  gpioInfo->gpioNumber);
            
            break;
        }
        case GPIO_PORT_OUTPUT:
        {
            // *(gpioInfo->pmcReg)  &= ~(1 << gpioInfo->gpioNumber);
            // *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            
            break;
        }
        case GPIO_ALT_OUT1:
        {
            *(gpioInfo->pmcReg)  |= (1 <<  gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   &= ~(1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_IN1:
        {
            *(gpioInfo->pmcReg)  |= ( 1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   |=  (1 << gpioInfo->gpioNumber); 
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   &= ~(1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_OUT2:
        {
            *(gpioInfo->pmcReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   |=  (1 << gpioInfo->gpioNumber);
            break; 
        }
        case GPIO_ALT_IN2:
        {
            *(gpioInfo->pmcReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   |=  (1 << gpioInfo->gpioNumber); 
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   |=  (1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_OUT3:
        {
            *(gpioInfo->pmcReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   &= ~(1 << gpioInfo->gpioNumber);
            break; 
        }
        case GPIO_ALT_IN3:
        {
            *(gpioInfo->pmcReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   |=  (1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   &= ~(1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_OUT4:
        {
            *(gpioInfo->pmcReg)  |=  (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  |= (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   |= (1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_IN4:
        {
            *(gpioInfo->pmcReg)  |= (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   |= (1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  &= ~(1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  |= (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   |= (1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_OUT5:
        {
            *(gpioInfo->pmcReg)  |= (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  |= (1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   &= ~(1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_ALT_IN5:
        {
            *(gpioInfo->pmcReg)  |= (1 << gpioInfo->gpioNumber);
            *(gpioInfo->pmReg)   |= (1 << gpioInfo->gpioNumber);
            if(NULL != gpioInfo->pfcaeReg)
            {
                *(gpioInfo->pfcaeReg)  |= (1 << gpioInfo->gpioNumber);
            }
            *(gpioInfo->pfceReg)  &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pfcReg)   &= ~(1 << gpioInfo->gpioNumber);
            break; 
        }
        case GPIO_AP_INPUT:
        {
            *(gpioInfo->pmReg)   |=  (1 <<  gpioInfo->gpioNumber);
            *(gpioInfo->pibcReg) |=  (1 <<  gpioInfo->gpioNumber);
            break;
        }
        case GPIO_AP_OUTPUT:
        {
            *(gpioInfo->pmReg)   &= ~(1 << gpioInfo->gpioNumber);
            break;
        }
        case GPIO_AP:
        {
            break;
        }
        default:
        {
            return FALSE;
        }   
    }
    */
    return TRUE;
}

/*************************************************
函数名称: GpioSetOutputLevel
函数功能: 设置GPIO高电平或低电平
输入参数: GPIO枚举端口, 配置高低电平 
输出参数: BOOL类型
函数返回类型值：
              TRUE---配置成功
              FALSE---配置失败
编写者: liaoyonggang
编写日期 :2016/09/13
*************************************************/
bool GpioSetOutputLevel(GpioInfo  *gpioInfo, GpioLevel gpioLevel)
{
    
    if(NULL == gpioInfo)
    {
        return FALSE;
    }
    //Richard:to do the real GPIO related work later
    
    if(GPIO_AP == gpioInfo->gpioSleepConfig)
    {
        PINS_DRV_WritePin(gpioInfo->gpioBase, gpioInfo->gpioNumber, gpioLevel);
        /*
        if(GPIO_OUTPUT_LOW == gpioLevel)
        {
           
            *(gpioInfo->pmReg) &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pReg)  &= ~(1 << gpioInfo->gpioNumber);
            return TRUE;
        }
        else
        {
            *(gpioInfo->pmReg) &= ~(1 << gpioInfo->gpioNumber);
            *(gpioInfo->pReg)  |= (1 << gpioInfo->gpioNumber);
            return TRUE;
        }y
        */
    }
    else
    {     
        PINS_DRV_WritePin(gpioInfo->gpioBase, gpioInfo->gpioNumber, gpioLevel);   
        /*
        if(GPIO_OUTPUT_LOW == gpioLevel)
        {
            *(gpioInfo->pReg) &= ~(1 << gpioInfo->gpioNumber);
        }
        else
        {
            *(gpioInfo->pReg) |= (1 << gpioInfo->gpioNumber);
        }
        */
    }
    return TRUE;
}

/*************************************************
函数名称: GpioReadInputLevel
函数功能: 读取GPIO输入的电平
输入参数: GPIO枚举端口，电平指针值
输出参数: 电平值
函数返回类型值：
              TRUE---读取成功
              FALSE---参数失败
编写者: liaoyonggang
编写日期 :2016/09/13
*************************************************/
bool GpioReadInputLevel(GpioInfo  *gpioInfo, GpioLevel *gpioLevel)
{
    pins_channel_type_t pinsLevel = 0;
    if((NULL == gpioLevel) ||(NULL == gpioInfo))
    {
        return FALSE;
    }
    //Richard: to do the work later
    pinsLevel = PINS_DRV_ReadPins(gpioInfo->gpioBase);
    *gpioLevel = (pinsLevel  >>  gpioInfo->gpioNumber) & 0x01;
    //*gpioLevel =(GpioLevel) (((*gpioInfo->pprReg) >> gpioInfo->gpioNumber)&0x01);
    
    return TRUE;
}

/*************************************************
函数名称: GpioDeinitPortMode
函数功能: 读取GPIO输入的电平
输入参数: GPIO枚举端口，电平指针值
输出参数: 电平值
函数返回类型值：
              TRUE---读取成功
              FALSE---参数失败
编写者: liaoyonggang
编写日期 :2016/09/13
*************************************************/
bool GpioDeinitPortMode(GpioInfo  *gpioInfo)
{  
    if(NULL == gpioInfo)
    {
        return FALSE;
    }
     //Richard: to do later
     /*
    *(gpioInfo->pmcReg)   &= ~(1 << gpioInfo->gpioNumber);
    *(gpioInfo->pmReg)    |= (1 << gpioInfo->gpioNumber);
    *(gpioInfo->pibcReg)  |= (1 << gpioInfo->gpioNumber);
    */
    
    return TRUE;
}
