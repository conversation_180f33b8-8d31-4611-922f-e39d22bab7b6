/*
      r_interrupt.c
描述：此文件为中断函数
作者：廖勇刚
时间：2016.10.9
*/

#include "Can_Cfg.h"
#include "LogApi.h"
#include "event.h"
#include "flexcan_driver.h"
#include "HrApi.h"
#include "CanFtm.h"
#include "CanApi.h"
#include "r_can.h"
#include "PmApi.h"
#include "BLELin.h"
#include "BtApi.h"
#include "airbag.h"
#include "r_iic.h"
#include "gpio.h"
#include "PmApi.h"
#include "CanNm.h"
#include "Nm.h"

/************************外部全局变量****************************/
extern CommonInfo g_commonInfo;
extern PmInfo     g_pmInfo;
extern GpioInfo   g_gpioPowerOnInfoList[];

/************************宏定义***************************/
#define INTRLIN21    0x00001033UL
#define INTRLIN30UR1 0x0000101BUL
#define INTRLIN31UR1 0x00001072UL
#define INTOSTM0     0x0000104cUL
#define INTRCANGRECC 0x0000100FUL
#define INTRCAN1REC  0x0000106aUL
#define INTADCA0I0   0x0000100AUL
#define INTADCA0I1   0x0000100BUL
#define INTADCA0ERR  0x0000102fUL
#define INTP5        0x00001024UL
#define INTP8        0x0000107aUL
#define INTRIIC0TI   0x00001044UL
#define INTRIIC0TEI  0x00001045UL
#define INTRIIC0RI   0x00001046UL
#define INTRIIC0EE   0x00001047UL
#define INTRCAN1ERR  0x00001069UL
#define INTRCAN2ERR  0x000010d1UL
#define INTRCAN3ERR  0x00001108UL
#define INTRCAN4ERR  0x00001117UL
#define INTDMA1      0x00001035UL
#define INTDMA7      0x0000103bUL
#define INTDMA9      0x0000103dUL
#define INTRCAN1TRX  0x0000106bUL
#define INTRCAN2TRX  0x000010d3UL
#define INTRCAN3TRX  0x0000110aUL
#define INTRCAN4TRX  0x00001119UL
#define INTTAUB0I0   0x00001086UL
#define INTTAUB0I9   0x0000108fUL
#define INTP1        0x0000101EUL
#define INTP2        0x0000101FUL
#define INTP3        0x00001022UL
#define INTP4        0x00001023UL
#define INTP12       0x0000107BUL

/************************函数接口***************************/
void priority7_interrupt(uint32_t);

/*************************************************
函数名称: priority7_interrupt
函数功能: 中断向量函数
输入参数: 向量值
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/09
*************************************************/
#pragma interrupt priority7_interrupt
void              priority7_interrupt(uint32_t regEIIC_value)
{
    switch (regEIIC_value)
    {
    case INTOSTM0: {
        McuOsTickHandle();
        break;
    }
    case INTRLIN21: {
        LinIsrRxFunction();
        break;
    }
    case INTRLIN30UR1: {
        BtUartIsrRxFunction();
        break;
    }
    case INTDMA1: {
        CanTransmitDataFromArm();
        break;
    }
    case INTDMA9: {
        // IpcUartIsrRxFunction(0);
        break;
    }
    case INTRCAN1ERR: {
         Can_BusOff_Handler(CAN_CHANNEL_1);
        break;
    }
    case INTRCAN2ERR: {
         Can_BusOff_Handler(CAN_CHANNEL_2);
        break;
    }
    case INTRCAN3ERR: {
        // Can_BusOff_Handler(CAN_CHANNEL_3);
        break;
    }
    case INTRCAN4ERR: {
//        Can_BusOff_Handler(CAN_CHANNEL_4);
        break;
    }
    case INTRCAN1TRX:
    case INTRCAN2TRX:
    case INTRCAN3TRX:
    case INTRCAN4TRX: {
        Can_MainFunction_Write();
        break;
    }
    case INTRCANGRECC: {
        // MCUCanRxFifoData();
        break;
    }
    case INTRCAN1REC: {
        MCUCanTxFifoIsr();
        break;
    }
    case INTADCA0I0: {
        McuIntAdcA0I0Isr();
        break;
    }
    case INTADCA0I1: {
        McuIntAdcA0I1Isr();
        break;
    }
    case INTADCA0ERR: {
        McuIntAdcA0ErrIsr();
        break;
    }
    case INTP2: {
        FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_CAN2_BIT);
        break;
    }
    case INTP4: {
        #ifdef CAN_ENABLE_OSEKNM
        OsekNm_UserHandleEvenSet(HANDLE_REQUEST_CAN_ENABLE);
        #endif
        FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_CAN4_BIT);
        break;
    }
    case INTP12: {
        FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_CAN5_BIT);
        break;
    }
    case INTP8: {
        FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_RTC_BIT + FTM_ALIVE_WAKE_SENSOR_BIT);
        break;
    }
    case INTP5: {
        FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_BLE_BIT);
        break;
    }
    case INTRIIC0TI: {
        McuI2cTransEmptyIsr();
        break;
    }
    case INTRIIC0TEI: {
        McuI2cTransEndIsr();
        break;
    }
    case INTRIIC0RI: {
        McuI2cRecvEndIsr();
        break;
    }
    case INTRIIC0EE: {
        McuI2cCommunErrIsr();
        break;
    }
    case INTTAUB0I0: {
        //        CanLogReadBuf();
        break;
    }
    case INTTAUB0I9: {
        EnsSignalCheckInt();
        break;
    }
    default:
        break;
    }
}

/*************************************************
函数名称: GPIOB_IRQHandler
函数功能: 处理PTB 端口中的所有中断处理函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/03/17
*************************************************/
void GPIOB_IRQHandler(void)
{
    FtmInfo  *pFtmInfo = FtmInitRead();
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Isr GPIOB\r\n");
    if(PINS_DRV_GetPortIntFlag(GPIOB)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_CAN2_RX].gpioNumber)) //CAN2 RX
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOB, g_gpioPowerOnInfoList[MCU_GPIO_CAN2_RX].gpioNumber);
        ConfigCanPinStatus(CAN2_PIN_INTERRUPT_DISENABLE);
        if(pFtmInfo->ftmMode == FTM_MODE_ENTER) 
        {
            FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_CAN2_BIT);
        }
        SetWakeupSource(TBOX_WAKEUP_CAN2);
    }
}

/*************************************************
函数名称: GPIOC_IRQHandler
函数功能: 处理PTC 端口中的所有中断处理函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/04/24
*************************************************/
void GPIOC_IRQHandler(void)
{
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Isr GPIOC\r\n");
    if(PINS_DRV_GetPortIntFlag(GPIOC)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_CAN3_RX].gpioNumber)) //CAN3 RX
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOC, g_gpioPowerOnInfoList[MCU_GPIO_CAN3_RX].gpioNumber);
        ConfigCanPinStatus(CAN3_PIN_INTERRUPT_DISENABLE);
        #ifdef CAN_ENABLE_OSEKNM
        OsekNm_UserHandleEvenSet(HANDLE_REQUEST_CAN_ENABLE);
        #endif
        SetWakeupSource(TBOX_WAKEUP_CAN3);
    }
    if(PINS_DRV_GetPortIntFlag(GPIOC)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_CAN1_RX].gpioNumber)) //CAN1 RX
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOC, g_gpioPowerOnInfoList[MCU_GPIO_CAN1_RX].gpioNumber);
        /* Avoid triggering an interrupt before going to sleep */
        if(g_pmInfo.workStatus != PM_STATUS_NORAML_SLEEP_READY
        || g_pmInfo.workStatus != PM_STATUS_BACKUP_SLEEP_READY
        || g_pmInfo.workStatus != PM_STATUS_ENTER_NORAML_SLEEP
        || g_pmInfo.workStatus != PM_STATUS_LIGHT_SLEEP
        || g_pmInfo.workStatus != PM_STATUS_DEEP_SLEEP)
        {
            ConfigCanPinStatus(CAN1_PIN_INTERRUPT_DISENABLE);
        }
        #if defined (CAN_ENABLE_AUTOSAR_NM)
        AutoNm_NetworkCanEnable();
        #endif
        SetWakeupSource(TBOX_WAKEUP_CAN1);
    }
}

/*************************************************
函数名称: GPIOD_IRQHandler
函数功能: 处理PTD 端口中的所有中断处理函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/04/24
*************************************************/
void GPIOD_IRQHandler(void)
{
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Isr GPIOC\r\n");
    if(PINS_DRV_GetPortIntFlag(GPIOD)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_CAN5_RX].gpioNumber)) //CAN5 RX
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOD, g_gpioPowerOnInfoList[MCU_GPIO_CAN5_RX].gpioNumber);
        ConfigCanPinStatus(CAN5_PIN_INTERRUPT_DISENABLE);
        SetWakeupSource(TBOX_WAKEUP_CAN4);
    }
}

// 云途的CAN中断处理为注册回调函数处理的方式，需要手动触发注册，所以初始化的时候要调用一次
int install_can_callback(void)
{
    // TODO: 接收来自ARM的数据，并透传给ECU
    // √TODO: BUS_OFF处理 四路
    // √TODO: 发送数据的主调函数，云途的发送是直接调用接口发送，主要是接收发送完成的回调，并使用canif协议栈确认发送完成
    // √TODO: fifo接收数据的处理函数，云途的接收是注册回调处理，记得实现MCUCanRxFifoData原来rh850的操作
    return 0;
}

extern flexcan_msgbuff_t g_canRxMsg[4][10];
extern const Can_HardwareObjectType Can_HardwareObjectConfigData[CAN_MAX_HARDWAREOBJECTS];
#define CAN_HWOBJ_CFG(mbId) (Can_HardwareObjectConfigData[mbId])
void FlexCan_MsgBuffCallBack(uint8_t instance, flexcan_event_type_t eventType, uint32_t buffIdx, flexcan_state_t *flexcanState)
{
    uint8_t channel = Can_GetCanChannel(instance);
    FtmInfo  *pFtmInfo = FtmInitRead();
    switch (eventType)
    {
    case FLEXCAN_EVENT_RX_COMPLETE:        // 在配置的接收buffer中接收到了一帧数据。
    {
        MCUCanRxFifoData(channel, *flexcanState->mbs[buffIdx].mb_message);
        status_t status         = STATUS_ERROR;
        status                  = FLEXCAN_DRV_GetTransferStatus(instance, buffIdx);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Isr1, %u-%u-%u-%u\r\n", instance, eventType, buffIdx, status);
        if (STATUS_SUCCESS == status)
        {
            // 使用buffer接收
            FLEXCAN_DRV_Receive(instance, buffIdx, &g_canRxMsg[channel][buffIdx-7]);
        }
        break;
    }
    case FLEXCAN_EVENT_RXFIFO_COMPLETE: {  // 在接收FIFO中接收到了一帧数据。
                                           // 处理全量接收的数据
        MCUCanRxFifoData(channel, *flexcanState->mbs[buffIdx].mb_message);
        status_t status         = STATUS_ERROR;
        status                  = FLEXCAN_DRV_GetTransferStatus(instance, CAN_RX_LEGACY_FIFO_MBID);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Isr2, %u-%u-%u-%u\r\n", instance, eventType, buffIdx, status);
        if (STATUS_SUCCESS == status)
        {
            // 使用普通fifo接收
            FLEXCAN_DRV_RxFifo(instance, &g_canRxMsg[channel][0]);
        }
        break;
    }
    case FLEXCAN_EVENT_TX_COMPLETE:      // 一帧数据已从配置的发送邮箱发送出去。
    {
        Can_TxProcess_YTConfirm(channel, buffIdx);
        break;
    }
    case FLEXCAN_EVENT_WAKEUP_TIMEOUT:  // 由于超时发生了唤醒事件。
    case FLEXCAN_EVENT_WAKEUP_MATCH:    // 由于匹配发生了唤醒事件。
    case FLEXCAN_EVENT_SELF_WAKEUP:     // 发生了自我唤醒事件。
    {
        FLEXCAN_DRV_DisableSelfWakeUp(instance);
        if (instance == 1)
        {
            SetWakeupSource(TBOX_WAKEUP_CAN1);
            #if defined (CAN_ENABLE_AUTOSAR_NM)
            AutoNm_NetworkCanEnable();
            #endif
        }
        else if (instance == 2)
        {
            if (pFtmInfo->ftmMode == FTM_MODE_ENTER)
            {
                FtmWakeupTestIntHandle(FTM_ALIVE_WAKE_CAN2_BIT);
            }
            SetWakeupSource(TBOX_WAKEUP_CAN2);
        }
        break;
    }
    case FLEXCAN_EVENT_RXFIFO_WARNING:   // 接收FIFO几乎满了（已经接收到5帧数据）。
    case FLEXCAN_EVENT_RXFIFO_OVERFLOW:  // 接收FIFO已满（新的入站消息已丢失）。
    case FLEXCAN_EVENT_ENHANCE_RXFIFO_AVAILABLEDATA:
    case FLEXCAN_EVENT_ENHANCE_RXFIFO_WATERMARK:
    case FLEXCAN_EVENT_ENHANCE_RXFIFO_OVERFLOW:
    case FLEXCAN_EVENT_DMA_COMPLETE:    // DMA上发生了一次完整的传输。
    case FLEXCAN_EVENT_DMA_ERROR:       // DMA传输失败，因为DMA通道出错。
    case FLEXCAN_EVENT_ERROR:           // 发生了错误。
    default:
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Can isr event:%u\r\n", eventType);
        break;
    }
}

//这个用来统计CAN错误计数，在common task中打印，默认关闭，需要时可以开启用于调试，因此代码不删除
uint8_t g_canErrorCnt[4][32] = {0};
void ErrorCountRecord(uint8_t channel, flexcan_error_event_type_t eventType)
{
    uint32_t i = 0;

    while((eventType & 0x01) == 0x00)
    {
        eventType = eventType >> 1;
        i++;
    }

    g_canErrorCnt[channel][i]++;
}

void FlexCan_ErrorEventCallBack(uint8_t instance, flexcan_error_event_type_t eventType, struct FlexCANState *driverState)
{
    uint32_t errCode = 0;
    //ErrorCountRecord(Can_GetCanChannel(instance), eventType);
    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "Got can error:%u-%u\r\n", instance, eventType);
#ifdef CAN_ENABLE_AUTOSAR_NM
    uint8_t status = 0;
    uint8_t mode = 0;
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
#endif
    
    switch (eventType)
    {
    case FLEXCAN_BUS_OFF_DONE_EVENT:       // 发生了总线关闭完成事件。
    case FLEXCAN_BUS_OFF_ENTER_EVENT:      // 发生了总线关闭进入事件。
    {
#ifdef CAN_ENABLE_AUTOSAR_NM
        if ((g_commonInfo.accStatus == WORK_STATUS_INACTIVE) && (status <= NM_STATE_BUS_SLEEP))
        {
            break;
        }
#elif defined(CAN_ENABLE_NO_NM)
        // 只要ACC ON或者 CAN工作状态是激活的，就处理BUS OFF
        if (g_commonInfo.accStatus == WORK_STATUS_INACTIVE && g_commonInfo.canStatus == WORK_STATUS_INACTIVE)
        {
            break;
        }
#endif
        errCode = FLEXCAN_DRV_GetErrorStatus(instance);
        if ((bool)((errCode & CAN_ESR1_BOFFINT_MASK) >> CAN_ESR1_BOFFINT_SHIFT))
        {
            Can_BusOff_Handler(Can_GetCanChannel(instance));
        }
        break;
    }
    case FLEXCAN_RX_WARNING_EVENT:     // 发生了接收警告。
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_LOW);
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_LOW);
        break;
    }
    case FLEXCAN_FD_DATA_BIT_ERROR_EVENT:  // 发生了FD数据位错误。
    case FLEXCAN_ERROR_OVERRUN_EVENT:      // 发生了溢出错误。
    case FLEXCAN_TX_WARNING_EVENT:         // 发生了发送警告。
    case FLEXCAN_BIT_ERROR_EVENT:          // 发生了位错误。
    case FLEXCAN_WAKEUP_EVENT:             // 发生了唤醒事件。
    default:
        break;
    }
}
