/*
      r_can.c
描述：此文件包括CAN初始化，CAN发送和接收函数
作者：廖勇刚
时间：2016.9.13
*/
#include <stdint.h>
#include <string.h>
#include "LogApi.h"
#include "NvApi.h"
#include "can_config.h"
#include "flexcan_driver.h"
#include "gpio.h"
#include "event.h"
#include "CanApi.h"
#include "r_can.h"
#include "r_interrupt.h"
#include "Can_Regs.h"
#include "Can_Cfg.h"
#include "CanIf_Cbk.h"
#include "CanMsgApi.h" /*add by sword with 2017.01.09 support for can msg manage */
#include "CanClient.h"
#include "PmApi.h"
#include "NetworkManagement_Cfg.h"
#if defined(CAN_ENABLE_OSEKNM)
#include "OsekNm.h"
#elif defined(CAN_ENABLE_AUTOSAR_NM)
#include "CanNm.h"
#include "CanNm_Cbk.h"
#elif defined(CAN_ENABLE_NO_NM)
/* 无网管模式下不需要包含网络管理头文件 */
#else
/* 默认情况下包含AUTOSAR NM头文件 */
#include "CanNm.h"
#include "CanNm_Cbk.h"
#endif
#include "Nm.h"
#include "NmStack_Types.h"
#include "CanIf.h"
#include "status.h"
#include "SystemApi.h"

/************************外部全局变量****************************/
extern GpioInfo                         g_gpioPowerOnInfoList[];
extern const UINT16                     Can_HohConfigData[CAN_MAX_HOHS];
extern const Can_ControllerPCConfigType Can_ControllerPCConfigData[CAN_MAX_CONTROLLERS];
extern const Can_HardwareObjectType     Can_HardwareObjectConfigData[CAN_MAX_HARDWAREOBJECTS];
extern CommonInfo                       g_commonInfo;
extern DtcInfo                          g_dtcInfo;
extern uint8                            g_busOffDtc[];
extern UINT32                           g_osCurrentTickTime;
extern CanTpInfo                        g_canTpInfo;

/************************函数接口***************************/
static UINT16 Can_GetRxMb(UINT16 HwObjId);
static UINT16 Can_GetTxMb(UINT16 HwObjId);
// static UINT32 Can_GetMBCanId(UINT16 HwObjId);
static bool   Can_IsTxMbFree(UINT16 HwObjId, UINT8 Controller);
static void    Can_WriteMb(UINT16 HwObjId, Can_PduType *PduInfo);
#if ((STD_ON == CAN_HW_TRANSMIT_CANCELLATION) && (STD_ON == CAN_MULTIPLEXED_TRANSMISSION))
static void Can_CheckAbortMb(UINT8 Controller, UINT8 Hth);
#endif
#if (STD_ON == CAN_TX_POLLING)
static void Can_TxProcess(UINT8 Controller);
#endif
#if (STD_ON == CAN_RX_POLLING)
static void Can_RxProcess(UINT8 Controller);
#endif
#if (STD_ON == CAN_MULTIPLEXED_TRANSMISSION)
static UINT16 Can_FindLowPriorityMb(UINT8 Hth);
#endif
#if ((STD_ON == CAN_HW_TRANSMIT_CANCELLATION) || (STD_ON == CAN_MULTIPLEXED_TRANSMISSION))
static bool Can_PriorityHigher(Can_IdType destId, Can_IdType srcId);
#endif
// static void    Can_Wakeup_Handler(uint8 Controller);

/************************全局变量****************************/
CanStopStatus               g_globalCanMode    = CAN_MODE_STOP;
CanModeSelect               g_globalSelectMode = CAN_MODE_SELECT_TEST;
Can_ControllerStatusType    g_canCntrl[CAN_MAX_CONTROLLERS];
static Can_DriverStatusType g_canDriverStatus                    = CAN_UNINIT;
uint8                       g_busOffInt[CAN_MAX_CONTROLLERS]     = {0};
uint8                       g_phyCanChannel[CAN_MAX_CONTROLLERS] = {CAN_PHY_CHANNEL_1, CAN_PHY_CHANNEL_2, CAN_PHY_CHANNEL_3, CAN_PHY_CHANNEL_4};
flexcan_msgbuff_t           g_canRxMsg[4][10] = {0};
static CanMsgInfos          g_canMsgInfo = {0};

/************************宏定义***************************/
#define CAN_HWOBJ_NUM     CAN_MAX_HARDWAREOBJECTS
#define CAN_HOH_NUM       CAN_MAX_HOHS

#define R_CAN_GRAMINIT_ON (0x8UL)
#define R_CAN_GSLPSTS_ON  (0x4UL)
#define R_CAN_GRSTSTS_ON  (0x1UL)
#define R_CAN_CSLPSTS_ON  (0x4UL)
#define R_CAN_CHLTSTS_ON  (0x2UL)
#define R_CAN_CRSTSTS_ON  (0x1UL)
#define R_CAN_TMTRM_ON    (0x8U)
#define R_CAN_TMTR_ON     (0x1U)
#define R_CAN_AFLDAE_ON   (0x100UL)
#define R_CAN_GSLPR_MASK  (0x4UL)
#define R_CAN_GMDC_MASK   (0x3UL)
#define R_CAN_CSLPR_MASK  (0x4UL)
#define R_CAN_CHMDC_MASK  (0x3UL)

#if (STD_OFF == CAN_DEM_ERROR_DETECT)
#define Dem_ReportErrorStatus(eventId, eventStatus) \
    do                                              \
    {                                               \
    } while (0)
#endif

#define CAN_CNTRL_CFG(controller)   (Can_ControllerConfigData[controller])
#define CAN_HWOBJ_CFG(mbId)         (Can_HardwareObjectConfigData[mbId])
#define CAN_HWOBJ_ID(hth)           (Can_HohConfigData[hth])
#define CAN_CNTRL_PCCFG(controller) (Can_ControllerPCConfigData[controller])

/* Rx hardware object ID to mailbox ID */
#define CAN_RX_MB_ID(hwObjId) \
    ((uint16)((hwObjId)-CAN_CNTRL_CFG(CAN_HWOBJ_CFG(hwObjId).CanControllerRef).CanRxHwObjFirst))

/* Tx hardware object ID to mailbox ID */
#define CAN_TX_MB_ID(hwObjId) \
    ((uint16)((hwObjId)-CAN_CNTRL_CFG(CAN_HWOBJ_CFG(hwObjId).CanControllerRef).CanTxHwObjFirst))

/* Tx hardware object ID to mailbox ID */
#define CAN_RX_MB_NUM(hwObjId) \
    (CAN_CNTRL_CFG(CAN_HWOBJ_CFG(hwObjId).CanControllerRef).CanRxHwObjCount)

/*************************************************
函数名称: SuspendAllInterrupts
函数功能: 挂起MCU所有中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/12
*************************************************/
void SuspendAllInterrupts(void)
{
//    __DI(); 有操作系统，不关中断
}

/*************************************************
函数名称: ResumeAllInterrupts
函数功能: 恢复MCU所有中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/12
*************************************************/
void ResumeAllInterrupts(void)
{
//    __EI();
}

/*************************************************
函数名称: Can_GetRxMb
函数功能: 获取接收邮箱ID
输入参数: 硬件ID索引
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/17
*************************************************/
static UINT16 Can_GetRxMb(UINT16 HwObjId)
{
    UINT16 mbId       = 8;
    //详见can控制器配置定义，can协议栈接收邮箱起始于每路can控制器第8个邮箱，故mbId初值为8

    mbId = mbId + CAN_RX_MB_ID(HwObjId);

    return mbId;
}

/*************************************************
函数名称: Can_GetTxMb
函数功能: 获取发送邮箱ID
输入参数: 硬件ID索引
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/17
*************************************************/
static UINT16 Can_GetTxMb(UINT16 HwObjId)
{
    UINT16 mbId = 15;
    //详见can控制器配置定义，can协议栈发送邮箱起始于每路can控制器第8个邮箱，其中FIFO接收占7个，故mbId初值为15

    mbId = mbId + CAN_RX_MB_NUM(HwObjId) + CAN_TX_MB_ID(HwObjId);
    return mbId;
}

/*************************************************
函数名称: Can_Init
函数功能: Can设置控制器模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/12
*************************************************/
void Can_Init(void)
{
    uint8 Controller = 0;

    for (Controller = 0; Controller < CAN_MAX_CONTROLLERS; Controller++)
    {
        g_canCntrl[Controller].SwIntFlag    = 0U;
        g_canCntrl[Controller].IntLockCount = 0U;
        g_canCntrl[Controller].RxMask[0]    = 0U;
        g_canCntrl[Controller].RxMask[1]    = 0U;
        g_canCntrl[Controller].TxMask[0]    = 0U;
        g_canCntrl[Controller].TxMask[1]    = 0U;
        g_canCntrl[Controller].IsWakeup     = FALSE;
        g_canCntrl[Controller].CntrlMode    = CAN_CS_STOPPED; /* @req <CAN259> */
    }
    /* @req <CAN246> */
    g_canDriverStatus = CAN_READY;
}

/*************************************************
函数名称: Can_InitMB
函数功能: 设置CAN接收规则设置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/11/25
*************************************************/
void Can_InitMB(void)
{
    for (int Controller = 0; Controller < CAN_MAX_CONTROLLERS; Controller++)
    {
        YT_Can_InitMB(Can_GetCanInstance(Controller));
    }

}

// 云途单独设置邮箱
void YT_Can_InitMB(UINT32 instance)
{
    // status_t                  status;
    UINT32                    CAN_INST;
    flexcan_msgbuff_id_type_t rxMbType;
    uint8                     Controller   = 0;
    UINT16                    rxHwOhjIndex = 0;
    uint8                     mbId         = 0;
    flexcan_data_info_t       rxMbStdInfo  = {
               .msg_id_type = FLEXCAN_MSG_ID_EXT,
               .data_length = 8,
               .fd_enable   = false,
               .fd_padding  = 0,
               .enable_brs  = false,
               .is_remote   = false,
    };

    // 协议栈有17个规则，其中有12个接收，5个发送
    // 云途设计接收为两部分，每个控制器的前8个邮箱为FIFO接收，设置掩码000为全量接收，其余邮箱为协议栈接收
    // 第一部分:fifo
    // 不设置过滤表，全量接收
    // 第二部分:协议栈接收
    // 控制器1：TP层接收占用第9个邮箱       1
    // 控制器2：TP层接收占用第9个邮箱       1
    // 控制器3：TP层接收占用第8~16个邮箱    9
    // 控制器5：TP层接收占用第9个邮箱       1
    for (rxHwOhjIndex = 0; rxHwOhjIndex < CAN_MAX_HARDWAREOBJECTS; rxHwOhjIndex++)
    {
        if (CAN_OBJECT_TYPE_RECEIVE == Can_HardwareObjectConfigData[rxHwOhjIndex].CanObjectType)
        {
            Controller = Can_HardwareObjectConfigData[rxHwOhjIndex].CanControllerRef;
            CAN_INST   = Can_GetCanInstance(Controller);
            if (CAN_INST != instance)
            {
                continue;
            }
            FLEXCAN_DRV_SetRxMaskType(CAN_INST, FLEXCAN_RX_MASK_INDIVIDUAL);  // 设置为独立掩码模式，然后才能设置特定邮箱的掩码
            if (CAN_ID_TYPE_EXTENDED == Can_HardwareObjectConfigData[rxHwOhjIndex].CanIdType)
            {
                rxMbType                = FLEXCAN_MSG_ID_EXT;
                rxMbStdInfo.msg_id_type = FLEXCAN_MSG_ID_EXT;
            }
            else
            {
                rxMbType                = FLEXCAN_MSG_ID_STD;
                rxMbStdInfo.msg_id_type = FLEXCAN_MSG_ID_STD;
            }
            mbId = Can_GetRxMb(rxHwOhjIndex);
            FLEXCAN_DRV_SetRxIndividualMask(CAN_INST, rxMbType, mbId, Can_HardwareObjectConfigData[rxHwOhjIndex].CanFilterMask);
            FLEXCAN_DRV_ConfigRxMb(CAN_INST, mbId, &rxMbStdInfo, Can_HardwareObjectConfigData[rxHwOhjIndex].CanIdValue);
            //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Can_InitMB: Controller=%d, mbId=%d, CanIdValue=0x%x, CanFilterMask=0x%x, status=%u\r\n",
            // Controller, mbId, Can_HardwareObjectConfigData[rxHwOhjIndex].CanIdValue, Can_HardwareObjectConfigData[rxHwOhjIndex].CanFilterMask, status);
        }
    }
}

/*************************************************
函数名称: Can_GetMBInfo
函数功能: 获取邮箱相关信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void Can_GetMBInfo(UINT16 mbId, Can_PduType *pdu)
{
    volatile Can_RegType *CanRegs = NULL;

    CanRegs = (volatile Can_RegType *)CAN_BASE_REG;

    // ID帧
    if (0x00000000 != (CanRegs->RxMsg[mbId].RMID & 0x80000000))
    {
        pdu->id = CanRegs->RxMsg[mbId].RMID & CAN_MBID_ID_EXTENDED;
        pdu->id |= 0x80000000U; /* CanIf need extended Canid set 31 bit */
    }
    else
    {
        pdu->id = CanRegs->RxMsg[mbId].RMID & CAN_RX_ID_STANDARD;
    }

    /* DLC */
    pdu->length = (UINT8)((CanRegs->RxMsg[mbId].RMPTR & CAN_RX_DLC_MASK) >> 28);

    /* SDU */
    pdu->sdu[0] = (UINT8)CanRegs->RxMsg[mbId].RMDF0;
    pdu->sdu[1] = (UINT8)(CanRegs->RxMsg[mbId].RMDF0 >> 8);
    pdu->sdu[2] = (UINT8)(CanRegs->RxMsg[mbId].RMDF0 >> 16);
    pdu->sdu[3] = (UINT8)(CanRegs->RxMsg[mbId].RMDF0 >> 24);
    pdu->sdu[4] = (UINT8)CanRegs->RxMsg[mbId].RMDF1;
    pdu->sdu[5] = (UINT8)(CanRegs->RxMsg[mbId].RMDF1 >> 8);
    pdu->sdu[6] = (UINT8)(CanRegs->RxMsg[mbId].RMDF1 >> 16);
    pdu->sdu[7] = (UINT8)(CanRegs->RxMsg[mbId].RMDF1 >> 24);

    return;
}

/*************************************************
函数名称: Can_IsTxMbFree
函数功能: 判断发送邮箱是否为空
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
static bool Can_IsTxMbFree(UINT16 HwObjId, UINT8 Controller)
{
    volatile Can_RegType *CanRegs = NULL;
    UINT16                mbId    = 0;
    bool                  retVal  = FALSE;

    status_t status = STATUS_ERROR;
    UINT32   CAN_INST;

    mbId = Can_GetTxMb(HwObjId);

    switch (MCU_CHIP_TYPE)
    {
    case CHIP_RH850:
    default: {
        CanRegs = (volatile Can_RegType *)CAN_BASE_REG;
        if (0 == (CanRegs->TMSTS[mbId] & R_CAN_TMTRM_ON))
        {
            retVal = TRUE;
        }
        break;
    }
    case CHIP_YTM32B1ME05G0MLQT: {
        CAN_INST = Can_GetCanInstance(Controller);
        status   = FLEXCAN_DRV_GetTransferStatus(CAN_INST, mbId);
        if (status == STATUS_SUCCESS)
        {
            retVal = TRUE;
        }
    }
    }

    return retVal;
}

/*************************************************
函数名称: Can_TxCancel
函数功能: 发送取消特定的邮箱
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/09
*************************************************/
#if (STD_ON == CAN_HW_TRANSMIT_CANCELLATION)
void Can_TxCancel(UINT16 HwObjId)
{
    volatile Can_RegType *CanRegs = NULL;
    UINT16                mbId    = 0;

    CanRegs = (volatile Can_RegType *)CAN_BASE_REG;
    mbId    = Can_GetTxMb(HwObjId);

    CanRegs->TMC[mbId] = CAN_TX_TMTAR_MASK;

    return;
}
#endif

/*************************************************
函数名称: Can_CheckAbortMb
函数功能: 检查禁止发送的邮箱和回调
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/09
*************************************************/
#if ((STD_ON == CAN_HW_TRANSMIT_CANCELLATION) && (STD_ON == CAN_MULTIPLEXED_TRANSMISSION))
static void Can_CheckAbortMb(UINT8 Controller, UINT8 Hth)
{
}
#endif

uint16_t g_pduIdSave = 0;
/*************************************************
函数名称: Can_WriteMb
函数功能: McuCan写数据接口
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/09
*************************************************/
static void Can_WriteMb(UINT16 HwObjId, Can_PduType *PduInfo)
{
    UINT8 Controller = 0;
    UINT8 mbId       = 0;
    Controller       = CAN_HWOBJ_CFG(HwObjId).CanControllerRef;
    mbId             = Can_GetTxMb(HwObjId);
    UINT32            CAN_INST;
    status_t          status        = STATUS_ERROR;
    flexcan_msgbuff_t txMsg         = {0};  // 发送数据

    CAN_INST                        = Can_GetCanInstance(Controller);
    flexcan_data_info_t txMbStdInfo = {
        .msg_id_type = FLEXCAN_MSG_ID_EXT,
        .data_length = 8,
        .fd_enable   = false,
        .fd_padding  = 0,
        .enable_brs  = false,
        .is_remote   = false,
    };

    // 清除发送状态
    //FLEXCAN_DRV_AbortTransfer(CAN_INST, mbId);
    if (TX_BDCAN_CAN_NM != HwObjId)
    {
        PduInfo->id = g_canTpInfo.reqcanId;
        if (DiagagosticPhysicalRequestAddress() == g_canTpInfo.reqcanId)
        {
            g_canTpInfo.reqcanId = DiagagosticPhysicalResponseAddress();
            PduInfo->id          = DiagagosticPhysicalResponseAddress();
        }
        if (g_canTpInfo.localDiagTBox)
        {
            PduInfo->id = DiagagosticPhysicalResponseAddress();
        }
    }

    // 设置发送数据
    if (CAN_ID_TYPE_EXTENDED == CAN_HWOBJ_CFG(HwObjId).CanIdType)
    {
        txMbStdInfo.msg_id_type = FLEXCAN_MSG_ID_EXT;
        txMsg.msgId             = CAN_RX_IDE_MASK | PduInfo->id;
    }
    else if (CAN_ID_TYPE_STANDARD == CAN_HWOBJ_CFG(HwObjId).CanIdType)
    {
        txMbStdInfo.msg_id_type = FLEXCAN_MSG_ID_EXT;
        txMsg.msgId             = PduInfo->id;
    }
    else
    {
        if (0x80000000U == (0x80000000U & PduInfo->id)) /* extended ID */
        {
            txMbStdInfo.msg_id_type = FLEXCAN_MSG_ID_EXT;
            txMsg.msgId             = CAN_RX_IDE_MASK | PduInfo->id;
        }
        else
        {
            txMbStdInfo.msg_id_type = FLEXCAN_MSG_ID_EXT;
            txMsg.msgId             = PduInfo->id;
        }
    }

    txMsg.dataLen = PduInfo->length;
    for (uint8 i = 0; i < PduInfo->length; i++)
    {
        txMsg.data[i] = PduInfo->sdu[i];
    }

    // 发送数据
    status = FLEXCAN_DRV_ConfigTxMb(CAN_INST, mbId, &txMbStdInfo, txMsg.msgId);
    if (STATUS_SUCCESS == status)
    {
        // SystemApiLogPrintf(LOG_INFO_OUTPUT, "Send can:%x-%02x-%02x, mb:%u, pduId:%u, can status:%u\r\n", txMsg.msgId, txMsg.data[0], txMsg.data[1], mbId, PduInfo->swPduHandle, status);
        status = FLEXCAN_DRV_Send(CAN_INST, mbId, &txMbStdInfo, txMsg.msgId, txMsg.data);
        if (STATUS_SUCCESS == status)
        {
            /* save pdu handle */
            g_canCntrl[Controller].TxPduHandles[CAN_TX_MB_ID(HwObjId)] = PduInfo->swPduHandle;
            g_pduIdSave = (uint16_t)Controller << 8 | PduInfo->swPduHandle;
            // 记录发送数据
            CanLogWriteTxBuf(Controller, PduInfo->id, PduInfo->length, PduInfo->sdu);
            // 确认发送成功
            // Can_TxProcess_YTConfirm(Controller, PduInfo->swPduHandle);
        }
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Send can fail:%x-%02x-%02x, mb:%u, pduId:%u\r\n", txMsg.msgId, txMsg.data[0], txMsg.data[1], mbId, PduInfo->swPduHandle);
    }

}

void CanTxConfirmInMain(void)
{
    if(g_pduIdSave != 0)
    {
        Can_TxProcess_YTConfirm((uint8_t)(g_pduIdSave >> 8), (uint8_t)g_pduIdSave + CAN_PROTOCOL_START_MB);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Confirm in main:%x\r\n", g_pduIdSave);
        g_pduIdSave = 0;
    }
}

/*************************************************
函数名称: Can_Write
函数功能: McuCan写数据接口
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/09
*************************************************/
Can_ReturnType Can_Write(UINT8 Hth, Can_PduType *PduInfo)
{
    Can_ReturnType ret        = CAN_NOT_OK;
    UINT16         HwObjId    = 0;
    UINT8          Controller = 0;

#if (STD_ON == CAN_DEV_ERROR_DETECT)
    bool errFlag = FALSE;

    if (CAN_READY != g_canDriverStatus)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu write diver status error is %d\r\n", g_canDriverStatus);
        errFlag = TRUE;
        ret     = CAN_NOT_OK;
    }
    /* check hth is vaild */
    else if ((Hth >= CAN_HOH_NUM) || (CAN_OBJECT_TYPE_TRANSMIT != CAN_HWOBJ_CFG(CAN_HWOBJ_ID(Hth)).CanObjectType))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu write Hth error is %d\r\n", Hth);
        errFlag = TRUE;
        ret     = CAN_NOT_OK;
    }
    else if ((NULL_PTR == PduInfo) || (NULL_PTR == PduInfo->sdu))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu write PduInfo error\r\n");
        errFlag = TRUE;
        ret     = CAN_NOT_OK;
    }
    else if (CAN_DATA_LENGTH < PduInfo->length)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu write len error\r\n");
        errFlag = TRUE;
        ret     = CAN_NOT_OK;
    }
    else
    {
        /* do nothing */
    }
    if (FALSE == errFlag)
#endif
    {
        HwObjId    = CAN_HWOBJ_ID(Hth);
        Controller = CAN_HWOBJ_CFG(HwObjId).CanControllerRef;
        if (CAN_CS_STARTED == g_canCntrl[Controller].CntrlMode)
        {
            if (TRUE == Can_IsTxMbFree(HwObjId, Controller) && (0 == PmBplusNotSendCan()))
            {
                Can_WriteMb(HwObjId, PduInfo);
                ret = CAN_OK;
            }
            else
            {
                ret = CAN_BUSY;
            }
        }
    }

    return ret;
}

/*************************************************
函数名称: Can_TxProcess
函数功能: CAN发送处理流程
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
#if (STD_ON == CAN_TX_POLLING)
static void Can_TxProcess(UINT8 Controller)
{
    volatile Can_RegType *CanRegs    = NULL;
    UINT16                HwObjId    = 0;
    UINT16                EndHwObjId = 0;
    UINT16                mbId       = 0;

    EndHwObjId = (uint16)(CAN_CNTRL_CFG(Controller).CanTxHwObjFirst + CAN_CNTRL_CFG(Controller).CanTxHwObjCount);

    switch (MCU_CHIP_TYPE)
    {
    case CHIP_RH850:
    default: {
        CanRegs = (volatile Can_RegType *)CAN_BASE_REG;

        for (HwObjId = CAN_CNTRL_CFG(Controller).CanTxHwObjFirst; HwObjId < EndHwObjId; HwObjId++)
        {
            mbId = Can_GetTxMb(HwObjId);
            if (32 > mbId)
            {
                // 发送确认
                if ((1u << mbId) == (CanRegs->TMTCSTS[0] & (1u << mbId)))
                {
                    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "tx process:%d %d\r\n",mbId,Controller);
                    CanRegs->TMSTS[mbId] = 0x00;
                    CanIf_TxConfirmation(g_canCntrl[Controller].TxPduHandles[mbId - g_phyCanChannel[Controller] * CAN_HW_MAX_MAILBOXES]);
                }
            }
            else if ((32 <= mbId) && (64 > mbId))
            {
                if ((1u << mbId) == (CanRegs->TMTCSTS[1] & (1u << mbId)))
                {
                    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "tx process:%d %d\r\n",mbId,Controller);
                    CanRegs->TMSTS[mbId] = 0x00;
                    CanIf_TxConfirmation(g_canCntrl[Controller].TxPduHandles[mbId - g_phyCanChannel[Controller] * CAN_HW_MAX_MAILBOXES]);
                }
            }
            else if ((64 <= mbId) && (96 > mbId))
            {
                if ((1u << mbId) == (CanRegs->TMTCSTS[2] & (1u << mbId)))
                {
                    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "tx process:%d %d\r\n",mbId,Controller);
                    CanRegs->TMSTS[mbId] = 0x00;
                    CanIf_TxConfirmation(g_canCntrl[Controller].TxPduHandles[mbId - g_phyCanChannel[Controller] * CAN_HW_MAX_MAILBOXES]);
                }
            }
            else
            {
                /* NULL */
            }
        }
    }
    case CHIP_YTM32B1ME05G0MLQT: {
        // 下方单独处理，云途直接确认发送成功，Txprocess是协议层的确认发送成功，调用处在Can_Write
        break;
    }
    }
}
#endif

//暂时收不到协议栈报文的发送响应中断，因此直接在main函数里面确认（但不能在发送后直接确认，否则会因确认太早，导致协议栈超时reset）
void Can_TxProcess_YTConfirm(UINT8 Controller, UINT32 mbId)
{
    UINT16 pduId = 0;
    UINT16 HwObjId    = 0;
    UINT16 EndHwObjId = (uint16)(CAN_CNTRL_CFG(Controller).CanTxHwObjFirst
                              + CAN_CNTRL_CFG(Controller).CanTxHwObjCount);

    for(HwObjId = CAN_CNTRL_CFG(Controller).CanTxHwObjFirst; HwObjId < EndHwObjId; HwObjId++)
    {
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Got tx confirm, mb:%u, HwObjId:%u\r\n", mbId, HwObjId);
        if(mbId == Can_GetTxMb(HwObjId))
        {
            pduId = g_canCntrl[Controller].TxPduHandles[CAN_TX_MB_ID(HwObjId)];
            CanIf_TxConfirmation(pduId);
        }
    }
}

/*************************************************
函数名称: Can_MainFunction_Write
函数功能: CAN发送主调度函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_MainFunction_Write(void)
{
#if (STD_ON == CAN_TX_POLLING)
    UINT8 Controller;

#if (STD_ON == CAN_DEV_ERROR_DETECT)
    /*@req <CAN187>*/
    if (CAN_READY != g_canDriverStatus)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu Main Function Write error\r\n");
    }
    else
#endif /* STD_ON == CAN_DEV_ERROR_DETECT */
    {
        for (Controller = 0; Controller < CAN_MAX_CONTROLLERS; Controller++)
        {
            if ((CAN_PROCESS_TYPE_POLLING == CAN_CNTRL_PCCFG(Controller).CanTxProcessing) && (CAN_CS_STARTED == g_canCntrl[Controller].CntrlMode))
            {
                Can_TxProcess(Controller);
            }
        }
    }
#endif /* STD_ON == CAN_TX_POLLING */
    return;
}

/*************************************************
函数名称: Can_RxProcess
函数功能: CAN接收数据处理流程
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
#if (STD_ON == CAN_RX_POLLING)
static void Can_RxProcess(UINT8 Controller)
{
    UINT16      HwObjId    = 0;
    UINT16      EndHwObjId = 0;
    UINT16      mbId       = 0;

    EndHwObjId = (uint16)(CAN_CNTRL_CFG(Controller).CanRxHwObjFirst + CAN_CNTRL_CFG(Controller).CanRxHwObjCount);

    int               CAN_INST      = Can_GetCanInstance(Controller);
    status_t          status        = STATUS_ERROR;

    // 第一部分接收：FIFO全量接收
    // FIFO固定检查邮箱0 - CAN_RX_LEGACY_FIFO_MBID
    status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, CAN_RX_LEGACY_FIFO_MBID);
    if (STATUS_SUCCESS == status)
    {
        // 使用普通fifo接收
        status = FLEXCAN_DRV_RxFifo(CAN_INST, &g_canRxMsg[Controller][0]);
        // 接收后的数据在回调中处理了:UserApp/common/library/src/r_interrupt.c:MCUCanRxFifoData
    }

    // 第二部分接收：协议层接收
    for (HwObjId = CAN_CNTRL_CFG(Controller).CanRxHwObjFirst; HwObjId < EndHwObjId; HwObjId++)
    {
        mbId = Can_GetRxMb(HwObjId);
        // SystemApiLogPrintf(LOG_INFO_OUTPUT, "Controller is %d, HwObjId is %d, EndHwObjId is %d, mbId is %d\r\n", Controller, HwObjId, EndHwObjId, mbId);

        // obid:0,1,2,3,4,5,6,7
        if (mbId < 8)
        {
            // FIFO固定检查邮箱0
            status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, CAN_RX_LEGACY_FIFO_MBID);
            if (STATUS_SUCCESS == status)
            {
                // 使用普通fifo接收
                status = FLEXCAN_DRV_RxFifo(CAN_INST, &g_canRxMsg[Controller][0]);
            }
        }
        // 使用普通接收
        else if (mbId < 17) //按当前的方案，暂时不会有超过16的ID
        {
            status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, mbId);
            if (STATUS_SUCCESS == status)
            {
                status = FLEXCAN_DRV_Receive(CAN_INST, mbId, &g_canRxMsg[Controller][mbId - 7]);

            }
        }
    }

}
#endif

/*************************************************
函数名称: Can_GetCanInstance
函数功能: 根据用户指定的CAN通道枚举值获取云途对应的硬件CAN实例编号
输入参数:
- int channel：用户定义的CAN通道枚举值，可选值包括：
    - CAN_CHANNEL_1: 对应硬件CAN通道1
    - CAN_CHANNEL_2: 对应硬件CAN通道2
    - CAN_CHANNEL_3: 对应硬件CAN通道3
    - CAN_CHANNEL_4: 对应硬件CAN通道5
输出参数: 无
函数返回类型值：
- 返回整型值（int），表示与给定CAN通道枚举值获取云途对应的硬件CAN实例编号。
- 如果channel不在合法枚举范围内，则返回0。
编写者: guoyuchen
编写日期 : 2024/3/12
注释：
此函数主要用于云途MCU实现CAN通道逻辑映射到实际硬件接口的功能。在进行CAN通信时，上层应用可以通过调用此函数将抽象的通道标识转换为具体的硬件通道编号。
*************************************************/
int Can_GetCanInstance(int channel)
{
    int can_instance = 1;

    switch (channel)
    {
     case CAN_CHANNEL_1: {
         can_instance = 1;
         break;
     }
     case CAN_CHANNEL_2: {
         can_instance = 2;
         break;
     }
     case CAN_CHANNEL_3: {
         can_instance = 3;
         break;
     }
    case CAN_CHANNEL_4: {
        can_instance = 5;
        break;
    }
    default: {
        break;
    }
    }

    return can_instance;
}

/*************************************************
函数名称: Can_GetCanChannel
函数功能: 根据云途的硬件CAN实例编号获取软件CAN通道枚举值
输入参数:
- int instance：云途的硬件CAN实例编号
输出参数: 无
函数返回类型值：
- 返回整型值（int），表示与给定硬件CAN实例编号获取软件CAN通道枚举值。
- 如果instance不在合法范围内，则返回0。
编写者: guoyuchen
编写日期 : 2024/3/15
注释：
此函数主要用于云途MCU实现CAN通道逻辑映射到实际硬件接口的功能。在进行CAN通信时，上层应用可以通过调用此函数将具体的硬件通道编号转换为抽象的通道标识。
*************************************************/
int Can_GetCanChannel(int instance)
{
    int can_channel = CAN_CHANNEL_1;

    switch (instance)
    {
     case 1: {
         can_channel = CAN_CHANNEL_1;
         break;
     }
     case 2: {
         can_channel = CAN_CHANNEL_2;
         break;
     }
     case 3: {
         can_channel = CAN_CHANNEL_3;
         break;
     }
    case 5: {
        can_channel = CAN_CHANNEL_4;
        break;
    }
    default: {
        break;
    }
    }

    return can_channel;
}

/*************************************************
函数名称: Can_GetStartMailbox
函数功能: 获取指定CAN通道的起始邮箱号
输入参数:
- flexcan_rx_fifo_id_filter_num_t filterNum: 滤波表接收码数量
- int maxMailbox: 最大邮箱数量
输出参数: 无
函数返回类型值：
- 返回整型值（int），表示指定CAN通道的起始邮箱号（下标）。
编写者: guoyuchen
编写日期 : 2024/3/14
注释：
当指定CAN控制器设置了滤波表后，剩余可用邮箱数量根据云途手册有映射关系，此函数用于根据滤波表接收码数量和最大邮箱数量返回起始邮箱号。
*************************************************/
int Can_GetStartMailbox(flexcan_rx_fifo_id_filter_num_t filterNum, int maxMailbox)
{
    int startMailbox = 0;
    // filters为enum类型，需要转换为int类型，其值范围0x00~0x0F，十进制范围0~15
    int filters = (int)filterNum;
    filters     = 8 * (filters + 1);  // filters转换为实际滤波表接收码数量
    if (filters >= 8 && filters <= 128 && filters % 8 == 0)
    {
        startMailbox = 2 * (filters / 8) + 6;
    }
    if (104 <= filters)
    {
        startMailbox = 32;
        // 如果最大邮箱是32，则没有剩余邮箱
        // 如果最大邮箱是64，且滤波表接收码数量大于96，则剩余邮箱最少为32
    }
    return startMailbox;
}

/*************************************************
函数名称: McuCanMainFunctionRead
函数功能: CAN主函数接收读取数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void Can_MainFunction_Read(void)
{
    // 如果宏定义STD_ON == CAN_RX_POLLING为真，即开启了CAN总线的轮询接收模式
#if (STD_ON == CAN_RX_POLLING)
    UINT8 Controller;  // 定义一个UINT8类型的变量Controller，用于遍历所有CAN控制器

    /*@req <CAN181>*/  // 这是一个注释标记，可能与特定需求或规范相关

    // 如果同时开启了CAN设备错误检测功能（STD_ON == CAN_DEV_ERROR_DETECT为真）
#if (STD_ON == CAN_DEV_ERROR_DETECT)
    /*@req <CAN187>*/  // 同样是一个注释标记，与特定需求或规范相关
    // 检查CAN驱动器的状态是否为READY
    if (CAN_READY != g_canDriverStatus)
    {
        // 如果状态不为READY，则输出错误日志
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu Main Function Read error\r\n");
    }
    else
#endif /* STD_ON == CAN_DEV_ERROR_DETECT */
    {
        // 遍历所有CAN控制器
        for (Controller = 0; Controller < CAN_MAX_CONTROLLERS; Controller++)
        {
            // 判断当前控制器是否配置为轮询接收处理类型，并且当前工作模式为已启动
            if ((CAN_PROCESS_TYPE_POLLING == CAN_CNTRL_PCCFG(Controller).CanRxProcessing) /*&&
                (CAN_CS_STARTED == g_canCntrl[Controller].CntrlMode)*/) //去掉Start的条件，stop的时候应该是可以收不能发
            {
                // 若满足条件，则调用Can_RxProcess函数处理该控制器的接收数据
                Can_RxProcess(Controller);
            }
        }
    }

#endif /* STD_ON == CAN_RX_POLLING */

    // 函数执行完毕后返回
    return;
}

/*************************************************
函数名称: Can_FindLowPriorityMb
函数功能: 发现当前硬件句柄的低优先级邮箱
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
#if (STD_ON == CAN_MULTIPLEXED_TRANSMISSION)
static UINT16 Can_FindLowPriorityMb(UINT8 Hth)
{
}
#endif

/*************************************************
函数名称: Can_PriorityHigher
函数功能: 发现当前硬件句柄的高优先级邮箱
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
#if ((STD_ON == CAN_HW_TRANSMIT_CANCELLATION) || (STD_ON == CAN_MULTIPLEXED_TRANSMISSION))
static bool Can_PriorityHigher(Can_IdType destId, Can_IdType srcId)
{
}
#endif
/*************************************************
函数名称: Can_EnableControllerInterrupts
函数功能: 使能当前通道的中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void Can_EnableControllerInterrupts(uint8 Controller)
{
}

/*************************************************
函数名称: Can_DisableControllerInterrupts
函数功能: 不使能当前通道的中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void Can_DisableControllerInterrupts(uint8 Controller)
{
}

/*************************************************
函数名称: Can_RxTxInt_Handler
函数功能: CAN发送和接收中断函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_RxTxInt_Handler(void)
{
}

/*************************************************
函数名称: Can_Error_Handler
函数功能: CAN错误中断函数处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_Error_Handler(uint8 Controller)
{
}

/*************************************************
函数名称: Can_BusOff_Handler
函数功能: BUSOFF处理回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_BusOff_Handler(uint8 Controller)
{
    switch (MCU_CHIP_TYPE)
    {
        case CHIP_YTM32B1ME05G0MLQT:
        {
            g_busOffDtc[Controller] = 0x01;
            g_busOffInt[Controller] = 1;
        }
            break;
        default:
            break;
    }
}

/*************************************************
函数名称: Can_BusOff_Main_Handler
函数功能: BUSOFF处理主回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_BusOff_Main_Handler(void)
{
    UINT8 i = 0;

    for (i = CAN_CHANNEL_1; i < CAN_CHANNEL_4 + 1; i++)
    {
        if (1 == g_busOffInt[i])
        {
            CanIf_ControllerBusOff(i);
            g_busOffInt[i] = 0;
        }
    }
}

/**
 * @brief 获取指定CAN通道的状态
 *
 * 此函数通过检查g_busOffDtc数组和CanIf接口来确定指定CAN通道的当前状态。
 * 它可以用于诊断目的，以确定CAN总线是否处于正常工作状态或是否发生了总线关闭(bus-off)错误。
 *
 * @param channel CAN通道号，可以是以下值之一：
 *                - CAN_CHANNEL_1 (0): CAN通道1
 *                - CAN_CHANNEL_2 (1): CAN通道2
 *                - CAN_CHANNEL_3 (2): CAN通道3
 *                - CAN_CHANNEL_4 (3): CAN通道4
 *
 * @return CanChannelStatus 返回CAN通道的状态：
 *         - CAN_STATUS_NORMAL (0): CAN通道正常工作
 *         - CAN_STATUS_BUS_OFF (1): CAN通道处于总线关闭状态
 *         - CAN_STATUS_UNKNOWN (2): 无法确定CAN通道状态或通道号无效
 *
 * @note 此函数首先检查g_busOffDtc数组中的状态，如果发现总线关闭标志，则直接返回CAN_STATUS_BUS_OFF。
 *       如果g_busOffDtc中没有标记总线关闭，则通过CanIf_GetPduMode接口获取更详细的状态。
 *       如果通道号超出有效范围(>=CAN_MAX_CONTROLLERS)，函数将返回CAN_STATUS_UNKNOWN。
 *
 * @see Can_BusOff_Handler() - 设置g_busOffDtc数组的中断处理函数
 * @see CanIf_GetPduMode() - 获取CAN控制器PDU模式的接口函数
 */
CanChannelStatus CanGetChannelStatus(uint8_t channel)
{
    // 检查参数有效性
    if (channel >= CAN_MAX_CONTROLLERS)
    {
        return CAN_STATUS_UNKNOWN;
    }

    // 检查g_busOffDtc数组中的状态
    if (g_busOffDtc[channel] == 0x01)
    {
        return CAN_STATUS_BUS_OFF;
    }

    // 也可以通过CanIf接口获取更详细的状态
    CanIf_ChannelGetModeType canStatus = CANIF_GET_OFFLINE;
    CanIf_GetPduMode(channel, &canStatus);

    if (canStatus == CANIF_GET_ONLINE || canStatus == CANIF_GET_TX_ONLINE)
    {
        return CAN_STATUS_NORMAL;
    }
    else
    {
        return CAN_STATUS_BUS_OFF;
    }
}

/**
 * @brief 检查当前MCU的所有CAN通道是否有任何一路出现busoff或不可用状态
 *
 * 此函数通过CanIf接口依次检查每个CAN通道的状态。只要发现任何一个通道处于
 * 异常状态(非ONLINE/TX_ONLINE)或无法获取状态,就认为存在busoff或不可用情况。
 *
 * @details 函数工作流程:
 * - 遍历所有CAN控制器通道
 * - 通过CanIf_GetPduMode获取每个通道的状态
 * - 如果获取状态失败,直接返回true表示存在异常
 * - 如果状态既非ONLINE也非TX_ONLINE,认为该通道异常,返回true
 * - 所有通道都正常时返回false
 *
 * @note 此函数依赖CanIf层提供的接口函数CanIf_GetPduMode来获取通道状态
 *
 * @retval true 至少有一个CAN通道处于busoff或不可用状态
 * @retval false 所有CAN通道都工作正常
 *
 * @see CanIf_GetPduMode - 获取CAN通道状态的接口函数
 * @see CAN_MAX_CONTROLLERS - CAN控制器的最大数量配置
 * @see CanIf_ChannelGetModeType - CAN通道状态的枚举类型定义
 */
bool CheckMcuCanBusOffStatus(void)
{
    uint8_t channel;
    bool    hasBusOff = false;

    /* 遍历所有CAN通道检查状态 */
    for (channel = 0; channel < CAN_USED_CONTROLLERS; channel++)
    {
        // 检查g_busOffDtc数组中的状态
        if (CAN_STATUS_NORMAL != CanGetChannelStatus(channel))
        {
            hasBusOff = true;
            break;
        }
    }

    return hasBusOff;
}

/*************************************************
函数名称: Can_TwRwBusOff_Handler
函数功能: CAN发送 接收  BUSOFF错误处理函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_TwRwBusOff_Handler(uint8 Controller)
{
}

/*************************************************
函数名称: Can_Cbk_CheckWakeup
函数功能: 检查CAN通道的唤醒源
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
Std_ReturnType Can_Cbk_CheckWakeup(uint8 Controller)
{
#if (STD_ON == CAN_WAKEUP_SUPPORT)
    Std_ReturnType ret = E_NOT_OK;

#if (STD_ON == CAN_DEV_ERROR_DETECT)
    if (CAN_READY != g_canDriverStatus)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Can Cbk Check Wakeup not ready\r\n");
        ret = E_NOT_OK;
    }
    /*@req <CAN363>*/
    else if (Controller >= CAN_MAX_CONTROLLERS)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Can Cbk Check Wakeup Controller is %d error\r\n", Controller);
        ret = E_NOT_OK;
    }
#endif /* STD_ON == CAN_DEV_ERROR_DETECT */
    {
        /* 下步完善CAN的唤醒源*/
        /* Check Controller Wakeup flag */

        if (TRUE == g_canCntrl[Controller].IsWakeup)
        {
            g_canCntrl[Controller].IsWakeup = FALSE;
            ret                             = E_OK;
        }
    }

    return ret;
#else
    return E_OK;
#endif /* STD_ON == CAN_WAKEUP_SUPPORT */
}

/*************************************************
函数名称: Can_Wakeup_Handler
函数功能: CAN通道唤醒处理函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
// static void Can_Wakeup_Handler(uint8 Controller)
// {
// }

/*************************************************
函数名称: Can_MainFunction_Wakeup
函数功能: 检查CAN通道的唤醒源
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
void Can_MainFunction_Wakeup(uint8 Controller)
{
#if ((STD_ON == CAN_WAKEUP_POLLING) && (STD_ON == CAN_WAKEUP_SUPPORT))
    uint8 Controller;

#if (STD_ON == CAN_DEV_ERROR_DETECT)
    /*@req <CAN184>*/
    if (CAN_READY != g_canDriverStatus)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Can Main found Wakeup not ready\r\n");
    }
#endif /* STD_ON == CAN_DEV_ERROR_DETECT */
    {
        for (Controller = 0; Controller < CAN_MAX_CONTROLLERS; Controller++)
        {
            if ((CAN_PROCESS_TYPE_POLLING == Can_ControllerPCConfigData[Controller].CanWakeupProcessing) && (CAN_CS_SLEEP == g_canCntrl[Controller].CntrlMode))
            {
                Can_Wakeup_Handler(Controller);
            }
        }
    }
#endif /* (STD_ON == CAN_WAKEUP_POLLING) && (STD_ON == CAN_WAKEUP_SUPPORT) */
}

/*************************************************
函数名称: Can_ApplySWF
函数功能: 配置CAN中断切换
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/10
*************************************************/
Can_ReturnType Can_ApplySWF(UINT8 channel, UINT8 swIntFlag)
{
    volatile Can_RegType *CanRegs = NULL;

    CanRegs = (volatile Can_RegType *)CAN_BASE_REG;

    /* BUSOFF Interrupt */
    if (SWF_BOFF_MASK == (swIntFlag & SWF_BOFF_MASK))
    {
        CanRegs->channelInfo[channel + 1].CTR |= CAN_CHANNEL_INTERRUPT_BOEIE;
    }
    else
    {
        CanRegs->channelInfo[channel + 1].CTR &= ~CAN_CHANNEL_INTERRUPT_BOEIE;
    }

    return CAN_OK;
}

/*************************************************
函数名称: Can_StartMode
函数功能: CAN通道初始化为开始模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/16
*************************************************/
Can_ReturnType Can_StartMode(uint8 Controller)
{
    Can_ReturnType ret = CAN_NOT_OK;
    UINT32   CAN_INST = 0;
    //status_t status   = STATUS_ERROR;

    if (CAN_CS_STARTED == g_canCntrl[Controller].CntrlMode)
    {
        // SystemApiLogPrintf(LOG_INFO_OUTPUT, "Can:%d already started\r\n", Controller);
        ret = CAN_OK;
    }
    else if (CAN_CS_STOPPED == g_canCntrl[Controller].CntrlMode)
    {
        CAN_INST = Can_GetCanInstance(Controller);
        FLEXCAN_DRV_Deinit(CAN_INST);
        switch (CAN_INST)
        {
            case 1:
            {
                FLEXCAN_DRV_Init(CAN_INST, &flexcan1_InitConfig_State, &flexcan1_InitConfig);
                break;
            }
            case 2:
            {
                FLEXCAN_DRV_Init(CAN_INST, &flexcan2_InitConfig_State, &flexcan2_InitConfig);
                break;
            }
            case 3:
            {
                FLEXCAN_DRV_Init(CAN_INST, &flexcan3_InitConfig_State, &flexcan3_InitConfig);
                break;
            }
            case 5:
            {
                FLEXCAN_DRV_Init(CAN_INST, &flexcan5_InitConfig_State, &flexcan5_InitConfig);
                break;
            }
            default:
            {
                ret = CAN_NOT_OK;
                break;
            }
        }

        // 初始化邮箱配置
        YT_Can_InitMB(CAN_INST);

        CAN1->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;
        CAN2->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;
        CAN3->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;
        CAN5->CTRL1 |= CAN_CTRL1_BOFFREC_MASK;

        FLEXCAN_DRV_SetRxMaskType(CAN_INST, FLEXCAN_RX_MASK_GLOBAL);            // 这里的掩码类型是全局掩码，表示作用于所有邮箱
        FLEXCAN_DRV_SetRxFifoGlobalMask(CAN_INST, FLEXCAN_MSG_ID_STD, 0x000U);  // 接收所有标准帧
        FLEXCAN_DRV_SetRxFifoGlobalMask(CAN_INST, FLEXCAN_MSG_ID_EXT, 0x000U);  // 接收所有扩展帧
        // 注册回调
        FLEXCAN_DRV_InstallEventCallback(CAN_INST, FlexCan_MsgBuffCallBack, NULL);
        // 注册错误事件回调
        FLEXCAN_DRV_InstallErrorCallback(CAN_INST, FlexCan_ErrorEventCallBack, NULL);

        if (CAN_PROCESS_TYPE_INTERRUPT == Can_ControllerPCConfigData[Controller].CanBusOffProcessing)
        {
            /* BusOff here represents all errors and warnings*/
            g_canCntrl[Controller].SwIntFlag |= SWF_BOFF_MASK;
        }

        g_canCntrl[Controller].CntrlMode = CAN_CS_STARTED;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Start Can:%d\r\n", Controller);
        ret = CAN_OK;
    }

    return ret;
}

/*************************************************
函数名称: Can_StopMode
函数功能: CAN通道初始化为停止模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/16
*************************************************/
Can_ReturnType Can_StopMode(uint8 Controller)
{
    Can_ReturnType        ret     = CAN_NOT_OK;

    // UINT32   CAN_INST = 0;
    // status_t status   = STATUS_ERROR;

    //CAN_INST = Can_GetCanInstance(Controller);
    //status   = FLEXCAN_DRV_Deinit(CAN_INST);
    //if (STATUS_SUCCESS == status)
    {
        g_canCntrl[Controller].CntrlMode = CAN_CS_STOPPED;
        ret                              = CAN_OK;
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Stop Can:%d\r\n", Controller);
    return ret;
}

/*************************************************
函数名称: Can_SleepMode
函数功能: CAN设置为睡眠模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
Can_ReturnType Can_SleepMode(uint8 Controller)
{
    return CAN_OK;
}

/*************************************************
函数名称: Can_WakeupMode
函数功能: CAN设置为唤醒模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
Can_ReturnType Can_WakeupMode(uint8 Controller)
{
    return CAN_OK;
}

/*************************************************
函数名称: Can_SetControllerMode
函数功能: Can设置控制器模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/17
*************************************************/
Can_ReturnType Can_SetControllerMode(uint8 Controller, Can_StateTransitionType Transition)
{

    Can_ReturnType ret = CAN_NOT_OK;

    switch (Transition)
    {
    case CAN_T_START:
        ret = Can_StartMode(Controller);
        break;
    case CAN_T_STOP:
        ret = Can_StopMode(Controller);
        break;
    case CAN_T_SLEEP:
        ret = Can_SleepMode(Controller);
        break;
    case CAN_T_WAKEUP:
        ret = Can_WakeupMode(Controller);
        break;
    default:
        ret = CAN_NOT_OK;
        break;
    }

    return ret;
}

/*************************************************
函数名称: McuCanInit
函数功能: CAN初始化
          注意点:1 接收BUFFER FIFO深度要大于发送FIFO深度
                 2 发送FIFO BUFFER 不能与单独发送BUFFER冲突
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void McuCanInit(void)
{
    UINT8 channel = 0;

    // NOTE: 云途已经在main.c的board init中初始化了CAN，这里不需要再次初始化,只需要配置CAN的工作模式即可
    for (channel = CAN_CHANNEL_1; channel <= CAN_CHANNEL_2; channel++)
    {
        Can_StartMode(channel);
    }
    //Can_InitMB();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Mcu can init finished\r\n");
}

/*************************************************
函数名称: McuCanSetTxRule
函数功能: MCU CAN发送缓存设置
输入参数: channelId ----CAN 通道
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/07
*************************************************/
void McuCanSetTxRule(UINT8 channelId)
{
    if (CAN_CHANNEL_4 < channelId)
    {
        return;
    }

    switch (channelId)
    {
    case CAN_CHANNEL_1: {
        /*配置发送FIFO模式*/
        // RSCAN0.CFCC3.UINT32 = 0x00110101UL;
        // RSCAN0.CFCC4.UINT32 = 0x00110101UL;
        // RSCAN0.CFCC5.UINT32 = 0x00110101UL;
        break;
    }
    default:
        break;
    }
}

/*************************************************
函数名称: MCUCanSendData
函数功能: CAN发送数据，这个函数是MCU发送CAN数据的接口，然后再将CAN数据回传给4G模块
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: guoyuchen
编写日期 :2024/3/11
*************************************************/
bool MCUCanSendData(UINT8 channel, UINT32 canId, UINT8 canDlc, UINT8 *buf)
{
    /*
        云途重构版本
        发送步骤：
        1.初始化CAN控制器，这一步应该在MCU启动时的canTask完成
        2.检查是否可以发送，在配置can数据前，是否有前置条件，比如休眠的情况，这里我根据rh850的源码只判断了PmBplusNotSendCan
        3.遍历对应CAN通道的邮箱，检查是否空闲，如果空闲则组装数据，并调用发送函数
        重构步骤：
        1. 注释掉之前的部分
        2. 不修改接口名，只将内部逻辑替换为云途的接口
    */
    status_t            status   = STATUS_SUCCESS;  // 状态信息
    int                 CAN_INST = channel;         // CAN实例,应该对应传入的channel
    flexcan_msgbuff_t   txMsg;                      // 发送数据
    const UINT32        TX_MSG_ID   = canId;        // 发送ID
    UINT8               TX_MB_START = 0;            // 发送邮箱起始位置
    flexcan_data_info_t txMbStdInfo = {
        .msg_id_type = FLEXCAN_MSG_ID_EXT,
        .data_length = 8,
        .fd_enable   = false,
        .fd_padding  = 0,
        .enable_brs  = false,
        .is_remote   = false,
    };
    flexcan_user_config_t canConfig;

    // MCU B+低电不发CAN数据
    if (1 == PmBplusNotSendCan())
    {
        return false;
    }

    // 检查当前CAN状态
    if (FALSE == CanIf_SendStatusCheck(channel))
    {
        return false;
    }

    // 3.遍历邮箱，找到空闲邮箱配置发送数据
    // 云途接口层使用4路CAN通道
    // board\can_config.c: 配置信息为flexcan1_InitConfig，flexcan2_InitConfig，flexcan3_InitConfig，flexcan5_InitConfig，分别对应CAN通道1，2，3，4
    // 3.1映射查找CAN通道对应的配置信息
    CAN_INST = Can_GetCanInstance(channel);
    switch (channel)
    {
    case CAN_CHANNEL_1: {
        /*前1个邮箱 TP层占用，前16个是接收邮箱*/
        TX_MB_START = 17;
        canConfig   = flexcan1_InitConfig;
        break;
    }
    case CAN_CHANNEL_2: {
        /*前1个邮箱 TP层占用，前16个是接收邮箱*/
        TX_MB_START = 17;
        canConfig   = flexcan2_InitConfig;
        break;
    }
    case CAN_CHANNEL_3: {
        /*前2个邮箱 TP层占用，前17个是接收邮箱*/
        TX_MB_START = 17;
        canConfig   = flexcan3_InitConfig;
        break;
    }
    case CAN_CHANNEL_4: {
        /*前1个邮箱 TP层占用，前7个是接收邮箱*/
        TX_MB_START = 26;
        canConfig   = flexcan5_InitConfig;
        break;
    }
    default: {
        return false;
    }
    }

    // 3.2遍历邮箱，找到空闲邮箱配置发送数据
    for (int i = TX_MB_START; canConfig.max_num_mb > i; i++)
    {
        // 3.2.1检查邮箱是否空闲
        status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, i);
        if (STATUS_SUCCESS == status)
        {
            // 3.2.2配置发送邮箱
            status = FLEXCAN_DRV_ConfigTxMb(CAN_INST, i, &txMbStdInfo, TX_MSG_ID);
            if (status != STATUS_SUCCESS)
            {
                continue;
            }

            // 3.2.3准备要发送的数据
            txMsg.dataLen = canDlc;
            for (int i = 0; i < canDlc; i++)
            {
                txMsg.data[i] = buf[i];
            }

            // 3.2.4检查消息缓冲区状态，调用这个函数来确定当前收发进度是在进行中（忙）还是已完成（成功）
            status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, i);
            // 3.2.5如果空闲则发送数据
            if (STATUS_SUCCESS == status)
            {
                // 3.2.6发送数据
                status = FLEXCAN_DRV_Send(CAN_INST, i, &txMbStdInfo, TX_MSG_ID, txMsg.data);
                if (STATUS_SUCCESS == status)
                {
                    CanLogWriteTxBuf(channel, canId, canDlc, buf);
                    return true;
                }
                else
                {

                }
            }
            else
            {
                return false;
            }
        }
    }

    return false;

}

/*************************************************
函数名称: MCUCanSendData
函数功能: CAN发送数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
bool MCUCanSendFtmData(UINT8 channel, UINT32 canId, UINT8 canDlc, UINT8 *buf)
{
    /*
        云途重构版本
        发送步骤：
        1.初始化CAN控制器，这一步应该在MCU启动时的canTask完成
        2.检查是否可以发送，在配置can数据前，是否有前置条件，比如休眠的情况，这里我根据rh850的源码只判断了PmBplusNotSendCan
        3.遍历对应CAN通道的邮箱，检查是否空闲，如果空闲则组装数据，并调用发送函数
        重构步骤：
        1. 注释掉之前的部分
        2. 不修改接口名，只将内部逻辑替换为云途的接口
    */
    status_t            status   = STATUS_SUCCESS;  // 状态信息
    int                 CAN_INST = channel;         // CAN实例,应该对应传入的channel
    flexcan_msgbuff_t   txMsg;                      // 发送数据
    const UINT32        TX_MSG_ID   = canId;        // 发送ID
    UINT8               TX_MB_START = 0;            // 发送邮箱起始位置
    flexcan_data_info_t txMbStdInfo = {
        .msg_id_type = FLEXCAN_MSG_ID_STD,
        .data_length = 8,
        .fd_enable   = false,
        .fd_padding  = 0,
        .enable_brs  = false,
        .is_remote   = false,
    };
    flexcan_user_config_t canConfig;

    // 2.发送前要检查是否可以发送
    // PmBplusNotSendCan功能为“MCU B+低电不发CAN数据”
    // if (1 == PmBplusNotSendCan())
    // {
    //     return false;
    // }

    // 通过检查 CAN 控制器的模式和 PDU 模式来进行判断
    // if (FALSE == CanIf_SendStatusCheck(channel))
    // {
    //     return false;
    // }

    // 3.遍历邮箱，找到空闲邮箱配置发送数据
    // 云途接口层使用4路CAN通道
    // board\can_config.c: 配置信息为flexcan1_InitConfig，flexcan2_InitConfig，flexcan3_InitConfig，flexcan5_InitConfig，分别对应CAN通道1，2，3，4
    // 3.1映射查找CAN通道对应的配置信息
    CAN_INST = Can_GetCanInstance(channel);
    switch (channel)
    {
    case CAN_CHANNEL_1: {
        /*前1个邮箱 TP层占用，前16个是接收邮箱*/
        TX_MB_START = 17;
        canConfig   = flexcan1_InitConfig;
        break;
    }
    case CAN_CHANNEL_2: {
        /*前1个邮箱 TP层占用，前16个是接收邮箱*/
        TX_MB_START = 17;
        canConfig   = flexcan2_InitConfig;
        break;
    }
    case CAN_CHANNEL_3: {
        /*前2个邮箱 TP层占用，前17个是接收邮箱*/
        TX_MB_START = 19;
        canConfig   = flexcan3_InitConfig;
        break;
    }
    case CAN_CHANNEL_4: {
        /*前1个邮箱 TP层占用，前16个是接收邮箱*/
        TX_MB_START = 17;
        canConfig   = flexcan5_InitConfig;
        break;
    }
    default: {
        return false;
    }
    }

    // 3.2遍历邮箱，找到空闲邮箱配置发送数据
    for (int i = TX_MB_START; canConfig.max_num_mb > i; i++)
    {
        // 3.2.1检查邮箱是否空闲
        status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, i);
        if (STATUS_SUCCESS == status)
        {
            // 3.2.2配置发送邮箱
            status = FLEXCAN_DRV_ConfigTxMb(CAN_INST, i, &txMbStdInfo, TX_MSG_ID);
            if (status != STATUS_SUCCESS)
            {
                continue;
            }

            // 3.2.3准备要发送的数据
            txMsg.dataLen = canDlc;
            for (int i = 0; i < canDlc; i++)
            {
                txMsg.data[i] = buf[i];
            }

            // 3.2.4检查消息缓冲区状态，调用这个函数来确定当前收发进度是在进行中（忙）还是已完成（成功）
            status = FLEXCAN_DRV_GetTransferStatus(CAN_INST, i);
            // 3.2.5如果空闲则发送数据
            if (STATUS_SUCCESS == status)
            {
                // 3.2.6发送数据
                status = FLEXCAN_DRV_Send(CAN_INST, i, &txMbStdInfo, TX_MSG_ID, txMsg.data);
                if (STATUS_SUCCESS == status)
                {
                    CanLogWriteTxBuf(channel, canId, canDlc, buf);
                    return true;
                }
            }
            else
            {
                return false;
            }
        }
    }

#if (LOG_SWITCH_CONFIG_DEBUG == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can is busy\r\n");
#endif
    return FALSE;
}

/*************************************************
函数名称: McuCanGetMailData
函数功能: CAN接收获取邮箱相应数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/09/21
*************************************************/
UINT8 McuCanGetMailData(UINT8 channel, CanPduInfo *pPduInfo)
{
    UINT8 checkSum = 0;

    pPduInfo->channel = channel;
    pPduInfo->time    = g_osCurrentTickTime;
    switch (MCU_CHIP_TYPE)
    {
    case CHIP_RH850:
    default: {
        volatile Can_RegType *CanRegs = (volatile Can_RegType *)CAN_BASE_REG;
        pPduInfo->id                  = (UINT32)(CanRegs->RxFifo[channel].RFID & 0xffffffff);
        pPduInfo->dlc                 = (UINT8)(CanRegs->RxFifo[channel].RFPTR >> 28);
        pPduInfo->data[0]             = (UINT8)CanRegs->RxFifo[channel].RFDF0;
        pPduInfo->data[1]             = (UINT8)(CanRegs->RxFifo[channel].RFDF0 >> 8);
        pPduInfo->data[2]             = (UINT8)(CanRegs->RxFifo[channel].RFDF0 >> 16);
        pPduInfo->data[3]             = (UINT8)(CanRegs->RxFifo[channel].RFDF0 >> 24);
        pPduInfo->data[4]             = (UINT8)CanRegs->RxFifo[channel].RFDF1;
        pPduInfo->data[5]             = (UINT8)(CanRegs->RxFifo[channel].RFDF1 >> 8);
        pPduInfo->data[6]             = (UINT8)(CanRegs->RxFifo[channel].RFDF1 >> 16);
        pPduInfo->data[7]             = (UINT8)(CanRegs->RxFifo[channel].RFDF1 >> 24);
    }
    case CHIP_YTM32B1ME05G0MLQT: {
        // 在上层处理了
    }
    }
    checkSum = channel;
    checkSum = checkSum + (UINT8)(pPduInfo->time >> 24);
    checkSum = checkSum + (UINT8)(pPduInfo->time >> 16);
    checkSum = checkSum + (UINT8)(pPduInfo->time >> 8);
    checkSum = checkSum + (UINT8)(pPduInfo->time >> 0);
    checkSum = checkSum + (UINT8)(pPduInfo->id >> 24);
    checkSum = checkSum + (UINT8)(pPduInfo->id >> 16);
    checkSum = checkSum + (UINT8)(pPduInfo->id >> 8);
    checkSum = checkSum + (UINT8)(pPduInfo->id >> 0);
    checkSum = checkSum + pPduInfo->dlc;
    checkSum = checkSum + pPduInfo->data[0];
    checkSum = checkSum + pPduInfo->data[1];
    checkSum = checkSum + pPduInfo->data[2];
    checkSum = checkSum + pPduInfo->data[3];
    checkSum = checkSum + pPduInfo->data[4];
    checkSum = checkSum + pPduInfo->data[5];
    checkSum = checkSum + pPduInfo->data[6];
    checkSum = checkSum + pPduInfo->data[7];

    return checkSum;
}

/*************************************************
函数名称: CanNmAnalyzeData
函数功能: CAN网络管理信号确认并处理
输入参数: channel，pduInfo
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/04/11
*************************************************/
void CanNmAnalyzeData(UINT8 channel, CanPduInfo pduInfo)
{
    // UINT16      HwObjId    = 0;
    // UINT16      EndHwObjId = 0;
    PduInfoType nmPduInfo;

    if((CAN_CHANNEL_1 != channel) || (0x600 > pduInfo.id) || (pduInfo.id > 0x67F))
    {
        return;
    }

    nmPduInfo.SduLength = pduInfo.dlc;
    nmPduInfo.SduDataPtr = &pduInfo.data[0];

    #if defined(CAN_ENABLE_OSEKNM)
    /**
     * @brief OSEK NM模式下的网络管理数据处理
     *
     * OSEK NM模式下可能需要特殊的数据处理逻辑
     */
    /* OSEK NM模式下的处理逻辑 */
    #elif defined(CAN_ENABLE_AUTOSAR_NM)
    /**
     * @brief AUTOSAR NM模式下的网络管理数据处理
     *
     * 调用AUTOSAR CAN网络管理的接收指示函数
     */
    CanNm_RxIndication(NM_NETWORK_CHANNEL_ID, &nmPduInfo);
    #elif defined(CAN_ENABLE_NO_NM)
    /**
     * @brief 无网管模式下的数据处理
     *
     * 在无网络管理模式下，不进行网络管理相关的数据处理
     */
    /* 无网管模式下的空实现 */
    #else
    /**
     * @brief 默认情况使用AUTOSAR NM
     */
    CanNm_RxIndication(NM_NETWORK_CHANNEL_ID, &nmPduInfo);
    #endif

    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "Got can:%x-%02x-%02x\r\n", pduInfo.id, pduInfo.data[0], pduInfo.data[1]);
    // EndHwObjId = (uint16)(CAN_CNTRL_CFG(channel).CanRxHwObjFirst + CAN_CNTRL_CFG(channel).CanRxHwObjCount);
    // for (HwObjId = CAN_CNTRL_CFG(channel).CanRxHwObjFirst; HwObjId < EndHwObjId; HwObjId++)
    // {
    //     if(CAN_HWOBJ_CFG(HwObjId).CanIdValue == pduInfo.id)
    //     {
    //         SystemApiLogPrintf(LOG_INFO_OUTPUT, "Got nm can:%x-%02x-%02x\r\n", pduInfo.id, pduInfo.data[0], pduInfo.data[1]);
    //         CanIf_RxIndication(CAN_HWOBJ_CFG(HwObjId).CanObjectId, pduInfo.id, pduInfo.dlc, pduInfo.data);
    //         break;
    //     }
    // }
}

/*************************************************
函数名称: MCUCanRxFifoData
函数功能: 接收CAN数据并存于缓存中，这是中断的回调函数
编写者: zhengyong
编写日期 :2024/04/12
*************************************************/
void MCUCanRxFifoData(UINT32 channel, flexcan_msgbuff_t rxfifoMsg)
{
    if(g_canMsgInfo.readIndex == g_canMsgInfo.writeIndex + 1 || (g_canMsgInfo.writeIndex == MAX_CAN_BUFFER_COUNT-1 && g_canMsgInfo.readIndex == 0))
    {
        g_canMsgInfo.lostTotal++;
        return;
    }

    g_canMsgInfo.canData[g_canMsgInfo.writeIndex].channel = channel;
    memcpy(&g_canMsgInfo.canData[g_canMsgInfo.writeIndex].canMsg, &rxfifoMsg, sizeof(flexcan_msgbuff_t));
    g_canMsgInfo.writeIndex++;
    g_canMsgInfo.receiveTotal++;
    if(g_canMsgInfo.writeIndex >= MAX_CAN_BUFFER_COUNT)
    {
        g_canMsgInfo.writeIndex = 0;
    }

#if defined(CAN_ENABLE_OSEKNM)
    /**
     * @brief OSEK NM模式的CAN消息接收处理逻辑
     *
     * 当CAN通道为CAN_CHANNEL_3且网管状态≤NM_STATE_BUS_SLEEP时，
     * 检查接收到的CAN消息ID是否包含0x400标志位，并根据数据字节1的特定位模式
     * 决定是打印日志还是调用OsekNm_UserHandleEvenSet函数
     */
    if(CAN_CHANNEL_3 == channel
    && g_commonInfo.Nm_State <= NM_STATE_BUS_SLEEP
    && (rxfifoMsg.msgId & 0x400) == 0x400
    && ((0x0010u&OsekNm_GetCurrentStatus())!= 0x0010u))
    {
        if((0x10 == (rxfifoMsg.data[1]&0x10)) || (0x20 == (rxfifoMsg.data[1]&0x20)))
        {
            // SystemApiLogPrintf(LOG_INFO_OUTPUT, "int nm net request,xxxxxx\r\n");
        }
        else
        {
            OsekNm_UserHandleEvenSet(HANDLE_REQUEST_ALIVE);
        }
    }
#elif defined(CAN_ENABLE_AUTOSAR_NM)
    /**
     * @brief AUTOSAR NM模式的CAN消息接收处理逻辑
     *
     * 获取网管状态，当状态≤NM_STATE_READY_SLEEP时：
     * - 对NM消息ID范围内的消息调用AutoNm_NetworkRequest()
     * - 对其他消息在ACC OFF且网管状态≤NM_STATE_BUS_SLEEP时调用PmCanIntStbFunction()
     */
    uint8_t status = 0;
    uint8_t mode = 0;
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    if(NM_STATE_READY_SLEEP >= status)
    {
        if((rxfifoMsg.msgId >= NM_MESSAGE_ID_RANGE_START) && (rxfifoMsg.msgId <= NM_MESSAGE_ID_RANGE_END))
        {
            AutoNm_NetworkRequest();
        }
        else
        {
            /* ACC OFF状态下无法接收应用报文，返回错误帧 */
            if((WORK_STATUS_INACTIVE == g_commonInfo.accStatus) && (NM_STATE_BUS_SLEEP >= status))
            {
                PmCanIntStbFunction();
            }
        }
    }
#elif defined(CAN_ENABLE_NO_NM)
    /**
     * @brief 无网管模式
     *
     * 在无网络管理模式下，不进行任何网络管理相关的处理
     */
    /* 无网管模式下的空实现 */
#else
    /**
     * @brief 默认情况（未定义任何网管模式）
     *
     * 如果没有定义任何网络管理模式，则不进行处理
     */
    /* 默认情况下的空实现 */
#endif
}

/*************************************************
函数名称: MCUCanRxFifoData
函数功能: CAN处理数据处理，用于处理缓存的CAN控制器接收FIFO中的数据
函数描述:
    - 这个C函数的主要功能是处理CAN总线控制器接收FIFO中的数据。它首先初始化了指向CAN寄存器结构体的指针以及一些必要的全局变量。然后遍历所有CAN通道，对于每个通道：
        1.检查是否有新的接收消息。
        2.如果有新消息，则获取未读消息的数量，并逐条处理这些消息。
        3.对于每条消息，调用McuCanGetMailData函数获取数据并计算校验和，然后分析数据帧内容。
        4.根据接收功能的状态和当前通道判断是否需要进一步处理数据（如记录日志、更新车辆信息等）。
        5.清除已处理完的消息。
    - 最后，在处理完该通道的所有消息后，清除新消息标志位。
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: guoyuchen
编写日期 :2024/3/15
*************************************************/
void MCUCanProcessData(UINT32 channel, flexcan_msgbuff_t rxfifoMsg)
{
    // 定义局部变量
    UINT8      checkSum    = 0;        // 数据帧校验和
    CanPduInfo pduInfo;                // 存储接收到的CAN报文信息的数据结构

    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "Rx length= %d, id = %x, data = %x %x %x %x %x %x %x %x\r\n", rxfifoMsg.dataLen, rxfifoMsg.msgId, rxfifoMsg.data[0], rxfifoMsg.data[1], rxfifoMsg.data[2], rxfifoMsg.data[3], rxfifoMsg.data[4], rxfifoMsg.data[5], rxfifoMsg.data[6], rxfifoMsg.data[7]);
    pduInfo.dlc     = rxfifoMsg.dataLen;
    pduInfo.id      = rxfifoMsg.msgId;
    pduInfo.data[0] = rxfifoMsg.data[0];
    pduInfo.data[1] = rxfifoMsg.data[1];
    pduInfo.data[2] = rxfifoMsg.data[2];
    pduInfo.data[3] = rxfifoMsg.data[3];
    pduInfo.data[4] = rxfifoMsg.data[4];
    pduInfo.data[5] = rxfifoMsg.data[5];
    pduInfo.data[6] = rxfifoMsg.data[6];
    pduInfo.data[7] = rxfifoMsg.data[7];
    pduInfo.time    = g_osCurrentTickTime;

    // 从当前通道获取邮件数据并计算校验和
    checkSum = McuCanGetMailData(channel, &pduInfo);

    // 检查接收到的数据长度是否在允许范围内
    if (8 >= pduInfo.dlc)
    {
        // 分析接收到的CAN数据帧
        CanMsgAnalyzeData(channel, pduInfo);
        //节点状态维护
        WriteVehicleInfoToAnalyze(channel,pduInfo);
        if ((g_commonInfo.udsCtrlNmPduStatus & 0x02) == 0x00)
        {
            CanNmAnalyzeData(channel, pduInfo);
        }

        // 判断接收功能是否开启，目前所有CAN都走第四路，所以取消了CAN通道的判断
        if (((g_commonInfo.udsCtrlNormPduStatus & 0x02) == 0x00) || (0x700 == (pduInfo.id & 0x700)))
        {
            // 记录校验和及CAN报文信息到日志
            CanLogWriteBuf(checkSum, pduInfo);
        }
    }
}

/*************************************************
函数名称: MCUCanTxFifoData
函数功能: 处理CAN buffer中的数据
编写者: zhengyong
编写日期 :2024/04/12
*************************************************/
void McuProcessCanBuferData(void)
{
    static uint16_t logCount = 0;

    while(g_canMsgInfo.readIndex != g_canMsgInfo.writeIndex)
    {
        MCUCanProcessData(g_canMsgInfo.canData[g_canMsgInfo.readIndex].channel, g_canMsgInfo.canData[g_canMsgInfo.readIndex].canMsg);
        g_canMsgInfo.readIndex++;
        g_canMsgInfo.processTotal++;
        if(g_canMsgInfo.readIndex >= MAX_CAN_BUFFER_COUNT)
        {
            g_canMsgInfo.readIndex = 0;
        }

        if(logCount++ > 30000)
        {
            logCount = 0;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Can data receive:%u, process:%u, lost:%u\r\n", g_canMsgInfo.receiveTotal, g_canMsgInfo.processTotal, g_canMsgInfo.lostTotal);
        }
    }
}

/*************************************************
函数名称: MCUCanTxFifoData
函数功能: CAN发送/接收FIFO数据处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/11/23
*************************************************/
void MCUCanTxFifoData(UINT8 channel, UINT32 canId, UINT8 canDlc, UINT8 *buf)
{
    /*
    // 检查FIFO是否为满
    if (RSCAN0.CFSTS3.UINT32 & 0x01)
    {
        RSCAN0.CFCC3.UINT32   = 0x64152101;
        RSCAN0.CFID3.UINT32   = canId;
        RSCAN0.CFPTR3.UINT32  = (canDlc << 28);
        RSCAN0.CFDF03.UINT32  = *((uint32_t *)&buf[0]);
        RSCAN0.CFDF13.UINT32  = *((uint32_t *)&buf[0]);
        RSCAN0.CFSTS3.UINT32  = 0x10;
        RSCAN0.CFPCTR3.UINT32 = 0xff;
    }
    else
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can tx status is 0x%x\r\n", RSCAN0.CFSTS3.UINT32);
    }
    */
}

/*************************************************
函数名称: MCUCanTxFifoIsr
函数功能: CAN发送/接收FIFO数据发送完成中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/11/23
*************************************************/
void MCUCanTxFifoIsr(void)
{
    /*
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "can tx isr is 0x%x\r\n", RSCAN0.CFSTS3.UINT32);
    if (RSCAN0.CFSTS3.UINT32 & 0x8UL)
    {
        RSCAN0.CFSTS3.UINT32 &= ~0x8UL;
    }
    */
}

/*************************************************
函数名称: MCUCanCheckStatus
函数功能: 判断CAN接收状态投票休眠
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: heyuhui
编写日期 :2024/12/27
*************************************************/
void MCUCanCheckStatus(void)
{
    static uint32_t lastCanCnt = 0;
    static uint16_t CanTimeCnt = 0;
    uint32_t curCanCnt = g_canMsgInfo.receiveTotal;

    if (curCanCnt > lastCanCnt)
    {
        g_commonInfo.canStatus = WORK_STATUS_ACTIVE;
        lastCanCnt = curCanCnt;
        CanTimeCnt = 0;
    }
    else if(CanTimeCnt++ >= 100)
    {
        g_commonInfo.canStatus = WORK_STATUS_INACTIVE;
        CanTimeCnt = 0;
    }
}