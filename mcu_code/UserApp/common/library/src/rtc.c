/*
      rtc.c 
      读和写RTC寄存器函数接口，设置和读取时间函数接口
描述：
作者：廖勇刚
时间：2016.9.13
*/
#include "FreeRTOS.h"
#include "rtc.h"
#include "LogApi.h"
#include "event.h"
//#include "rtc_driver.h"
#include "SystemApi.h"
#include "r_iic.h"
#include "status.h"

/************************外部全局变量****************************/
extern I2cRam  g_i2cRam;


/*************************************************
函数名称: RtcReadRegister
函数功能: 读取RTC寄存器数据
输入参数: 寄存器值
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void RtcReadRegister(UINT8 reg,  UINT8 *buf,  UINT8 len)
{
    UINT32 count = 0;
    
    if((NULL == buf)||(0x11 < reg) ||(0x11 < len))
    {
        return;
    }
    
    //McuI2cMasterSend(RTC_SLAVE_ADDR, 1, &reg);
    //McuI2cMasterReceive(len, buf);
    /*
    do
    {
        count++;
    }while ((g_i2cRam.state != IDLE_STATE )&&(0x20000 >= count));
    */
    if(0x20000 == count)
    {
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "rtc read time out");
        #endif
    }
    return;
}

/*************************************************
函数名称: RtcWriteRegister
函数功能: 设置RTC寄存器数据
输入参数: 设置寄存器 数据和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void RtcWriteRegister(UINT8 *buf,  UINT8 len)
{
    if((NULL == buf)||(0x11 < len))
    {
        return;
    }

    McuI2cMasterSend(RTC_SLAVE_ADDR, len, buf);
}

/*************************************************
函数名称: RtcGetDateTime
函数功能: 获取日期时间等
输入参数: 时间结构指针
输出参数: RTC获取到的当前时间
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/05/10
*************************************************/
void RtcGetDateTime(RtcTime *rtcTime)
{
    //TboxSystemTimeStruct *pTimeStruct = TboxSystemTimeInfoRead();
     
    /*
    rtcDateTime.seconds = pTimeStruct->currentTime.second;
    rtcDateTime.minutes = pTimeStruct->currentTime.minute;
    rtcDateTime.hour    = pTimeStruct->currentTime.hour;
    rtcDateTime.day     = pTimeStruct->currentTime.day;
    rtcDateTime.month   = pTimeStruct->currentTime.month;
    rtcDateTime.year    = pTimeStruct->currentTime.year;
    */
#ifdef USE_INTERNAL_RTC
    rtc_timedate_t rtcDateTime = {0};
    RTC_DRV_GetCurrentTimeDate(0, &rtcDateTime);
    rtcTime->second = rtcDateTime.seconds;
    rtcTime->minute = rtcDateTime.minutes;
    rtcTime->hour = rtcDateTime.hour;
    rtcTime->day = rtcDateTime.day;
    rtcTime->month = rtcDateTime.month;
    rtcTime->year = rtcDateTime.year;
    /*
    if(rtcTime->year < PROJECT_INITIATE_YEAR)
    {
        rtcTime->year = PROJECT_INITIATE_YEAR;
        rtcTime->month = PROJECT_INITIATE_MONTH;
        rtcDateTime.year = PROJECT_INITIATE_YEAR;
        rtcDateTime.month = PROJECT_INITIATE_MONTH;
        
        //as the RTC time is NOT correct, so need to update
        RTC_DRV_StopCounter(0);
        RTC_DRV_SetTimeDate(0, &rtcDateTime);
        RTC_DRV_StartCounter(0);
    }
    
    */

    /*
    rtcTime->second = Bin2Tobcd(rtcDateTime.seconds);
    rtcTime->minute = Bin2Tobcd(rtcDateTime.minutes);
    rtcTime->hour = Bin2Tobcd(rtcDateTime.hour);
    rtcTime->day = Bin2Tobcd(rtcDateTime.day);
    rtcTime->month = Bin2Tobcd(rtcDateTime.month);
    if(rtcDateTime.year > 2000)
    {
        //rtcTime->year = Bin2Tobcd(rtcDateTime.year - 2000); //内部RTC没有偏移
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC get date time found year=%d,adjusted to year:%d \r\n", rtcDateTime.year, rtcTime->year);
    }
    else
    {
        rtcTime->year = Bin2Tobcd(rtcDateTime.year); //还未设置时间
    }
    */
    
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC get time: y=%d,m=%d,d=%d,h=%d,m=%d,s=%d. \r\n", rtcTime->year, rtcTime->month, rtcTime->day, rtcTime->hour, rtcTime->minute, rtcTime->second);
    
#else
    RtcReadRegister(PCF85063_REG_SC, (UINT8 *)rtcTime, sizeof(RtcTime));
    rtcTime->second = (rtcTime->second&0x7f);
    rtcTime->minute = (rtcTime->minute&0x7f);
    rtcTime->hour   = (rtcTime->hour&0x3f);
    rtcTime->day    = (rtcTime->day&0x3f);
    rtcTime->weekday= (rtcTime->weekday&0x07);
    rtcTime->month  = (rtcTime->month&0x1f);
#endif
    return;
}

/*************************************************
函数名称: RtcRecoverDateTime
函数功能: 重新计算恢复日期时间
输入参数: 之前的时间
输出参数: 计算后的时间
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/29
*************************************************/
void RtcRecoverDateTime(RtcTime *rtcTime)
{
    if(NULL != rtcTime)
    {
        rtc_timedate_t rtcDateTime = {0};
        RTC_DRV_GetCurrentTimeDate(0, &rtcDateTime);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup Sync: year:%d, month:%d, day:%d, hour:%02d, minute:%02d, second:%02d\r\n", rtcDateTime.year, rtcDateTime.month, rtcDateTime.day, rtcDateTime.hour, rtcDateTime.minutes, rtcDateTime.seconds);

        TboxSystemTimeStruct *pTimeStruct = TboxSystemTimeInfoRead();
        pTimeStruct->currentTime.second = rtcDateTime.seconds;
        pTimeStruct->currentTime.minute = rtcDateTime.minutes;
        pTimeStruct->currentTime.hour   = rtcDateTime.hour;
        pTimeStruct->currentTime.day    = rtcDateTime.day;
        pTimeStruct->currentTime.month  = rtcDateTime.month;
        pTimeStruct->currentTime.year   = rtcDateTime.year-2000;
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Before Recover time, y=%d,m=%d,d=%d,h=%d,m=%d,s=%d. \r\n", rtcDateTime.year, rtcDateTime.month, rtcDateTime.day, rtcDateTime.hour, rtcDateTime.minutes, rtcDateTime.seconds);
        //AdjustRtcTime(&rtcDateTime);
        
        rtcTime->year = rtcDateTime.year;
        rtcTime->month = rtcDateTime.month;
        rtcTime->day = rtcDateTime.day;
        rtcTime->hour = rtcDateTime.hour;
        rtcTime->minute = rtcDateTime.minutes;
        rtcTime->second = rtcDateTime.seconds;
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "RTC recover time passed with NULL param.\r\n");
    }
}

/*************************************************
函数名称: AdjustRtcTime
函数功能: 获取日期时间等
输入参数: rtcTime
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void AdjustRtcTime(rtc_timedate_t *rtcTime)
{
    if(NULL != rtcTime)
    {
        rtc_timedate_t rtcDateTime = {0};
        //RtcTime rtcLocalTime;
        
        UINT32 secondsRtc = 0;
        UINT32 secondsPrevious = 0;
        UINT32 secondsDiff = 0;
        RTC_DRV_GetCurrentTimeDate(0, &rtcDateTime);
        *rtcTime = rtcDateTime;
        /*
        RtcGetDateTime(&rtcLocalTime);
        rtcDateTime.year = rtcLocalTime.year;
        rtcDateTime.month = rtcLocalTime.month;
        rtcDateTime.day = rtcLocalTime.day;
        rtcDateTime.hour = rtcLocalTime.hour;
        rtcDateTime.minutes = rtcLocalTime.minute;
        rtcDateTime.seconds = rtcLocalTime.second;

        //convert the datetime into seconds
        RTC_DRV_ConvertTimeDateToSeconds(&rtcDateTime, &secondsRtc);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Before Adjust RTC time,seconds=%u. \r\n", secondsRtc);
        
        //to convert rtcTime into seconds
       
        RTC_DRV_ConvertTimeDateToSeconds(rtcTime, &secondsPrevious);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Before sleep time, y=%d, m=%d, day=%d, hour=%d, minute=%d, second=%d, totoal seconds=%u. \r\n", rtcDateTime.year, rtcDateTime.month, rtcDateTime.day, rtcDateTime.hour, rtcDateTime.minutes, rtcDateTime.seconds, secondsPrevious);
        secondsDiff = abs(secondsRtc - secondsPrevious);
        secondsPrevious += secondsDiff;
        RTC_DRV_ConvertSecondsToTimeDate(&secondsPrevious, rtcTime);
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "After Adjust RTC time, year=%d, month=%d, day=%d, hour=%d, minute=%d, second=%d, secondDiff=%d. \r\n", rtcTime->year, rtcTime->month, rtcTime->day, rtcTime->hour, rtcTime->minutes, rtcTime->seconds, secondsDiff);
       */
        
    }
}

/*************************************************
函数名称: RtcGetDateTime
函数功能: 设置日期时间等
输入参数: 设置RTC时间值
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/27
*************************************************/
void RtcSetDateTime(RtcTime rtcTime)
{
#ifdef USE_INTERNAL_RTC
    status_t ret = 0;
    rtc_timedate_t rtcDateTime = {0};

    rtcDateTime.seconds = rtcTime.second;
    rtcDateTime.minutes = rtcTime.minute;
    rtcDateTime.hour = rtcTime.hour;
    rtcDateTime.day = rtcTime.day;
    rtcDateTime.month = rtcTime.month;
    rtcDateTime.year = rtcTime.year;        // + 2000; 云途MCU内部RTC无偏移，且采用uint16_t存储，故此处不需要加2000
    ret = RTC_DRV_StopCounter(0);
    if(STATUS_SUCCESS == ret)
    {
        ret = RTC_DRV_SetTimeDate(0, &rtcDateTime);
        ret |= RTC_DRV_StartCounter(0);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Set date time:%d-%02d-%02d %02d:%02d:%02d, ret=%d\r\n",
         rtcDateTime.year, rtcDateTime.month, rtcDateTime.day, rtcDateTime.hour, rtcDateTime.minutes, rtcDateTime.seconds, ret);
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Set date time failed as the RTC stop failed.:%d-%02d-%02d %02d:%02d:%02d\r\n",
         rtcDateTime.year, rtcDateTime.month, rtcDateTime.day, rtcDateTime.hour, rtcDateTime.minutes, rtcDateTime.seconds);
    }
    
#else
    UINT8 buf[8] = {0};

    memset(buf, 0x00, 8);
    buf[0] = PCF85063_REG_SC;
    memcpy(buf + 1, (UINT8*)&rtcTime, sizeof(RtcTime));

    RtcWriteRegister(buf, 8);
#endif
    return;
}


