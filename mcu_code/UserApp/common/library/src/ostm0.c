/*
      ostm0.c
描述：此文件包括OSTM0初始化和开始函数
作者：廖勇刚
时间：2016.9.30
*/
#include "FreeRTOS.h"
#include "ostm0.h"
#include "iodefine.h"








/*************************************************
函数名称: McuOstm0Init
函数功能: os tick初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void McuOstm0Init(void)
{
    //DEBUG 调试 OS TICK停止
    OSTM0.EMU = 0x00;

    //计数开始后中断产生
    OSTM0.CTL = 0x01;

    //配置为1MS
    //OSTM0.CMP = 39940; 
    OSTM0.CMP = 39999; 
    
    //配置中断
    MKOSTM0   = 1U;
    RFOSTM0   = 0U;
    TBOSTM0   = 0U;

}

/*************************************************
函数名称: McuOstm0Start
函数功能: os tick开始
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/30
*************************************************/
void McuOstm0Start(void)
{
    //开启计数器
    OSTM0.TS = 0x01;

    //配置OS TICK中断
    MKOSTM0  = 0U; 
}
/*************************************************
函数名称: McuOstm0Stop
函数功能: os tick停止
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2019/03/11
*************************************************/
void McuOstm0Stop(void)
{
    //停止计数器
    OSTM0.TS = 0x00;
    MKOSTM0  = 1U; 
}
