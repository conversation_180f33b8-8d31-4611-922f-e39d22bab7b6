/*
      clock.c
描述：此文件包括配置芯片的时钟
作者：廖勇刚
时间：2016.10.8
*/



#include "clocks.h"
#include "iodefine.h"



/*************************************************
函数名称: McuClockInit
函数功能: Mcu时钟初始化配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/08
*************************************************/
void McuClockInit(void)
{
    /*
    //设置外部晶体16MHZ到20MHZ
    MOSCC=0x06;    //MOSCC=0x05;
    
    //设置外部晶体震荡稳定时钟时间8,19 ms
    MOSCST=0xFFFF;

    PROTCMD0=0xa5;
    MOSCE=0x01;
    MOSCE=~0x01;
    MOSCE=0x01;
    while ((MOSCS&0x07) != 0x7);

    /* Prepare PLL*/
    PLLC=0x00000a27;                      
    PROTCMD1=0xa5;
    PLLE=0x01;
    PLLE=~0x01;
    PLLE=0x01;
    while((PLLS&0x07) != 0x07);              

    //ISO CLK分频选择为1
    PROTCMD1=0xa5;
    CKSC_CPUCLKD_CTL=0x01;
    CKSC_CPUCLKD_CTL=~0x01;
    CKSC_CPUCLKD_CTL=0x01;
    while(CKSC_CPUCLKD_ACT!=0x01);

    //ISO CLK频率为MainOSC
    PROTCMD1=0xa5;
    CKSC_CPUCLKS_CTL=0x03;
    CKSC_CPUCLKS_CTL=~0x03;
    CKSC_CPUCLKS_CTL=0x03;
    while(CKSC_CPUCLKS_ACT!=0x03);

    //开始启动subOSC
    PROTCMD0=0xa5;
    SOSCE=0x01;
    SOSCE=~0x01;
    SOSCE=0x01;

    //TAUJ0 时钟配置
    do
    {
        PROTCMD0                  = 0x000000A5UL;    
        CKSC_ATAUJS_CTL           = 0x00000001UL;
        CKSC_ATAUJS_CTL           = ~0x00000001UL;
        CKSC_ATAUJS_CTL           = 0x00000001UL;
    } while ( PROTS0 != 0x00000000UL );
    while(CKSC_ATAUJS_ACT != 0x00000001UL)
    {}

    //TAUJ 时钟分频
    do
    {
        PROTCMD0                  = 0x000000A5UL;    
        CKSC_ATAUJD_CTL           = 0x00000001UL;
        CKSC_ATAUJD_CTL           = ~0x00000001UL;
        CKSC_ATAUJD_CTL           = 0x00000001UL;
    } while ( PROTS0 != 0x00000000UL );
    while( CKSC_ATAUJD_ACT != 0x00000001UL)
    {}

    do
    {
        PROTCMD1                   = 0x000000A5UL;   
        CKSC_IPERI1S_CTL           = 0x00000002UL;
        CKSC_IPERI1S_CTL           = ~0x00000002UL;
        CKSC_IPERI1S_CTL           = 0x00000002UL;
    } while ( PROTS1 != 0x00000000UL );
    while ( CKSC_IPERI1S_ACT != 0x00000002UL )
    {
    }
    
    do
    {
        PROTCMD1                   = 0x000000A5UL;   
        CKSC_IPERI2S_CTL           = 0x00000002UL;
        CKSC_IPERI2S_CTL           = ~0x00000002UL;
        CKSC_IPERI2S_CTL           = 0x00000002UL;
    } while ( PROTS1 != 0x00000000UL );
    while ( CKSC_IPERI2S_ACT != 0x00000002UL )
    {
    }
    
    //CSIH时钟分频
    do
    {
        PROTCMD1                   = 0x000000A5UL;   
        CKSC_ICSIS_CTL             = 0x00000002UL;       
        CKSC_ICSIS_CTL             = ~0x00000002UL;       
        CKSC_ICSIS_CTL             = 0x00000002UL;       
    } while ( PROTS1 != 0x00000000UL );
    while ( CKSC_ICSIS_ACT != 0x00000002UL )
    {
    }
}
