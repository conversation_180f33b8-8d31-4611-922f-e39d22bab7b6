/*
      Rtc.h
      读和写RTC寄存器函数接口，设置和读取时间函数接口头文件
描述：
作者：廖勇刚
时间：2016.9.26
*/

#ifndef  _RTC_H_
#define  _RTC_H_


//#include "Rtc.h"
#include "event.h"
#include "rtc_driver.h"


#define USE_INTERNAL_RTC
//#define RTC_ENBALE_BCD_TIME

/************************宏定义***************************/
#define PCF85063_REG_CTRL1      0x00 /* status */
#define PCF85063_REG_CTRL2      0x01
#define PCF85063_REG_OFFSET     0x02
#define PCF85063_REG_RAM        0x03


#define PCF85063_REG_SC         0x04 /* datetime */
#define PCF85063_REG_MN         0x05
#define PCF85063_REG_HR         0x06
#define PCF85063_REG_DM         0x07
#define PCF85063_REG_DW         0x08
#define PCF85063_REG_MO         0x09
#define PCF85063_REG_YR         0x0A

#define PCF85063_REG_AS         0x0B  /* alarmtime */
#define PCF85063_REG_AM         0x0C
#define PCF85063_REG_AH         0x0D
#define PCF85063_REG_AD         0x0E
#define PCF85063_REG_AW         0x0F




/************************数据结构定义***************************/
typedef struct rtcTime
{
    UINT8 second;
    UINT8 minute;
    UINT8 hour;
    UINT8 day;
    UINT8 weekday;
    UINT8 month;
    UINT16 year;
}RtcTime;

/************************函数接口***************************/
UINT8 BcdTo2bin(UINT8 val);
UINT8 Bin2Tobcd(UINT8 val);
void RtcReadRegister(UINT8 reg,  UINT8 *buf,  UINT8 len);
void RtcWriteRegister(UINT8 *buf,  UINT8 len);
void RtcGetDateTime(RtcTime *rtcTime);
void RtcSetDateTime(RtcTime rtcTime);
void AdjustRtcTime(rtc_timedate_t *rtcTime);
void RtcRecoverDateTime(RtcTime *rtcTime);
#endif
