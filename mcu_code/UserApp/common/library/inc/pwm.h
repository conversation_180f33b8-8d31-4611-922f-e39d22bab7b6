/*
      Pwm.h
描述：此文件包括Pwm配置，PWM开始函数，PWM设置函数，PWM停止函数头文件
作者：廖勇刚
时间：2016.9.26
*/


#ifndef  _PWM_H_
#define  _PWM_H_


#include "event.h"
#include <stdint.h>





/************************宏定义***************************/
#define    PWM_DUTY_CYCLE                           40.96
#define    PWM_LED_CHANNEL_RED_POWER                8    // PWGA8O
#define    PWM_LED_CHANNEL_GREEN_POWER              9    // PWGA9O
#define    PWM_LED_CHANNEL_RED_4G                   20   // PWGA20O
#define    PWM_LED_CHANNEL_GREEN_4G                 21   // PWGA21O
#define    PWM_LED_CHANNEL_RED_GNSS                 33   // PWGA330
#define    PWM_LED_CHANNEL_GREEN_GNSS               34   // PWGA34O
#define    PWM_LED_CHANNEL_9                        32   // PWGA32O
#define    PWM_LED_CHANNEL_GPS_PHY                  35    // PWGA35




/************************函数接口***************************/
void PwmInit(void);
void PwmChannelInit(UINT8 channel, UINT8 duty);
void PwmChannelStart(UINT8 channel);
void PwmChannelStop(UINT8 channel);
void SpeedPwmInit(uint16_t speed);
#endif
