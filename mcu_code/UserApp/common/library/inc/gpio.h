/*
      Gpio.h
描述：此文件包括gpio功能管脚配置，输出高低电平和重新初始化GPIO头文件
作者：廖勇刚
时间：2016.9.13
*/

#ifndef  _GPIO_H_
#define  _GPIO_H_

#include "event.h"
#include "YTM32B1ME0.h"
#include <stdbool.h>

/************************数据结构定义***************************/
typedef enum gpioIndex
{
    MCU_GPIO_AIRBAG_INPUT = 0,
    MCU_GPIO_REBOOT,
    MCU_GPIO_UART3_RX,
    MCU_GPIO_UART3_TX,
    MCU_GPIO_ARM_STATUS,
    MCU_GPIO_B_KEY_INPUT,
    MCU_GPIO_I_KEY_INPUT,
    MCU_GPIO_M_EXTAL,
    MCU_GPIO_M_XTAL,
    MCU_GPIO_IO_FWD_IO,
    MCU_GPIO_SOS_KEY_INPUT,
    MCU_GPIO_M_POWER_WIFI,
    MCU_GPIO_M_POWER_GNSSBACKUP,
    MCU_GPIO_M_EXTAL32,
    MCU_GPIO_M_XTAL32,
    MCU_GPIO_ACC,
    MCU_GPIO_M_RST_GNSS,
    MCU_GPIO_ARM_NET_STATUS_MCU,
    MCU_GPIO_BLE_STATUS_INT,
    MCU_GPIO_WAKEUP_ARM,
    MCU_GPIO_WHEEL_TICK_IO,
    MCU_GPIO_UART2_TX,
    MCU_GPIO_UART2_RX,
    MCU_GPIO_4G_LED_RED,
    MCU_GPIO_4G_LED_GREEN,
    MCU_GPIO_RTC_INT,
    MCU_GPIO_BLE_WAKEUP_CTL,
    MCU_GPIO_BLE_RST_CTL,
    MCU_GPIO_GNSS_ANT_SW,
    MCU_GPIO_CAN3_TX,
    MCU_GPIO_CAN3_RX,
    MCU_GPIO_RLIN21_TX,
    MCU_GPIO_RLIN21_RX,
    MCU_GPIO_BAT_VOLTAGE_DET,
    MCU_GPIO_MIC_VOLTAGE_DET,
    MCU_GPIO_BAT_NTC_DET,
    MCU_GPIO_RUN_VOLTAGE_DET,
    MCU_GPIO_M_TRANSLATOR_EN,
    MCU_GPIO_GNSS_ANT_OPEN,
    MCU_GPIO_GNSS_ANT_SHORT,
    MCU_GPIO_MAN_ANT_SW,
    MCU_GPIO_INFO_ARM_IO,
    MCU_GPIO_BAT_CHARGE_EN,
    MCU_GPIO_SENSOR_INT2,
    MCU_GPIO_SENSOR_INT1,
    MCU_GPIO_CAN3_STB,
    MCU_GPIO_CAN5_STB,
    MCU_GPIO_I2C_SCL,
    MCU_GPIO_I2C_SDA,
    MCU_GPIO_EN_VCC8V,
    MCU_GPIO_HANDSHAKE_LED_GREEN,
    MCU_GPIO_HANDSHAKE_LED_RED, 
    MCU_GPIO_BATTERY_CUTOFF,
    MCU_GPIO_M_PWR_ONOFF,
    MCU_GPIO_GPS_LED_GREEN,
    MCU_GPIO_CAN2_TX,
    MCU_GPIO_CAN2_RX,
    MCU_GPIO_GPS_LED_RED,
    MCU_GPIO_CAN5_TX,
    MCU_GPIO_CAN5_RX,
    MCU_GPIO_CAN2_STB,
    MCU_GPIO_CAN1_STB,
    MCU_GPIO_4V1_PWR_SW,
    MCU_GPIO_M_ECALL_BTN_BL,
    MCU_GPIO_LTE_ANT_SHORT,   
    MCU_GPIO_LTE_ANT_OPEN,
    MCU_GPIO_3V3_PWR_SW,
    MCU_GPIO_MUTE_OUTPUT,
    MCU_GPIO_CAN1_TX,
    MCU_GPIO_CAN1_RX,
    MCU_GPIO_ACC_OUT_CTL,
    MCU_GPIO_M_SOS_LED_RED,
    MCU_GPIO_RLIN21_SLP_N,
    MCU_GPIO_SCHG_STA_INPUT,
    MCU_GPIO_M_SOS_LED_GREEN,
    MCU_GPIO_M_SPI_CS,
    MCU_GPIO_M_SPI_CLK,
    MCU_GPIO_M_SPI_MISO,
    MCU_GPIO_M_SPI_MOSI,
    MCU_GPIO_FCHG_STA_INPUT,
    MCU_GPIO_M_WAKEUP_OUT,
    MCU_GPIO_ARM_SHDN_N,
    MCU_GPIO_M_SWDCLK,
    MCU_GPIO_ARM_RESET_MCU,
    MCU_GPIO_M_SWDIO,
    MCU_GPIO_ARM_RESET,
    MCU_GPIO_M_PRTRG_GNSS,
    MCU_MAX_NUM,
}GpioIndex;

typedef enum gpioConfig
{
    GPIO_PORT_INPUT = 0,
    GPIO_PORT_OUTPUT,
    GPIO_ALT_OUT1,
    GPIO_ALT_IN1,
    GPIO_ALT_OUT2,
    GPIO_ALT_IN2,
    GPIO_ALT_OUT3,
    GPIO_ALT_IN3,
    GPIO_ALT_OUT4,
    GPIO_ALT_IN4,
    GPIO_ALT_OUT5,
    GPIO_ALT_IN5,
    GPIO_AP,
    GPIO_AP_INPUT,
    GPIO_AP_OUTPUT,
}GpioConfig; 

typedef enum gpioLevel
{
    GPIO_OUTPUT_LOW = 0,
    GPIO_OUTPUT_HIGH,
    GPIO_DEFAULT_LEVEL,
}GpioLevel;

typedef enum gpioStatus
{
    GPIO_STATUS_POWER = 0,
    GPIO_STATUS_SLEEP,
}GpioStatus;

typedef enum gpioFlag
{
    GPIO_FLAG_UNCHANGE = 0,
    GPIO_FLAG_CHANGE,
}GpioFlag;


typedef struct gpioInfo
{
    UINT8  gpioIndex;
    UINT8  gpioPowerConfig;
    UINT8  gpioSleepConfig;
    UINT8  gpioDeepSleepConfig;
    GpioLevel gpioPowerLevel;
    GpioLevel gpioSleepLevel;
    GpioLevel gpioDeepSleepLevel;
    GpioFlag gpioFlag;
    UINT8  gpioNumber;
    GPIO_Type* gpioBase;
    PCTRL_Type * pctrlBase;
    //volatile unsigned short *pmcReg;
    //volatile unsigned short *pmReg;
    //volatile unsigned short *pfcaeReg;
    //volatile unsigned short *pfceReg;
    //volatile unsigned short *pfcReg;
    //volatile const unsigned short * pprReg;
    //volatile unsigned short *pibcReg;
    //volatile unsigned short *pReg;
}GpioInfo;




/************************函数接口***************************/
bool GpioInitPortMode(GpioStatus status, GpioInfo  *gpioInfo);
bool GpioSetOutputLevel(GpioInfo  *gpioInfo, GpioLevel gpioLevel);
bool GpioReadInputLevel(GpioInfo  *gpioInfo, GpioLevel *gpioLevel);
bool GpioDeinitPortMode(GpioInfo  *gpioInfo);

typedef uint32_t pins_channel_type_t;
typedef uint8_t pins_level_type_t;
void PINS_DRV_WritePin(GPIO_Type * const base, pins_channel_type_t pin, pins_level_type_t value);

#endif
