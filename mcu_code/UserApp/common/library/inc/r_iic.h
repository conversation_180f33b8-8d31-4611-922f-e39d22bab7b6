/*
      R_iic.h
描述：此文件包括IIC初始化，I2C发送， I2C接收，以及中断回调函数头文件
作者：廖勇刚
时间：2016.9.28
*/

#ifndef    _R_IIC_
#define    _R_IIC_

#include "AppTask.h"
#include "event.h"


/************************宏定义***************************/
#define IDLE_STATE               ( 0x00U )

#define ADDR_W_STATE             ( 0x10U )
#define ADDR_R_STATE             ( 0x11U )

#define DATA_SEND_STATE          ( 0x20U )
#define DATA_SEND_END_STATE      ( 0x22U )

#define DATA_RECV_STATE          ( 0x30U )
#define DATA_RECV_STATE_2        ( 0x31U )
#define DATA_RECV_STATE_3        ( 0x32U )
#define DATA_RECV_STATE_4        ( 0x33U )

#define DATA_RECV_END_STATE      ( 0x40U )
#define DATA_RECV_END_STATE_2    ( 0x41U )

#define RTC_SLAVE_ADDR           ( 0xA2U )               
#define SENSOR_SLAVE_ADDR        ( 0x3cU ) 


#define IIC_DIR_W                ( 0x00U )
#define IIC_DIR_R                ( 0x01U )


/************************数据结构定义***************************/
typedef struct i2cRam
{
    UINT8     slaveAddr;
    UINT32    state;
    UINT32    txCount;
    UINT32    txLen;
    UINT8     *txBuf;
    UINT32    rxCount;
    UINT32    rxLen;
    UINT8    *rxBuf;
} I2cRam;





/************************函数接口***************************/
void McuI2cInit(void);
void McuI2cMasterSend(UINT8 slaveAddr, UINT32 len, UINT8 *buf);
void McuI2cMasterReceive(UINT32 len, UINT8 *buf);
void McuI2cTransEmptyIsr(void);
void McuI2cTransEndIsr(void);
void McuI2cRecvEndIsr(void);
void McuI2cCommunErrIsr(void);
#endif

