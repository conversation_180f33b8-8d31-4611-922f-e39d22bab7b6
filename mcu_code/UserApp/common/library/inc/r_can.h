/*
      R_can.c
描述：此文件包括CAN初始化，CAN发送和接收函数头文件
作者：廖勇刚
时间：2016.9.29
*/

#ifndef __R_CAN_H_
#define __R_CAN_H_
#include "NetworkManagement_Cfg.h"
#include "ComStack_Types.h"
#include "event.h"
#include "flexcan_driver.h"

/************************宏定义***************************/
#define CAN_IRQ_H_AR_MAJOR_VERSION 2
#define CAN_IRQ_H_AR_MINOR_VERSION 4
#define CAN_IRQ_H_AR_PATCH_VERSION 0

#define CAN_IRQ_H_SW_MAJOR_VERSION 3
#define CAN_IRQ_H_SW_MINOR_VERSION 1
#define CAN_IRQ_H_SW_PATCH_VERSION 0

#define CAN_H_AR_MAJOR_VERSION 2U
#define CAN_H_AR_MINOR_VERSION 4U
#define CAN_H_AR_PATCH_VERSION 0U

#define CAN_H_SW_MAJOR_VERSION 1U
#define CAN_H_SW_MINOR_VERSION 0U
#define CAN_H_SW_PATCH_VERSION 3U

// 驱动API ID报错
#define CAN_INIT_ID                        0x00
#define CAN_MAINFUCTION_WRITE_ID           0x01
#define CAN_INITCONTROLLER_ID              0x02
#define CAN_SETCONTROLLERMODE_ID           0x03
#define CAN_DISABLECONTROLLERINTERRUPTS_ID 0x04
#define CAN_ENABLECONTROLLERINTERRUPTS_ID  0x05
#define CAN_WRITE_ID                       0x06
#define CAN_GETVERSIONINFO_ID              0x07
#define CAN_MAINFUNCTION_READ_ID           0x08
#define CAN_MAINFUNCTION_BUSOFF_ID         0x09
#define CAN_MAINFUNCTION_WAKEUP_ID         0x0A
#define CAN_CBK_CHECKWAKEUP_ID             0x0B

// 驱动API 参数检查
#define CAN_E_PARAM_POINTER    0x01
#define CAN_E_PARAM_HANDLE     0x02
#define CAN_E_PARAM_DLC        0x03
#define CAN_E_PARAM_CONTROLLER 0x04

// 驱动API 状态检查
#define CAN_E_UNINIT     0x05
#define CAN_E_TRANSITION 0x06

#define CAN_CHANNEL_1  0x00
#define CAN_CHANNEL_2  0x01
#define CAN_CHANNEL_3  0x02
#define CAN_CHANNEL_4  0x03
#define CAN_CHANNEL_LL 0xFF

#define CAN_PHY_CHANNEL_1 0x01
#define CAN_PHY_CHANNEL_2 0x02
#define CAN_PHY_CHANNEL_3 0x04
#define CAN_PHY_CHANNEL_4 0x05

#define RX_EVCAN_CAN1_HRH 0
#define RX_CHCAN_CAN2_HRH 2
#define RX_BDCAN_CAN3_HRH 4
#define RX_DCAN_CAN4_HRH  14

#define RX_EVCAN_CAN1_TP 0
#define RX_CHCAN_CAN2_TP 1
#define RX_BDCAN_CAN3_TP 2
#define RX_DCAN_CAN4_TP  11
#define TX_BDCAN_CAN_NM  15

#define CAN_CHANNEL_INTERRUPT_BOEIE 0x00000800 // Bus off entry interrupt

#define CAN_BASE_REG         0xffd00000
#define CAN_HW_MAX_MAILBOXES 16U // 所有发送邮箱总数

#define CAN_RX_LEGACY_FIFO_MBID (0UL) // FIFO固定检查邮箱ID为0（不可修改）

#define MAX_CAN_BUFFER_COUNT 128 //缓存CAN数据

//协议栈使用的发送邮箱，暂时每路CAN 1个
#define CAN_PROTOCOL_START_MB 28
#define CAN_PROTOCOL_END_MB   31

// MCU芯片型号
#define MCU_CHIP_TYPE CHIP_YTM32B1ME05G0MLQT

#define Can_GetVersionInfo(versionInfo)                                                                                \
    do                                                                                                                 \
    {                                                                                                                  \
        ((Std_VersionInfoType *) (versionInfo))->vendorID         = CAN_VENDOR_ID;                                     \
        ((Std_VersionInfoType *) (versionInfo))->moduleID         = CAN_MODULE_ID;                                     \
        ((Std_VersionInfoType *) (versionInfo))->instanceID       = CAN_INSTANCE;                                      \
        ((Std_VersionInfoType *) (versionInfo))->sw_major_version = CAN_SW_MAJOR_VERSION;                              \
        ((Std_VersionInfoType *) (versionInfo))->sw_minor_version = CAN_SW_MINOR_VERSION;                              \
        ((Std_VersionInfoType *) (versionInfo))->sw_patch_version = CAN_SW_PATCH_VERSION;                              \
    } while (0)

/************************数据结构定义***************************/
typedef enum
{
    CAN_OK = 0U,
    CAN_NOT_OK,
    CAN_BUSY
} Can_ReturnType;

typedef enum
{
    CAN_T_START = 0U,
    CAN_T_STOP,
    CAN_T_SLEEP,
    CAN_T_WAKEUP,
    CAN_T_CNT
} Can_StateTransitionType;

typedef enum
{
    CAN_UNINIT = 0,
    CAN_READY
} Can_DriverStatusType;

typedef enum
{
    CAN_CS_UNINT = 0U,
    CAN_CS_STOPPED,
    CAN_CS_STARTED,
    CAN_CS_SLEEP
} Can_ControllerModeType;

typedef enum
{
    CAN_MODE_NORMAL = 0,
    CAN_MODE_RESET,
    CAN_MODE_HALT,
    CAN_MODE_STANDBY,
    CAN_MODE_WAKEUP,
} CanWorkMode;

typedef enum
{
    CAN_MODE_NOT_STOP = 0,
    CAN_MODE_STOP,
} CanStopStatus;

typedef enum
{
    CAN_MODE_SELECT_NORMAL = 0,
    CAN_MODE_SELECT_RESET,
    CAN_MODE_SELECT_TEST,
} CanModeSelect;

typedef enum
{
    CAN_INTERRUPT_ENABLE = 0,
    CAN_INTERRUPT_DISABLE,
} CanInterruptStatus;

typedef enum
{
    CAN_INTERRUPT_RECEIVE = 0,
    CAN_INTERRUPT_ERROR,
} CanInterruptType;

// 对象类型是接收或发送
typedef enum
{
    CAN_OBJECT_TYPE_RECEIVE,
    CAN_OBJECT_TYPE_TRANSMIT
} Can_ObjectTypeType;

// 对象ID是标准、扩展、混合
typedef enum
{
    CAN_ID_TYPE_EXTENDED,
    CAN_ID_TYPE_MIXED,
    CAN_ID_TYPE_STANDARD
} Can_IdTypeType;

// 硬件CAN 对象是专有还是几个PDU共用
typedef enum
{
    CAN_HANDLE_TYPE_BASIC,
    CAN_HANDLE_TYPE_FULL
} Can_HohType;

// 处理类型是中断或查询
typedef enum
{
    CAN_PROCESS_TYPE_INTERRUPT,
    CAN_PROCESS_TYPE_POLLING
} Can_ProcessType;

typedef UINT32 Can_IdType;

typedef struct
{
    UINT8     *sdu;         /* Pointer to L-PDU */
    Can_IdType id;          /* CANID */
    PduIdType  swPduHandle; /* Handle */
    uint8      length;      /* DLC */
} Can_PduType;

typedef UINT32 Can_FilterMaskType;

typedef struct
{
    Can_ControllerModeType CntrlMode; /* controller mode */

    /* Interrupt masks software backup */
    UINT32 RxMask[2];
    UINT32 TxMask[2];
    UINT8  SwIntFlag; /* software interrupt flag */
    bool   IsWakeup;
    UINT32 IntLockCount;
    /* Transmit PDU handles for TxConfirmation callbacks to CANIF */
    PduIdType TxPduHandles[CAN_HW_MAX_MAILBOXES];
} Can_ControllerStatusType;

// 硬件对象当前配置参数
typedef struct
{
    Can_HohType        CanHandleType;
    Can_IdTypeType     CanIdType;
    UINT32             CanIdValue;
    UINT8              CanObjectId;
    Can_ObjectTypeType CanObjectType;
    UINT8              CanControllerRef;
    Can_FilterMaskType CanFilterMask;
} Can_HardwareObjectType;

// CAN驱动通道当前配置参数
typedef struct
{
    UINT32 CanCtrlValue;
    UINT16 CanRxHwObjFirst;
    UINT16 CanRxHwObjCount;
    UINT16 CanTxHwObjFirst;
    UINT16 CanTxHwObjCount;
} Can_ControllerConfigType;

// CAN通道处理配置参数
typedef struct
{
    UINT8           CanControllerId;
    Can_ProcessType CanBusOffProcessing;
    Can_ProcessType CanRxProcessing;
    Can_ProcessType CanTxProcessing;
    Can_ProcessType CanWakeupProcessing;
} Can_ControllerPCConfigType;

#pragma pack(1)
typedef struct
{
    UINT8  channel;
    UINT32 time;
    UINT32 id;
    UINT8  dlc;
    UINT8  data[8];
} CanPduInfo;
#pragma pack()

// 芯片型号定义
typedef enum
{
    CHIP_RH850,             // 开沃基线版本瑞萨芯片
    CHIP_YTM32B1ME05G0MLQT, // 开沃海外版本云途芯片
} MCUChipType;

typedef struct
{
    uint8_t           channel;
    flexcan_msgbuff_t canMsg;
} CanMsgInfo;

typedef struct
{
    uint16_t   canNum;
    uint16_t   writeIndex;
    uint16_t   readIndex;
    uint16_t   lostTotal;
    uint32_t   receiveTotal;
    uint32_t   processTotal;
    CanMsgInfo canData[MAX_CAN_BUFFER_COUNT];
} CanMsgInfos;

/*************************************************
函数名称: CanGetChannelStatus
函数功能: 获取指定CAN通道的状态
输入参数: channel - CAN通道号（CAN_CHANNEL_1, CAN_CHANNEL_2等）
输出参数: 无
函数返回类型值：CAN_STATUS_NORMAL - 正常，CAN_STATUS_BUS_OFF - 总线关闭
编写者: 郭语宸
编写日期: 2025/4/28
*************************************************/
typedef enum
{
    CAN_STATUS_NORMAL  = 0,
    CAN_STATUS_BUS_OFF = 1,
    CAN_STATUS_UNKNOWN = 2
} CanChannelStatus;

/************************外部全局变量****************************/
extern const Can_ControllerConfigType Can_ControllerConfigData[];

/************************函数接口***************************/
void SuspendAllInterrupts(void);
void ResumeAllInterrupts(void);
void Can_InitMB(void);
void Can_GetMBInfo(UINT16 mbId, Can_PduType *pdu);
#if (STD_ON == CAN_HW_TRANSMIT_CANCELLATION)
void Can_TxCancel(UINT16 HwObjId);
#endif
Can_ReturnType Can_Write(UINT8 Hth, Can_PduType *PduInfo);
void           Can_MainFunction_Write(void);
void           Can_MainFunction_Read(void);
void           Can_EnableControllerInterrupts(uint8 Controller);
void           Can_DisableControllerInterrupts(uint8 Controller);
void           Can_RxTxInt_Handler(void);
void           Can_Error_Handler(uint8 Controller);
void           Can_BusOff_Handler(uint8 Controller);
void           Can_BusOff_Main_Handler(void);
void           Can_TwRwBusOff_Handler(uint8 Controller);
Std_ReturnType Can_Cbk_CheckWakeup(uint8 Controller);
void           Can_MainFunction_Wakeup(uint8 Controller);
Can_ReturnType Can_ApplySWF(UINT8 channel, UINT8 swIntFlag);
Can_ReturnType Can_SetControllerMode(uint8 Controller, Can_StateTransitionType Transition);
void           McuCanInit(void);
bool           MCUCanSendData(UINT8 channel, UINT32 canId, UINT8 canDlc, UINT8 *buf);
void           MCUCanTxFifoData(UINT8 channel, UINT32 canId, UINT8 canDlc, UINT8 *buf);
void           MCUCanRxFifoData(UINT32 channel, flexcan_msgbuff_t rxfifoMsg);
void           MCUCanTxFifoIsr(void);

bool             MCUCanSendFtmData(UINT8 channel, UINT32 canId, UINT8 canDlc, UINT8 *buf);
Can_ReturnType   Can_StopMode(uint8 Controller);
Can_ReturnType   Can_StartMode(uint8 Controller);
void             Can_Init(void);
int              Can_GetCanInstance(int channel);
int              Can_GetCanChannel(int instance);
void             Can_TxProcess_YTConfirm(UINT8 Controller, UINT32 mbId);
void             YT_Can_InitMB(UINT32 instance);
void             McuProcessCanBuferData(void);
void             CanTxConfirmInMain(void);
void             MCUCanCheckStatus(void);
CanChannelStatus CanGetChannelStatus(uint8_t channel);
bool             CheckMcuCanBusOffStatus(void);

#endif
