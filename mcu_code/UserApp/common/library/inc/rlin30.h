/*
      Rlin30.h
描述：此文件包括UART30 、UART32初始化函数、发送数据接口头文件
作者：廖勇刚
时间：2016.9.28
*/

#ifndef RLIN30_H
#define RLIN30_H

#include "event.h"
#include "linflexd_uart_driver.h"
#include "linflexd_uart_config.h"

#define UART_PORT_IPC    3
#define UART_PORT_BLE    0

#define BLE_SEND_TIMEOUT 1000 /**< 蓝牙串口发送超时时间，单位：ms */

/************************函数接口***************************/
void     McuUartIPCInit(uart_callback_t dataCallback, uart_callback_t errorCallback);
void     McuUartIPCReInit(uart_callback_t dataCallback, uart_callback_t errorCallback);
void     McuUartBLEInit(uart_callback_t dataCallback, uart_callback_t errorCallback);
void     McuUartBLEReInit(uart_callback_t dataCallback, uart_callback_t errorCallback);
void     UartBLESendData(UINT16 len, UINT8 *buf);
void     UartIPCSendData(UINT16 len, UINT8 *buf);
uint32_t UartGetRecvLen(uint8_t uartNum);
void     McuUartIPCRxData(void);
void     McuUartIPCSetRxBuff(void);
void     McuUartBLERxData(UINT16 index);
void     McuUartBLESetRxBuff(UINT16 index);
void     SendDataIPCCallback(void *LINFLexDState, uart_event_t event, void *userData);
void     SendDataBLECallback(void *LINFLexDState, uart_event_t event, void *userData);

#endif  // RLIN30_H
