#pragma once
#ifndef _BT_TASK_H_
#define _BT_TASK_H_

#include "BtApi.h"
#include "task.h"

#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define BT_TASK_STACK_SIZE 384
#define BT_TASK_PRIORITY   3

#define BT_TASK_ENABLED    0 // 1: 使能蓝牙任务 0: 关闭蓝牙任务

extern TaskHandle_t BTTask_Handle;

void StartBTTask(void);
void BTTask(void* param);

/************************函数接口***************************/
void BtTaskInitHook(void);
void BtTaskPostInitHook(void);
void BtTaskPeriodHook(void);
void BtTaskFunctionHook(Msg msg);

#endif