/*
      BLELin.h
描述：定义蓝牙具体业务功能，
作者：ben
时间：2018/12/18
*/

#ifndef  _BLE_LIN_H_
#define  _BLE_LIN_H_

#include "AppTask.h"
#include "iodefine.h"
#include "IpcApi.h"
#include <stdbool.h>

/************************宏定义***************************/
#define BLE_UPDATE_MAX_LEN                  300
#define LIN_ID_RANGE                        12
#define BEACON_SCAN_DURATION                300 //STB侧扫描间隔，单位1ms
#define BEACON_READ_COUNT                   32  //MCU侧扫描间隔，单位10ms，要比STB侧大20ms
#define BEACON_FIRST_SCAN_DURATION          500 //STB侧首次扫描间隔，单位1ms
#define BEACON_FIRST_READ_COUNT             52  //MCU侧首次扫描间隔，单位10ms，要比STB侧大20ms

#define LIN_STB_CHECK_TIMEOUT               4

#define LOCATION_POW_PARA                   0.1
#define LOCATION_VOTE_NUM                   3
#define LOCATION_VOTE_ENABLE                FALSE

#define LINE_CALI_STB_SACN_SECOND           10
#define LINE_CALI_STB_CONDITION_START       25
#define LINE_CALI_STB_MAIN_CONDITION_MAX    0x0D
#define LINE_CALI_STB_SUB_CONDITION_MAX     0x05
#define LINE_CALI_STB_RSSI_CONDITION_START  25

#define APP_FREE_NOTIFY_MINUTE              5

#define STB_FAULT_CHECK_RSSI                50

/************************数据结构定义***************************/
typedef enum 
{
    LIN_ID_READ_STATE = 0,
    LIN_ID_READ_MAC_ADD,
    LIN_ID_START_UPDATE,
    LIN_ID_READ_WORK_MODE,
    LIN_ID_FLASH_ERASE,
    LIN_ID_SEND_UPDATE_DATA,
    LIN_ID_READ_UPDATE_DATA_STATUS,
    LIN_ID_END_UPDATE,
    LIN_ID_REBOOT,
    LIN_ID_READ_SCAN_MAC,
    LIN_ID_LINE_CALI_PUBLIC_ID = 46,
    LIN_ID_LINE_CALI_READ_RSSI = 47,
    LIN_ID_LINE_CALI_READ_IDLE_MAC = 48,
    LIN_ID_DIAG_REQUEST = 60,
    LIN_ID_DIAG_RESPONSE = 61,
    LIN_ID_PUBLIC_ID = 62,
} LinIdIndex;

typedef enum 
{
    LIN_PUBLIC_SET_SCAN_MAC = 0,
    LIN_PUBLIC_SET_SCAN_UUID,
    LIN_PUBLIC_SET_SCAN_DURATION,
    LIN_PUBLIC_SET_SCAN_SWITCH,
    LIN_PUBLIC_NOTIFY_WAKE_UP,
    LIN_PUBLIC_NOTIFY_SLEEP,
    LIN_PUBLIC_NOTIFY_KEEP_ALIVE,
}LinPublicIdCmd;   

typedef enum 
{
    LIN_LINE_PUBLIC_START = 1,
    LIN_LINE_PUBLIC_STOP,
    LIN_LINE_PUBLIC_NOTIFY_CONDITION,
    LIN_LINE_PUBLIC_CALI_NUMBER,
    LIN_LINE_PUBLIC_FINISH,
}LinLinePublicIdCmd;   

typedef enum
{
    LIN_CHECK_STB_WAKEUP = 0,
    LIN_CHECK_STB_SEND_MAC_INDEX,
    LIN_CHECK_STB_WORK_MODE,
}LinCheckStb;

typedef enum
{
    LIN_STB_ENTER_SLEEP = 0,
    LIN_STB_SLEEPING,
}LinStbSleep;

typedef enum
{
    LIN_STATE_SEND_IDLE = 0,    //空闲状态
    LIN_STATE_SENDING,          //发送传输中
}LinSendState;

typedef enum 
{
    STB_ERROR_CODE_NORMAL                = 0xF0, //正常工作
    STB_ERROR_CODE_BLE_INIT_FAIL         = 0xF0, //BLE初始化失败
    STB_ERROR_CODE_SCAN_FAIL             = 0xF1, //扫描失败
    STB_ERROR_CODE_SET_SCAN_DURATION_FAIL= 0xF2, //设置扫描间隔失败
    STB_ERROR_CODE_SET_FILTER_MAC_FAIL   = 0xF3, //设置扫描过滤mac地址失败
    STB_ERROR_CODE_NOT_SET_FILTER        = 0xF4, //未设置过滤
} StbDeviceRrrorCode;

typedef enum 
{
    LOCATION_AREA_UNKNOW  = 0x00, //未知区
    LOCATION_AREA_PS,             //PS区
    LOCATION_AREA_PE,             //PE区
    LOCATION_AREA_BLE_FAR,        //BLE区
    LOCATION_AREA_BLE_NEAR,       //BLE区
    LOCATION_AREA_COUNT,
} LocationArea;

typedef enum 
{
    STB_WORK_BOOT_MODE = 0,
    STB_WORK_APP_MODE,
    STB_WORK_OFFLINE,            // 不在线/未标定
    STB_WORK_UNKONW_MODE = 0xf,
} StbWorkMode;

typedef enum 
{
    FRAME_IDLE              = 0xF0, //空闲状态
    FRAME_RECEIVED          = 0xF1, //收到升级包
    FRAME_WRITE_SUCCESS     = 0xF2, //写入成功
    FRAME_NUMBER_REPETITIVE = 0xF3, //帧号重复
    FRAME_ERROR             = 0xF4, //帧错误
    FRAME_WRITE_FAIL        = 0xF5, //写入失败
} StbUpdateFrameStatus;

typedef enum 
{
    STB_PACK_WRITE_SUCCEED = 0,
    STB_PACK_NUMBER_REPETITIVE,
    STB_PACK_ERROR,
    STB_PACK_WRITE_FAIL,

    STB_PACK_IDLE = 0xFF,
}StbPackStatus;

typedef enum
{
    VEHICLE_EVENT_LEFT_DOOR = 0,
    VEHICLE_EVENT_RIGHT_DOOR,
    VEHICLE_EVENT_TRUNK_DOOR,
    VEHICLE_EVENT_CENTER,
} VehicleEvent;
    
typedef enum{
    APPLE_PHONE = 1,
    ANDROID_PHONE = 2,
}PhoneModel;

typedef enum{
    APP_RSSI_REQU_OFF = 0,
    APP_RSSI_REQU_ON  = 1,
}APPRssiRequestSwitch;

typedef enum
{
    LIN_SET_WAKEUP = 0,
    LIN_SET_SCAN_UUID,
    LIN_SET_SCAN_DURATION,
    LIN_SET_SCAN_SWITCH
}LinStbScanSet;

typedef enum
{
    LIN_STB_CHECK_DEVICE = 0,
    LIN_SET_STB_SCAN_PARA,
    LIN_WAIT_READ_STB_RSSI,
    LIN_READ_STB_RSSI,
    LIN_HANDLE_STB_RSSI,
}LinStbNormalStatus;

typedef enum
{
    LIN_CALI_STB_STATUS = 0,
    LIN_CALI_STB_START,
    LIN_CALI_STB_DELETE,
    LIN_CALI_STB_LINE_START,
    LIN_CALI_STB_LINE_CHECK,
    LIN_CALI_STB_ADJUST,
    LIN_CALI_STB_APP_FAULT_CHECK,
    LIN_CALI_STB_LINE_SEARCH_FAULT,
    LIN_GET_APP_UUID_DATA_CONFIG,
    LIN_CALI_STB_DIAG_CALI = 0x80,
    LIN_CALI_STB_DIAG_ADJUST,
    LIN_CALI_STB_IDLE = 0xFF, 
}LinCaliStbCmd;

typedef enum
{
    LIN_CALI_IDLE_ID   = 0,
    LIN_CALI_START_ID  = 1,
    LIN_CALI_DELETE_ID = 2,
}LinCaliStbId; 

typedef enum 
{
    STB_UPDATE_IDLE = 0,  
    STB_UPDATE_START,            
    STB_UPDATE_WAIT_BOOT_MODE,   
    STB_UPDATE_FLASH_ERASE,      
    STB_UPDATE_WAIT_PACK,     
    STB_UPDATE_PACK_UPDATING,     
    STB_UPDATE_WAIT_STB_RESULT,   
    STB_UPDATE_END,       
    STB_UPDATE_WAIT_APP_MODE,     
    STB_UPDATE_WORK_MODE,
} LinStbUpdate;

typedef enum
{
    LIN_LINE_CALI_STB_IDLE = 0,  
    LIN_LINE_CALI_STB_START_SCAN,
    LIN_LINE_CALI_STB_WAIT_SCAN,
    LIN_LINE_CALI_STB_STOP_SCAN,
    LIN_LINE_CALI_STB_NOTIFY_CONDITION,
    LIN_LINE_CALI_STB_READ_RSSI,    
    LIN_LINE_CALI_STB_TX_RESULT,
    LIN_LINE_CALI_STB_INDEX_CHECK,    
    LIN_LINE_CALI_STB_FAIL,
    LIN_LINE_CALI_STB_SUCCESS,
    LIN_LINE_CALI_STB_EXCHANGE,
} LinLineCaliStb;

typedef enum
{
    LIN_LINE_SEARCH_STB_WAKEUP = 0,  
    LIN_LINE_SEARCH_STB_SET_UUID,
    LIN_LINE_SEARCH_STB_SET_DURATION,
    LIN_LINE_SEARCH_STB_WAIT_READ,
    LIN_LINE_SEARCH_STB_READ_RSSI,
    LIN_LINE_SEARCH_STB_END,
} LinLineSearchStb;

typedef enum
{
    LIN_LINE_RESET_STB_WAKEUP = 0,  
    LIN_LINE_RESET_STB_READ_MAC,
    LIN_LINE_RESET_STB_READ_MAC_RESULT,
    LIN_LINE_RESET_STB_WRITE_NUM,
    LIN_LINE_RESET_STB_END,
} LinLineResetStb;

typedef enum 
{
    LIN_STATUS_POWER_ON_CHECK_STB = 0,
    LIN_STATUS_UPDATE_STB,  
    LIN_STATUS_SLEEP,
    LIN_STATUS_LINE_CALI_STB,
    LIN_STATUS_NORMAL,
    LIN_STATUS_LINE_SEARCH_STB,
} LinStbStatus;

typedef enum
{
    LIN_IPC_UPDATE_CMD_READ_WORK_MODE = 0,
    LIN_IPC_UPDATE_CMD_START,
    LIN_IPC_UPDATE_CMD_PACKAGE,
    LIN_IPC_UPDATE_CMD_END,
    LIN_IPC_UPDATE_CMD_REQUEST,
}LinIpcUpdateCmd;

typedef enum 
{
    STB_GET_RSSI_ON = 0,
    STB_GET_RSSI_OFF,
} StbRssiMode;

typedef enum 
{
    STB_PACKET_WRITE_SUCCEED = 0,
    STB_PACKET_NUMBER_REPETITIVE,
    STB_PACKET_ERROR,
    STB_PACKET_WRITE_FAIL,
    STB_PACKET_IDLE = 0xFF,
}StbPacketStatus;

typedef enum 
{
    STB_DEVICE_IDLE = 0,
    STB_DEVICE_LEFT_A_PILLAR,
    STB_DEVICE_RIGHT_A_PILLAR,
    STB_DEVICE_TRUNK,
    STB_DEVICE_TOTAL,
    STB_DEVICE_ALL = 0xFF,
} StbDeviceId;

typedef enum 
{
    STB_SET_MAC_IDLE = 0,
    STB_SET_MAC_REQUEST,
    STB_SET_MAC_WAIT_RESPONSE,
    STB_SET_MAC_SET,
    STB_SET_MAC_WAIT,
} StbSetScanMacState;
    
typedef enum 
{
    STB_LINE_CALI_READ_RSSI_IDLE = 0,
    STB_LINE_CALI_READ_RSSI_RX,
    STB_LINE_CALI_READ_RSSI_ERROR,
} StbLineCaliReadRssiState;
    
typedef enum 
{
    STB_APP_NOT_SUPPORT_SALVE_MODE = 0,
    STB_APP_SUPPORT_SALVE_MODE,
} StbAPPSalveMode;

typedef enum 
{
    STB_APP_NO_BACKGROUND_MODE = 0,
    STB_APP_BACKGROUND_MODE,
} StbAPPBackgroundMode;

typedef enum
{
    STB_CHECK_FAULT_FIRST_READ_MAC = 0,
    STB_CHECK_FAULT_READ_MAC,
    STB_CHECK_FAULT_SYNC_INDEX,
    STB_CHECK_FAULT_READ_IDLE_MAC,
    STB_CHECK_FAULT_CHECK_IDLE_STB,
} StbCheckFaultStep;

typedef enum
{
    STB_CALI_RESULT_DONE = 0,      /*已标定*/
    STB_CALI_RESULT_NOT_DONE = 1,  /*未标定*/
    STB_CALI_RESULT_UNKNOWN_ERROR,
    STB_CALI_RESULT_PARA_ERROR,
    STB_CALI_RESULT_TIMEOUT,
    STB_CALI_RESULT_1_LIN_ERROR,
    STB_CALI_RESULT_2_LIN_ERROR,
    STB_CALI_RESULT_3_LIN_ERROR,
    STB_CALI_RESULT_1_TIMEOUT,
    STB_CALI_RESULT_2_TIMEOUT,
    STB_CALI_RESULT_3_TIMEOUT,
    STB_CALI_RESULT_1_CHECK_FAIL,
    STB_CALI_RESULT_2_CHECK_FAIL,
    STB_CALI_RESULT_3_CHECK_FAIL,
    STB_CALI_RESULT_DOING,      /*标定执行中*/
} StbLineCaliResult;

typedef struct
{
    uint8_t stbIndexCheck;  //STB 编号检测阈值
}LocationThreshold;

typedef struct
{
    LocationThreshold   threshold;
    bool                stbRssiIsUpdated;
}LocationInfo;

typedef struct
{
    uint8_t        StbLeftNormal;
    uint8_t        StbRightNormal;
    uint8_t        StbTrunkNormal;
    uint8_t        StbDataSendCnt;
}StbModleNormal;

typedef struct
{   
    StbDeviceId     deviceID;
    LinStbUpdate    status;
    uint16_t        packetTotalNum;
    uint16_t        currentPacketIndex;
    uint16_t        currentPacketDataPos;
    uint16_t        currentPacketDataLen;
    uint8_t         packetData[BLE_UPDATE_MAX_LEN];   //升级包数据
    uint8_t         frameIndex;
    uint8_t         frameData[8];    
    StbUpdateFrameStatus  frameStatus;
}StbUpDateInfo;

typedef struct 
{
    uint8_t  currentRssi;
    uint8_t  calcRssi;
    uint8_t  mac[6];
    uint8_t  errorCode;
    uint8_t  workMode;   
    uint8_t  version;
    uint8_t  watchDogCount;
    uint8_t  watchDogState[4];
}StbDeviceInfo;

typedef struct
{
    uint8_t MAC[6];    
    uint8_t rssi;
    uint8_t index;
}StbLineCaliReadRssi;

/* 产线标定 */
typedef struct
{
    uint8_t scanMode;   /* 0:UUID 1:MAC */
    uint16_t UUID; 
    uint8_t MAC[6];
    uint8_t caliMode[3];
    uint8_t mainCondition;
    uint8_t subCondition;    
    uint8_t rssiConditionMax;
    uint8_t rssiConditionMin;
    bool subConditionEnable;    
    uint8_t failCount;
    StbLineCaliReadRssi ReadRssiRecord[3];     
    StbLineCaliReadRssi ReadRssiBuff;      
    uint8_t ReadRssiIndex; 
    StbLineCaliReadRssiState ReadRssiState; 
    uint8_t idleBoardMac[6];
    StbLineCaliResult caliResult;
    bool isCaliRunning;
    uint8_t conflictCnt;   /*检测到总线冲突次数*/
}StbLineCaliInfo;

typedef struct
{
    uint8_t         scanMac[6];
    uint8_t         scanMacType;
    StbSetScanMacState setMacState;
    uint16_t        scanUuid;
    uint16_t        scanDuration;   
    PhoneModel      phoneModel;
}StbSetPara;

typedef struct 
{
    LinStbStatus    majorStatus;
    uint8_t         subStatus;
    StbAPPSalveMode APPSalveMode;
    bool            stbLocationSwitch;
    StbAPPBackgroundMode APPBackgroundMode;
    uint16_t        linLostCount;
    StbDeviceInfo   deviceInfo[STB_DEVICE_TOTAL];
    StbSetPara      setPara;
    StbLineCaliInfo lineCaliInfo;
    StbUpDateInfo   updateInfo;
    LinSendState    sendState;    
    APPRssiRequestSwitch appRssiSwitch;
    uint8_t         deviceFaultNum;
    bool            isIdleBoardOnLine;
    bool            needReportSTBFaultInfo;
    bool            needReportAreaInfo;
    uint16_t        beaconScanDuration;   //单位10ms
}LinInfo;

/************************函数接口***************************/
void LinIsrRxFunction(void);
void LinInitRam(void);
void LinPeriodFunction(void);
void LinIpcRxCalibrationHandle(uint8_t *para, uint16_t len);
void LinClearNVMacIndex(void);
void LinChangeNVMacIndex(uint8_t index, uint8_t *mac);
void LinExchangeNVMacIndex(uint8_t a, uint8_t b);
void LinIpcTxAPPSTBFaultCheck(void);
bool MacIsValid(uint8_t *mac);
void AutoCalibrationHandler(void);
void RcvAppData(uint8_t * buf);
void LinLineRssiSendIpc(LinInfo Rssi);
void OffStbGetRssi(void);
void LinUpdatePeriodHandle(void);
void LinIpcRxUpdateHandle(uint8_t* para, uint16_t len);

#endif

