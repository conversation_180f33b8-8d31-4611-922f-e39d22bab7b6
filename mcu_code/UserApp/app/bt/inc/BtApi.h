/*
      BtApi.h
描述：定义蓝牙具体业务功能，
作者：廖勇刚
时间：2016.7.5
*/

#ifndef _BT_API_H_
#define _BT_API_H_

#include "AppTask.h"
#include "Platform_Types.h"

/************************宏定义***************************/
#define BT_RX_BUFF_SIZE                 20   /**< 蓝牙接收缓冲区大小 */
#define BT_MAX_DATA_SIZE                281  /**< 蓝牙最大数据长度 */
#define BT_SELFCHECK_TIMEOUT            3    /**< 周期1s， 超时时间设为3s */
#define BT_SELFCHECK_FAILCNT            3    /**< 自检失败次数后，写入故障码 */
#define BT_PASS_REQ_EVNET_TIMEOUT       10   /**< 蓝牙数据透传接收请求，超时时间, ms单位 */
#define BT_PASS_DATA_TYPE_OPT           0x00 /**< 透传数据类型控制字节 */
#define BT_PASS_DATA_REAL_OPT           0x01 /**< 透传实际数据起始字节 */

#define BT_SLAVE_SHUTDOWN_INIT          5    /**< 初始化从模式的关闭过程 */
#define BT_SLAVE_SHUTDOWN_OPEN_ADV      1    /**< 打开广播以准备从模式关闭 */
#define BT_SLAVE_SHUTDOWN_CLOS_ADV      2    /**< 关闭广播并完成从模式的关闭 */

#define BT_CLIENT_STATUS_CHECK_INTERVAL ((5000 + TASK_BLE_PERIOD_TIME - 1) / TASK_BLE_PERIOD_TIME) /**< 5s检测一次客户端连接状态 */

#define BT_SERVICE_NOT_INIT             (-1)    /**< 服务未初始化 */
#define BT_MODULE_SEQUENCE_ERR          (-2)    /**< 模块序列错误 */
#define BT_MODULE_INT_LOW               0       /**< 模块中断低电平 */
#define BT_MODULE_INT_HIGH              1       /**< 模块中断高电平 */
/************************数据结构定义***************************/
typedef enum
{
    BT_MODULE_UPGRADE_START = 0,
    BT_MODULE_UPGRADE_END,
    BT_MODULE_UPGRADE_FAIL,
} BtModuleUpgradeStatus;

/**
 * @enum BtModuleDevicesStatus
 * @brief 蓝牙模块设备状态枚举
 *
 * 该枚举定义了蓝牙模块的设备状态。
 */
typedef enum
{
    BT_MODULE_HARD_DEVICE_INACTIVE = 0x00, /**< 硬件设备不活动 */
    BT_MODULE_HARD_DEVICE_ACTIVE,          /**< 硬件设备活动 */
    BT_MODULE_UPGRADE_DEVICE_INACTIVE,     /**< 升级设备不活动 */
    BT_MODULE_UPGRADE_DEVICE_ACTIVE,       /**< 升级设备活动 */
} BtModuleDevicesStatus;

/**
 * @enum BtModuleType
 * @brief 蓝牙模块类型枚举
 *
 * 该枚举定义了蓝牙模块的主从类型。
 */
typedef enum
{
    BT_MODULE_MASTER_TYPE, /**< 主模式 */
    BT_MODULE_SLAVE_TYPE,  /**< 从模式 */
} BtModuleType;

/**
 * @enum BtModulePassDataType
 * @brief 蓝牙模块透传数据类型枚举
 *
 * 该枚举定义了蓝牙模块的透传数据类型。
 */
typedef enum
{
    BT_MODULE_PASS_DATA_TYPE_NORMAL = 0x01, /**< 正常数据类型 */
    BT_MODULE_PASS_DATA_TYPE_UPGRADE,       /**< 升级数据类型 */
} BtModulePassDataType;

typedef enum
{
    BT_MODULE_INIT_REST_CLOSE_ADV = 0x00,
    BT_MODULE_INIT_WAKEUP_STEP,
    BT_MODULE_INIT_RESET_STEP1,
    BT_MODULE_INIT_RESET_STEP2,
    BT_MODULE_INIT_UPGRADE_STEP1,
    BT_MODULE_INIT_UPGRADE_STEP2,
    BT_MODULE_INIT_UPGRADE_STEP3,
} BtModuleInitStepType;

typedef enum
{
    BT_MODULE_INIT_EVENT = 0x00,              /*蓝牙模块初始化*/
    BT_MODULE_SELFCHECKTX_EVENT,              /*蓝牙模块自检*/
    BT_MODULE_SELFCHECKRX_EVENT,              /*蓝牙模块自检*/
    BT_MODULE_NORMAL_PASSTHROUGH_EVENT,       /*蓝牙模块正常状态下数据透传*/
    BT_MODULE_FTM_PASSTHROUGH_EVENT,          /*蓝牙模块装备状态下数据透传*/
    BT_MODULE_SLEEP_EVENT,                    /*蓝牙模块进入休眠模式*/
    BT_MODULE_HARD_FAULT_EVENT,               /*蓝牙模块硬件故障*/
    BT_MODULE_UPGRADE_PASSTHROUGH_INIT_EVENT, /*蓝牙主模块升级状态下数据透传准备*/
    BT_MODULE_UPGRADE_PASSTHROUGH_EVENT,      /*蓝牙主模块升级状态下数据透传准备*/
} BtModuleEvent;

/**
 * @enum BtModuleRestHandleType
 * @brief 蓝牙模块复位处理类型枚举
 *
 * 该枚举定义了蓝牙模块复位时的处理类型。
 */
typedef enum
{
    BT_HARD_RESET_CLOSE_ADV = 0x00, /**< 硬复位并关闭广播 */
    BT_HARD_RESET_OPEN_ADV,         /**< 硬复位并打开广播 */
    BT_SOFT_RESET_CLOSE_ADV,        /**< 软复位并关闭广播 */
    BT_SOFT_RESET_OPEN_ADV,         /**< 软复位并打开广播 */
} BtModuleRestHandleType;

typedef void (*BtDeviceStatusNotifyCbk)(BtModuleDevicesStatus staus);
typedef void (*BtPassReqCbk)(uint8_t *txBuff, uint16_t txBuffLen);
typedef void (*BtModuleIniStepCbk)(BtModuleType type, BtModuleInitStepType step);

/**
 * @struct BtModuleInfoStruct
 * @brief 蓝牙模块信息结构体
 *
 * 该结构体用于存储蓝牙模块透传请求的相关信息。
 */
typedef struct
{
    uint16_t     passReqLen;            /**< 透传请求数据长度 */
    uint8_t     *passReqDataPtr;        /**< 透传请求数据指针 */
    boolean      passReqEvent;          /**< 透传请求事件标志 */
    boolean      passReqFinish;         /**< 透传请求完成标志 */
    BtPassReqCbk btPassReqCbk;          /**< 透传请求回调函数 */
    uint32_t     passReqEventStartTime; /**< 透传请求事件开始时间 */
} BtModuleInfoStruct;

/**
 * @struct BtServices
 * @brief 蓝牙服务结构体
 *
 * 该结构体用于存储蓝牙模块的各种服务信息和状态。
 */
typedef struct
{
    BtModuleType            btModuleType;                  /**< 蓝牙模块主从模式定义，在初始化函数中定义 */
    BtModuleDevicesStatus   btModuleDevicesStatus;         /**< 蓝牙设备状态 */
    BtDeviceStatusNotifyCbk btDeviceStatusNotifyCbk;       /**< 蓝牙设备状态通知回调函数 */
    BtModuleInitStepType    btModuleInitStepType;          /**< 蓝牙模块初始化步骤类型 */
    BtModuleIniStepCbk      btModuleIniStepCbk;            /**< 蓝牙模块初始化回调函数 */
    BtModuleEvent           btModuleEvent;                 /**< 蓝牙模块当前事件状态 */
    BtModulePassDataType    btModulePassDataType;          /**< 蓝牙模块透传数据类型 */
    uint8_t                 btModuleSelfCheckTimeOutCnt;   /**< 蓝牙模块自检超时计时器 */
    uint8_t                 btModuleSelfCheckFailCnt;      /**< 蓝牙模块自检失败计数器 */
    BtModuleInfoStruct      btModulePassTxStruct;          /**< 蓝牙模块透传请求发送结构体 */
    BtModuleInfoStruct      btModulePassRxStruct;          /**< 蓝牙模块透传请求接收结构体 */
    boolean                 btModuleUpgradeFinish;         /**< 蓝牙模块升级完成标志 */
    boolean                 btModuleSlaveShutdownStarted;  /**< 蓝牙从模块关机启动标志 */
    uint8_t                 btMoudleSlaveShutdownTimerCnt; /**< 蓝牙从模块关机定时器倒计时 */
    BtModuleRestHandleType  btModuleRestType;              /**< 蓝牙模块复位处理类型 */
    boolean                 btModuleHardClosed;            /**< 蓝牙模块是否使用了硬件关机 */
} BtServices;

/************************函数接口***************************/
void BtTxUartCmd(const char *fmt, ...);
void BtEventFunction(Msg msg);
void BtUartIsrRxFunction(void);
int  BtDataPassServicesMainFunction(void);
void BtModuleServiceInit(void);
// Richard changed below code
// void BtSendData(uint16_t len, uint8_t *buf);
void BtSendData(uint8_t *buf, uint16_t len);
void BtModuleDevicesStatusUpdate(BtModuleDevicesStatus status);
int  BtModulePassReqTxEvent(uint8_t *dataPtr, uint16_t dataLen);
int  BtModulePassReqRxEvent(uint8_t *dataPtr, uint16_t dataLen);
int  BtModuleHandShakeSend(HandShakeStatus handShakeStatus, BtModuleRestHandleType handleType);
void BtTaskVinCodeWriteHook(void);

void        BtModulePostInit(void);
int         BtModulePassReqRxEventTimerStart(void);
BtServices *BtModuleServiceRead(void);
int         BtModuleEventPeriodFunction(void);
void        BTWakeupInit(void);
void        BtModuleInterruptWakeUpHande(void);
void        PrintHexData(const char* strHeader, const uint8_t* data, uint16_t dataLen);
int         BtModulePassReqRxEventCheck(void);
// function delcaration
void BtModuleDriverInit(void);
void BtModuleSleep(void);
#endif
