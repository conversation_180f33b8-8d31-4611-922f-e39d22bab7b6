/*
      BLELin.c
描述：BLE蓝牙定位
作者：ben
时间：2018/12/18
*/

#include <string.h>
#include "event.h"
#include "LogApi.h"
#include "BLELin.h"
#include "BtApi.h"
#include "IpcApi.h"
#include "PmApi.h"
#include "SystemApi.h"
#include "NvApi.h"

/************************外部全局变量****************************/
extern CommonInfo           g_commonInfo;
extern TboxSelfConfigPara   g_nvTboxSelfConfigData;
extern DtcInfo              g_dtcInfo;
extern TboxBleKeyHardPara   g_tboxBleKeyHardPara;
extern CarInfo              g_carInfo;
extern StaticDidInfo        g_staticDid;
extern LocationInfo         g_locationInfo;

/************************全局变量****************************/
LinInfo             g_linInfo;
LocationInfo        g_locationInfo;
StbModleNormal      g_stbmodlenormal ;

/*************************************************
函数名称: LinCheckWhetherSupportStb
函数功能: 检测T-Box是否支持蓝牙信标功能
输入参数:
输出参数:
函数返回类型值: 0-不支持蓝牙信标, 1--支持蓝牙信标
编写： zxl
编写日期 :2022/03/12
*************************************************/
uint8_t LinCheckWhetherSupportStb(void)
{
    TboxSelfConfigPara *tboxSelfConfigPara = GetTboxSelfConfigData();
    return (tboxSelfConfigPara->tboxCfgType & TBOX_CFG_WITH_STB) ? 0x01 : 0x00;
}

/*************************************************
函数名称: GetRealId
函数功能: 通过index和id获取真实ID
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
static uint8_t GetRealId(uint8_t deviceIndex, uint8_t id)
{
    /*ZXL, ID范围0-11代表A柱左侧定位蓝牙模块接收处理命令,即一号从设备*/
    /*ZXL, ID范围12-23代表A柱右侧定位蓝牙模块接收处理命令，即二号从设备*/
    /*ZXL, ID范围24-35代表后备箱定位蓝牙模块接收处理命令，即三号从设备*/
    return ((deviceIndex-1) * LIN_ID_RANGE + id);
}

bool MacIsValid(uint8_t *mac)
{
    uint8_t buf[6];

    memset(buf, 0x00, 6);
    if(0 == memcmp(buf, mac, 6))
    {
        return false;
    }

    memset(buf, 0xFF, 6);
    if(0 == memcmp(buf, mac, 6))
    {
        return false;
    }

    return true;
}

/*************************************************
函数名称: LinTxStbUpdateStatus
函数功能: Stb发送升级包状态
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinTxStbUpdateStatus(uint8_t deviceId, StbPackStatus status)
{
    uint8_t buf[5] = {0};
    Msg msg;

    buf[0] = LIN_IPC_UPDATE_CMD_PACKAGE;   
    buf[1] = deviceId;  
    buf[2] = (uint8_t)(g_linInfo.updateInfo.currentPacketIndex >> 8);
    buf[3] = (uint8_t)(g_linInfo.updateInfo.currentPacketIndex);
    buf[4] = status;

    msg.event = MESSAGE_TX_STB_UPDATE_RESPONSE;
    msg.len    = 5;
    msg.lparam = (uint32_t)&buf[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: LinTxStbWorkMode
函数功能: Stb发送工作模式
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinTxStbWorkMode(uint8_t deviceId)
{
    uint8_t buf[4] = {0};
    Msg msg;

    buf[0] = LIN_IPC_UPDATE_CMD_READ_WORK_MODE;  //工作模式查询
    buf[1] = deviceId;
    buf[2] = g_linInfo.deviceInfo[deviceId].workMode;
    buf[3] = g_linInfo.deviceInfo[deviceId].version; 

    msg.event = MESSAGE_TX_STB_UPDATE_RESPONSE;
    msg.len    = 4;
    msg.lparam = (uint32_t)&buf[0];
    SystemSendMessage(TASK_ID_IPC, msg);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb:%d workmode:%d version:%d\r\n", deviceId, g_linInfo.deviceInfo[deviceId].workMode, g_linInfo.deviceInfo[deviceId].version);
}

/*************************************************
函数名称: LinTxStbRequestUpdate
函数功能: Stb发送请求升级
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/04/22
*************************************************/
void LinTxStbRequestUpdate(uint8_t deviceId)
{
    uint8_t buf[2] = {0};
    Msg msg;

    buf[0] = LIN_IPC_UPDATE_CMD_REQUEST; 
    buf[1] = deviceId; 

    msg.event = MESSAGE_TX_STB_UPDATE_RESPONSE;
    msg.len    = 2;
    msg.lparam = (uint32_t)&buf[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: LinParseMessage
函数功能: Lin解析信息
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/04/17
*************************************************/
// #define RSSI_BUFF_LEN (5)
// static void LinParseMessage(uint8_t id, uint8_t *data)
// {
//     uint8_t cmd = 0;
//     StbDeviceId    deviceId = STB_DEVICE_ALL;
//     static uint8_t frameStateCount = 0;
//     static uint8_t Rssi_buf[4][RSSI_BUFF_LEN];
//     static int cnt_num = 0;
//     int avg_data1 = 0;
//     int avg_data2 = 0;
//     int avg_data3 = 0;
//     char data_0_cnt[3]={0};
//     if((LIN_ID_DIAG_RESPONSE < id)||(NULL == data))
//     {
//         return;
//     }
//     else if(MCU_FTM_TEST_LIN_ID == id)
//     {
//         FtmRxLinResponse(&data[0]);
//         return;
//     }
//     else if(LIN_ID_DIAG_RESPONSE == id)  
//     {
//         return;
//     }
//     else if(LIN_ID_LINE_CALI_READ_RSSI == id)
//     {
//         memcpy(&g_linInfo.lineCaliInfo.ReadRssiBuff.MAC[0], &data[0], 6);
//         g_linInfo.lineCaliInfo.ReadRssiBuff.rssi = data[6];
//         g_linInfo.lineCaliInfo.ReadRssiState = STB_LINE_CALI_READ_RSSI_RX;
//         return;
//     }
//     else if(LIN_ID_LINE_CALI_READ_IDLE_MAC == id)
//     {
//         g_linInfo.isIdleBoardOnLine = TRUE;
//         memcpy(&g_linInfo.lineCaliInfo.idleBoardMac[0], &data[0], 6);
//         return;
//     }
//     else if(GetRealId(STB_DEVICE_RIGHT_A_PILLAR, 0) > id)
//     {
//         deviceId = STB_DEVICE_LEFT_A_PILLAR;
//         g_stbmodlenormal.StbDataSendCnt++;
//     }
//     else if((GetRealId(STB_DEVICE_RIGHT_A_PILLAR, 0) <= id) && (GetRealId(STB_DEVICE_TRUNK, 0) > id))
//     {
//         deviceId = STB_DEVICE_RIGHT_A_PILLAR;
//         g_stbmodlenormal.StbDataSendCnt++;
//     }
//     else if(GetRealId(STB_DEVICE_TRUNK, 0) <= id)
//     {
//         deviceId = STB_DEVICE_TRUNK;
//         g_stbmodlenormal.StbDataSendCnt++;
//     }
//     else
//     {
//         return;
//     }

//     cmd = id - GetRealId(deviceId, 0);
//     switch(cmd)
//     {
//         case LIN_ID_READ_STATE:
//         {
//             g_linInfo.deviceInfo[deviceId].currentRssi = data[0];
//             if((g_stbmodlenormal.StbDataSendCnt-1) >= (g_stbmodlenormal.StbLeftNormal+g_stbmodlenormal.StbRightNormal+g_stbmodlenormal.StbTrunkNormal))
//             {
//                 g_stbmodlenormal.StbDataSendCnt = 1;
//                 LinLineRssiSendIpc(g_linInfo);
//             }
//             if(deviceId >= 3)
//             {
//                 LinLineRssiSendIpc(g_linInfo);    
//             }
//               Rssi_buf[deviceId][cnt_num] = data[0];
//             if(deviceId >= 3)
//             {        
//                 cnt_num++;                
//             }
//             if(cnt_num > RSSI_BUFF_LEN)
//             {
//                 for(cnt_num = 0 ; cnt_num < RSSI_BUFF_LEN; cnt_num++)
//                 {
//                     avg_data1 += Rssi_buf[1][cnt_num];
//                     if(!Rssi_buf[1][cnt_num])
//                     {
//                         data_0_cnt[0]++;
//                     }

//                     avg_data2 += Rssi_buf[2][cnt_num];
//                     if(!Rssi_buf[2][cnt_num])
//                     {
//                         data_0_cnt[1]++;
//                     }
//                     avg_data3 += Rssi_buf[3][cnt_num];
//                     if(!Rssi_buf[3][cnt_num])
//                     {
//                         data_0_cnt[2]++;
//                     }                    
//                 }
//                 cnt_num = 0;
//                 g_linInfo.deviceInfo[1].currentRssi= avg_data1/(RSSI_BUFF_LEN-data_0_cnt[0]);
//                 g_linInfo.deviceInfo[2].currentRssi= avg_data2/(RSSI_BUFF_LEN-data_0_cnt[1]);            
//                 g_linInfo.deviceInfo[3].currentRssi= avg_data3/(RSSI_BUFF_LEN-data_0_cnt[2]);
//                 //SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb_avg: %d %d %d\r\n", g_linInfo.deviceInfo[1].currentRssi,g_linInfo.deviceInfo[2].currentRssi,g_linInfo.deviceInfo[3].currentRssi);
//                 LinLineRssiSendIpc(g_linInfo);
//             }
//             g_linInfo.deviceInfo[deviceId].errorCode = data[1];
//             break;
//         }
//         case LIN_ID_READ_MAC_ADD:
//         {
//             memcpy(&g_linInfo.deviceInfo[deviceId].mac[0],&data[0],6);
//             break;
//         }
//         case LIN_ID_READ_WORK_MODE:
//         {
//             g_linInfo.deviceInfo[deviceId].workMode = data[0];
//             g_linInfo.deviceInfo[deviceId].version = data[1];
//             g_linInfo.deviceInfo[deviceId].watchDogCount = data[2];
//             g_linInfo.deviceInfo[deviceId].watchDogState[0] = data[3];
//             g_linInfo.deviceInfo[deviceId].watchDogState[1] = data[4];
//             g_linInfo.deviceInfo[deviceId].watchDogState[2] = data[5];
//             g_linInfo.deviceInfo[deviceId].watchDogState[3] = data[6];
//             break;
//         }
//         case LIN_ID_READ_SCAN_MAC:
//         {
//             if(STB_SET_MAC_WAIT_RESPONSE == g_linInfo.setPara.setMacState)
//             {
//                 memcpy(&g_linInfo.setPara.scanMac[0], &data[0], 6);
//                 g_linInfo.setPara.scanMacType = data[6];
//                 g_linInfo.setPara.setMacState = STB_SET_MAC_SET;
//             }
//             break;
//         }
//         case LIN_ID_READ_UPDATE_DATA_STATUS:
//         {
//             if(STB_UPDATE_WAIT_STB_RESULT == g_linInfo.updateInfo.status)
//             {
//                 if(((data[0] == g_linInfo.updateInfo.frameIndex) && (FRAME_IDLE != data[1])) ||
//                     (FRAME_ERROR == data[1]))
//                 {
//                     g_linInfo.updateInfo.frameStatus = (StbUpdateFrameStatus)data[1];
//                     g_linInfo.updateInfo.status = STB_UPDATE_PACK_UPDATING;
//                 }
//                 else if(FRAME_IDLE == data[1])
//                 {
//                     frameStateCount++;
//                     if(5 < frameStateCount)
//                     {
//                         frameStateCount = 0;
//                         g_linInfo.updateInfo.frameStatus = FRAME_ERROR;
//                         g_linInfo.updateInfo.status = STB_UPDATE_PACK_UPDATING;
//                     }
//                 }
//             }
//             break;
//         }
//         default:
//             break;
//     }
// }
/*************************************************
函数名称: LinIsrRxFunction
函数功能: Lin中断处理
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinIsrRxFunction(void)
{
    
    /*
    uint8_t id = RLN2401.L1IDB;
    uint8_t state = RLN2401.L1ST;
    uint8_t len = RLN2401.L1DFC&0x0F;
    uint8_t i = 0;
    volatile uint8_t *dataAddr = &RLN2401.L1DBR1;
    uint8_t buf[8] = {0};

    //检查响应接收标志
    if(state&0x02)  
    {
        RLN2401.L1ST &= 0xFD;        
        for(i = 0; i < len; i++)
        {
            buf[i]= dataAddr[i];  
        }
        LinParseMessage(id, buf);

        g_linInfo.sendState = LIN_STATE_SEND_IDLE;
        g_linInfo.linLostCount = 0;
    }
    //帧发送成功标志
    else if(state & 0x01) 
    {
        RLN2401.L1ST &= 0xFE;
        g_linInfo.sendState = LIN_STATE_SEND_IDLE;
    }
    else if(state & 0x08)
    {
        RLN2401.L1ST &= 0xF7;
        if(0x20 == RLN2401.L1EST) //LIN总线错误
        {
            RLN2401.L1EST = 0x00;
            if(LIN_ID_LINE_CALI_READ_RSSI == id)
            {
                g_linInfo.lineCaliInfo.ReadRssiState = STB_LINE_CALI_READ_RSSI_ERROR;
            }
            else if(LIN_ID_LINE_CALI_READ_IDLE_MAC == id)
            {
                g_linInfo.isIdleBoardOnLine = TRUE;
            }
            else
            {
                if(g_linInfo.lineCaliInfo.isCaliRunning == TRUE)
                {
                    return;
                }
                if(LIN_STATUS_NORMAL == g_linInfo.majorStatus && LIN_CHECK_STB_WAKEUP == g_linInfo.subStatus)
                {
                    //检测到有总线冲突后，主动进行一次信标重新标定，将三个信标进行1/2/3排序
                    if(g_linInfo.lineCaliInfo.conflictCnt++<3)
                    {
                        BtTaskVinCodeWriteHook();
                    }
                    else
                    {
                        g_linInfo.lineCaliInfo.conflictCnt = 3;
                    }
                }
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "major:%d, sub:%d\r\n", g_linInfo.majorStatus, g_linInfo.subStatus);
            }
            
        }
    }
    */
}

/*************************************************
函数名称: LinSendPublicData
函数功能: Lin发送公共数据
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinSendPublicData(LinPublicIdCmd id, uint8_t len, uint8_t *data)
{
    uint8_t sendBuf[8] = {0};

    if(7 < len)
    {
        return;
    }

    sendBuf[0] = id;
    
    if((LIN_PUBLIC_NOTIFY_WAKE_UP == id) || (LIN_PUBLIC_NOTIFY_SLEEP == id))
    {
        //RLin21SendData(LIN_CMD_TX_DATA, LIN_ID_PUBLIC_ID, 1, &sendBuf[0]);
    }
    else
    {
        memcpy(&sendBuf[1], &data[0], len);
        //RLin21SendData(LIN_CMD_TX_DATA, LIN_ID_PUBLIC_ID, (len+1), &sendBuf[0]);
    }
}

/*************************************************
函数名称: LinSendLineCaliPublicData
函数功能: Lin发送产线标定公共数据
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinSendLineCaliPublicData(LinPublicIdCmd id, uint8_t len, uint8_t *data)
{
    uint8_t sendBuf[8] = {0};

    if(7 < len)
    {
        return;
    }

    sendBuf[0] = id;
    
    if(0 < len)
    {
        memcpy(&sendBuf[1], &data[0], len);
    }

    //RLin21SendData(LIN_CMD_TX_DATA, LIN_ID_LINE_CALI_PUBLIC_ID, 8, &sendBuf[0]);
}

/*************************************************
函数名称: LinInitRam
函数功能: Lin初始化状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/12/19
*************************************************/
void LinInitRam(void)
{
    //g_commonInfo.btStatus = BT_STATUS_INACTIVE;
    g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
    g_linInfo.APPSalveMode = STB_APP_SUPPORT_SALVE_MODE;

    g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].currentRssi = 0xFF;
    g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].currentRssi = 0xFF;
    g_linInfo.deviceInfo[STB_DEVICE_TRUNK].currentRssi = 0xFF;

    g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].workMode = STB_WORK_UNKONW_MODE;
    g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].workMode = STB_WORK_UNKONW_MODE;
    g_linInfo.deviceInfo[STB_DEVICE_TRUNK].workMode = STB_WORK_UNKONW_MODE;

    g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].errorCode = STB_ERROR_CODE_NORMAL;
    g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].errorCode = STB_ERROR_CODE_NORMAL;
    g_linInfo.deviceInfo[STB_DEVICE_TRUNK].errorCode = STB_ERROR_CODE_NORMAL;

    /*ZXL, stb模块的看门狗复位次数记录， 是否应该在初始化时清零*/
    g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].watchDogCount = 0x00;
    g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].watchDogCount = 0x00;
    g_linInfo.deviceInfo[STB_DEVICE_TRUNK].watchDogCount = 0x00;

    g_linInfo.sendState = LIN_STATE_SEND_IDLE;

    g_linInfo.setPara.phoneModel  = ANDROID_PHONE;
    g_linInfo.setPara.scanUuid    = 0x1218;
    g_linInfo.setPara.setMacState = STB_SET_MAC_IDLE;

    g_linInfo.majorStatus = LIN_STATUS_POWER_ON_CHECK_STB;
    g_linInfo.subStatus = LIN_CHECK_STB_WAKEUP;

    g_linInfo.beaconScanDuration = BEACON_FIRST_READ_COUNT;
    g_linInfo.stbLocationSwitch = FALSE;

    g_locationInfo.threshold.stbIndexCheck = STB_FAULT_CHECK_RSSI;

    g_linInfo.deviceFaultNum = 0xFF;
    g_linInfo.isIdleBoardOnLine = FALSE;

    if( (TRUE == MacIsValid(&g_tboxBleKeyHardPara.STBPara[0].Mac[0])) &&
        (TRUE == MacIsValid(&g_tboxBleKeyHardPara.STBPara[1].Mac[0])) &&
        (TRUE == MacIsValid(&g_tboxBleKeyHardPara.STBPara[2].Mac[0])) )
    {
        g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_DONE;
    }
    else
    {
        g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_NOT_DONE;
    }

    g_linInfo.lineCaliInfo.isCaliRunning = false;
    g_linInfo.lineCaliInfo.conflictCnt = 0x00;
}

/*************************************************
函数名称: LinUpdatePeriodHandle     
函数功能: Lin升级周期处理函数
输入参数: 
输出参数: 
函数返回类型值：0:未升级     1：升级中
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinUpdatePeriodHandle(void)
{
    uint8_t buf[8];
    static uint8_t buf_bak[8];    
    static uint8_t errorTimeCount = 0;
    static uint8_t rebootTimeCount = 0;
    static uint8_t frameLenBak = 0;
    static uint8_t waitAppModeCount = 0;
    static uint8_t waitBootModeCount = 0;
    static uint8_t waitResultCount = 0;
    static uint8_t retryCount = 0;

    uint8_t frameLen = 0;
    StbDeviceId deviceId; 
    static uint16_t sendPacketCount = 0;

    if(STB_UPDATE_IDLE == g_linInfo.updateInfo.status)
    {
        return;
    }
    if((STB_DEVICE_LEFT_A_PILLAR > g_linInfo.updateInfo.deviceID) ||
        (STB_DEVICE_TRUNK < g_linInfo.updateInfo.deviceID))
    {
        return;
    }

    /*20220312,zxl,T-Box是否支持蓝牙信标功能*/
    if(0x00 == LinCheckWhetherSupportStb())
    {
        return;
    }

    deviceId = g_linInfo.updateInfo.deviceID;

    if(LIN_STATE_SENDING == g_linInfo.sendState)
    {
        g_linInfo.linLostCount++;
        if(1500 < g_linInfo.linLostCount) //
        {
            g_linInfo.linLostCount = 0;
            g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_OFFLINE;
            g_linInfo.deviceInfo[deviceId].version = 0;
            LinTxStbWorkMode(deviceId);
            g_linInfo.sendState = LIN_STATE_SEND_IDLE;
            g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Stb %d LOST\r\n", g_linInfo.updateInfo.deviceID);
            if(STB_DEVICE_TRUNK == deviceId)
            {
                g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
            }
        }
        else if(0 == g_linInfo.linLostCount%100) 
        {
            //超过一定时间一直是传输中，则清除状态，防止卡死
            g_linInfo.sendState = LIN_STATE_SEND_IDLE;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "lin trans clean\r\n");
        }
        return;
    }
    
    switch (g_linInfo.updateInfo.status)
    {
        case STB_UPDATE_START:  //开始升级
        {
            sendPacketCount = 0;
            g_linInfo.sendState = LIN_STATE_SENDING;
            //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_START_UPDATE), 0, NULL);
            g_linInfo.updateInfo.status = STB_UPDATE_WAIT_BOOT_MODE;
            waitBootModeCount = 0;
            retryCount = 0;
            g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_UNKONW_MODE;
            g_linInfo.updateInfo.frameStatus = FRAME_WRITE_SUCCESS;            
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Stb %d tx start update\r\n", deviceId);
            break;
        }
        case STB_UPDATE_PACK_UPDATING:  // 升级包升级中
        {
            if((FRAME_NUMBER_REPETITIVE == g_linInfo.updateInfo.frameStatus) || 
                (FRAME_ERROR == g_linInfo.updateInfo.frameStatus))
            {
                errorTimeCount ++;
                if(10 < errorTimeCount)
                {
                    errorTimeCount = 0;
                    rebootTimeCount++;
                    if(10 < rebootTimeCount)
                    {
                        rebootTimeCount = 0;
                        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "stb tx error 100 times, reboot\r\n");                
                        //RLin21SendData(LIN_CMD_TX_DATA,GetRealId(deviceId, LIN_ID_REBOOT), 0, NULL);
                        LinTxStbUpdateStatus(g_linInfo.updateInfo.deviceID, STB_PACK_WRITE_FAIL);
                        g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
                    }
                    g_linInfo.updateInfo.status = STB_UPDATE_WAIT_PACK;
                    LinTxStbUpdateStatus(g_linInfo.updateInfo.deviceID, STB_PACK_ERROR);
                    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "stb tx error 10 times\r\n");
                    break;
                }
                if(0 == (errorTimeCount%10))
                {
                    memcpy(&buf[0], &buf_bak[0], 8);//有错误，重发
                    frameLen = frameLenBak;
                }
                else
                {
                    break;
                }
            }
            else if(FRAME_WRITE_FAIL == g_linInfo.updateInfo.frameStatus)
            {
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "stb flash write error, reboot\r\n");                
                //RLin21SendData(LIN_CMD_TX_DATA,GetRealId(deviceId, LIN_ID_REBOOT), 0, NULL);
                LinTxStbUpdateStatus(g_linInfo.updateInfo.deviceID, STB_PACK_WRITE_FAIL);
                g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
                break;
            }
            else
            {
                rebootTimeCount = 0;
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "stb packageLength %d  packageDataIndex %d\r\n",g_linInfo.updateInfo.currentPacketDataLen, g_linInfo.updateInfo.currentPacketDataPos);
                if(g_linInfo.updateInfo.currentPacketDataLen == (g_linInfo.updateInfo.currentPacketDataPos))
                {
                    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx frame finish\r\n");
                    //升级包发送完成
                    LinTxStbUpdateStatus(g_linInfo.updateInfo.deviceID, STB_PACK_WRITE_SUCCEED);
                    if(0 == g_linInfo.updateInfo.currentPacketIndex%50)
                    {
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d Packnum %d succeed\r\n", deviceId, g_linInfo.updateInfo.currentPacketIndex);
                    }

                    g_linInfo.updateInfo.status = STB_UPDATE_WAIT_PACK;
                    return;
                }
                    
                buf[0] = g_linInfo.updateInfo.frameIndex;

                if(0 == g_linInfo.updateInfo.currentPacketDataPos) 
                {
                    frameLen = 6;
                    buf[1] = frameLen|(1<<6); //起始帧
                }
                else if(6 < (g_linInfo.updateInfo.currentPacketDataLen - g_linInfo.updateInfo.currentPacketDataPos))
                {
                    frameLen = 6;
                    buf[1] = frameLen;//len
                }
                else
                {
                    frameLen = g_linInfo.updateInfo.currentPacketDataLen - g_linInfo.updateInfo.currentPacketDataPos;//len                    
                    buf[1] = frameLen|(1<<7);  //结束帧
                    sendPacketCount++;
                }
                memcpy(&buf[2], &g_linInfo.updateInfo.packetData[g_linInfo.updateInfo.currentPacketDataPos], frameLen);
                g_linInfo.updateInfo.frameIndex += 1;
                g_linInfo.updateInfo.currentPacketDataPos += frameLen;
                //备份
                memcpy(&buf_bak[0], &buf[0], 8);
                frameLenBak = frameLen;
            }
            
            g_linInfo.sendState = LIN_STATE_SENDING;
            //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_SEND_UPDATE_DATA), 8, &buf[0]);
            //if((0x01 == (buf[1]>>6)&0x01) || (0x01 == (buf[1]>>7)&0x01))
            //{
            //    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d tx len %d Num %d buf[1]0x%x\r\n", deviceId, (frameLen+2), buf[0], buf[1]);
            //}
            g_linInfo.updateInfo.status = STB_UPDATE_WAIT_STB_RESULT;
            waitResultCount = 0;
            break;
        }
        case STB_UPDATE_WAIT_STB_RESULT:
        {
            if(200 < waitResultCount++)
            {
                waitResultCount = 0;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "no result\r\n");                    
                g_linInfo.updateInfo.frameStatus = FRAME_ERROR;
                g_linInfo.updateInfo.status = STB_UPDATE_PACK_UPDATING;
            }
            if(0 == waitResultCount%10)  //2ms
            {
                g_linInfo.sendState = LIN_STATE_SENDING;
                //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_UPDATE_DATA_STATUS), 2, NULL);      
            }
            break;
        }
        case STB_UPDATE_END:  // 升级结束
        {
            g_linInfo.sendState = LIN_STATE_SENDING;
            //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_END_UPDATE), 0, NULL);
            g_linInfo.updateInfo.status = STB_UPDATE_WAIT_APP_MODE;            
            waitAppModeCount = 0;
            retryCount = 0;
            g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_UNKONW_MODE;
            break;
        }
        case STB_UPDATE_WORK_MODE:  // 读工作模式
        {
            if(STB_WORK_UNKONW_MODE != g_linInfo.deviceInfo[deviceId].workMode)
            {
                g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
                LinTxStbWorkMode(deviceId);
                break;
            }
            g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_UNKONW_MODE;            
            g_linInfo.sendState = LIN_STATE_SENDING;
            //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_WORK_MODE), 7, NULL);
            break;
        }
        case STB_UPDATE_WAIT_APP_MODE:   //等待进入APP模式
        {
            if(STB_WORK_APP_MODE == g_linInfo.deviceInfo[deviceId].workMode)
            {
                g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
                g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                LinTxStbWorkMode(deviceId);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "STB switch App, update finish %d\r\n", sendPacketCount);                
                break;
            }
            waitAppModeCount++;
            if(20 < waitAppModeCount)
            {                
                waitAppModeCount = 0;
                if(10 < retryCount++)
                {
                    retryCount = 0;
                    g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
                    g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                    g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                    LinTxStbWorkMode(deviceId);
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "STB switch App, update fail %d\r\n", sendPacketCount);                  
                    break;
                }
                //多次查询没有转换到APP模式，重发指令
                g_linInfo.sendState = LIN_STATE_SENDING;
                //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_END_UPDATE), 0, NULL);
            }
            else if(10 == waitAppModeCount)  
            {
                g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_UNKONW_MODE;            
                g_linInfo.sendState = LIN_STATE_SENDING;
                //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_WORK_MODE), 7, NULL);
            }
            break;
        }
        case STB_UPDATE_WAIT_BOOT_MODE:   //等待进入BOOT模式
        {
            if(STB_WORK_BOOT_MODE == g_linInfo.deviceInfo[deviceId].workMode)
            {
                g_linInfo.sendState = LIN_STATE_SENDING;
                //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_FLASH_ERASE), 0, NULL);
                g_linInfo.updateInfo.status = STB_UPDATE_WAIT_PACK;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "STB switch Boot, update start\r\n");
                LinTxStbWorkMode(deviceId);
                break;
            }
            waitBootModeCount++;
            if(20 < waitBootModeCount)  //多次查询没有转换到BOOT模式，重发指令
            {
                waitBootModeCount = 0;
                if(10 < retryCount++)
                {
                    retryCount = 0;
                    g_linInfo.updateInfo.status = STB_UPDATE_IDLE;
                    g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                    g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                    LinTxStbWorkMode(deviceId);
                    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "STB switch Boot fail\r\n");  
                    break;
                }
                g_linInfo.sendState = LIN_STATE_SENDING;
                //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_START_UPDATE), 0, NULL);            
                SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "Stb %d re tx start update %d  id:%d\r\n", deviceId, retryCount, GetRealId(deviceId, LIN_ID_START_UPDATE));
            }
            else if(10 == waitBootModeCount)
            {
                g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_UNKONW_MODE;            
                g_linInfo.sendState = LIN_STATE_SENDING;
                //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_WORK_MODE), 7, NULL);
                SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "Stb %d tx read mode\r\n", deviceId);
            }
            break;
        }
        case STB_UPDATE_FLASH_ERASE:   //擦除FLASH
        {
            g_linInfo.sendState = LIN_STATE_SENDING;
            //RLin21SendData(LIN_CMD_TX_DATA, GetRealId(deviceId, LIN_ID_FLASH_ERASE), 0, NULL);
            g_linInfo.updateInfo.status = STB_UPDATE_WAIT_PACK;
            break;
        }
        default:
            break;
    }
    return;
}

/*************************************************
函数名称: LinIpcRxUpdateHandle
函数功能: 接收到ipc升级命令
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/02/01
*************************************************/
void LinIpcRxUpdateHandle(uint8_t *para, uint16_t len)
{
    static uint16_t count = 0;

    switch (para[0])
    {
        case LIN_IPC_UPDATE_CMD_READ_WORK_MODE: //查询STB工作模式及软件版本号
        {
            g_linInfo.updateInfo.status = STB_UPDATE_WORK_MODE;
            if(STB_DEVICE_LEFT_A_PILLAR == para[1])// 左前门模块
            {
                g_linInfo.updateInfo.deviceID = STB_DEVICE_LEFT_A_PILLAR;                
                g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].workMode = STB_WORK_UNKONW_MODE;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "read work mode, left A pillar\r\n");
            }
            else if(STB_DEVICE_TRUNK == para[1]) //后备箱模块
            {
                g_linInfo.updateInfo.deviceID = STB_DEVICE_TRUNK;
                g_linInfo.deviceInfo[STB_DEVICE_TRUNK].workMode = STB_WORK_UNKONW_MODE;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "read work mode, trunk\r\n");
            }
            else if(STB_DEVICE_RIGHT_A_PILLAR == para[1]) //右前门
            {
                g_linInfo.updateInfo.deviceID = STB_DEVICE_RIGHT_A_PILLAR;
                g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].workMode = STB_WORK_UNKONW_MODE;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "read work mode, right A pillar\r\n");
            }
            break;
        }
        case LIN_IPC_UPDATE_CMD_START:  //开始升级
        {
            count = 0;
            if(STB_UPDATE_IDLE == g_linInfo.updateInfo.status)
            {
                g_linInfo.updateInfo.status = STB_UPDATE_START;
                g_linInfo.majorStatus = LIN_STATUS_UPDATE_STB;
                if(STB_DEVICE_LEFT_A_PILLAR == para[1])// 左前门模块
                {
                    g_linInfo.updateInfo.deviceID = STB_DEVICE_LEFT_A_PILLAR;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Start Update, left A pillar\r\n");
                }
                else if(STB_DEVICE_TRUNK == para[1]) //后备箱模块
                {
                    g_linInfo.updateInfo.deviceID = STB_DEVICE_TRUNK;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Start Update, trunk\r\n");
                }
                else if(STB_DEVICE_RIGHT_A_PILLAR == para[1]) //右前门
                {
                    g_linInfo.updateInfo.deviceID = STB_DEVICE_RIGHT_A_PILLAR;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Start Update, right a pillar\r\n");
                }
            }
            break;
        }
        case LIN_IPC_UPDATE_CMD_PACKAGE:  //升级包
        {
            if(para[1] == g_linInfo.updateInfo.deviceID)
            {
                if(STB_UPDATE_WAIT_PACK == g_linInfo.updateInfo.status)
                {
                    g_linInfo.updateInfo.currentPacketIndex    = (uint16_t)((para[2] << 8) + (para[3]));
                    g_linInfo.updateInfo.packetTotalNum  = (uint16_t)((para[4] << 8) + (para[5]));                       
                    g_linInfo.updateInfo.currentPacketDataLen = para[6];
                    g_linInfo.updateInfo.currentPacketDataPos = 0;
                    memcpy(&g_linInfo.updateInfo.packetData[0], &para[7], g_linInfo.updateInfo.currentPacketDataLen);

                    g_linInfo.updateInfo.status = STB_UPDATE_PACK_UPDATING; 
                    g_linInfo.updateInfo.frameStatus = FRAME_IDLE;
                    g_linInfo.updateInfo.frameIndex = 0;
                    count++;
                }
                else
                {
                    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rx STB update pack, BUT TBOX BUSY\r\n");
                }
            }
            else
            {
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rx update index error\r\n");
            }
            break;
        }
        case LIN_IPC_UPDATE_CMD_END:  //结束升级
        {
            if(para[1] == g_linInfo.updateInfo.deviceID)
            {
                g_linInfo.updateInfo.status = STB_UPDATE_END;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "deviceID:%d update end %d\r\n", g_linInfo.updateInfo.deviceID, count);
            }
            else
            {
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Rx update index error\r\n");
            }
            break;
        }
        default:
            break;
    }
}

/*************************************************
函数名称: LinIpcTxLineCaliResult
函数功能: 发送到ipc标定结果
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2020/01/01
*************************************************/
void LinIpcTxLineCaliResult(uint8_t cmd, uint8_t result)
{
    Msg msg;
    uint8_t buf[2] = {0};
    uint8_t index = 0;

    buf[index++] = cmd;
    buf[index++] = result;

    msg.event = MESSAGE_TX_STB_CALIBRATION_RESPONSE;
    msg.len = index;
    msg.lparam = (uint32_t)&buf;

    SystemSendMessage(TASK_ID_IPC, msg);

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Ipc Tx cali cmd:%d Result:%d\r\n", cmd, result);
}

/*************************************************
函数名称: LinIpcRxCalibrationHandle
函数功能: 接收到ipc标定指令处理
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/02/01
*************************************************/
void LinIpcRxCalibrationHandle(uint8_t *para, uint16_t len)
{
    /*ZXL, 接收来自ARM端的标定触发请求*/
    //int cnt = 0;

    switch(para[0])
    {
        case LIN_CALI_STB_STATUS:
        {
            break;
        }
        case LIN_CALI_STB_START:
        case LIN_CALI_STB_DELETE:
        {
            LinClearNVMacIndex();
            LinIpcTxLineCaliResult(LIN_CALI_STB_DELETE, 0);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Line Clear NV Mac Index\r\n");
            break;
        }
        case LIN_CALI_STB_LINE_START:
        {
            /*ZXL, 标定方案变更为使用T-Box本身自带的蓝牙模块进行标定，因此原标定通信协议方案废弃，使用同写VIN码同样的标定触发方案*/
            #if 0
            g_linInfo.lineCaliInfo.caliMode[0] = para[1];
            g_linInfo.lineCaliInfo.caliMode[1] = para[2];
            g_linInfo.lineCaliInfo.caliMode[2] = para[3];

            g_linInfo.lineCaliInfo.scanMode = para[4];
            g_linInfo.lineCaliInfo.scanMode = 0x01;
            if(0 == g_linInfo.lineCaliInfo.scanMode)
            {
                g_linInfo.lineCaliInfo.UUID = (uint16_t)(para[5] << 8) + para[6];
            }
            else if(1 == g_linInfo.lineCaliInfo.scanMode)
            {
                memcpy(&g_linInfo.lineCaliInfo.MAC[0], &para[5], 6);
            }
            else
            {
                LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_START, STB_CALI_RESULT_PARA_ERROR);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Line start scanMode error\r\n");
                break;
            }
            g_linInfo.majorStatus = LIN_STATUS_LINE_CALI_STB;
            g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
            g_linInfo.lineCaliInfo.failCount = 0;

            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Line start scan %d  uuid 0x%x MAC0x%x~%x mode %d-%d-%d\r\n",
                                                g_linInfo.lineCaliInfo.scanMode,
                                                g_linInfo.lineCaliInfo.UUID,
                                                g_linInfo.lineCaliInfo.MAC[0],
                                                g_linInfo.lineCaliInfo.MAC[5],
                                                g_linInfo.lineCaliInfo.caliMode[0],
                                                g_linInfo.lineCaliInfo.caliMode[1],
                                                g_linInfo.lineCaliInfo.caliMode[2]);
            #endif

            /*标定中，即使APP下发了标定命令，也不允许执行*/
            if(g_linInfo.lineCaliInfo.isCaliRunning == FALSE)
            {
                BtTaskVinCodeWriteHook();
            }
            break;
        }
        case LIN_CALI_STB_LINE_CHECK:
        {
            if(g_linInfo.lineCaliInfo.isCaliRunning == FALSE)
            {
                /*ZXL, 这个协议设计的很复杂,都改不了，只有MCU改, 不管APP要执行什么，要检查什么，默认两个一起回复*/
                LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_CHECK, g_linInfo.lineCaliInfo.caliResult);
                /*ZXL, 这个协议设计的很复杂，都改不了，只有MCU改， 不管APP要执行什么，要检查什么，默认两个一起回复*/
                //LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_START, g_linInfo.lineCaliInfo.caliResult);
            }
            else
            {
                /*ZXL, 这个协议设计的很复杂,都改不了，只有MCU改, 不管APP要执行什么，要检查什么，默认两个一起回复*/
                LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_CHECK, STB_CALI_RESULT_DOING);
                /*ZXL, 这个协议设计的很复杂，都改不了，只有MCU改， 不管APP要执行什么，要检查什么，默认两个一起回复*/
                //LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_START, STB_CALI_RESULT_DOING);
            }
            break;
        }
        case LIN_CALI_STB_LINE_SEARCH_FAULT:
        {
            //zhouguo ++
            //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Stb search 0x%x 0x%x 0x%x 0x%x\r\n", para[1], para[2], para[3], para[4]);
            g_linInfo.lineCaliInfo.UUID  = (uint16_t)((para[1] - '0') << 12);
            g_linInfo.lineCaliInfo.UUID += (uint16_t)((para[2] - '0') << 8);
            g_linInfo.lineCaliInfo.UUID += (uint16_t)((para[3] - '0') << 4);
            g_linInfo.lineCaliInfo.UUID += (uint16_t)(para[4] - '0');

            //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Stb search 0x%x\r\n", g_linInfo.lineCaliInfo.UUID);

            /*ZXL, 排故期间将, 标定进行中的变量清除，否则有可能影响APP无法触发标定*/
            g_linInfo.lineCaliInfo.isCaliRunning = FALSE;
            g_linInfo.majorStatus = LIN_STATUS_LINE_SEARCH_STB;
            break;
        }
        case LIN_CALI_STB_ADJUST:
        {
            if((1 == para[1]) || (2 == para[1]))
            {
                LinExchangeNVMacIndex(para[2],para[3]);
                LinExchangeNVMacIndex(para[4],para[5]);
                g_linInfo.majorStatus = LIN_STATUS_LINE_CALI_STB;
                g_linInfo.subStatus = LIN_LINE_CALI_STB_EXCHANGE;
            }
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Stb Adjust %d\r\n", para[1]);
            break;
        }
        case LIN_CALI_STB_APP_FAULT_CHECK:
        {
            LinIpcTxAPPSTBFaultCheck();
            break;
        }
        case LIN_GET_APP_UUID_DATA_CONFIG:
        {
            if(STB_GET_RSSI_ON == para[len-1])
            {
                RcvAppData(para);
            }
            if(STB_GET_RSSI_OFF == para[len-1])
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "STB_GET_RSSI_OFF\r\n");
                OffStbGetRssi();
            }
            break;
        }
        default:
            break;
    }
}

/*************************************************
函数名称: AutoCalibrationHandler
函数功能: 设置自动标定条件，后面蓝牙定时任务会直接执行标定
输入参数: 
输出参数: 
函数返回类型值：
编写： zhouguo
编写日期 :2020/05/28
*************************************************/
void AutoCalibrationHandler(void)
{
    /*ZXL, 触发自动标定，由车辆写VIN、自检失败时触发*/
    if(MacIsValid(g_commonInfo.btMac))
    {
        //已经知晓板载蓝牙模块MAC地址
        g_linInfo.lineCaliInfo.scanMode = 1;
        memcpy(&g_linInfo.lineCaliInfo.MAC[0], &g_commonInfo.btMac[0], 6);
    }
    else
    {
        g_linInfo.lineCaliInfo.scanMode = 0;
        g_linInfo.lineCaliInfo.UUID = 0x1000;
    }

    LinClearNVMacIndex();

    g_linInfo.lineCaliInfo.caliMode[0] = 3;
    g_linInfo.lineCaliInfo.caliMode[1] = 2;
    g_linInfo.lineCaliInfo.caliMode[2] = 1;

    g_linInfo.majorStatus = LIN_STATUS_LINE_CALI_STB;
    g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
    g_linInfo.lineCaliInfo.failCount = 0;
    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_NOT_DONE;
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "trigger auto Line cali start:mode=%d,uuid=0x%x,MAC:0x%02x~%02x\r\n",
                                                g_linInfo.lineCaliInfo.scanMode,
                                                g_linInfo.lineCaliInfo.UUID,
                                                g_linInfo.lineCaliInfo.MAC[0],
                                                g_linInfo.lineCaliInfo.MAC[5]);
}

/*************************************************
函数名称: LinSTBFaultRecord
函数功能: Lin故障记录函数
输入参数: status 0：无故障 1：有故障
输出参数: 
函数返回类型值：0：无变化 1：有变化
编写： benyulong
编写日期 :2019/02/10
*************************************************/
uint8_t LinSTBFaultRecord(uint8_t index, uint8_t status)
{
    /*
    uint8_t dtcIndex = 0;

    if(index == 1)
        dtcIndex = DTC_BT_STB1_FAULT;
    else if(index == 2)
        dtcIndex = DTC_BT_STB2_FAULT;
    else if(index == 3)
        dtcIndex = DTC_BT_STB3_FAULT;

    if(0x01 != Dem_GetDtcRecordEnableState())
    {
        return 0;
    }

    if(0x00 == (g_dtcInfo.dtc[dtcIndex].status&0x01))  //记录无故障
    {
        if(1 == status)    //lost且记录无故障
        {
            DiagWriteDevicesStatus(dtcIndex, DTC_STATUS_FAULT);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d Fault\r\n", index);
            return 1;
        }
    }
    else if(0x01 == (g_dtcInfo.dtc[dtcIndex].status&0x01)) //记录有故障
    {
        if(0 == status)             //恢复且记录有故障
        {
            DiagWriteDevicesStatus(dtcIndex, DTC_STATUS_NO_FAULT);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d Recovery\r\n", index);
            return 1;
        }
    }
    */
    return 0;
}

/*************************************************
函数名称: LinIpcTxAPPSTBFaultCheck
函数功能: LinIpcTxAPPSTBFaultCheck
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2020/01/09
*************************************************/
void LinIpcTxAPPSTBFaultCheck(void)
{
    Msg msg;
    uint8_t buf[3] = {0};
    uint8_t i = 0;
    uint8_t temp[STB_DEVICE_TOTAL] = {0};    

    buf[0] = LIN_CALI_STB_APP_FAULT_CHECK;
    buf[1] = STB_DEVICE_LEFT_A_PILLAR;

    for(i = STB_DEVICE_LEFT_A_PILLAR; i < STB_DEVICE_TOTAL; i++)
    {
        if(0 == g_linInfo.deviceInfo[i].calcRssi)
        {
            temp[i] = 0xFF;
        }
        else
        {
            temp[i] = g_linInfo.deviceInfo[i].calcRssi;
        }

        if(temp[i] < temp[buf[1]])
        {
            buf[1] = i;
        }
    }

    if((0 != g_linInfo.deviceFaultNum) || (0 == g_linInfo.deviceInfo[buf[1]].calcRssi))
    {
        buf[1] = 0xFF;
    }
    else if(g_locationInfo.threshold.stbIndexCheck < g_linInfo.deviceInfo[buf[1]].calcRssi)
    {
        buf[1] = 0;
    }
    // else if((APPLE_PHONE == g_linInfo.setPara.phoneModel) && 
    //         (APPLE_STB_FAULT_CHECK_RSSI < g_linInfo.deviceInfo[buf[1]].calcRssi))
    // {
    //     buf[1] = 0;
    // }
    // else if((ANDROID_PHONE == g_linInfo.setPara.phoneModel) && 
    //         (ANDROID_STB_FAULT_CHECK_RSSI < g_linInfo.deviceInfo[buf[1]].calcRssi))
    // {
    //     buf[1] = 0;
    // }

    msg.event = MESSAGE_TX_STB_CALIBRATION_RESPONSE;
    msg.len = 2;
    msg.lparam = (uint32_t)&buf;
    SystemSendMessage(TASK_ID_IPC, msg);
    
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "STB APP Fault Check %d-%d\r\n", buf[1], g_linInfo.deviceInfo[buf[1]].calcRssi); 
}

/*************************************************
函数名称: LinPowerOnCheckStb
函数功能: Lin检查STB设备
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinPowerOnCheckStb(void)
{
    static uint8_t count = 0;
    static StbDeviceId deviceId = STB_DEVICE_LEFT_A_PILLAR;
    static uint8_t readCount = 0;
    uint8_t buf[7] = {0};
#if 0    
    if(HANDSHAKE_SUCESS != g_commonInfo.handshakeStatus)
    {
        return;
    }
#endif
    switch (g_linInfo.subStatus)
    {
        case LIN_CHECK_STB_WAKEUP:
        {
            LinSendPublicData(LIN_PUBLIC_NOTIFY_WAKE_UP, 0, NULL);     
            memset(&g_stbmodlenormal,1,sizeof(g_stbmodlenormal));
            g_linInfo.subStatus = LIN_CHECK_STB_SEND_MAC_INDEX;
            deviceId = STB_DEVICE_LEFT_A_PILLAR;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu wakeup STB\r\n");
            break;
        }
        case LIN_CHECK_STB_SEND_MAC_INDEX:
        {
            if(20 > readCount++)  //delay 200ms 等待STB唤醒
            {
                break;
            }
            readCount = 0;

            /*ZXL, 唤醒起来后，如果MAC地址有效，则发送LIN_LINE_PUBLIC_CALI_NUMBER 给STB， 但是在LinParseMessage 里面解析的内容和这里不对应，实际接收逻辑是走不进来的*/
            if(MacIsValid(&g_tboxBleKeyHardPara.STBPara[deviceId-1].Mac[0]))
            {
                memcpy(&buf[0], &g_tboxBleKeyHardPara.STBPara[deviceId-1].Mac[0], 6);
                buf[6] = deviceId;
                LinSendLineCaliPublicData(LIN_LINE_PUBLIC_CALI_NUMBER, 7, &buf[0]);                                                             
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Mac 0x%x~%x index %d\r\n",
                                   g_tboxBleKeyHardPara.STBPara[deviceId-1].Mac[0],
                                   g_tboxBleKeyHardPara.STBPara[deviceId-1].Mac[5], 
                                   deviceId);
            }
            if(STB_DEVICE_TRUNK == deviceId)
            {
                g_linInfo.subStatus = LIN_CHECK_STB_WORK_MODE;
                deviceId = STB_DEVICE_LEFT_A_PILLAR;
                break;
            }
            deviceId += 1;
            break;
        }
        case LIN_CHECK_STB_WORK_MODE:
        {
            /*ZXL, 返回结果在  LinParseMessage  解析*/
            if(STB_WORK_UNKONW_MODE != g_linInfo.deviceInfo[deviceId].workMode)
            {                
                /*ZXL, 这里上电唤醒起来后默认设置无故障吗?*/
                LinSTBFaultRecord(deviceId, 0);
                if(STB_WORK_BOOT_MODE == g_linInfo.deviceInfo[deviceId].workMode)
                {
                    /*ZXL,通知ARM模块，请求对模块进行升级*/
                    LinTxStbRequestUpdate(deviceId);
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d BOOT mode, version %d, request update\r\n", 
                                        deviceId, g_linInfo.deviceInfo[deviceId].version);
                }
                else
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d APP mode, version %d \r\n", 
                                        deviceId, g_linInfo.deviceInfo[deviceId].version);
                }

                /*ZXL, stb模块看门狗复位次数过多，没有做异常处理吗?*/
                if(0 < g_linInfo.deviceInfo[deviceId].watchDogCount)
                {
                    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "stb %d watchDogCount %d_%d,%d,%d,%d\r\n", deviceId, 
                                        g_linInfo.deviceInfo[deviceId].watchDogCount,
                                        g_linInfo.deviceInfo[deviceId].watchDogState[0],
                                        g_linInfo.deviceInfo[deviceId].watchDogState[1],
                                        g_linInfo.deviceInfo[deviceId].watchDogState[2],
                                        g_linInfo.deviceInfo[deviceId].watchDogState[3] );
                }
                if(STB_DEVICE_TRUNK == deviceId)
                {
                    IpcMsgTxPowerOnReport(); 
                    deviceId = STB_DEVICE_LEFT_A_PILLAR;
                    g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                    g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                    return;
                }
                deviceId += 1;
                count = 0;
            }
            /*ZXL, 连续超过40ms都没有收到STB模块的回应，则判定为STB模块丢失, 对下一个模块进行轮询*/
            /*ZXL, 结合近期部分已装车模块，时而版本号正常，时而不正常，是否应该将次超时时间放宽一点, 将LIN_STB_CHECK_TIMEOUT设置为5*/
            else if(LIN_STB_CHECK_TIMEOUT < count++)
            {
                count = 0;

                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d Lost\r\n", deviceId);   
                LinSTBFaultRecord(deviceId, 1);
                if(STB_DEVICE_TRUNK == deviceId)
                {
                    deviceId = STB_DEVICE_LEFT_A_PILLAR;
                    g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                    g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                    return;   
                }
                deviceId += 1;
            }

            g_linInfo.deviceInfo[deviceId].workMode = STB_WORK_UNKONW_MODE;            
            //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_WORK_MODE), 7,NULL);
            break;
        }
        default:break;
    
    }
}

/*************************************************
函数名称: LinSetStbParaHandle
函数功能: Lin设置STB参数
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinSetStbScanUuid(void)
{
    uint8_t buf[3] = {0};

    buf[0] = (uint8_t)(g_linInfo.setPara.scanUuid >> 8);
    buf[1] = (uint8_t)(g_linInfo.setPara.scanUuid);
    buf[2] = g_linInfo.setPara.phoneModel;
//    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Scan UUID_0 %02x\r\n", buf[0] );
//    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Scan UUID_1 %02x\r\n", buf[1] );
    LinSendPublicData(LIN_PUBLIC_SET_SCAN_UUID, 3, &buf[0]);
}

/*************************************************
函数名称: LinSetStbParaHandle
函数功能: Lin设置STB参数
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinSetStbParaHandle(void)
{
    static LinStbScanSet step = LIN_SET_WAKEUP;
    static uint8_t readCount = 0;
    uint8_t buf[3] = {0};

    switch(step)
    {
        case LIN_SET_WAKEUP:
        {
            LinSendPublicData(LIN_PUBLIC_NOTIFY_WAKE_UP, 0, NULL);           
            // 每次鉴权后重新初始化滤波参数
            step = LIN_SET_SCAN_UUID;
            break;
        }
        case LIN_SET_SCAN_UUID:
        {        
            if(20 > readCount++)  //delay 200ms 等待STB唤醒
            {
                if(0 == readCount%5) //每50ms 再唤醒一次
                {
                    LinSendPublicData(LIN_PUBLIC_NOTIFY_WAKE_UP, 0, NULL);
                }
                break;
            }
            readCount = 0;
            LinSetStbScanUuid();
            step = LIN_SET_SCAN_DURATION;
            break;
        }    
        case LIN_SET_SCAN_DURATION:
        {                   
            if(5 > readCount++)  //delay 200ms 等待STB唤醒
            {
                break;
            }
            readCount = 0;     
            g_linInfo.setPara.scanDuration = BEACON_FIRST_SCAN_DURATION;
            g_linInfo.beaconScanDuration = BEACON_FIRST_READ_COUNT;
            buf[0] = (uint8_t)(g_linInfo.setPara.scanDuration >> 8);
            buf[1] = (uint8_t)(g_linInfo.setPara.scanDuration);
            LinSendPublicData(LIN_PUBLIC_SET_SCAN_DURATION, 2, &buf[0]);
            if(APPLE_PHONE == g_linInfo.setPara.phoneModel)
            {
                g_linInfo.setPara.setMacState = STB_SET_MAC_REQUEST;
            }
            g_linInfo.subStatus = LIN_WAIT_READ_STB_RSSI;
            step = LIN_SET_WAKEUP;
//            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Scan %d\r\n",g_linInfo.setPara.scanDuration);
            break;
        }
        default:
            break;
    }
}

/*************************************************
函数名称: StbMacChangeCheck
函数功能: 检测stb扫描MAC地址改变
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/08/01
*************************************************/
void StbMacChangeCheck()
{
    static uint8_t count = 0;
    static uint8_t lostCount[STB_DEVICE_TOTAL] = {0};
    static uint16_t lostTotalCount = 0;
    uint8_t deviceId = 0;
    uint8_t buf[7] = {0};

    for(deviceId = STB_DEVICE_LEFT_A_PILLAR; deviceId < STB_DEVICE_TOTAL; deviceId++)
    {
        if((0 == g_linInfo.deviceInfo[deviceId].currentRssi) ||
           (0xFF == g_linInfo.deviceInfo[deviceId].currentRssi))
        {
            if(0xFF > lostCount[deviceId])
            {
                lostCount[deviceId]++;
            }
            else
            {
                g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                g_linInfo.subStatus = LIN_SET_STB_SCAN_PARA;
                memset(lostCount, 0x00, sizeof(lostCount));               
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Scan Lost %d,%d, reset Stb\r\n",
                                   deviceId, lostCount[deviceId]);
                return;
            }
        }
        else
        {
            lostCount[deviceId] = 0;
        }
    }
    lostTotalCount = (lostCount[STB_DEVICE_LEFT_A_PILLAR] + 
                      lostCount[STB_DEVICE_RIGHT_A_PILLAR] +
                      lostCount[STB_DEVICE_TRUNK]);

    switch (g_linInfo.setPara.setMacState)
    {
        case STB_SET_MAC_IDLE:
        {
            if(0 == lostTotalCount)
            {
                if(BEACON_SCAN_DURATION != g_linInfo.setPara.scanDuration)
                {
                    g_linInfo.setPara.scanDuration = BEACON_SCAN_DURATION;
                    g_linInfo.beaconScanDuration = BEACON_READ_COUNT;
                    buf[0] = (uint8_t)(g_linInfo.setPara.scanDuration >> 8);
                    buf[1] = (uint8_t)(g_linInfo.setPara.scanDuration);
                    LinSendPublicData(LIN_PUBLIC_SET_SCAN_DURATION, 2, &buf[0]);

                }
            }
            if(10 < lostTotalCount)
            {
                g_linInfo.setPara.scanDuration = BEACON_FIRST_SCAN_DURATION;
                g_linInfo.beaconScanDuration = BEACON_FIRST_READ_COUNT;
                buf[0] = (uint8_t)(g_linInfo.setPara.scanDuration >> 8);
                buf[1] = (uint8_t)(g_linInfo.setPara.scanDuration);
                LinSendPublicData(LIN_PUBLIC_SET_SCAN_DURATION, 2, &buf[0]);

                if(APPLE_PHONE == g_linInfo.setPara.phoneModel)
                {
                    count = 0;
                    g_linInfo.setPara.setMacState = STB_SET_MAC_REQUEST;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Mac changed\r\n");
                }
            }
            break;
        }
        case STB_SET_MAC_REQUEST:
        {
            for(deviceId = STB_DEVICE_LEFT_A_PILLAR; deviceId < STB_DEVICE_TOTAL; deviceId++)
            {
                if((0 != g_linInfo.deviceInfo[deviceId].currentRssi) &&
                    (0xFF != g_linInfo.deviceInfo[deviceId].currentRssi))
                {                    
                    //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_SCAN_MAC), 7, NULL);                    
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Read scanMac 0x%d\r\n", deviceId);
                    g_linInfo.setPara.setMacState = STB_SET_MAC_WAIT_RESPONSE;
                    count = 0;
                    break;
                }
            }
            if(STB_SET_MAC_REQUEST == g_linInfo.setPara.setMacState)
            {
                if(80 < count++)  //20秒
                {
                    LinSetStbScanUuid(); // 会导致STB全部重新扫描
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Lin Set Stb Scan Uuid\r\n");
                    count = 0;
                }
            }
            break;
        }
        case STB_SET_MAC_WAIT_RESPONSE:
        {
            if(2 < count++)
            {
                count = 0;
                g_linInfo.setPara.setMacState = STB_SET_MAC_IDLE;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Read scanMac Wait timeout\r\n");
            }
            break;
        }
        case STB_SET_MAC_SET:
        {
            if((0 != g_linInfo.setPara.scanMac[0]) &&
                (0 != g_linInfo.setPara.scanMac[1]))
            {
                buf[0] = g_linInfo.setPara.scanMac[0];
                buf[1] = g_linInfo.setPara.scanMac[1];
                buf[2] = g_linInfo.setPara.scanMac[2];
                buf[3] = g_linInfo.setPara.scanMac[3];
                buf[4] = g_linInfo.setPara.scanMac[4];
                buf[5] = g_linInfo.setPara.scanMac[5];
                buf[6] = g_linInfo.setPara.scanMacType;
                
                LinSendPublicData(LIN_PUBLIC_SET_SCAN_MAC, 7, &buf[0]);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb set scanMac 0x%d-%x_%x\r\n", 
                    g_linInfo.setPara.scanMacType, g_linInfo.setPara.scanMac[0], g_linInfo.setPara.scanMac[1]);
            }            
            g_linInfo.setPara.setMacState = STB_SET_MAC_WAIT;
            count = 0;
            break;
        }
        case STB_SET_MAC_WAIT:
        {
            if(20 < count++) //20个扫描周期
            {
                g_linInfo.setPara.setMacState = STB_SET_MAC_IDLE;
                count = 0;
            }
            break;
        }
        default:
            break;
    }
}

/*************************************************
函数名称: LinLineCaliStbTxCondition
函数功能: Lin发送产线标定条件
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
static void LinLineCaliStbTxCondition(void)
{
    uint8_t buf[2] = {0};

    /*ZXL, 启动扫描时，设置该变量为FALSE*/
    if(TRUE == g_linInfo.lineCaliInfo.subConditionEnable)
    {
        g_linInfo.lineCaliInfo.subCondition++;
        if(LINE_CALI_STB_SUB_CONDITION_MAX < g_linInfo.lineCaliInfo.subCondition -1)
        {
            g_linInfo.lineCaliInfo.subConditionEnable = FALSE;
        }
    }

    /*ZXL, 启动扫描时，设置该变量为FALSE*/
    if(FALSE == g_linInfo.lineCaliInfo.subConditionEnable)
    {
        g_linInfo.lineCaliInfo.mainCondition++;
        if(LINE_CALI_STB_MAIN_CONDITION_MAX < g_linInfo.lineCaliInfo.mainCondition - 1)
        {
            if(2 <= g_linInfo.lineCaliInfo.failCount++)
            {
                if (0 == g_linInfo.lineCaliInfo.ReadRssiIndex)
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_1_TIMEOUT;
                }
                else if(1 == g_linInfo.lineCaliInfo.ReadRssiIndex)
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_2_TIMEOUT;
                }
                else if(2 == g_linInfo.lineCaliInfo.ReadRssiIndex)
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_3_TIMEOUT;
                }
                else
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_UNKNOWN_ERROR;
                }
                g_linInfo.subStatus = LIN_LINE_CALI_STB_FAIL;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx Condition timeout%d Fail\r\n",g_linInfo.lineCaliInfo.failCount);
            }
            else
            {
                g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
                /*ZXL, 标定失败，第一次警告APP不要连接*/
                LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_CHECK, STB_CALI_RESULT_DOING);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx Condition timeout%d retry\r\n",g_linInfo.lineCaliInfo.failCount);
            }
            return;
        }
    }
 
    if(TRUE == g_linInfo.lineCaliInfo.subConditionEnable)
    {
        buf[0] = LINE_CALI_STB_RSSI_CONDITION_START + ((g_linInfo.lineCaliInfo.mainCondition-1) * LINE_CALI_STB_SUB_CONDITION_MAX) + g_linInfo.lineCaliInfo.subCondition - 1;
        buf[1] = buf[0];
    }
    else
    {
        buf[0] = LINE_CALI_STB_RSSI_CONDITION_START + ((g_linInfo.lineCaliInfo.mainCondition-1) * LINE_CALI_STB_SUB_CONDITION_MAX);
        buf[1] = buf[0] + LINE_CALI_STB_SUB_CONDITION_MAX - 1;
    }
    g_linInfo.lineCaliInfo.rssiConditionMin = buf[0];
    g_linInfo.lineCaliInfo.rssiConditionMax = buf[1];

    /*ZXL,标定期间，T-Box周期向STB模块发送信号强度推测值，STB模块收到该推测值后判断该推测值与自己实际测试到的值是否在同一范围内，若在同一范围内，则返回STB模块自身的MAC地址*/
    /*ZXL,信号强度发送完毕后，切换到等待STB返回MAC地址的状态中 STB_LINE_CALI_READ_RSSI_IDLE*/
    g_linInfo.subStatus = LIN_LINE_CALI_STB_READ_RSSI;
    g_linInfo.lineCaliInfo.ReadRssiState = STB_LINE_CALI_READ_RSSI_IDLE;
    LinSendLineCaliPublicData(LIN_LINE_PUBLIC_NOTIFY_CONDITION, 2, &buf[0]);            
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx Condition %d~%d\r\n", buf[0], buf[1]);
}

/*************************************************
函数名称: SortTwoReadRssiRecord
函数功能: 按信号强度排序ReadRssiRecord中的两个记录
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
static void SortTwoReadRssiRecord(uint8_t a, uint8_t b)
{    
    StbLineCaliReadRssi readRssiTemp;

    if(g_linInfo.lineCaliInfo.ReadRssiRecord[a].rssi > g_linInfo.lineCaliInfo.ReadRssiRecord[b].rssi)
    {
        memcpy(&readRssiTemp, 
               &g_linInfo.lineCaliInfo.ReadRssiRecord[a], sizeof(StbLineCaliReadRssi));
        memcpy(&g_linInfo.lineCaliInfo.ReadRssiRecord[a], 
               &g_linInfo.lineCaliInfo.ReadRssiRecord[b], sizeof(StbLineCaliReadRssi));
        memcpy(&g_linInfo.lineCaliInfo.ReadRssiRecord[b], 
               &readRssiTemp, sizeof(StbLineCaliReadRssi));
    }
}

/*************************************************
函数名称: LinLineCaliStbTxResult
函数功能: Lin发送产线标定结果
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinLineCaliStbTxResult(void)
{
    static uint8_t step = 0;
    uint8_t buf[7] = {0};

    memcpy(&buf[0], &g_linInfo.lineCaliInfo.ReadRssiRecord[step].MAC[0], 6);
    buf[6] = g_linInfo.lineCaliInfo.ReadRssiRecord[step].index;
    LinSendLineCaliPublicData(LIN_LINE_PUBLIC_CALI_NUMBER, 7, &buf[0]);

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx result MAC %x index %d\r\n",
                                        g_linInfo.lineCaliInfo.ReadRssiRecord[step].MAC[5], 
                                        g_linInfo.lineCaliInfo.ReadRssiRecord[step].index);
    if(2 <= step++)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx result finish\r\n");
        step = 0;
        g_linInfo.subStatus = LIN_LINE_CALI_STB_INDEX_CHECK;
    }
}

/*************************************************
函数名称: LinLineCaliStbCheckReadMac
函数功能: Lin发送产线标定读MAC地址命令，用于检测标定结果
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinLineCaliStbCheckReadMac(void)
{
    static StbDeviceId deviceId = STB_DEVICE_LEFT_A_PILLAR;
    static uint8_t checkFlag = 0;
    static uint8_t count = 0;

    if(1 == checkFlag)
    {
        if(0 != g_linInfo.deviceInfo[deviceId].mac[0])
        {     
            count = 0;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb read %d Mac 0x%x~%x\r\n", deviceId, 
                                    g_linInfo.deviceInfo[deviceId].mac[0],
                                    g_linInfo.deviceInfo[deviceId].mac[5]);
            if(STB_DEVICE_TRUNK <= deviceId++)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb read Mac finish\r\n");
                g_linInfo.subStatus = LIN_LINE_CALI_STB_SUCCESS;
                deviceId = STB_DEVICE_LEFT_A_PILLAR;
                checkFlag = 0;
                return;
            }
        }
        else
        {
            if(2 < count++)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb read %d Mac fail\r\n",deviceId);
                if (STB_DEVICE_LEFT_A_PILLAR == deviceId)
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_1_CHECK_FAIL;
                }
                else if (STB_DEVICE_RIGHT_A_PILLAR == deviceId)
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_2_CHECK_FAIL;
                }
                else if (STB_DEVICE_TRUNK == deviceId)
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_3_CHECK_FAIL;
                }
                else
                {
                    g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_UNKNOWN_ERROR;
                }
                g_linInfo.subStatus = LIN_LINE_CALI_STB_FAIL;
                deviceId = STB_DEVICE_LEFT_A_PILLAR;
                checkFlag = 0;
                count = 0;
                return;
            }
        }
    }
    memset(&g_linInfo.deviceInfo[deviceId].mac[0], 0x00, 6);
    //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_MAC_ADD), 6,NULL);    
    checkFlag = 1;
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx read %d Mac \r\n", deviceId);
}

/*************************************************
函数名称: LinClearNVMacIndex
函数功能: 清除NV中的MAC标定信息
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinClearNVMacIndex(void)
{
    TboxBleKeyHardPara  tboxBleKeyHardPara;
    uint8_t i = 0;

    memcpy((uint8*)&tboxBleKeyHardPara,  (uint8*)&g_tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));

    for(i = 0; i < 3; i++)
    {
        tboxBleKeyHardPara.STBPara[i].index = 0;
        memset(tboxBleKeyHardPara.STBPara[i].Mac, 0x00, sizeof(tboxBleKeyHardPara.STBPara[i].Mac));
    }
    NvApiWriteData(NV_ID_BLE_HARDWARE_PARE, (uint8_t*)&tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));
}

/*************************************************
函数名称: LinChangeNVMacIndex
函数功能: 清除NV中的MAC标定信息
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinChangeNVMacIndex(uint8_t index, uint8_t *mac)
{
    TboxBleKeyHardPara  tboxBleKeyHardPara;
    NvErrorCode errorCode = NV_NO_ERROR;

    memcpy((uint8*)&tboxBleKeyHardPara,  (uint8*)&g_tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));
    memcpy(&tboxBleKeyHardPara.STBPara[index-1].Mac[0], &mac[0], 6);

    errorCode = NvApiWriteData(NV_ID_BLE_HARDWARE_PARE, (uint8_t*)&tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb add MAC 0x%x~%x index %d, result:%d\r\n", g_tboxBleKeyHardPara.STBPara[index-1].Mac[0],g_tboxBleKeyHardPara.STBPara[index-1].Mac[5], index, errorCode);
}

/*************************************************
函数名称: LinWriteNVMacIndex
函数功能: 将lineCaliInfo.ReadRssiRecord中的记录写入到NV里
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinWriteNVMacIndex(void)
{
    TboxBleKeyHardPara  tboxBleKeyHardPara;
    int idx = 0;
    uint8_t i = 0;
    NvErrorCode errorCode = NV_NO_ERROR;

    memcpy((uint8*)&tboxBleKeyHardPara,  (uint8*)&g_tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));

    for(i = 0; i < 3; i++)
    {
        idx = g_linInfo.lineCaliInfo.ReadRssiRecord[i].index-1;
        memcpy(&tboxBleKeyHardPara.STBPara[idx].Mac[0], &g_linInfo.lineCaliInfo.ReadRssiRecord[i].MAC[0], 6);
        tboxBleKeyHardPara.STBPara[idx].index = g_linInfo.lineCaliInfo.ReadRssiRecord[i].index;
    }
    // for(i = 0; i < 3; i++)
    // {
    //     memcpy(&selfConfigPara.STBPara[i].Mac[0], &g_linInfo.lineCaliInfo.ReadRssiRecord[i].MAC[0], 6);
    //     selfConfigPara.STBPara[i].index = g_linInfo.lineCaliInfo.ReadRssiRecord[i].index;
    // }
    errorCode = NvApiWriteData(NV_ID_BLE_HARDWARE_PARE, (uint8_t*)&tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));

    memcpy((uint8*)&g_tboxBleKeyHardPara, (uint8*)&tboxBleKeyHardPara,  sizeof(TboxBleKeyHardPara));

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "write cali result to flash:%d,%d,%d, status:%d\r\n", 
        g_tboxBleKeyHardPara.STBPara[0].index, g_tboxBleKeyHardPara.STBPara[1].index, 
        g_tboxBleKeyHardPara.STBPara[2].index, errorCode);
}

/*************************************************
函数名称: LinExchangeNVMacIndex
函数功能: 交换
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinExchangeNVMacIndex(uint8_t a, uint8_t b)
{
    TboxBleKeyHardPara  tboxBleKeyHardPara;
    uint8_t mac[6] = {0};
    NvErrorCode errorCode = NV_NO_ERROR;
    
    if(((1 > a) || (3 < a)) ||
        ((1 > b) || (3 < b)) ||
        (a == b))
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb exchange Waring %d %d\r\n", a, b);
        return;
    }

    memcpy((uint8*)&tboxBleKeyHardPara,  (uint8*)&g_tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));
    memcpy(&mac[0], &tboxBleKeyHardPara.STBPara[a-1].Mac[0], 6);
    memcpy(&tboxBleKeyHardPara.STBPara[a-1].Mac[0], &tboxBleKeyHardPara.STBPara[b-1].Mac[0], 6);
    memcpy(&tboxBleKeyHardPara.STBPara[b-1].Mac[0], &mac[0], 6);

    errorCode = NvApiWriteData(NV_ID_BLE_HARDWARE_PARE, (uint8_t*)&tboxBleKeyHardPara, sizeof(TboxBleKeyHardPara));
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb exchange MAC 0x%x~%x index %d MAC 0x%x~%x index %d, status:%d\r\n",
                                    g_tboxBleKeyHardPara.STBPara[a-1].Mac[0],
                                    g_tboxBleKeyHardPara.STBPara[a-1].Mac[5], 
                                    a,
                                    g_tboxBleKeyHardPara.STBPara[b-1].Mac[0],
                                    g_tboxBleKeyHardPara.STBPara[b-1].Mac[5], 
                                    b,
                                    errorCode);
}

/*************************************************
函数名称: LinTxStbCaliData
函数功能: Lin发送标定信息
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinTxStbCaliData(void)
{
    static uint8_t step = 0xFF;
    uint8_t buf[7] = {0};

    if(0xFF == step)
    {
        LinSendPublicData(LIN_PUBLIC_NOTIFY_WAKE_UP, 0, NULL);
        step = 0;
    }
    else
    {
        memcpy(&buf[0], &g_tboxBleKeyHardPara.STBPara[step].Mac[0], 6);
        buf[6] = step+1;
        LinSendLineCaliPublicData(LIN_LINE_PUBLIC_CALI_NUMBER, 7, &buf[0]);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx result MAC %x index %d\r\n", g_tboxBleKeyHardPara.STBPara[step].Mac[5], step+1);
        if(2 <= step++)
        {
            step = 0xFF;
            g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
            g_linInfo.majorStatus = LIN_STATUS_NORMAL;
        }
    }
}

/*************************************************
函数名称: LinLineCaliStbPeriodHandle 10ms
函数功能: Lin产线标定周期处理函数
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2019/12/20
*************************************************/
void LinLineCaliStbPeriodHandle(void)
{    
    static uint16_t count = 0;
    static uint8_t wakeupFlag = 0;
    uint8_t buf[8] = {0};

    if(HANDSHAKE_SUCESS != g_commonInfo.handshakeStatus)
    {
        return;
    }

    switch(g_linInfo.subStatus)
    {
        case LIN_LINE_CALI_STB_START_SCAN:
        {
            if(2 > wakeupFlag++)
            {
                LinSendPublicData(LIN_PUBLIC_NOTIFY_WAKE_UP, 0, NULL);
                break;
            }

            g_linInfo.lineCaliInfo.isCaliRunning = true; //zhouguo++

            wakeupFlag = 0;
            g_linInfo.lineCaliInfo.mainCondition = 0;
            g_linInfo.lineCaliInfo.subCondition = 0;
            g_linInfo.lineCaliInfo.ReadRssiIndex = 0;
            g_linInfo.lineCaliInfo.subConditionEnable = FALSE;
            if(0 == g_linInfo.lineCaliInfo.scanMode)
            {
                buf[0] = 0x00;
                buf[1] = (uint8_t)(g_linInfo.lineCaliInfo.UUID >> 8);
                buf[2] = (uint8_t)(g_linInfo.lineCaliInfo.UUID);            
                LinSendLineCaliPublicData(LIN_LINE_PUBLIC_START, 3, &buf[0]);
            }
            else if(1 == g_linInfo.lineCaliInfo.scanMode)
            {
                buf[0] = 0x01;
                memcpy(&buf[1], &g_linInfo.lineCaliInfo.MAC[0], 6);    
                LinSendLineCaliPublicData(LIN_LINE_PUBLIC_START, 7, &buf[0]);
            }
            else
            {   //error
                g_linInfo.subStatus = LIN_LINE_CALI_STB_FAIL;
                g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_PARA_ERROR;
                break;
            }
            /*ZXL, 检测到总线冲突后，触发了主动重标定，第一次总线冲突时通知APP断开，第二次默认将APP踢掉*/
            if(g_linInfo.lineCaliInfo.conflictCnt>2)
            {
                BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_OPEN_ADV);
            }
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Cali StartScan mode:%d\r\n", g_linInfo.lineCaliInfo.scanMode);
            g_linInfo.subStatus = LIN_LINE_CALI_STB_WAIT_SCAN;
            g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_DOING;
            LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_CHECK, g_linInfo.lineCaliInfo.caliResult);
            count = 0;
            break;
        }
        case LIN_LINE_CALI_STB_WAIT_SCAN:
        {
            /*ZXL, 此主函数运行周期为10ms， 总扫描超时时间为10s*/
            if(LINE_CALI_STB_SACN_SECOND*100 < count++)
            {
                g_linInfo.subStatus = LIN_LINE_CALI_STB_STOP_SCAN;
                count = 0;
            }
            break;
        }
        case LIN_LINE_CALI_STB_STOP_SCAN:
        {
            LinSendLineCaliPublicData(LIN_LINE_PUBLIC_STOP, 0, &buf[0]);                
            g_linInfo.subStatus = LIN_LINE_CALI_STB_NOTIFY_CONDITION;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Cali StopScan\r\n");
            break;
        }
        case LIN_LINE_CALI_STB_NOTIFY_CONDITION:
        {
            LinLineCaliStbTxCondition();
            count = 0;
            break;
        }
        case LIN_LINE_CALI_STB_READ_RSSI:
        {
            if(STB_LINE_CALI_READ_RSSI_RX == g_linInfo.lineCaliInfo.ReadRssiState)
            {
                if((g_linInfo.lineCaliInfo.rssiConditionMin <= g_linInfo.lineCaliInfo.ReadRssiBuff.rssi) &&
                   (g_linInfo.lineCaliInfo.rssiConditionMax >= g_linInfo.lineCaliInfo.ReadRssiBuff.rssi))
                {
                    memcpy( &g_linInfo.lineCaliInfo.ReadRssiRecord[g_linInfo.lineCaliInfo.ReadRssiIndex].MAC[0], 
                            &g_linInfo.lineCaliInfo.ReadRssiBuff.MAC[0], 6);
                    g_linInfo.lineCaliInfo.ReadRssiRecord[g_linInfo.lineCaliInfo.ReadRssiIndex].rssi = 
                            g_linInfo.lineCaliInfo.ReadRssiBuff.rssi;

                    //单个成功时先将标号写入STB,zhouguo++
                    memset(buf, 0, sizeof(buf));
                    memcpy(&buf[0], &g_linInfo.lineCaliInfo.ReadRssiBuff.MAC[0], 6);
                    buf[6] = g_linInfo.lineCaliInfo.ReadRssiIndex + 1;
                    LinSendLineCaliPublicData(LIN_LINE_PUBLIC_CALI_NUMBER, 7, &buf[0]);

                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "write stb %d index\r\n", buf[6]);

                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Cali MAC %d 0x%02x~%02x RSSI %d\r\n",
                             g_linInfo.lineCaliInfo.ReadRssiIndex,
                             g_linInfo.lineCaliInfo.ReadRssiRecord[g_linInfo.lineCaliInfo.ReadRssiIndex].MAC[0],
                             g_linInfo.lineCaliInfo.ReadRssiRecord[g_linInfo.lineCaliInfo.ReadRssiIndex].MAC[5],
                             g_linInfo.lineCaliInfo.ReadRssiRecord[g_linInfo.lineCaliInfo.ReadRssiIndex].rssi);
                    if(2 <= g_linInfo.lineCaliInfo.ReadRssiIndex++)
                    {
                        //由强到弱排序
                        SortTwoReadRssiRecord(0,1);
                        SortTwoReadRssiRecord(1,2);
                        SortTwoReadRssiRecord(0,1);
                        //编号
                        g_linInfo.lineCaliInfo.ReadRssiRecord[0].index = g_linInfo.lineCaliInfo.caliMode[0];
                        g_linInfo.lineCaliInfo.ReadRssiRecord[1].index = g_linInfo.lineCaliInfo.caliMode[1];
                        g_linInfo.lineCaliInfo.ReadRssiRecord[2].index = g_linInfo.lineCaliInfo.caliMode[2];
                        g_linInfo.subStatus = LIN_LINE_CALI_STB_TX_RESULT;
                        break;
                    }
                }
                g_linInfo.subStatus = LIN_LINE_CALI_STB_NOTIFY_CONDITION;
                break;
            }
            else if(STB_LINE_CALI_READ_RSSI_ERROR == g_linInfo.lineCaliInfo.ReadRssiState)
            {
                if(TRUE == g_linInfo.lineCaliInfo.subConditionEnable)
                {                    
                    if(2 <= g_linInfo.lineCaliInfo.failCount++)
                    {
                        g_linInfo.subStatus = LIN_LINE_CALI_STB_FAIL;
                        if (0 == g_linInfo.lineCaliInfo.ReadRssiIndex)
                        {
                            g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_1_LIN_ERROR;
                        }
                        else if(1 == g_linInfo.lineCaliInfo.ReadRssiIndex)
                        {
                            g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_2_LIN_ERROR;
                        }
                        else if(2 == g_linInfo.lineCaliInfo.ReadRssiIndex)
                        {
                            g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_3_LIN_ERROR;
                        }
                        else
                        {
                            g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_UNKNOWN_ERROR;
                        }
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d cali lin error, %d.\r\n",g_linInfo.lineCaliInfo.ReadRssiIndex, g_linInfo.lineCaliInfo.caliResult);
                    }
                    else
                    {
                        g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Condition timeout%d retry\r\n",g_linInfo.lineCaliInfo.failCount);
                    }
//                    g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb cali subCondition read rssi error, restart scan\r\n");
                    break;
                }
                g_linInfo.lineCaliInfo.subConditionEnable = TRUE;
                g_linInfo.subStatus = LIN_LINE_CALI_STB_NOTIFY_CONDITION;
            }
            if(3 < count++)
            {
                g_linInfo.subStatus = LIN_LINE_CALI_STB_NOTIFY_CONDITION;
                count = 0;
                break;
            }
            //RLin21SendData(LIN_CMD_RX_DATA, LIN_ID_LINE_CALI_READ_RSSI, 7, NULL);      
            break;
        }
        case LIN_LINE_CALI_STB_TX_RESULT:
        {
            LinLineCaliStbTxResult();
            break;
        }
        case LIN_LINE_CALI_STB_INDEX_CHECK:
        {
            LinLineCaliStbCheckReadMac();
            break;
        }
        case LIN_LINE_CALI_STB_FAIL:
        {
            /*ZXL, 这个协议设计的很复杂,都改不了，只有MCU改, 不管APP要执行什么，要检查什么，默认两个一起回复*/
            LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_CHECK, g_linInfo.lineCaliInfo.caliResult);
            /*ZXL, 这个协议设计的很复杂，都改不了，只有MCU改， 不管APP要执行什么，要检查什么，默认两个一起回复*/
            //LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_START, g_linInfo.lineCaliInfo.caliResult);

            LinSendLineCaliPublicData(LIN_LINE_PUBLIC_FINISH, 0, &buf[0]);
            g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
            g_linInfo.majorStatus = LIN_STATUS_NORMAL;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb cali fail, %d\r\n", g_linInfo.lineCaliInfo.caliResult);    

            g_linInfo.lineCaliInfo.isCaliRunning = false; //zhouguo++
            #if 0 //test
            g_linInfo.majorStatus = LIN_STATUS_LINE_CALI_STB;
            g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
            g_linInfo.lineCaliInfo.failCount = 0;
            #endif
            break;
        }
        case LIN_LINE_CALI_STB_SUCCESS:
        {
            g_linInfo.lineCaliInfo.caliResult = STB_CALI_RESULT_DONE;
            LinWriteNVMacIndex();
            /*ZXL, 这个协议设计的很复杂,都改不了，只有MCU改, 不管APP要执行什么，要检查什么，默认两个一起回复*/
            LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_CHECK, g_linInfo.lineCaliInfo.caliResult);
            /*ZXL, 这个协议设计的很复杂，都改不了，只有MCU改， 不管APP要执行什么，要检查什么，默认两个一起回复*/
            //LinIpcTxLineCaliResult(LIN_CALI_STB_LINE_START, g_linInfo.lineCaliInfo.caliResult);

            LinSendLineCaliPublicData(LIN_LINE_PUBLIC_FINISH, 0, &buf[0]);
            g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
            g_linInfo.majorStatus = LIN_STATUS_NORMAL;
            g_linInfo.lineCaliInfo.conflictCnt = 0x00;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb cali success\r\n");  

            g_linInfo.lineCaliInfo.isCaliRunning = false; //zhouguo++

            #if 0 //test
            g_linInfo.majorStatus = LIN_STATUS_LINE_CALI_STB;
            g_linInfo.subStatus = LIN_LINE_CALI_STB_START_SCAN;
            g_linInfo.lineCaliInfo.failCount = 0;
            #endif             
            break;
        }
        case LIN_LINE_CALI_STB_EXCHANGE:
        {
            LinTxStbCaliData();
        }
        default:
            break;
    }
}

/*************************************************
函数名称: LinLineRssiSendIpc 
函数功能: 通过IPC上报Rssi的值
输入参数: 
输出参数: 
函数返回类型值：
编写：zhenghao
编写日期 :2021/09/14
*************************************************/
void LinLineRssiSendIpc(LinInfo Rssi)
{
    uint8_t Buf[3];
    Msg msg;
    memset(Buf,0,sizeof(Buf));

    Buf[0] = Rssi.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].currentRssi;
    Buf[1] = Rssi.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].currentRssi;
    Buf[2] = Rssi.deviceInfo[STB_DEVICE_TRUNK].currentRssi;

    msg.event   = MESSAGE_TX_STB_CALIBRATION_RESPONSE;
    msg.len     = sizeof(Buf);
    msg.lparam  = (uint32_t)&Buf;
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: LinLineSearchStbPeriodHandle 10ms
函数功能: Lin产线标定时故障件搜索排除
输入参数: 
输出参数: 
函数返回类型值：
编写：zhouguo
编写日期 :2021/04/13
*************************************************/
void LinLineSearchStbPeriodHandle(void)
{
    static uint8_t step = LIN_LINE_SEARCH_STB_WAKEUP;
    static uint32_t wait = 0;
    static int8_t waitReadRssiCount = 0;
    static uint32_t stbIndx = STB_DEVICE_LEFT_A_PILLAR;
    static uint16_t uuid = 0;

    uint8_t scanSwitch = 0;
    uint8_t buf[8] = {0};
    Msg msg;

    switch(step)
    {
        case LIN_LINE_SEARCH_STB_WAKEUP:
            if(2 > wait++)
            {
                LinSendPublicData(LIN_PUBLIC_NOTIFY_WAKE_UP, 0, NULL);
                break;
            }

            
            wait = 0;            
            step = LIN_LINE_SEARCH_STB_SET_UUID;
            break;

        case LIN_LINE_SEARCH_STB_SET_UUID:
            uuid = g_linInfo.lineCaliInfo.UUID;
            
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "scan uuid:%x\r\n", uuid);
            
            buf[0] = (uint8_t)(uuid >> 8);
            buf[1] = (uint8_t)(uuid);
            buf[2] = ANDROID_PHONE;
            LinSendPublicData(LIN_PUBLIC_SET_SCAN_UUID, 3, &buf[0]);
            step = LIN_LINE_SEARCH_STB_SET_DURATION;
            break;

        case LIN_LINE_SEARCH_STB_SET_DURATION:
            if(5 > wait++)
            {
                break;
            }
            wait = 0; 

            g_linInfo.setPara.scanDuration = BEACON_FIRST_SCAN_DURATION;
            g_linInfo.beaconScanDuration = BEACON_FIRST_READ_COUNT;
            buf[0] = (uint8_t)(g_linInfo.setPara.scanDuration >> 8);
            buf[1] = (uint8_t)(g_linInfo.setPara.scanDuration);
            LinSendPublicData(LIN_PUBLIC_SET_SCAN_DURATION, 2, &buf[0]);
            
            step = LIN_LINE_SEARCH_STB_WAIT_READ;
            break;
            
        case LIN_LINE_SEARCH_STB_WAIT_READ:
            waitReadRssiCount--;
            if(waitReadRssiCount <= 0)
            {
                step = LIN_LINE_SEARCH_STB_READ_RSSI;
                waitReadRssiCount = g_linInfo.beaconScanDuration;
            }
            break;

        case LIN_LINE_SEARCH_STB_READ_RSSI:
            switch(stbIndx)
            {
                case STB_DEVICE_LEFT_A_PILLAR:
                case STB_DEVICE_RIGHT_A_PILLAR:
                case STB_DEVICE_TRUNK:
                    //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(stbIndx, LIN_ID_READ_STATE), 2, NULL);
                    stbIndx++;
                    break;        
            }
            
            if(stbIndx > STB_DEVICE_TRUNK) 
            {
                stbIndx = STB_DEVICE_LEFT_A_PILLAR;
                step = LIN_LINE_SEARCH_STB_END;
                break;
            }
            break;

        case LIN_LINE_SEARCH_STB_END:
            {
                uint8_t i = STB_DEVICE_LEFT_A_PILLAR;
                uint8_t strongIdx = 0;
                uint8_t strongRssi = 0xff;
                
                LinSendPublicData(LIN_PUBLIC_SET_SCAN_SWITCH, 1, &scanSwitch);
                for(i=STB_DEVICE_LEFT_A_PILLAR; i<STB_DEVICE_TOTAL; i++) 
                {
                    if(STB_ERROR_CODE_NORMAL != g_linInfo.deviceInfo[i].errorCode)
                    {
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d error 0x%x\r\n", i, g_linInfo.deviceInfo[i].errorCode);
                    }
                    else 
                    {
                        if(0 != g_linInfo.deviceInfo[i].currentRssi && 0xff != g_linInfo.deviceInfo[i].currentRssi) 
                        {
                            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb %d rssi:%d\r\n", i, g_linInfo.deviceInfo[i].currentRssi);
                        }
                    }

                    if(g_linInfo.deviceInfo[i].currentRssi != 0 && g_linInfo.deviceInfo[i].currentRssi != 0xff && g_linInfo.deviceInfo[i].calcRssi < strongRssi)
                    {
                        strongIdx = i;
                        strongRssi = g_linInfo.deviceInfo[i].calcRssi;
                    }
                }

                step = LIN_LINE_SEARCH_STB_WAIT_READ;

                //if(strongIdx != 0 && strongRssi != 0xff)
                //{
                //    SystemApiLogPrintf(LOG_INFO_OUTPUT, "strong[%d]==%d\r\n", strongIdx, strongRssi);
                //}

                buf[0] = LIN_CALI_STB_LINE_SEARCH_FAULT;
                buf[1] = strongIdx;
                buf[2] = strongRssi;

                msg.event = MESSAGE_TX_STB_CALIBRATION_RESPONSE;
                msg.len = 3;
                msg.lparam = (uint32_t)&buf;
                SystemSendMessage(TASK_ID_IPC, msg);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "search %x strong[%d]==%d\r\n", uuid, strongIdx, strongRssi);

                if(0xffff == g_linInfo.lineCaliInfo.UUID) 
                {
                    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb search exit wait %d %d\r\n", wait, g_linInfo.beaconScanDuration);
                    wait += g_linInfo.beaconScanDuration;
                    if(wait > 300) 
                    {
                        wait=0;
                        g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                        g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                        step = LIN_LINE_SEARCH_STB_WAKEUP;
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "exit stb search:%d,%d\r\n", wait, g_linInfo.beaconScanDuration);
                    }      
                }
                else 
                {
                    g_linInfo.lineCaliInfo.UUID = 0xffff; //读完成后清理uuid，用于判断APP是否停止发送探测命令
                }
            }
            break;
            
        default:
            step = LIN_LINE_SEARCH_STB_WAKEUP;
            break;
    }
}

/*************************************************
函数名称: LinStbSleepHandle
函数功能: STB休眠处理
输入参数: 
输出参数: 
函数返回类型值：
编写： BenYuLong
编写日期：2020/06/10
*************************************************/
void LinStbSleepHandle(void)
{
    switch(g_linInfo.subStatus)
    {
        case LIN_STB_ENTER_SLEEP:
        {
            if(BPLUS_VOL_LOW_STB_ALIVE > g_commonInfo.bPlusAdcValueFilter)
            {
                /*NULL*/
            }
            else
            {
                LinSendPublicData(LIN_PUBLIC_NOTIFY_SLEEP, 0, NULL);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb NOTIFY_SLEEP\r\n");
            }

            g_linInfo.subStatus = LIN_STB_SLEEPING;
            break;
        }
        case LIN_STB_SLEEPING:
        {
            break; 
        }
        default:
            break;
    }
}

/*************************************************
函数名称: LinSTBCheckFaultHandle 需要检测时，每10ms调用一次
函数功能: Lin故障检测函数
输入参数: 
输出参数: 
函数返回类型值：0:检测完成 1：检测中
编写： benyulong
编写日期 :2018/12/19
*************************************************/
uint8_t LinSTBCheckFaultHandle(void)
{
    //Msg msg;
    static StbDeviceId deviceId = STB_DEVICE_LEFT_A_PILLAR;
    static uint8_t checkCount = 0;
    static uint8_t step = STB_CHECK_FAULT_FIRST_READ_MAC;
    static uint8_t changeFlag = 0;
    static uint8_t status[STB_DEVICE_TOTAL] = {0};   //0xFF:未检测 0:无故障 1:有故障
    static uint8_t faultIndex = 0;
    uint8_t buf[7] = {0};
    uint8_t i = 0;

    switch(step)
    {
        case STB_CHECK_FAULT_FIRST_READ_MAC:
        {
            //清变量，读首个模块MAC，
            changeFlag = 0;
            memset(status, 0xFF, STB_DEVICE_TOTAL);
            deviceId = STB_DEVICE_LEFT_A_PILLAR;
            g_linInfo.deviceInfo[deviceId].mac[0] = 0;
            //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_MAC_ADD), 6,NULL);
            step = STB_CHECK_FAULT_READ_MAC;
            break;
        }
        case STB_CHECK_FAULT_READ_MAC:
        {
            if(0 != g_linInfo.deviceInfo[deviceId].mac[0])
            {
                //读到有效MAC，判断为无故障
                if(0 != memcmp(&g_linInfo.deviceInfo[deviceId].mac[0], &g_tboxBleKeyHardPara.STBPara[deviceId-1].Mac[0], 6))
                {
                    /*ZXL, 如果实际读到STB编号的MAC地址与MCU的Flash中保存的不一致，则更新MCU中保存的MAC地址信息*/
                    LinChangeNVMacIndex(deviceId, &g_linInfo.deviceInfo[deviceId].mac[0]);
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb update stb%d MAC \r\n", deviceId);
                }
                status[deviceId] = 0;
                changeFlag += LinSTBFaultRecord(deviceId, 0);
                deviceId += 1;
                checkCount = 0;
            }
            else if(LIN_STB_CHECK_TIMEOUT < checkCount++)
            {
                //重试3次都没有读到有效MAC，判断为有故障
                checkCount = 0;
                status[deviceId] = 1;
                changeFlag += LinSTBFaultRecord(deviceId, 1);
                if(deviceId == 1)
                {
                    g_stbmodlenormal.StbLeftNormal = 0;
                    g_linInfo.deviceInfo[deviceId].currentRssi = 0x00;
                }
                else if(deviceId == 2)
                {
                    g_stbmodlenormal.StbRightNormal = 0;
                    g_linInfo.deviceInfo[deviceId].currentRssi = 0x00;
                }
                else if(deviceId == 3)
                {
                    g_stbmodlenormal.StbTrunkNormal = 0;
                    g_linInfo.deviceInfo[deviceId].currentRssi = 0x00;
                }
                SystemApiLogPrintf(LOG_WARING_OUTPUT, "stb %d read Mac timeout\r\n", deviceId);
                deviceId += 1;
            }

            // 3个模块都读完了
            if(STB_DEVICE_TRUNK < deviceId)
            {
                deviceId = STB_DEVICE_LEFT_A_PILLAR;

                //统计STB故障个数
                buf[0] = 0;
                for(i = STB_DEVICE_LEFT_A_PILLAR; i < STB_DEVICE_TOTAL; i++)
                {
                    if(1 == status[i])
                    {
                        buf[0]++;
                    }
                }
                g_linInfo.deviceFaultNum = buf[0];

                // 如果故障情况有变化，或首次，通知ARM/APP
                if( (0xFF != status[STB_DEVICE_LEFT_A_PILLAR]) && 
                    (0xFF != status[STB_DEVICE_RIGHT_A_PILLAR]) && 
                    (0xFF != status[STB_DEVICE_TRUNK]) &&
                    ((0 < changeFlag) || (TRUE == g_linInfo.needReportSTBFaultInfo)) )
                {
                    buf[0] = 0;
                    for(i = STB_DEVICE_LEFT_A_PILLAR; i < STB_DEVICE_TOTAL; i++)
                    {
                        if(1 == status[i])
                        {
                            buf[0]++;
                            buf[buf[0]] = i;
                        }
                    }
                    // msg.event  = MESSAGE_TX_STB_FAULT_NOTIFY;
                    // msg.len    = (buf[0] + 1);
                    // msg.lparam = (uint32_t)&buf[0];
                    // SystemSendMessage(TASK_ID_IPC, msg);
                    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Fault total:%d, %d-%d-%d\r\n",buf[0],buf[1],buf[2],buf[3]);

                    //如果故障恢复了需要上报一次区域变化
                    if((TRUE != g_linInfo.needReportSTBFaultInfo) && (0 == buf[0]))
                    {
                        g_linInfo.needReportAreaInfo = TRUE; 
                    }
                    g_linInfo.needReportSTBFaultInfo = FALSE;                
                }

                //找出首个缺失的编号
                for(faultIndex = STB_DEVICE_LEFT_A_PILLAR; faultIndex < STB_DEVICE_TOTAL; faultIndex++)
                {
                    if(1 == status[faultIndex])
                    {
                        break;
                    }
                }
                if(STB_DEVICE_TOTAL > faultIndex)
                {
                    if(TSP_STATUS_REGISTERED == g_commonInfo.tspConnectStatus)
                    {
                        // TSP已注册尝试自动恢复编号
                        step = STB_CHECK_FAULT_SYNC_INDEX;
                        break;
                    }
                    else if (TSP_STATUS_UNREGISTERED == g_commonInfo.tspConnectStatus)
                    {
                        // TSP未注册但有故障，检查是否有空板在线
                        step = STB_CHECK_FAULT_READ_IDLE_MAC;
                        break;
                    }
                }

                // SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "stb check MAC End\r\n");
                step = STB_CHECK_FAULT_FIRST_READ_MAC;
                return 0; //故障检测完成
            }
            g_linInfo.deviceInfo[deviceId].mac[0] = 0;
            //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(deviceId, LIN_ID_READ_MAC_ADD), 6,NULL);
            break;
        }
        case STB_CHECK_FAULT_SYNC_INDEX: 
        {
            // 同步标定信息(MAC-Index), STB的Index丢失的情况下的恢复
            memcpy(&buf[0], &g_tboxBleKeyHardPara.STBPara[faultIndex-1].Mac[0], 6);
            buf[6] = faultIndex;
            LinSendLineCaliPublicData(LIN_LINE_PUBLIC_CALI_NUMBER, 7, &buf[0]);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb tx result MAC0x%x~%x index %d\r\n",
                                        g_tboxBleKeyHardPara.STBPara[faultIndex-1].Mac[0],
                                        g_tboxBleKeyHardPara.STBPara[faultIndex-1].Mac[5], 
                                        faultIndex);
            step = STB_CHECK_FAULT_READ_IDLE_MAC;
            break;
        }
        case STB_CHECK_FAULT_READ_IDLE_MAC:
        {
            // 尝试读取0号板的MAC地址
            memset(&g_linInfo.lineCaliInfo.idleBoardMac[0], 0x00, 6);
            //RLin21SendData(LIN_CMD_RX_DATA, LIN_ID_LINE_CALI_READ_IDLE_MAC, 6, NULL);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb read idle Board Mac\r\n");
            if (TSP_STATUS_UNREGISTERED == g_commonInfo.tspConnectStatus)
            {
                step = STB_CHECK_FAULT_FIRST_READ_MAC;
                return 0; //故障检测完成
            }
            else
            {
                step = STB_CHECK_FAULT_CHECK_IDLE_STB;
            }
            break;
        }
        case STB_CHECK_FAULT_CHECK_IDLE_STB:
        {
            // 读到有效的0号板MAC
            if(0 != g_linInfo.lineCaliInfo.idleBoardMac[0])
            {
                // 读到的0号板MAC与MCU本地保存的匹配
                for(i = 0; i < 3; i++)
                {
                    if(0 == memcmp(&g_linInfo.lineCaliInfo.idleBoardMac[0], &g_tboxBleKeyHardPara.STBPara[i].Mac[0], 6))
                    {
                        // 如果0号板的MAC在本地已经有记录，为0号板标定记录的编号
                        faultIndex = i+1;
                        break;
                    }
                }
                LinChangeNVMacIndex(faultIndex, &g_linInfo.lineCaliInfo.idleBoardMac[0]);  

                // 通知STB标定信息
                memcpy(&buf[0], &g_linInfo.lineCaliInfo.idleBoardMac[0], 6);
                buf[6] = faultIndex;
                LinSendLineCaliPublicData(LIN_LINE_PUBLIC_CALI_NUMBER, 7, &buf[0]);
                
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb cali MAC 0x%x~%x index %d\r\n",
                                                g_linInfo.lineCaliInfo.idleBoardMac[0],
                                                g_linInfo.lineCaliInfo.idleBoardMac[5], 
                                                faultIndex);
            }
            step = STB_CHECK_FAULT_FIRST_READ_MAC;
            return 0; //故障恢复完成
        }
        default:
            break;
    }
    return 1; //检测中
}

/*************************************************
函数名称: LinStbNormalHandle
函数功能: STB正常工作处理
输入参数: 
输出参数: 
函数返回类型值：
编写： BenYuLong
编写日期：2020/06/10
*************************************************/
void LinStbNormalHandle(void)
{
    static uint8_t count = 0;
    static uint16_t stbCheckCount = 20;
    static int8_t waitReadRssiCount = 0;
    static uint8_t count1s = 0;
    uint8_t scanSwitch = 0;
    /*ZXL, ARM模块通知MCU当前蓝牙是否处于连接状态*/
    if(BT_STATUS_ACTIVE == g_commonInfo.btStatus)
    {
        if(100 <= count1s++) //10ms * 100 1s
        {
            count1s = 0;
            //APP空闲判断
            /*ZXL, MCU通过CAN网络状态、远程控制执行状态、BLE位置判断， 每隔5MIN通知一次ARM当前蓝牙状态，连续3次后，APP主动断开蓝牙*/
        }
    }
    switch(g_linInfo.subStatus)
    {
        case LIN_STB_CHECK_DEVICE:
        {
            waitReadRssiCount--;
            stbCheckCount++;
            if(20 < stbCheckCount) //每隔10s、6s (20 * g_linInfo.beaconScanDuration(500ms、300ms))
            {
                if(0 == LinSTBCheckFaultHandle())
                {
                    g_linInfo.subStatus = LIN_WAIT_READ_STB_RSSI;
                    stbCheckCount = 0;
                }
            }
            else
            {
                g_linInfo.subStatus = LIN_WAIT_READ_STB_RSSI;
            }
            break;
        }
        case LIN_SET_STB_SCAN_PARA:
        {
            LinSetStbParaHandle();
            break;
        }
        case LIN_WAIT_READ_STB_RSSI:
        {
            //至少隔 g_linInfo.beaconScanDuration 的时间读一次信号强度
            waitReadRssiCount--;
            if(0 >= waitReadRssiCount)
            {
                if(TRUE == g_linInfo.stbLocationSwitch)
                {
                    // 需要STB测距，先去读STB信号强度
                    g_linInfo.subStatus = LIN_READ_STB_RSSI;            
                }
                else
                {
                    // 不需要STB测距，直接去检测STB故障
                    g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;                   
                }
                waitReadRssiCount = g_linInfo.beaconScanDuration;
            }
            if((TRUE == g_linInfo.stbLocationSwitch) &&
               (STB_SET_MAC_SET == g_linInfo.setPara.setMacState))
            {
                //如果需要设置扫描MAC地址，放在这里可以尽快处理
                StbMacChangeCheck();
            }
            break;
        }
        case LIN_READ_STB_RSSI:
        {
            count++;
            if(1 == count)
            {
                //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(STB_DEVICE_LEFT_A_PILLAR, LIN_ID_READ_STATE), 2, NULL);
            }
            else if(2 == count)
            {
                //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(STB_DEVICE_RIGHT_A_PILLAR, LIN_ID_READ_STATE), 2, NULL);            
            }
            else if(3 == count)
            {
                //RLin21SendData(LIN_CMD_RX_DATA, GetRealId(STB_DEVICE_TRUNK, LIN_ID_READ_STATE), 2, NULL);
                g_linInfo.subStatus = LIN_HANDLE_STB_RSSI;
                waitReadRssiCount -= 3;
                count = 0;
            }
            break;
        }
        case LIN_HANDLE_STB_RSSI:
        {
            count++;
            if(1 == count)
            {
                LinSendPublicData(LIN_PUBLIC_SET_SCAN_SWITCH, 1, &scanSwitch);
                if(STB_ERROR_CODE_NORMAL != g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].errorCode)
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb LeftAPillar error 0x%x\r\n", g_linInfo.deviceInfo[STB_DEVICE_LEFT_A_PILLAR].errorCode);
                }
                if(STB_ERROR_CODE_NORMAL != g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].errorCode)
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb RightAPillar error 0x%x\r\n", g_linInfo.deviceInfo[STB_DEVICE_RIGHT_A_PILLAR].errorCode);
                }
                if(STB_ERROR_CODE_NORMAL != g_linInfo.deviceInfo[STB_DEVICE_TRUNK].errorCode)
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "stb Trunk error 0x%x\r\n", g_linInfo.deviceInfo[STB_DEVICE_TRUNK].errorCode);
                }
            }
            else if(2 == count)
            {                
                StbMacChangeCheck();
                g_locationInfo.stbRssiIsUpdated = TRUE;
            }
            else
            {
                g_linInfo.subStatus = LIN_STB_CHECK_DEVICE;
                waitReadRssiCount -= count;
                count = 0;
            }
            break;
        }
        default:
            break;
    }
}

void OffStbGetRssi(void)
{
    g_linInfo.stbLocationSwitch = FALSE;
}

void RcvAppData(uint8_t * buf)
{
    uint16_t uuidTemp = 0;
    uint8_t *tempBuf = buf;

    g_commonInfo.btStatus = tempBuf[1];

    if(BT_STATUS_ACTIVE == g_commonInfo.btStatus)
    {
        if((ANDROID_PHONE == tempBuf[2]) || (APPLE_PHONE == tempBuf[2]))
        {
            g_linInfo.setPara.phoneModel = (PhoneModel)tempBuf[2];
        }
        uuidTemp = ((tempBuf[3]<<8)|tempBuf[4]);
        if(0 != uuidTemp)
        {
            g_linInfo.setPara.scanUuid = uuidTemp;
        }
        else
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "bt uuid is 0\r\n");
        }
        g_linInfo.APPSalveMode = (StbAPPSalveMode)tempBuf[5];
        g_linInfo.appRssiSwitch = APP_RSSI_REQU_OFF;
        g_linInfo.needReportAreaInfo = TRUE;
        g_linInfo.needReportSTBFaultInfo = TRUE;
        if(STB_APP_SUPPORT_SALVE_MODE == g_linInfo.APPSalveMode)
        {
            g_linInfo.stbLocationSwitch = TRUE;
            if(LIN_STATUS_UPDATE_STB != g_linInfo.majorStatus)
            {
                g_linInfo.majorStatus = LIN_STATUS_NORMAL;
                g_linInfo.subStatus = LIN_SET_STB_SCAN_PARA;
            }
            else
            {
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "bt Not Idle %d\r\n", g_linInfo.majorStatus);
            }
        }
        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt active phone:%d uuid:0x%x App:%d\r\n", g_linInfo.setPara.phoneModel, g_linInfo.setPara.scanUuid, g_linInfo.APPSalveMode);
        #endif
    }
    else
    {
        g_linInfo.stbLocationSwitch = FALSE;
    }
}

/*************************************************
函数名称: LinPeriodFunction     10ms
函数功能: Lin周期处理函数
输入参数: 
输出参数: 
函数返回类型值：
编写： benyulong
编写日期 :2018/12/19
*************************************************/
void LinPeriodFunction(void)
{
    /*20220312,zxl,T-Box是否支持蓝牙信标功能*/
    if(0x00 == LinCheckWhetherSupportStb())
    {
        return;
    }

    switch (g_linInfo.majorStatus)
    {
        case LIN_STATUS_POWER_ON_CHECK_STB:
        {
            LinPowerOnCheckStb();
            break;
        }
        case LIN_STATUS_UPDATE_STB:
        {
            //升级任务在BtMainFunction函数中处理
            break;
        }
        case LIN_STATUS_SLEEP:
        {
            LinStbSleepHandle();
            break;
        }
        case LIN_STATUS_LINE_CALI_STB://产线标定处理函数
        {
            LinLineCaliStbPeriodHandle();
            break;
        }
        case LIN_STATUS_LINE_SEARCH_STB:
        {
            LinLineSearchStbPeriodHandle();
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "LIN_STATUS_LINE_SEARCH_STB\r\n");            
            break;
        }
        case LIN_STATUS_NORMAL:
        {
            LinStbNormalHandle();
            break;
        }
        default:
            break;
    }
}
