/*
      BtApi.c
描述：此文件主要是Bt任务具体实现的业务功能
作者：廖勇刚
时间：2016.7.5
*/
#include "HrApi.h"
#include "LogApi.h"
#include "BtApi.h"
#include <string.h>
#include "CanFtm.h"
#include "event.h"
#include <stdarg.h>
#include "DiagApi.h"
#include "PmApi.h"
#include "LedApi.h"
#include "BLELin.h"
#include "gpio.h"
#include "AppTask.h"
#include "pins_driver.h"
#include "rlin30.h"
#include "BtTask.h"
#include <stdlib.h>
#include <stdio.h>

/************************外部全局变量****************************/
extern CommonInfo           g_commonInfo;
extern GpioInfo             g_gpioPowerOnInfoList[];
extern PmInfo               g_pmInfo;
extern ModulePowerStatus    g_powerModuleStatus;
extern LinInfo              g_linInfo;
extern BackupRamInfo        g_backupRamInfo;

/************************全局变量****************************/
static uint8_t              g_BtTxBuf[BT_MAX_DATA_SIZE] = {0x00};
static uint8_t              g_BtRxBuf[BT_MAX_DATA_SIZE] = {0x00};
uint8_t                     g_BtRxData[BT_RX_BUFF_SIZE] = {0};          //the BT module will send at least 7 bytes which is: AT+OK0D0A
uint16_t                    g_BtRxDataCount = 0;
BtServices                  g_BtServices;

/************************函数接口***************************/
static void BtPeriodNotifyFunction(Msg msg);
static void BtModuleEnterUpgradeEvent(Msg msg);
static void BtModuleRxArmConnectEvent(Msg msg);
static void BtModuleHardInitFunction(BtModuleType type, BtModuleInitStepType step);
static void BtVinCodeWriteNotifyFunction(Msg msg);
void BleDataRcvCbk(void *driverState, uart_event_t event, void *userData);
void BleErrorCallback(void *driverState, uart_event_t event, void *userData);

EventInfo g_btEventFunctionMap[] = 
{
    {EVENT_ID_BT_PERIOD_NOTIFY,           BtPeriodNotifyFunction},
    {EVENT_ID_BT_VIN_WR_NOTIFY,           BtVinCodeWriteNotifyFunction},
    {EVENT_ID_BT_UPGRADE_CMD,             BtModuleEnterUpgradeEvent},
    {EVENT_ID_BT_CLIENT_HANDSHAKE_NOTIFY, BtModuleRxArmConnectEvent},  
};

/*************************************************
函数名称: BtMasterServiceInit
函数功能: 蓝牙主模块服务初始化
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
BtServices *BtModuleServiceRead(void)
{
    return &g_BtServices;
}

/*************************************************
函数名称: BtModuleServiceInit
函数功能: 蓝牙主模块服务初始化
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
void BtModuleServiceInit(void)
{
    //below code was moved from LinInitRam()
    g_commonInfo.btStatus = BT_STATUS_INACTIVE;

    /*Init Bluetooth Module as Slave*/
    g_BtServices.btModuleType = BT_MODULE_SLAVE_TYPE;

    g_BtServices.btModuleEvent = BT_MODULE_INIT_EVENT;
    g_BtServices.btModuleInitStepType = BT_MODULE_INIT_WAKEUP_STEP;
    g_BtServices.btModuleIniStepCbk = BtModuleHardInitFunction;
    g_BtServices.btModuleSelfCheckTimeOutCnt = 0x00;
    g_BtServices.btModuleSelfCheckFailCnt = 0x00;
    g_BtServices.btModuleDevicesStatus = BT_MODULE_HARD_DEVICE_INACTIVE;
    g_BtServices.btDeviceStatusNotifyCbk = BtModuleDevicesStatusUpdate;
    g_BtServices.btModulePassDataType = BT_MODULE_PASS_DATA_TYPE_NORMAL;

    g_BtServices.btModulePassRxStruct.passReqDataPtr = g_BtRxBuf;
    memset(g_BtServices.btModulePassRxStruct.passReqDataPtr, 0x00, BT_MAX_DATA_SIZE);
    g_BtServices.btModulePassRxStruct.passReqEvent = FALSE;
    g_BtServices.btModulePassRxStruct.passReqFinish = TRUE;
    g_BtServices.btModulePassRxStruct.passReqLen = 0x00;
    g_BtServices.btModulePassRxStruct.btPassReqCbk = NULL;
    g_BtServices.btModulePassRxStruct.passReqEventStartTime = 0x00;

    g_BtServices.btModulePassTxStruct.passReqDataPtr = g_BtTxBuf;
    memset(g_BtServices.btModulePassTxStruct.passReqDataPtr, 0x00, BT_MAX_DATA_SIZE);
    g_BtServices.btModulePassTxStruct.passReqEvent = FALSE;
    g_BtServices.btModulePassTxStruct.passReqFinish = TRUE;
    g_BtServices.btModulePassTxStruct.passReqLen = 0x00;
    g_BtServices.btModulePassTxStruct.btPassReqCbk = BtSendData;

    g_BtServices.btModuleUpgradeFinish = TRUE;
    g_BtServices.btModuleHardClosed = FALSE;
    g_BtServices.btModuleSlaveShutdownStarted = FALSE;

    if(BT_MODULE_MASTER_TYPE == g_BtServices.btModuleType)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Master\r\n");
    }
    else if(BT_MODULE_SLAVE_TYPE == g_BtServices.btModuleType)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Slave\r\n");
    }
}

/*************************************************
函数名称: BtModuleDriverInit
函数功能: 蓝牙模块驱动初始化
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/30  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
void BtModuleDriverInit(void)
{
    //to init the UART BLE to prepare the data transfer
    McuUartBLEInit(BleDataRcvCbk, BleErrorCallback);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt driver init.\r\n");
}


/*************************************************
函数名称: BtUpgradeFunction
函数功能: 蓝牙接收数据处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2018/09/26
*************************************************/
void BtModuleInterruptWakeUpHande(void)
{
    BtServices  *pBtServices = &g_BtServices;

    if((g_commonInfo.tspStatus == WORK_STATUS_INACTIVE) && (g_pmInfo.workStatus == PM_STATUS_NORAML_SLEEP_READY))
    {
        if(pBtServices->btModuleHardClosed == FALSE)
        {
            g_powerModuleStatus = MODULE_POWER_WAKEUP_ARM_HIGH;
            g_pmInfo.workStatus = PM_STATUS_NORAML;
        }
    }
}

/*************************************************
函数名称: BtModulePostInit
函数功能: 蓝牙主模块服务初始化
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/11  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
void BtModulePostInit(void)
{
    if((TBOX_WAKEUP_WATCHDOE        == g_backupRamInfo.mcuWakeupSource) || \
       (TBOX_WAKEUP_UNKNOWN         == g_backupRamInfo.mcuWakeupSource) || \
       (TBOX_WAKEUP_EXTERN_RESET    == g_backupRamInfo.mcuWakeupSource) || \
       (TBOX_WAKEUP_BPLUS           == g_backupRamInfo.mcuWakeupSource) || \
       (TBOX_WAKEUP_SOFT_RESET      == g_backupRamInfo.mcuWakeupSource))
    {
        BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
    }
}

/*************************************************
函数名称: BtModuleServiceFinish
函数功能: 蓝牙主模块服务结束
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
void BtModuleServiceFinish(BtModuleInfoStruct *pBtModuleInfoStruct)
{
    pBtModuleInfoStruct->passReqEvent = FALSE;
    pBtModuleInfoStruct->passReqFinish = TRUE;

    pBtModuleInfoStruct->passReqLen = 0x00;
    // 这里需要判断一下指针是否为空(有概率为NULL)，再操作
    if(pBtModuleInfoStruct->passReqDataPtr != NULL)
    {
        memset(pBtModuleInfoStruct->passReqDataPtr, 0x00, BT_MAX_DATA_SIZE);
    }
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE pass finished.\r\n");
}

/*************************************************
函数名称: BtModuleHandShakeSend
函数功能: 心跳状态状态通知函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2017/02/16
注释：当handShakeStatus为HANDSHAKE_FAIL时
      若handleType为0，则是立即使用硬件管脚复位蓝牙模块，不主动打开广播
      若handleType为1，则是立即使用硬件管脚复位蓝牙模块，并主动打开广播
      若handleType为2，则是使用复位指令延迟5s复位蓝牙模块，不主动打开蓝牙广播
      若handleType为3，则是使用复位指令延迟5s复位蓝牙模块，并主动打开蓝牙广播
      当handShakeStatus为HANDSHAKE_SUCESS时，
      handleType不参与功能实现，为无用参数
*************************************************/
int BtModuleHandShakeSend(HandShakeStatus handShakeStatus, BtModuleRestHandleType handleType)
{
    Msg msg;
    uint8_t  bufTemp[2] = {0x00};

    bufTemp[0] = handShakeStatus;
    bufTemp[1] = handleType;

    msg.event = EVENT_ID_BT_CLIENT_HANDSHAKE_NOTIFY;
    msg.len = 2;
    msg.lparam = (uint32_t)&bufTemp[0];
    SystemSendMessage(TASK_ID_BT, msg);
    return 0;
}

/*************************************************
函数名称: BtModuleDevicesStatusUpdate
函数功能: 蓝牙模块设备状态更新函数
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
void BtModuleDevicesStatusUpdate(BtModuleDevicesStatus status)
{
    Msg msg;
    uint8_t tempBuf[2] = {0X00};

    switch(status)
    {
        case BT_MODULE_HARD_DEVICE_INACTIVE:
        case BT_MODULE_HARD_DEVICE_ACTIVE:
        {
            tempBuf[1] = (status == BT_MODULE_HARD_DEVICE_INACTIVE)? DTC_STATUS_FAULT : DTC_STATUS_NO_FAULT;
//            DiagWriteDevicesStatus(DTC_BLE_INDEX, tempBuf[1]);
            break;
        }
        case BT_MODULE_UPGRADE_DEVICE_INACTIVE:
        case BT_MODULE_UPGRADE_DEVICE_ACTIVE:
        {
            /*通知arm，mcu当前升级蓝牙模块的条件具备状态*/
            tempBuf[0] = BT_MODULE_PASS_DATA_TYPE_UPGRADE;
            tempBuf[1] = status;
            msg.event  = MESSAGE_TX_BT_RX_DATA;
            msg.len    = 2;
            msg.lparam = (uint32_t)&tempBuf[0];
            SystemSendMessage(TASK_ID_IPC, msg);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Upgrade Precondition:0x%02x\r\n", status);

            break;
        }
        default:
        {
            break;
        }
    }
}

/*************************************************
函数名称: BtModuleHardInitFunction
函数功能: 蓝牙模块服务初始化
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
static void BtModuleHardInitFunction(BtModuleType type, BtModuleInitStepType step)
{
    switch(step)
    {
        case BT_MODULE_INIT_REST_CLOSE_ADV:
        {
            /*复位管脚常态为高， 低电平复位*/
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_LOW);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt closed with step=%d\r\n", step);
            break;
        }
        case BT_MODULE_INIT_WAKEUP_STEP:
        {
            /*复位管脚常态为高， 低电平复位*/
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_HIGH);
            if(BT_MODULE_SLAVE_TYPE == type)
            {
                /*从模块，wake 管脚低电平状态， RX/TX 才能进行数据传输*/
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_WAKEUP_CTL], GPIO_OUTPUT_LOW);
            }
            else
            {
                /*主模块，wake 管脚高电平状态， RX/TX 才能进行数据传输*/
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_WAKEUP_CTL], GPIO_OUTPUT_HIGH);
            }
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt opend with type=%d\r\n", type);
            break;
        }
        case BT_MODULE_INIT_RESET_STEP1:
        {
            /*低电平复位*/
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_LOW);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt step=BT_MODULE_INIT_RESET_STEP1 LOW\r\n");
            break;
        }
        case BT_MODULE_INIT_RESET_STEP2:
        {
            /*低电平复位*/
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_HIGH);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt step=BT_MODULE_INIT_RESET_STEP2 HIGH\r\n");
            if(BT_MODULE_SLAVE_TYPE == type)
            {
                /*从模块，wake 管脚低电平状态， RX/TX 才能进行数据传输*/
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_WAKEUP_CTL], GPIO_OUTPUT_LOW);
            }
            else
            {
                /*主模块，wake 管脚高电平状态， RX/TX 才能进行数据传输*/
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_WAKEUP_CTL], GPIO_OUTPUT_HIGH);
            }
            break;
        }
        case BT_MODULE_INIT_UPGRADE_STEP1:
        {
            if(BT_MODULE_MASTER_TYPE == type)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_WAKEUP_CTL], GPIO_OUTPUT_LOW);
            }
            break;
        }
        case BT_MODULE_INIT_UPGRADE_STEP2:
        {
            if(BT_MODULE_MASTER_TYPE == type)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_LOW);
            }
            break; 
        }
        case BT_MODULE_INIT_UPGRADE_STEP3:
        {
            if(BT_MODULE_MASTER_TYPE == type)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_HIGH);
            }
            break; 
        }
        default:
        {
            break;            
        }
    }
}

/*************************************************
函数名称: BtMasterPassReqTxEvent
函数功能: 蓝牙主模块数据透传请求发送事件
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtModulePassReqTxEvent(uint8_t *dataPtr, uint16_t dataLen)
{
    //int ret = 0x00 ,a =0;
    int ret = 0x00;

    BtServices  *pBtServices = &g_BtServices;

    if(NULL == pBtServices)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Tx init fail\r\n");
        ret = -2;
        return ret;
    }

    if(BT_MAX_DATA_SIZE < dataLen)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Tx over limit\r\n");
        ret = -2;
        return ret;
    }

    if(TRUE == pBtServices->btModulePassTxStruct.passReqFinish)
    {
        memcpy(pBtServices->btModulePassTxStruct.passReqDataPtr, dataPtr, dataLen);
        pBtServices->btModulePassTxStruct.passReqLen = dataLen;
        pBtServices->btModulePassTxStruct.passReqEvent = TRUE;
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Prepare to Tx data: ");
                        
        char strTemp[5] = {0};
        char strCmd[50] = {"BLE Tx data: "};
        for(uint8_t index=0; index < dataLen; index ++)
        {
            if(dataPtr[index] <= 0x0f)
            {
                snprintf(strTemp, sizeof(strTemp), "%02x", dataPtr[index]);
            }
            else
            {
                snprintf(strTemp, sizeof(strTemp), "%c", dataPtr[index]);
            }
            strcat(strCmd, strTemp);
            memset(strTemp, 0, sizeof(strTemp));
            //SystemApiLogPrintf(LOG_INFO_OUTPUT, " %c", dataPtr[index]);
        }
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "%s \r\n", strCmd);
        
    }

    return ret;
}

/*************************************************
函数名称: BtMasterPassReqRxEvent
函数功能: 蓝牙主模块数据透传请求接收事件
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtModulePassReqRxEvent(uint8_t *dataPtr, uint16_t dataLen)
{
    int ret = 0x00;

    BtServices  *pBtServices = &g_BtServices;

    if(NULL == pBtServices)
    {
        ret = -1;
        return ret;
    }

    pBtServices->btModulePassRxStruct.passReqEvent = TRUE;
    pBtServices->btModulePassRxStruct.passReqFinish = TRUE;

    return ret;
}

/*************************************************
函数名称: BtModulePassReqRxPushData
函数功能: 蓝牙主模块数据透传请求接收事件
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtModulePassReqRxPushData(uint8_t rxData)
{
    int ret = 0x00;
    uint16_t len = 0x00;

    BtServices  *pBtServices = &g_BtServices;

    if(NULL == pBtServices)
    {
        ret = -1;
        return ret;
    }

    len = pBtServices->btModulePassRxStruct.passReqLen;
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE rx data len=%d, data:%02x \r\n", len, rxData);
    if(len >= BT_MAX_DATA_SIZE)
    {
        BtModulePassReqRxEvent(NULL, 0x00);
        //Richard:Discard the previous rcved data
        pBtServices->btModulePassRxStruct.passReqLen = 0;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE rx len=BT_MAX_DATA_SIZE.\r\n");
        return ret;
    }
    //if(7 == len)
    //{
      
    //    PrintHexData("BLE rx 7 data=", pBtServices->btModulePassRxStruct.passReqDataPtr, len);
    //}
    //if(10 == len)
    //{
      
    //    PrintHexData("BLE rx 10 data=", pBtServices->btModulePassRxStruct.passReqDataPtr, len);
    //}
    BtModulePassReqRxEventTimerStart();
    //Richard:moved the BT wakeup check procedure to the PM task
    //BtModuleInterruptWakeUpHande();
    pBtServices->btModulePassRxStruct.passReqFinish = FALSE;

    pBtServices->btModulePassRxStruct.passReqDataPtr[len++] = rxData;
    pBtServices->btModulePassRxStruct.passReqLen = len;
    if(BT_MAX_DATA_SIZE == len)
    {
        pBtServices->btModulePassRxStruct.passReqLen = 0;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE rx len reach BT_MAX_DATA_SIZE, discard all data.\r\n");
    }

    return ret;
}

/*************************************************
函数名称: BtModulePassReqRxEventTimerStart
函数功能: 蓝牙主模块数据透传请求接收事件
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtModulePassReqRxEventTimerStart(void)
{
    int ret = 0x00;

    BtServices  *pBtServices = &g_BtServices;

    if(NULL == pBtServices)
    {
        ret = -1;
        return ret;
    }
    //to record the start of the receiving time
    //GetCounterValue(0x00, &(pBtServices->btModulePassRxStruct.passReqEventStartTime));
    pBtServices->btModulePassRxStruct.passReqEventStartTime = xTaskGetTickCount();

    return ret;
}

/*************************************************
函数名称: BtModulePassReqRxEventTimerCheck
函数功能: 蓝牙主模块数据透传请求接收事件超时检测
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtModulePassReqRxEventTimerOutCheck(void)
{
    int ret = 0x00;
    uint32_t elapsedTicks = 0x00;
    uint32_t osTimeTick = 0x00;

    BtServices  *pBtServices = &g_BtServices;

    if(NULL == pBtServices)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Rx init fail\r\n");
        ret = -1;
        return ret;
    }

    if(FALSE == pBtServices->btModulePassRxStruct.passReqFinish)
    {
        //GetElapsedCounterValue(0x00, &(pBtServices->btModulePassRxStruct.passReqEventStartTime), &elapsedTicks);
        //GetCounterValue(0x00, &osTimeTick);
        osTimeTick = xTaskGetTickCount();
        elapsedTicks = abs(osTimeTick - pBtServices->btModulePassRxStruct.passReqEventStartTime);
        if(elapsedTicks >= BT_PASS_REQ_EVNET_TIMEOUT)
        {
            BtModulePassReqRxEvent(NULL, 0x00);
        }
    }

    return ret;
}

/*************************************************
函数名称: BtModuleEventPeriodFunciont
函数功能: 蓝牙模块事件周期更新处理函数,period 1000ms,见task.c
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtModuleEventPeriodFunction(void)
{
    int ret = 0x00;
    BtServices  *pBtServices = BtModuleServiceRead();
    uint8_t btVersionBuf[] = {"AT+VERION=?\r\n"};
    uint8_t closeAdv[] = "AT+ADV=0\r\n";
    uint8_t btTestSendBuf[40] = {0x00};
    static uint8_t checkClientTickTime = 0x00;
    FtmInfo *pFtmInfo = NULL;
    static BtModuleInitStepType preBtModuleInitStepType = BT_MODULE_INIT_WAKEUP_STEP;

    pFtmInfo = FtmInitRead();

    if(NULL == pBtServices)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Period  fail\r\n");
        ret = -1;
        return ret;
    }

    /*每5s检测一次客户端连接状态，若客户端离线，则主动关闭蓝牙模块*/
    if(++checkClientTickTime == BT_CLIENT_STATUS_CHECK_INTERVAL)
    {
        checkClientTickTime = 0x00;
        if((g_pmInfo.workStatus == PM_STATUS_NORAML) && (g_commonInfo.armClinetStatus == ARM_CLINET_OFFLINE) && (pFtmInfo->ftmMode == FTM_MODE_EXIT))
        {
            BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
        }
    }

    switch(pBtServices->btModuleEvent)
    {
        case BT_MODULE_INIT_EVENT:
        {
            if(NULL != pBtServices->btModuleIniStepCbk)
            {
                pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
            }

            if((g_backupRamInfo.mcuWakeupSource == TBOX_WAKEUP_MODULE) || (g_backupRamInfo.mcuWakeupSource == TBOX_WAKEUP_BLE))
            {
                pBtServices->btModuleEvent = BT_MODULE_NORMAL_PASSTHROUGH_EVENT;
                pBtServices->btModuleDevicesStatus = BT_MODULE_HARD_DEVICE_ACTIVE;
                if(NULL != pBtServices->btDeviceStatusNotifyCbk)
                {
                    pBtServices->btDeviceStatusNotifyCbk(pBtServices->btModuleDevicesStatus);
                }
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Check Ok\r\n");
            }
            else
            {
                pBtServices->btModuleEvent = BT_MODULE_SELFCHECKTX_EVENT;
                preBtModuleInitStepType = pBtServices->btModuleInitStepType;
                
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE set event=SELFCHECKTX.\r\n");
            }
            break;
        }
        case BT_MODULE_SELFCHECKTX_EVENT:
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Event=SELFCHECKTX. \r\n");
            btTestSendBuf[0] = BT_MODULE_PASS_DATA_TYPE_NORMAL;
            strcat((char*)&btTestSendBuf[1], (char*)btVersionBuf);
            pBtServices->btModuleSelfCheckTimeOutCnt = 0x00;
            BtModulePassReqTxEvent(btTestSendBuf, strlen((char*)btVersionBuf) + 1);
            pBtServices->btModuleEvent = BT_MODULE_SELFCHECKRX_EVENT;
            break;
        }
        case BT_MODULE_SELFCHECKRX_EVENT:
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Event=SELFCHECKRX. \r\n");
            if(++(pBtServices->btModuleSelfCheckTimeOutCnt) > BT_SELFCHECK_TIMEOUT)
            {
                //SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bt Module Rx Check Fail\r\n");

                if((++pBtServices->btModuleSelfCheckFailCnt) > BT_SELFCHECK_FAILCNT)
                {
                    pBtServices->btModuleEvent = BT_MODULE_HARD_FAULT_EVENT;
                    pBtServices->btModuleDevicesStatus = BT_MODULE_HARD_DEVICE_INACTIVE;
                    if(NULL != pBtServices->btDeviceStatusNotifyCbk)
                    {
                        pBtServices->btDeviceStatusNotifyCbk(pBtServices->btModuleDevicesStatus);
                    }
                    break;
                }

                /*自检失败，启动复位流程*/
                pBtServices->btModuleInitStepType = BT_MODULE_INIT_RESET_STEP1;
                if(NULL != pBtServices->btModuleIniStepCbk)
                {
                    pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
                }
                pBtServices->btModuleInitStepType = BT_MODULE_INIT_RESET_STEP2;

                pBtServices->btModuleEvent = BT_MODULE_INIT_EVENT;
                preBtModuleInitStepType = pBtServices->btModuleInitStepType;
            }
            break;
        }
        case BT_MODULE_HARD_FAULT_EVENT:
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Event=HARD_FAULT. \r\n");
            pBtServices->btModuleEvent = BT_MODULE_INIT_EVENT;
            break;
        }
        case BT_MODULE_UPGRADE_PASSTHROUGH_INIT_EVENT:
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Event=UPGRADE_PASSTHROUGH_INIT. \r\n");
            if(BT_MODULE_INIT_UPGRADE_STEP1 > preBtModuleInitStepType)
            {
                pBtServices->btModuleInitStepType = BT_MODULE_INIT_UPGRADE_STEP1;
                if(NULL != pBtServices->btModuleIniStepCbk)
                {
                    pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
                }
            }
            else if(BT_MODULE_INIT_UPGRADE_STEP1 == preBtModuleInitStepType)
            {
                pBtServices->btModuleInitStepType = BT_MODULE_INIT_UPGRADE_STEP2;
                if(NULL != pBtServices->btModuleIniStepCbk)
                {
                    pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
                }                
            }
            else if(BT_MODULE_INIT_UPGRADE_STEP2 == preBtModuleInitStepType)
            {
                pBtServices->btModuleInitStepType = BT_MODULE_INIT_UPGRADE_STEP3;
                if(NULL != pBtServices->btModuleIniStepCbk)
                {
                    pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
                }

                /*Notify ARM, Ble Upgrade Precondition is ok*/
                pBtServices->btModuleDevicesStatus = BT_MODULE_UPGRADE_DEVICE_ACTIVE;
                if(NULL != pBtServices->btDeviceStatusNotifyCbk)
                {
                    pBtServices->btDeviceStatusNotifyCbk(pBtServices->btModuleDevicesStatus);
                }
                pBtServices->btModuleUpgradeFinish = FALSE;

                /*Reset Module Init Step to  BT_MODULE_INIT_WAKEUP_STEP*/
                pBtServices->btModuleInitStepType = BT_MODULE_INIT_WAKEUP_STEP;
                pBtServices->btModuleEvent = BT_MODULE_UPGRADE_PASSTHROUGH_EVENT;
            }

            preBtModuleInitStepType = pBtServices->btModuleInitStepType;
            break;
        }
        case BT_MODULE_UPGRADE_PASSTHROUGH_EVENT:
        {
            break;
        }
        default:
            break;
    }

    if(TRUE == pBtServices->btModuleSlaveShutdownStarted)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Slave Shutdown Started. \r\n");
        if(pBtServices->btMoudleSlaveShutdownTimerCnt == 0x00)
        {
            switch(pBtServices->btModuleRestType)
            {
                case BT_HARD_RESET_OPEN_ADV:
                {
                    if(NULL != pBtServices->btModuleIniStepCbk)
                    {
                        pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, BT_MODULE_INIT_WAKEUP_STEP);
                    }
                    pBtServices->btModuleSlaveShutdownStarted = FALSE;
                    pBtServices->btModuleHardClosed = FALSE;
                    break;
                }
                case BT_SOFT_RESET_CLOSE_ADV:
                {
                    BtTxUartCmd((char*)closeAdv);
                    pBtServices->btModuleSlaveShutdownStarted = FALSE;
                    break;
                }
                default:
                    break;
            }
        }
        //Richard changed below code
        else
        {
           pBtServices->btMoudleSlaveShutdownTimerCnt--;
        }
    }
    return ret;
}

/*************************************************
函数名称: BtDataPassServicesMainFunction
函数功能: 蓝牙模块数据透传服务主处理函数
输入参数: 
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/11/25  
变更原因 :重新构造奇点蓝牙主模块功能函数 
*************************************************/
int BtDataPassServicesMainFunction(void)
{
    Msg msg;
    //Richard changed below code
    //uint8_t  cmdData[BT_MAX_DATA_SIZE] = {0x00};
    char  cmdData[BT_MAX_DATA_SIZE] = { 0x00 };
    BtModulePassDataType  passDataType;
    uint8_t  resetCmd[] = "AT+BTRST\r\n";
    uint8_t  *pFtmVerCheckHead = NULL;
    uint8_t  *pFtmVerCheckTail = NULL;
    uint8_t  tempLen = 0x00;
    const uint8_t btLoginPassward[] = "000000000000";
    static uint8_t bleMacTable[20]  = {0x00};
    static uint8_t bleVersionBuf[6] = {0x00};
    BtServices  *pBtServices = BtModuleServiceRead();
    FtmInfo     *pFtmInfo = FtmInitRead();

    if(NULL == pBtServices)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE main with BT serverice=NULL\r\n");
        return -1;
        
    }

    /*execut ble tx request event*/
    if(TRUE == pBtServices->btModulePassTxStruct.passReqEvent)
    {
        passDataType = pBtServices->btModulePassTxStruct.passReqDataPtr[BT_PASS_DATA_TYPE_OPT];
        switch(passDataType)
        {
            case BT_MODULE_PASS_DATA_TYPE_NORMAL:
            {
                if(0 == memcmp(resetCmd,&(pBtServices->btModulePassTxStruct.passReqDataPtr[BT_PASS_DATA_REAL_OPT]),strlen((char*)resetCmd)))
                {
                    pBtServices->btModuleInitStepType = BT_MODULE_INIT_RESET_STEP1;
                    if(NULL != pBtServices->btModuleIniStepCbk)
                    {
                        pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
                    }
                    pBtServices->btModuleInitStepType = BT_MODULE_INIT_RESET_STEP2;

                    pBtServices->btModuleEvent = BT_MODULE_INIT_EVENT;

                    break;
                }
                break;
            }
            case BT_MODULE_PASS_DATA_TYPE_UPGRADE:
            {
                break;
            }
            default:
                break;
        }
 
        if(NULL != pBtServices->btModulePassTxStruct.btPassReqCbk)
        {
            /*Delete the Pass Data First Byte, This Byte indict the data type  wether normal data or upgrade data*/
            pBtServices->btModulePassTxStruct.btPassReqCbk(&(pBtServices->btModulePassTxStruct.passReqDataPtr[BT_PASS_DATA_REAL_OPT]) ,pBtServices->btModulePassTxStruct.passReqLen - 1);
        }
        BtModuleServiceFinish(&(pBtServices->btModulePassTxStruct));
    }

    /*execute ble rx timeout check*/
    BtModulePassReqRxEventTimerOutCheck();

    /*execute ble rx finish event*/
    if((TRUE == pBtServices->btModulePassRxStruct.passReqEvent) && (TRUE == pBtServices->btModulePassRxStruct.passReqFinish))
    {
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE main rcved:event=%d\r\n", pBtServices->btModuleEvent);
        //PrintHexData("BLE main rcv data:", pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen);
        switch(pBtServices->btModuleEvent)
        {
            case  BT_MODULE_SELFCHECKRX_EVENT:
            {
                char* verSion = strstr((char*)pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+OK");
                //SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Version:%s\r\n", &(pBtServices->btModulePassRxStruct.passReqDataPtr[0]));
                //PrintHexData("BLE Version:", pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen);
                //if(strstr((char*)pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+OK"))
                if(NULL != verSion)
                {   
                    //to bypass the 7 bytes:AT+OK0d0a
                    verSion += 7;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Version:%s", verSion);
                    pBtServices->btModuleEvent = BT_MODULE_NORMAL_PASSTHROUGH_EVENT;
                    pBtServices->btModuleDevicesStatus = BT_MODULE_HARD_DEVICE_ACTIVE;
                    if(NULL != pBtServices->btDeviceStatusNotifyCbk)
                    {
                        pBtServices->btDeviceStatusNotifyCbk(pBtServices->btModuleDevicesStatus);
                    }
                    uint8_t bleVersionLen = strlen((char*)pBtServices->btModulePassRxStruct.passReqDataPtr);
                    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Check Ok, version len=%d\r\n", bleVersionLen);

                    //if(strlen((char*)pBtServices->btModulePassRxStruct.passReqDataPtr) == 13)
                    if(strlen((char*)pBtServices->btModulePassRxStruct.passReqDataPtr) >= 13)
                    {
                        memcpy(&bleVersionBuf[0], pBtServices->btModulePassRxStruct.passReqDataPtr + 7, 4);
                    }

                    if((g_commonInfo.armClinetStatus == ARM_CLINET_OFFLINE)&& (FTM_MODE_EXIT == pFtmInfo->ftmMode))
                    {
                        BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
                    }
                }
                break;
            }
            case  BT_MODULE_NORMAL_PASSTHROUGH_EVENT:
            {
                cmdData[0] = BT_MODULE_PASS_DATA_TYPE_NORMAL;
                memcpy(cmdData + 1, pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen);
                msg.event  = MESSAGE_TX_BT_RX_DATA;
                msg.len    = pBtServices->btModulePassRxStruct.passReqLen + 1;
                msg.lparam = (uint32_t)&cmdData[0];
                SystemSendMessage(TASK_ID_IPC, msg);
                PrintHexData("BLE Rx data:", pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen);
                //if(strstr(pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+CON=SUCCESS") || strstr(pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+CON=STOP") || strstr(pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+CON=FAILURE"))
                //{
                //    SystemApiLogPrintf(LOG_INFO_OUTPUT, "bt:%s", pBtServices->btModulePassRxStruct.passReqDataPtr);
                //}

                if(FTM_MODE_ENTER == pFtmInfo->ftmMode)
                {
                    if(FTM_RX_MCU_BT_VERSION_CHECK_INDEX == pFtmInfo->ftmCurrentRxIndex)
                    {
                        cmdData[0] = pBtServices->btModuleType;
                        pFtmVerCheckHead = (uint8_t*)strstr((char*)pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+OK\r\n");
                        if(pFtmVerCheckHead)
                        {
                            /*规避装备测试时读版本号偶发性漏一个字节问题*/
                            /*41 54 2B 4F 4B 0D 0A 30 32 30 35 0D 0A 版本号结果16进制内容*/
                            if(strlen((char*)pBtServices->btModulePassRxStruct.passReqDataPtr)<13)
                            {
                                memcpy(&cmdData[1], bleVersionBuf, 4);
                            }
                            else
                            {
                                /*BT Module Version Len is 4*/
                                memcpy(&cmdData[1], pFtmVerCheckHead + 7, 4);
                            }
                        }

                        if(pFtmInfo->ftmTestCbk)
                        {
                            pFtmInfo->ftmTestCbk(pFtmInfo->ftmCurrentTxIndex, (uint8_t*)cmdData, 5);
                        }

                        pFtmInfo->ftmCurrentTxIndex = 0x00;
                        pFtmInfo->ftmCurrentRxIndex = 0x00;
                        pFtmInfo->ftmTestCbk = NULL;
                    }
                    else if(FTM_RX_MCU_BT_CONNECT_INFO_INDEX == pFtmInfo->ftmCurrentRxIndex)
                    {
                        if(BT_MODULE_MASTER_TYPE == pBtServices->btModuleType)
                        {
                            return 0;
                        }

                        /*蓝牙从模块测试时返回连接信息如下： MAC=0C61CF386C8F:PASS=112233445566*/
                        strcat((char*)cmdData, "MAC=");
                        tempLen = strlen((char*)cmdData);

                        /*读MAC地址后，模块可能返回这种格式"AT+OK\r\n0C61CF39E005\r\n" 或者这种格式 "0C61CF39E005\r\n"*/
                        pFtmVerCheckHead = (uint8_t*)strstr((char*)pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+OK\r\n");
                        if(pFtmVerCheckHead)
                        {
                            pFtmVerCheckTail= (uint8_t*)strstr((char*)(pFtmVerCheckHead+7), "\r\n");
                            {
                                memcpy(cmdData+tempLen, pFtmVerCheckHead+7, pFtmVerCheckTail-(pFtmVerCheckHead+7));
                                memcpy(bleMacTable, pFtmVerCheckHead+7, pFtmVerCheckTail-(pFtmVerCheckHead+7));
                            }
                        }
                        else if(pBtServices->btModulePassRxStruct.passReqLen>=12)
                        {
                            memcpy(cmdData+tempLen, pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen-2);
                        }
                        /*设置登陆密码*/
                        BtTxUartCmd("AT+LOGIN_CFG=10000000000000\r\n");
                        tempLen = strlen(cmdData);
                        strcat(cmdData + tempLen , ":PASS=");
                        tempLen = strlen(cmdData);
                        strcat(cmdData + tempLen , (char*)btLoginPassward);
                        tempLen = strlen(cmdData);

                        if(pFtmInfo->ftmTestCbk)
                        {
                            pFtmInfo->ftmTestCbk(pFtmInfo->ftmCurrentTxIndex, (uint8_t*)cmdData, tempLen);
                        }

                        pFtmInfo->ftmCurrentTxIndex = 0x00;
                        pFtmInfo->ftmCurrentRxIndex = 0x00;
                        pFtmInfo->ftmTestCbk = NULL;
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Ftm Bt Slave connect info:%s\r\n", cmdData);
                    }
                    else if(FTM_RX_MCU_READ_BLE_MAC == pFtmInfo->ftmCurrentRxIndex)
                    {
                         /*读MAC地址后，模块可能返回这种格式"AT+OK\r\n0C61CF39E005\r\n" 或者这种格式 "0C61CF39E005\r\n"*/
                        pFtmVerCheckHead = (uint8_t*)strstr((char*)pBtServices->btModulePassRxStruct.passReqDataPtr, "AT+OK\r\n");
                        if(pFtmVerCheckHead)
                        {
                            pFtmVerCheckTail= (uint8_t*)strstr((char*)(pFtmVerCheckHead+7), "\r\n");
                            {
                                memcpy(bleMacTable, pFtmVerCheckHead+7, pFtmVerCheckTail-(pFtmVerCheckHead+7));
                            }
                        }
                        else if(pBtServices->btModulePassRxStruct.passReqLen>=12)
                        {
                            memcpy(cmdData+tempLen, pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen-2);
                        }

                        if(pFtmInfo->ftmTestCbk)
                        {
                            pFtmInfo->ftmTestCbk(pFtmInfo->ftmCurrentTxIndex, bleMacTable, strlen((char*)bleMacTable));
                        }
                        pFtmInfo->ftmCurrentTxIndex = 0x00;
                        pFtmInfo->ftmCurrentRxIndex = 0x00;
                        pFtmInfo->ftmTestCbk = NULL;
                    }
                }
                break;
            }
            case BT_MODULE_UPGRADE_PASSTHROUGH_EVENT:
            {
                cmdData[0] = BT_MODULE_PASS_DATA_TYPE_UPGRADE;
                memcpy(cmdData + 1, pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen);

                msg.event  = MESSAGE_TX_BT_RX_DATA;
                msg.len    = pBtServices->btModulePassRxStruct.passReqLen + 1;
                msg.lparam = (uint32_t)&cmdData[0];
                SystemSendMessage(TASK_ID_IPC, msg);

                //SystemApiLogPrintf(LOG_INFO_OUTPUT, "0x%02x,0x%02x,0x%02x,0x%02x,0x%02x,0x%02x,0x%02x\r\n",cmdData[0],cmdData[1],cmdData[2],cmdData[3],cmdData[4],cmdData[5],cmdData[6]);

                break;
            }
            default:
                PrintHexData("BLE Rx default:", pBtServices->btModulePassRxStruct.passReqDataPtr, pBtServices->btModulePassRxStruct.passReqLen);
                //BtModuleServiceFinish(&(pBtServices->btModulePassRxStruct));
                
                break;
        }

        BtModuleServiceFinish(&(pBtServices->btModulePassRxStruct));
    }

    return 0;
}

/*************************************************
函数名称: BtUpgradeFunction
函数功能: 蓝牙接收数据处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2018/09/26
*************************************************/
static void BtModuleEnterUpgradeEvent(Msg msg)
{
    uint8_t *tempBuf = NULL;
    BtServices  *pBtServices = &g_BtServices;

    if(NULL == (void *)msg.lparam)
    {
        return;
    }

    tempBuf = (uint8_t *)msg.lparam;

    /*Ble Slave Module Do not Support Upgrade**/
    if(BT_MODULE_SLAVE_TYPE == pBtServices->btModuleType)
    {
        if(NULL != pBtServices->btDeviceStatusNotifyCbk)
        {
            pBtServices->btDeviceStatusNotifyCbk(BT_MODULE_UPGRADE_DEVICE_INACTIVE);
        }
        return;
    }

    if(BT_MODULE_UPGRADE_START == tempBuf[5])
    {
        pBtServices->btModuleEvent = BT_MODULE_UPGRADE_PASSTHROUGH_INIT_EVENT;
    }
    else
    {
        /*Upgrade Finished, Reset Ble and Start SelfCheck*/
        pBtServices->btModuleInitStepType = BT_MODULE_INIT_RESET_STEP1;
        if(NULL != pBtServices->btModuleIniStepCbk)
        {
            pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, pBtServices->btModuleInitStepType);
        }
        pBtServices->btModuleInitStepType = BT_MODULE_INIT_RESET_STEP2;

        pBtServices->btModuleEvent = BT_MODULE_INIT_EVENT;
    }
}

/*************************************************
函数名称: BtUartIsrRxFunction
函数功能: 蓝牙串口中断函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/05/14
*************************************************/
void BtUartIsrRxFunction(void)
{
    //uint8_t   btRxDataByte = 0x00;
    //btRxDataByte = RLN30.LURDR.uint16_t;
    //BtModulePassReqRxPushData(btRxDataByte);
    //RLN30.LEST = 0x00U;
}

/*************************************************
函数名称: BtTxUartCmd
函数功能: 发送蓝牙命令到蓝牙模块
输入参数: const char *fmt
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/06/26
修改者: RichardChen
修改日期 :2024/04/17
*************************************************/
void BtTxUartCmd(const char *fmt,...)
{
    va_list args;
    uint16_t len = BT_MAX_DATA_SIZE;
    char   buffer[BT_MAX_DATA_SIZE];

    memset(buffer, 0x00, BT_MAX_DATA_SIZE);

    va_start(args,fmt);
    len = vsprintf(buffer,fmt,args);
    va_end(args);

    BtSendData((uint8_t*)buffer, len);
}

/*************************************************
函数名称: BtSendData
函数功能: 蓝牙发送数据
输入参数: uint8_t  * buf
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/05/21
修改者: RichardChen
修改日期 :2024/04/17
*************************************************/
void BtSendData(uint8_t *txBuff, uint16_t txBuffLen)
{
    if((NULL == txBuff) || (BT_MAX_DATA_SIZE < txBuffLen))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "txBuf=%x, txBuff Len=%d.\r\n", txBuff, txBuffLen);
        return;
    }

    UartBLESendData(txBuffLen, txBuff);
}

/*************************************************
函数名称: BtPeriodNotifyFunction
函数功能: 蓝牙周期性执行函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/05/14
*************************************************/
static void BtPeriodNotifyFunction(Msg msg)
{
    //McuScheduleTimeOutReInit(TASK_ID_BT, 0x00);
    BtModuleEventPeriodFunction();
}

/*************************************************
函数名称: BtUpgradeFunction
函数功能: 蓝牙接收数据处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2018/09/26
*************************************************/
static void BtModuleRxArmConnectEvent(Msg msg)
{
    uint8_t *para = NULL;
    HandShakeStatus  handShakeStatus;
    BtModuleRestHandleType resetType;
    //uint8_t closeAdv[] = "AT+ADV=0\r\n";
    uint8_t closeConnect[] = "AT+SOFT_RST=1\r\n";
    //BtServices  *pBtServices = &g_BtServices;
    BtServices  *pBtServices = BtModuleServiceRead();

    para = (uint8_t *)msg.lparam;
    handShakeStatus = para[0];
    resetType = para[1];
    pBtServices->btModuleRestType = resetType;

    if(BT_MODULE_SLAVE_TYPE == pBtServices->btModuleType)
    {
        /*任何硬复位或者软复位蓝牙模块，都应该将蓝牙模块的鉴权认证状态设置为0, 即认证失败*/
        g_commonInfo.bleKeyAuthStatus = 0x00;

        if(handShakeStatus == HANDSHAKE_FAIL)
        {
            switch(resetType)
            {
                case BT_HARD_RESET_CLOSE_ADV:
                {
                    if(NULL != pBtServices->btModuleIniStepCbk)
                    {
                        pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, BT_MODULE_INIT_REST_CLOSE_ADV);
                    }

                    pBtServices->btModuleHardClosed = TRUE;
                    pBtServices->btModuleSlaveShutdownStarted = FALSE;
                    pBtServices->btMoudleSlaveShutdownTimerCnt = 0x00;
                    break;
                }
                case BT_HARD_RESET_OPEN_ADV:
                {
                    if(NULL != pBtServices->btModuleIniStepCbk)
                    {
                        pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, BT_MODULE_INIT_REST_CLOSE_ADV);
                    }
                    pBtServices->btModuleSlaveShutdownStarted = TRUE;
                    pBtServices->btMoudleSlaveShutdownTimerCnt = BT_SLAVE_SHUTDOWN_OPEN_ADV;
                    pBtServices->btModuleHardClosed = FALSE;
                    break;
                }
                case BT_SOFT_RESET_CLOSE_ADV:
                {
                    if(NULL != pBtServices->btModuleIniStepCbk)
                    {
                        pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, BT_MODULE_INIT_WAKEUP_STEP);
                    }
                    pBtServices->btModuleHardClosed = FALSE;
                    BtTxUartCmd((char*)closeConnect);
                    pBtServices->btModuleSlaveShutdownStarted = TRUE;
                    pBtServices->btMoudleSlaveShutdownTimerCnt = BT_SLAVE_SHUTDOWN_CLOS_ADV;
                    break;
                }
                case BT_SOFT_RESET_OPEN_ADV:
                {
                    if(NULL != pBtServices->btModuleIniStepCbk)
                    {
                        pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, BT_MODULE_INIT_WAKEUP_STEP);
                    }
                    pBtServices->btModuleHardClosed = FALSE;
                    BtTxUartCmd((char*)closeConnect);
                    pBtServices->btModuleSlaveShutdownStarted = FALSE;
                    pBtServices->btMoudleSlaveShutdownTimerCnt = 0x00;
                    break;
                }
            }
        }
        else
        {
            if(NULL != pBtServices->btModuleIniStepCbk)
            {
                    pBtServices->btModuleIniStepCbk(pBtServices->btModuleType, BT_MODULE_INIT_WAKEUP_STEP);
            }
            pBtServices->btModuleSlaveShutdownStarted = FALSE;
            pBtServices->btMoudleSlaveShutdownTimerCnt = 0x00;
        }
    }
}

/*************************************************
函数名称: BtVinCodeWriteNotifyFunction
函数功能: 设置周期通知BTVIN码
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhouguo
编写日期:2021/05/28
*************************************************/
static void BtVinCodeWriteNotifyFunction(Msg msg)
{
    AutoCalibrationHandler();
}

/*************************************************
函数名称: BtTaskVinCodeWriteHook
函数功能: 写入vin码后触发一次自动标定
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhouguo
编写日期:2021/05/28
*************************************************/
void BtTaskVinCodeWriteHook(void)
{
    Msg msg;

    msg.event  = EVENT_ID_BT_VIN_WR_NOTIFY;
    msg.len    = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_BT, msg);
}

/*************************************************
函数名称: BtEventFunction
函数功能: 蓝牙事件执行函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void BtEventFunction(Msg msg)
{
    uint8_t index = 0;

    for(index = 0; index < (uint8_t)(sizeof(g_btEventFunctionMap)/sizeof(g_btEventFunctionMap[0])); index++)
    {
        if(g_btEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_btEventFunctionMap[index].TaskFunctionHook)
            {
                g_btEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

/*************************************************
函数名称: BleErrorCallback
函数功能: BT串口数据接收错误回调函数
输入参数: driverState、userData - 未使用，event-中断事件
输出参数: 无
函数返回类型值： 无
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void BleErrorCallback(void *driverState, uart_event_t event, void *userData)
{
    //linflexd_uart_state_t * uartState = (linflexd_uart_state_t*)driverState;
    //uint8_t tempData = uartState->rxBuff[0];

    //SystemApiLogPrintf(LOG_WARING_OUTPUT, "BLE err cbk at datalen=%d\r\n", g_BtRxDataCount);
    /*
    static uint8_t errorBuff[20] = {0};
    static uint8_t errorDataIndex = 0;
    g_BtRxData[g_BtRxDataCount] = uartState->rxBuff[0];
    g_BtRxDataCount ++;
    if(g_BtRxDataCount >= sizeof(g_BtRxData))
    {
        g_BtRxDataCount = 0;
    }
    errorBuff[errorDataIndex] = uartState->rxBuff[0];
    errorDataIndex ++;
   
    //LINFlexD_UART_DRV_ReceiveData(UART_PORT_BLE, &g_BtRxData[g_BtRxDataCount], sizeof(uint8_t));
    if(errorDataIndex >= sizeof(errorBuff))
    {
        //SystemApiLogPrintf(LOG_WARING_OUTPUT, "BLE err cbk datalen=%d\r\n", g_BtRxDataCount);
        //if(0 == g_BtRxDataCount)
        //{
        //    PrintHexData("BLE err cbk. ", g_BtRxData, sizeof(g_BtRxData));
        //}
        //else
        //{
        //    PrintHexData("BLE err cbk. ", g_BtRxData, g_BtRxDataCount);
        //}
        PrintHexData("BLE rx err:", errorBuff, errorDataIndex);
        errorDataIndex = 0;
    }
    */    
    //McuUartBLESetRxBuff();
    McuUartBLERxData(g_BtRxDataCount);
    //BtModulePassReqRxPushData(tempData);
}

/*************************************************
函数名称: BleDataRcvCbk
函数功能: BT串口数据接收完成回调函数
输入参数: driverState、userData - 未使用，event-中断事件
输出参数: 无
函数返回类型值： 无
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void BleDataRcvCbk(void *driverState, uart_event_t event, void *userData)
{
    //linflexd_uart_state_t *state = (linflexd_uart_state_t *)driverState;
    //uint32_t rxDataAddr = 0;

    if(UART_EVENT_RX_FULL == event)
    {
        //BtServices   *pBtServices = BtModuleServiceRead();
        //if(BT_MODULE_NORMAL_PASSTHROUGH_EVENT == pBtServices->btModuleEvent)
        {
            //rxDataAddr = ((uint32_t)&state->rxBuff[0]) - 1;
            //uint8_t tempData[BT_RX_BUFF_SIZE] = {0};
            //memcpy(tempData, g_BtRxData, sizeof(g_BtRxData));
            uint8_t tempData = g_BtRxData[g_BtRxDataCount];
            //uint8_t tempData = *(uint8_t*)rxDataAddr;
            //McuUartBLERxData();
        
            g_BtRxDataCount ++;
            if(g_BtRxDataCount >= sizeof(g_BtRxData))
            {
                g_BtRxDataCount = 0;
            }

            //McuUartBLESetRxBuff(g_BtRxDataCount);
        
            //if(0 == g_BtRxDataCount)
            //{
            //    PrintHexData("BLE rcv:", g_BtRxData, sizeof(g_BtRxData));
            //    memset(g_BtRxData, 0, sizeof(g_BtRxData));
            //}
            //LINFlexD_UART_DRV_SetRxBuffer(UART_PORT_BLE, (uint8_t*)&g_BtRxData[g_BtRxDataCount], sizeof(uint8_t));
            //LINFlexD_UART_DRV_SetRxBuffer(UART_PORT_BLE, (uint8_t *)rxDataAddr, sizeof(uint8_t));
        
            BtModulePassReqRxPushData(tempData);
        }
        McuUartBLESetRxBuff(g_BtRxDataCount);
    }
    else if(UART_EVENT_TIMEOUT == event || UART_EVENT_END_TRANSFER == event)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE rcv cbk end with error, event=%d\r\n", event);
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE rcv cbk with event=%d\r\n", event);
    }
}

/*************************************************
函数名称: BTWakeupInit
函数功能: MCU被唤醒后的BT UART重新初始化
输入参数: 无
输出参数: 无
函数返回类型值：无 
编写者: RichardChen
编写日期 :2024/04/17
*************************************************/
void BTWakeupInit(void)
{
    McuUartBLEReInit(BleDataRcvCbk, BleErrorCallback);
    BtModuleServiceInit();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BLE Uart wakeup init finished.\r\n");
}

/*
 *  @brief 蓝牙休眠配置唤醒引脚
 *  @param 无
 *  @return 无
 */
void BtModuleSleep(void)
{
    McuSetPinModule(MCU_GPIO_BLE_STATUS_INT, PCTRL_MUX_AS_GPIO);
#if (BT_TASK_ENABLED == 1)
    McuSetPinIntConfig(MCU_GPIO_BLE_STATUS_INT, PCTRL_INT_FALLING_EDGE);
#else
    McuSetPinIntConfig(MCU_GPIO_BLE_STATUS_INT, PCTRL_DMA_INT_DISABLED);
#endif
}

/*************************************************
函数名称: PrintHexData
函数功能: 打印二进制uffer里面的内容 
输入参数: 无
输出参数: 无
函数返回类型值：无 
编写者: RichardChen
编写日期 :2024/04/24
*************************************************/
void PrintHexData(const char* strHeader, const uint8_t* data, uint16_t dataLen)
{
    char strTemp[8] = {0};
    char strInfo[200] = {0};
    strcpy(strInfo, strHeader);
    for(uint16_t index=0; index < dataLen; index ++)
    {
        if((data[index] < 0x20) || (data[index] >= 0x80))
        {
           snprintf(strTemp, sizeof(strTemp), " 0x%02x ", data[index]);
        }
        else
        {
            snprintf(strTemp, sizeof(strTemp), "%c", data[index]);
        }
        strcat(strInfo, strTemp);
        memset(strTemp, 0, sizeof(strTemp));
        
    }
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "%s \r\n", strInfo);
}

/*************************************************
函数名称: BtModulePassReqRxEventCheck
函数功能: 蓝牙主模块数据透传请求接收状态检测
输入参数: 
输出参数: int: 0-表示蓝牙模组输出低电平；
               1-表示蓝牙模组输出高电平；
               -1-表示蓝牙服务还没有建立；
               -2-表示蓝牙模块的中断顺序出错，没有按照预期的先拉低再拉高的顺序
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/25  
变更原因 :利用蓝牙模组的输出中断来判断接收蓝牙数据的开始和结束
*************************************************/
int BtModulePassReqRxEventCheck(void)
{
    int checkResult = BT_SERVICE_NOT_INIT;
    //BtServices  *pBtServices = &g_BtServices;
    BtServices *pBtServices  = BtModuleServiceRead();
    if(NULL != pBtServices)
    {
        GpioLevel bleTxStatus = GPIO_OUTPUT_HIGH;
        GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_STATUS_INT], &bleTxStatus);
    
        if(GPIO_OUTPUT_LOW == bleTxStatus)
        {
            //to mark the start of the receiving 
            
            //to mark a new receiving activity
            pBtServices->btModulePassRxStruct.passReqEvent = TRUE;
            pBtServices->btModulePassRxStruct.passReqFinish = FALSE;
            checkResult = BT_MODULE_INT_LOW;
        }
        else if((TRUE == pBtServices->btModulePassRxStruct.passReqEvent) &&
                (FALSE == pBtServices->btModulePassRxStruct.passReqFinish))
        {
            //to mark the end of the receiving
            pBtServices->btModulePassRxStruct.passReqEvent = TRUE;
            pBtServices->btModulePassRxStruct.passReqFinish = TRUE;
            checkResult = BT_MODULE_INT_HIGH;
        }
        else
        {
            checkResult = BT_MODULE_SEQUENCE_ERR;
        }
    }
    
    return checkResult;
}