/*
     IpcApi.h
描述：定义IPC任务(MCU与ARM核间通信)具体实现的业务功能头文件，待实现
      转移字符 0xfc--> 0xfd 0x5c
               0xfd--> 0xfd 0x5d
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _IPC_API_H_
#define  _IPC_API_H_

#include "AppTask.h"
#include "stdbool.h"
#include "linflexd_uart_driver.h"

/************************宏定义***************************/
#define   IPC_MESSAGE_IDENTIFIER            0xfc
#define   IPC_MESSAGE_MAX_LEN               712
#define   IPC_ESCAPE_DATA                   0xfb         // 转义字节
#define   IPC_MAX_SIZE_LEN                  (1024 + 256)          // IPC串口接收缓冲区大小

#define   IPC_HEARTBEAT_PERIOD_TIMEOUT      1200         // 100ms周期  超时时间为2分钟
#define   IPC_HANDSHAKE_PERIOD_TIMEOUT      6000         // 100ms周期  超时时间为10分钟
#define   IPC_UART_QUEUE_LENTH              5            // 串口接收队列长度
#define   MESSAGE_HEAD_ID_HIGH              0   
#define   MESSAGE_HEAD_ID_LOW               1
#define   MESSAGE_HEAD_ATTRIBUTE_HIGH       2
#define   MESSAGE_HEAD_ATTRIBUTE_LOW        3
#define   MESSAGE_HEAD_LEN_HIGH             4
#define   MESSAGE_HEAD_LEN_LOW              5
#define   MESSAGE_HEAD_TID_HIGH             6
#define   MESSAGE_HEAD_TID_LOW              7
#define   MESSAGE_HEAD_DATA_LEN             8

#define   MSG_ATTR_LEN_MASK                 0xFFFF
#define   MSG_ATTR_ENC_MOD_MASK             (0x07 << 16)
#define   MSG_ATTR_TYPE_MASK                (0x01 << 19)
#define   MSG_ATTR_ACK_FLAG_MASK            (0x01 << 20)

// 当在循环中检查DMA接收数据量数次不变时，认为已经传输完毕
#define   IPC_CHECK_DMA_STATUS_TIME         3

#define   MESSAGE_RX_NAD_ACK                     (uint16_t)0x0800    // NAD应答消息
#define   MESSAGE_TX_MCU_ACK                     (uint16_t)0x0200    // MCU应答消息

//ARM--->MCU消息ID
#define   MESSAGE_RX_HEARTBEAT                   (uint16_t)0x0801    // NAD心跳消息
#define   MESSAGE_RX_HANDSHAKE                   (uint16_t)0x0802    // NAD握手消息
#define   MESSAGE_RX_REPORT_ARM_DTC              (uint16_t)0x0803    // NAD上报ARM侧故障码
#define   MESSAGE_RX_REPORT_ARM_STATUS           (uint16_t)0x0804    // NAD上报ARM业务状态
#define   MESSAGE_RX_ARM_PM_REQUESET             (uint16_t)0x0805    // ARM请求MCU进行相关电源控制
#define   MESSAGE_RX_BT_REQUESET                 (uint16_t)0x0806    // 蓝牙命令请求
#define   MESSAGE_RX_STB_UPDATE_REQUEST          (uint16_t)0x0807    // STB升级请求
#define   MESSAGE_RX_APP_INFO_RESPONSE           (uint16_t)0x0809    // MCU查询APP有关信息(信号强度)
#define   MESSAGE_RX_BLE_CALIBRATION             (uint16_t)0x080a    // 蓝牙标定数据下发
#define   MESSAGE_RX_TIME_CALIBRATION            (uint16_t)0x080c    // NAD上报时间校准
#define   MESSAGE_RX_ID_INFO                     (uint16_t)0x080d    // 读取did信息
#define   MESSAGE_RX_SET_RTC_ALARM               (uint16_t)0x080e    // 设置下一次RTC唤醒时间
#define   MESSAGE_RX_ARM_DID_INFO                (uint16_t)0x080f    // ARM诊断DID配置信息
#define   MESSAGE_RX_ARM_REQUEST_DTC             (uint16_t)0x0811    // ARM发送故障码
#define   MESSAGE_RX_ARM_SET_GPIO                (uint16_t)0x0812    // ARM设置MCU的GPIO
#define   MESSAGE_RX_ARM_CLIENTSTA               (uint16_t)0x0817    // ARM 客户端运行状态
#define   MESSAGE_RX_REMOTE_CONTROL              (uint16_t)0x0822    // 国标远程控制请求命令
#define   MESSAGE_RX_STB_CALIBRATION_REQUEST     (uint16_t)0x0823    // STB标定请求命令
#define   MESSAGE_RX_BT_MAC_COMMAND              (uint16_t)0x0825    // ARM发送板载BT MAC命令
#define   MESSAGE_RX_PARA_CONFIG_CMD             (uint16_t)0x0830    // 车辆参数配置命令
#define   MESSAGE_RX_PERIOD_VEHICLE_REPORT       (uint16_t)0x0835    // NAD状态信息周期下发命令
#define   MESSAGE_RX_CHECK_MIC_FAULT             (uint16_t)0x0836    // ARM发送检测MIC故障命令
#define   MESSAGE_RX_VEHICLE_REMOTE_DIAGNOSIS    (uint16_t)0x0850    // 远程诊断或远程升级命令
#define   MESSAGE_RX_REMOTE_UPGRADE_DOWNLOAD     (uint16_t)0x0895    // 远程升级包下发消息 
#define   MESSAGE_RX_CAN_LOG_CONTROL_COMMAND     (uint16_t)0x0897    // CAN Log控制命令
#define   MESSAGE_RX_SET_WAKEUP_TIME             (uint16_t)0x0898    // 设置唤醒时间命令
#define   MESSAGE_RX_ARM_FTM_COMMAND             (uint16_t)0x08ff    // ARM发送装备命令
#define   MESSAGE_RX_ARM_FTM_PC_COMMAND          (uint16_t)0x1001    // ARM发送PC装备命令
#define   MESSAGE_RX_ARM_FTM_DUT_COMMAND         (uint16_t)0x3001    // ARM发送DUT装备命令
#define   MESSAGE_RX_ARM_CONTROL_ARM_RESET       (uint16_t)0X0899    //控制mcu复位ARM使能，0：不能 1：能//TODO

//MCU--->ARM消息ID
#define   MESSAGE_TX_MCU_STATUS                  (uint16_t)0x0201    // MCU周期性消息发送
#define   MESSAGE_TX_PM_REQUEST                  (uint16_t)0x0204    // MCU电源状态
#define   MESSAGE_TX_ECALL_REQUEST               (uint16_t)0x0205    // 紧急呼叫请求消息
#define   MESSAGE_TX_POWER_ON_REPORT             (uint16_t)0x0206    // MCU开机上报
#define   MESSAGE_TX_STB_UPDATE_RESPONSE         (uint16_t)0x0207    // STB升级响应
#define   MESSAGE_TX_STB_FAULT_NOTIFY            (uint16_t)0x0208    // T-box定位模块故障通知消息
#define   MESSAGE_TX_BT_RX_DATA                  (uint16_t)0x0209    // 蓝牙数据接收
#define   MESSAGE_TX_WAKEUP_INFO                 (uint16_t)0x020b    // 唤醒相关内容
#define   MESSAGE_TX_TIME_CALIBRATION            (uint16_t)0x020c    // 时间校准上报
#define   MESSAGE_TX_CAR_INFO                    (uint16_t)0x020d    // 汽车厂家信息
#define   MESSAGE_TX_TBOX_HARD_CONFIG            (uint16_t)0x020E    // TBOX 模块硬件配置
#define   MESSAGE_TX_APP_INFO_REQUEST            (uint16_t)0x020f    // MCU查询APP有关信息(信号强度)
#define   MESSAGE_TX_BLE_RSSI                    (uint16_t)0x0210    // 蓝牙采集数据上报
#define   MESSAGE_TX_DID_INFO                    (uint16_t)0x0211    // DID info
#define   MESSAGE_TX_TBOX_DTC_RESPONSE           (uint16_t)0x0212    // TBOX故障码响应
#define   MESSAGE_TX_ARM_CAN_NET_NOTIFY          (uint16_t)0x0213    // 查询ARM网络请求和释放
#define   MESSAGE_TX_REMOTE_CONTROL_RESPONSE     (uint16_t)0x0214    // 国标远程控制结果返回
#define   MESSAGE_TX_MCU_LOG                     (uint16_t)0x0216    // MCU上报日志信息(MCU通过ipc接口上报日志信息)
#define   MESSAGE_TX_BLE_LOCATION                (uint16_t)0x0217    // 蓝牙定位参数[测试用]
#define   MESSAGE_TX_PARA_CONFIG_RESPONSE        (uint16_t)0x0230    // 车辆参数配置回应消息
#define   MESSAGE_TX_CAN_PARA_REPORT             (uint16_t)0x0235    // CAN数据周期上报命令
#define   MESSAGE_TX_MCU_FAULT_INFO              (uint16_t)0x0236    // mcu异常上报
#define   MESSAGE_TX_STB_CALIBRATION_RESPONSE    (uint16_t)0x0220    // STB数据上报
#define   MESSAGE_TX_APP_FREE_NOTIFY             (uint16_t)0x0219    // APP空闲通知
#define   MESSAGE_TX_BLE_LOCATION_AREA           (uint16_t)0x0218    // 蓝牙定位区域变化通知
#define   MESSAGE_TX_MCU_TSP_REGISTER            (uint16_t)0x0238    // MCU上报注册条件状态
#define   MESSAGE_TX_SWITCH_BT_AD                (uint16_t)0x0239    // MCU通知ARM打开关闭广播
#define   MESSAGE_TX_VEHICLE_REMOTE_DIAGNOSIS    (uint16_t)0x0255    // 远程诊断或远程升级命令回应
#define   MESSAGE_TX_REMOTE_UPGRADE_RESPONSE     (uint16_t)0x0295    // 远程MCU升级应答消息
#define   MESSAGE_TX_UPDATE_CODE_RESPONSE        (uint16_t)0x029b    // 升级包的帧应答
#define   MESSAGE_TX_ARM_FTM_CMD                 (uint16_t)0x02FF    // ARM装备产线命令
#define   MESSAGE_TX_MCU_SECURITY_EVENT          (uint16_t)0x0222    // MCU安全事件日志


#define ARM_FTM_RX_SPI                  0x1a

#define ALL_FAULT                       0x09
#define HISTORY_FAULT                   0x08
#define NOW_FAULT                       0x01

#define IPC_PERIOD_CHECK_INTERVAL       (10*1000/100)    //每10s检测一次客户端连接状态，目前函数设计不为10s待解决，10*1000？
#define ACK_TIMEOUT                     2
#define ACK_TIMEOUT_COUNT               2

#define Remote_UpGrade_Start            0x01
#define Remote_UpGrade_End              0x00

/************************数据结构定义***************************/
typedef enum
{
    NO_ENCRYPT_MODE = 0,                 //无加密
    RSA_ENCRYPT_MODE,                    //RSA加密方式
} EncryptMode;

typedef enum
{
    NORAML_MESSAGE = 0,                 //普通消息
    LONG_MESSAGE,                       //长消息
} MessageType;

typedef enum
{
    FLAG_NOT_WATI_ACK = 0,              //发送不需等待ACK应答
    FLAG_WAIT_ACK,                      //发送需等待ACK应答
} AckFlag;

typedef enum
{
    FLAG_NOT_RX_HEARTBEAT = 0,          //未接收到心跳消息
    FLAG_RX_HEARTBEAT,                  //已接收到心跳消息
} HeartbeatFlag;


typedef enum
{
    STATUS_RX_IDLE = 0,                 // IPC UART串口接收空闲
    STATUS_RX_ONGOING,                  // IPC UART串口接收数据中
    STATUS_RX_HANDLE,                   // IPC 数据处理中
} RxStatus;

typedef enum
{
    IPC_STATUS_HEAD = 0,
    IPC_STATUS_TAIL, 
    IPC_STATUS_END, 
}IpcStatus;

typedef enum
{
    FLAG_NOT_ESCAPE = 0,                //未出现转义字节
    FLAG_ESCAPE,                        //出现转义字节
} EscapeFlag;

typedef enum
{
    FLAG_FIXED_LEN = 0,                //此报文是定长
    FLAG_NOT_FIXED_LEN,                //此报文不是定长
} FixedFlag;


typedef enum
{
    IPC_NO_ERROR = 0,                   //执行正确
    IPC_INPUT_PARA_ERROR,               //函数输入参数错误
    IPC_FRAME_LEN_TOO_LONG,             //消息报文长度过长
    IPC_ID_ERROR,                       //消息报文ID错误
    IPC_WAIT_ACK_RX_EVNET,              //等待ACK报文接收到其他非ACK报文
} ipcErrorCode;

typedef enum
{
    DID_WRITE_TYPE = 0x00,
    DID_READ_TYPE,
}DidRwType;

typedef enum
{
    DID_RW_RESULT_SUCCESS = 0x00,
    DID_RW_RESULT_FAIL,
}DidRwReult;

typedef enum
{
    CAR_STATUS_ON = 0,
    CAR_STATUS_OFF,
} CarStatus;

typedef enum
{
    GPIO_CONTROL_INDEX_SOS = 0,
    GPIO_CONTROL_INDEX_FDC,
    GPIO_CONTROL_INDEX_GPS_PHY,
}GpioControlIndex;

typedef enum
{
    MESSAGE_ID_RX_HEARTBEAT = 0x00,
    MESSAGE_ID_RX_HANDSHAKE,
    MESSAGE_ID_RX_REPORT_ARM_DTC,
    MESSAGE_ID_RX_REPORT_ARM_STATUS,
    MESSAGE_ID_RX_ARM_PM_REQUESET,
    MESSAGE_ID_RX_BT_REQUESET,
    MESSAGE_ID_RX_TIME_CALIBRATION,
    MESSAGE_ID_RX_ID_INFO,
    MESSAGE_ID_RX_SET_RTC_ALARM,
    MESSAGE_ID_RX_ARM_DID_INFO,
    MESSAGE_ID_RX_ARM_REQUEST_DTC,
    MESSAGE_ID_RX_ARM_SET_GPIO,
    MESSAGE_ID_RX_ARM_CLIENTSTA,
    MESSAGE_ID_RX_GB_REMOTE_CONTROL,
    MESSAGE_ID_RX_PARA_CONFIG_CMD,
    MESSAGE_ID_RX_PERIOD_VEHICLE_REPORT,
    MESSAGE_ID_RX_CHECK_MIC_FAULT,
    MESSAGE_ID_RX_VEHICLE_REMOTE_DIAGNOSIS,
    MESSAGE_ID_REMOTE_UPGRADE_DOWNLOAD,
    MESSAGE_ID_CAN_LOG_CONTROL_COMMAND,
    MESSAGE_ID_SET_WAKEUP_TIME,
    MESSAGE_ID_RX_ARM_FTM_COMMAND,
    MESSAGE_ID_RX_ARM_FTM_PC_COMMAND,
    MESSAGE_ID_RX_ARM_FTM_DUT_COMMAND,
    MESSAGE_ID_RX_ARM_CONTROL_ARM_RESET,
    MESSAGE_ID_RX_MAX,
} MessageRxIndex;

typedef enum
{
    MESSAGE_ID_TX_MCU_STATUS = 0x00,
    MESSAGE_ID_TX_PM_REQUEST ,
    MESSAGE_ID_TX_ECALL_REQUEST,
    MESSAGE_ID_TX_POWER_ON_REPORT,
    MESSAGE_ID_TX_BT_RX_DATA,
    MESSAGE_ID_TX_WAKEUP_INFO,
    MESSAGE_ID_TX_TIME_CALIBRATION,
    MESSAGE_ID_TX_CAR_INFO,
    MESSAGE_ID_TX_TBOX_HARD_CONFIG,
    MESSAGE_ID_TX_DID_INFO,
    MESSAGE_ID_TX_TBOX_DTC_RESPONSE,
    MESSAGE_ID_TX_ARM_CAN_NET_NOTIFY,
    MESSAGE_ID_TX_REMOTE_CONTROL_RESPONSE,
    MESSAGE_ID_TX_MCU_LOG,
    MESSAGE_ID_TX_MCU_SECURITY_EVENT,
    MESSAGE_ID_TX_PARA_CONFIG_RESPONSE,
    MESSAGE_ID_TX_CAN_PARA_REPORT,
    MESSAGE_ID_TX_MCU_FAULT_INFO,
    MESSAGE_ID_TX_VEHICLE_REMOTE_DIAGNOSIS,
    MESSAGE_ID_TX_REMOTE_UPGRADE_RESPONSE,
    MESSAGE_ID_TX_UPDATE_CODE_RESPONSE,
    MESSAGE_ID_TX_ARM_FTM_CMD,
    MESSAGE_ID_TX_MAX,
} MessageTxIndex;

typedef enum
{
    DTC_FAULT_CURRENT_MASK = 0x01,     /*当前故障*/
    DTC_FAULT_CONFIRMED_MASK = 0x08,   /*历史故障*/
    ALL_FAULT_ALL_MASK = 0x09,         /*历史故障 +  当前故障*/
}DtcFaultRequestMaskValue;

typedef enum
{
    DIAG_REMOTE_NOT_ENABLE,    /*0 不使能*/
    DIAG_REMOTE_ENABLE,        /*1 使能*/
}DiagRemoteEnableOrNot;

typedef  void(*pIpcRxCallBack)(uint8_t *para, uint16_t len);

typedef union
{
    uint32_t byte;
    struct
    {
        uint32_t len: 16;                  //消息体总长度
        uint32_t encryptionMode: 3;        //消息加密方式
        uint32_t type: 1;                  //消息类型
        uint32_t ackFlag: 1;               //ACK标志
        uint32_t reserved: 11;             //保留字段
    } bit;
} MessageAttribute;

typedef struct
{
    uint16_t totalNumber;                 //总的包数
    uint16_t currentNumber;               //当前包数
} MessagePackage;

typedef struct messageHead
{
    uint16_t           id;               //消息头ID
    MessageAttribute attribute;        //消息属性
    uint16_t           serialNumber;     //消息流水号
    MessagePackage   package;          //消息包封装项  
}MessageHead;

typedef struct messageRxInfo           
{
    MessageRxIndex   index;              //消息索引值
    uint16_t           id;                 //消息ID
    FixedFlag        fixedFlag;          //消息是否定长
    MessageAttribute attribute;          //消息属性
    pIpcRxCallBack   IpcRxCallBack;      //消息接收回调函数
} MessageRxInfo;

typedef struct messageTxInfo  
{
    MessageTxIndex   index;              //消息索引值
    uint16_t           id;                 //消息ID
    AckFlag          ackFlag;            //是否需要应答标志
    FixedFlag        fixedFlag;          //消息是否定长
    MessageType      type;               //消息类型
    EncryptMode      encryptMode;        //数据加密方式
    uint8_t            len;                //消息长度
} MessageTxInfo;

typedef struct ipcTx
{
    uint16_t len;
    uint8_t  buffer[IPC_MAX_SIZE_LEN];
}IpcTx;

typedef struct ipcInfo
{
    AckFlag ackFlag;                          //是否需要应答标志
    AckFlag txAck;                            //是否需要发送应答
    uint16_t  retransmissionTimeout;            //超时重传时间
    uint16_t  retransmissionCount;              //超时重传次数
    HeartbeatFlag heartbeatFlag;              //是否接收到心跳消息
    uint16_t  heartbeatTimeout;                 //未接收到心跳超时时间
    uint16_t  handshakeTimeout;                 //未接收到握手超时时间
    RxStatus rxStatus;                        //IPC UART串口接收数据状态
    uint16_t  txTid;                            //发送流水号
    uint16_t  rxTid;                            //接收流水号
    IpcTx   ipcTx;                            //发送给ARM长度和数据   
    uint8_t   uartBuf[IPC_MAX_SIZE_LEN];
    uint16_t  uartLen;
    status_t          lastUartStatus;         // 最近一次的串口状态
    volatile bool     uartRxFlag;             // IPC串口接收标志
    volatile bool     uartTxFlag;             // IPC串口发送标志
    volatile bool     uartErrFlag;            // IPC串口错误标志
    volatile uint32_t   uartErrCount;           // IPC串口错误计数
    volatile uint32_t   uartLostBytes;          // IPC串口丢失数据计数
    volatile uint32_t   TxCount;                // IPC发送计数
    volatile uint32_t   RxCount;                // IPC接收计数
} IpcInfo;

/************************函数接口***************************/
void IpcMsgRxTimeCalibration(uint8_t *para, uint16_t len);
void IpcInitRamData(void);
void IpcPeriodFunction(void);
void IpcUartIsrRxFunction(void);                               //void IpcUartIsrRxFunction(void);
void IpcUartMainFunction(void);
void IpcPowerOnTxArm(void);
void IpcCheckUartRxStatus(void);
void IpcMsgTxPowerOnReport(void);
void IpcSendData(uint16_t len, uint8_t *buf);
void IpcUartReInit(void);
void IpcTxWakeupStatus(void);
ipcErrorCode IpcFunctionCallback(void);
ipcErrorCode IpcXorCalculation(uint8_t *pData, uint16_t len, uint8_t *resultData);
uint8_t CheckSleepRequestFromServer(void);
void CleanServerSleepRequest(void);
void SetServerSleepRequset(uint8_t sleepLevel);
#endif

