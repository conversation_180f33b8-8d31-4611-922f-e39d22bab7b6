#pragma once
#ifndef _IPC_TASK_H_
#define _IPC_TASK_H_

#include "AppTask.h"
#include "IpcApi.h"
#include "task.h"


#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define IPC_TASK_STACK_SIZE (1024 * 2)
#define IPC_TASK_PRIORITY   3

extern TaskHandle_t IPCTask_Handle;

void StartIPCTask(void);
void IPCTask(void* param);

void IpcTaskInitHook(void);
void IpcTaskPostInitHook(void);
void IpcTaskPeriodHook(void);
void IpcTaskFunctionHook(Msg msg);

#endif