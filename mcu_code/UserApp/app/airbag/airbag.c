#include <string.h>
#include "AppTask.h"
#include "airbag.h"
#include "LogApi.h"
#include "etmr_ic_driver.h"
#include "etmr_config.h"
#include "event.h"
#include "IpcApi.h"

extern CommonInfo  g_commonInfo;

static EnsInformation_t   g_EnsInformation;

/*************************************************
函数名称: EnsInformationGet
函数功能: 安全气囊信息获取
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/03/15
*************************************************/
EnsInformation_t   *EnsInformationRead(void)
{
    return &g_EnsInformation;
}

/*************************************************
函数名称: EnsInformtionRamInit
函数功能: 信息变量初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/03/15
*************************************************/
void EnsInformationRamInit(void)
{
    EnsInformation_t *p_EnsInformation = NULL;
    p_EnsInformation = EnsInformationRead();

    p_EnsInformation->ensSignalStatus = ENS_SIGNAL_NORMAL;
    p_EnsInformation->ensCanStatus    = ENS_CAN_NORMAL;
    p_EnsInformation->ensSignalNotifyArmStatus = ENS_NOTIFY_NOT_SNED;
    p_EnsInformation->ensCanNotifyArmStatus = ENS_NOTIFY_NOT_SNED;
}


/*************************************************
函数名称: TboxSendEnsSignalStatus
函数功能: 发送ens状态信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2018/09/14
*************************************************/
void TboxSendEnsSignalStatus(EnsSignalStatus siganlSta,EnsCanStatus canSta,uint8_t *canInfo)
{
    uint8_t tempBuffer[11];
    Msg msg;
    
    tempBuffer[0] = MCU_FAULT_AIRBAG;
    tempBuffer[1] = siganlSta;
    tempBuffer[2] = canSta;
    memcpy(&tempBuffer[3],canInfo,8);

    msg.event = MESSAGE_TX_MCU_FAULT_INFO;
    msg.len   = 11;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: EnsSignalCheckInt
函数功能: 安全气囊信号检测中断函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/03/15
*************************************************/
void EnsSignalCheckInt(void)
{
    uint16_t count= 0;
    uint32_t plusWidth= 0;
    EnsInformation_t *p_EnsInformation = NULL;
    static uint8_t airBagTrigValidCount = 0;
    static uint8_t airBagNormalCount = 0;
    
    p_EnsInformation = EnsInformationRead();

    /*因为脉冲捕获使用的时钟是PCLK = 40MHz, 分频了8，因此count值要乘以(40M/8 = 5M, 1.6),单位是微妙哦 */

    
    //count = ((TAUB0.CSR9 * 0x10000) + (TAUB0.CDR9 + 1));
    if (eTMR_DRV_GetInputCaptureComplete(ETMR_AIRBAG_PWM_INST, AIRBAG_CAPTURE_CHAN))
    {
        count = ETMR_CM_Config0_State.measurementPosPulseCnt[AIRBAG_CAPTURE_CHAN];
        eTMR_DRV_ClearInputCaptureComplete(ETMR_AIRBAG_PWM_INST, AIRBAG_CAPTURE_CHAN);
        
    }
    else
    {
        //SystemApiLogPrintf(LOG_INFO_OUTPUT, "airbag PWM NOT checked\r\n");
        count = 0;
    }
    plusWidth = count;
    //if(0 != count)
    //{
       //SystemApiLogPrintf(LOG_INFO_OUTPUT, "airbag PWM counter=%d\r\n", count);
    //}
    /*正常情况下安全气囊信号*/
    if((ENS_NORMAL_HIGH_LEVEL_WIDTH_LOWLIMIT <= plusWidth)&&(ENS_NORMAL_HIGH_LEVEL_WIDTH_TOPLIMIT >= plusWidth))
    {
        airBagNormalCount++;
        if(airBagNormalCount>=2)
        {

            /*安全气囊状态正常， 模式正常*/
            p_EnsInformation->ensSignalStatus = ENS_SIGNAL_NORMAL;
            p_EnsInformation->ensSignalMode  = ENS_SIGNAL_NORMAL;
            airBagNormalCount = 0;
            airBagTrigValidCount = 0;
        }
    }
    /*触发情况下安全气囊信号*/
    else if((ENS_TRIG_HIGH_LEVEL_WIDTH_LOWLIMIT <= plusWidth)&&(ENS_TRIG_HIGH_LEVEL_WIDTH_TOPLIMIT >= plusWidth))
    {
       airBagTrigValidCount++;
       if(airBagTrigValidCount>=2)
       {
            airBagTrigValidCount = 0;
            /*安全气囊状态正常， 模式触发*/
            p_EnsInformation->ensSignalStatus = ENS_SIGNAL_NORMAL;

            if(ENS_NOTIFY_NOT_SNED == p_EnsInformation->ensSignalNotifyArmStatus)
            {
                p_EnsInformation->ensSignalMode  = ENS_SIGNAL_TRIG;
                p_EnsInformation->ensChangedStatus = ENS_STATUS_CHANGED;
                p_EnsInformation->ensSignalNotifyArmStatus = ENS_NOTIFY_HAD_SNED;
#if (AIRBAG_TEST == 1)        
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "airbag signal trigged=ENS_SIGNAL_TRIG.\r\n");
#endif
            }
       }
    }
    else
    {
        airBagTrigValidCount = 0;
        airBagNormalCount = 0;
         /*安全气囊状态离线， 模式离线*/
    }

    //if(TAUB0.CSR9 & 0x01)
    //{
    //    TAUB0.CSC9 = 0x01U;/* clear overflow flag. */
    //}
}

/*************************************************
函数名称: EnsCanAnalays
函数功能: can信号解析
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/10/29
*************************************************/
void EnsCanAnalays(CanPduInfo pduInfo)
{
    uint8_t temp = 0;
    EnsInformation_t *p_EnsInformation = NULL;
    p_EnsInformation = EnsInformationRead();

    /*第二位表示碰撞状态， 0x00:No crash 0x01:Front 0x02:Side 0x03:Front and side*/
    temp = ((pduInfo.data[2])&0x03);
    if(0x00 == temp)
    {
        memcpy(p_EnsInformation->ensCanBuf, pduInfo.data, 8);
        p_EnsInformation->ensCanStatus = ENS_CAN_NORMAL;
        p_EnsInformation->ensCanMode = ENS_CAN_NORMAL;
    }
    else
    {
        if(ENS_NOTIFY_NOT_SNED == p_EnsInformation->ensCanNotifyArmStatus)
        {
            p_EnsInformation->ensCanNotifyArmStatus = ENS_NOTIFY_HAD_SNED;
            p_EnsInformation->ensChangedStatus = ENS_STATUS_CHANGED;
            p_EnsInformation->ensCanMode = ENS_CAN_TRIG;
        }

        p_EnsInformation->ensCanStatus = ENS_CAN_NORMAL;
        memcpy(p_EnsInformation->ensCanBuf, pduInfo.data, 8);
    }

}

/*************************************************
函数名称: EnsSignalCheck
函数功能: 周期性检查ACMCAN信号是否在线                  周期100ms
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2018/10/29
*************************************************/
void EnsSignalCheck(void)
{
    static uint16_t time[3] = {0,0,0};
    static uint8_t  flag[2] = {0,0};
    EnsInformation_t *p_EnsInformation = NULL;
    p_EnsInformation = EnsInformationRead();

    /*触发模式下和非触发模式下，安全气囊can信号状态都定义为正常*/
    if(ENS_CAN_NORMAL == p_EnsInformation->ensCanStatus)
    {
        time[0] = 0;
        p_EnsInformation->ensCanStatus = ENS_CAN_OFFLINE;
        // 持续为正常，超过一定时间，则重置通知ARM标志位
        if(!(p_EnsInformation->ensCanBuf[2] & 0x03))
        {
            time[2]++;
        }
        else
        {
            time[2] = 0;
        }
    }
    else
    {
        time[0]++;
    }

    /*触发模式下和非触发模式下，安全气囊波形信号状态都定义为正常*/
    if(ENS_SIGNAL_NORMAL == p_EnsInformation->ensSignalStatus)
    {
        time[1] = 0;
        /*启动离线检测定时，超过时间则最终定义为信号离线*/
        p_EnsInformation->ensSignalStatus = ENS_SIGNAL_OFFLINE;
    }
    else
    {
        time[1]++;
    }
    // 安全气囊通知ARM标志更新
    if(ENS_NORMAL_NOTIFY_ARM_TIME < time[2] && ENS_NOTIFY_HAD_SNED == p_EnsInformation->ensCanNotifyArmStatus)
    {
        p_EnsInformation->ensCanNotifyArmStatus = ENS_NOTIFY_NOT_SNED;
    }
    //安全气囊CAN信号 离线超时
    if(ENS_CAN_OFFLINE_TIMEOUT_TIME < time[0])
    {
        time[0] = ENS_CAN_OFFLINE_TIMEOUT_TIME;
        if(0 == flag[0])
        {
            p_EnsInformation->ensCanMode = ENS_CAN_OFFLINE;
            p_EnsInformation->ensChangedStatus = ENS_STATUS_CHANGED;
            p_EnsInformation->ensCanNotifyArmStatus = ENS_NOTIFY_NOT_SNED;
            memset(p_EnsInformation->ensCanBuf, 0, 8);
            flag[0] = 1;
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "AirBag CAN  Signal OffLine\r\n");
        }
    }
    else
    {
        if(1 == flag[0])
        {
            p_EnsInformation->ensChangedStatus = ENS_STATUS_CHANGED;
            flag[0] = 0;
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "AirBag CAN  Signal OnLine,mode is %d\r\n",p_EnsInformation->ensCanMode);
        }
    }

    //安全气囊模拟信号 离线超时
    if(ENS_SIGNAL_OFFLINE_TIMEOUT_TIME < time[1])
    {
        time[1] = ENS_SIGNAL_OFFLINE_TIMEOUT_TIME;
        if(0 == flag[1])
        {
            p_EnsInformation->ensSignalMode = ENS_SIGNAL_OFFLINE;
            p_EnsInformation->ensChangedStatus = ENS_STATUS_CHANGED;
            p_EnsInformation->ensSignalNotifyArmStatus = ENS_NOTIFY_NOT_SNED;
            flag[1] = 1;
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "AirBag ENS Signal Offline\r\n");
        }
    }
    else
    {
        if(1 == flag[1])
        {
            p_EnsInformation->ensChangedStatus = ENS_STATUS_CHANGED;
            flag[1] = 0;
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "AirBag ENS Signal Online,mode is %d\r\n",p_EnsInformation->ensSignalMode);
        }
    }
}

/*************************************************
函数名称: EnsPeriodNotifyFuncion
函数功能: 安全气囊周期运行检测, 50ms运行一次
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/03/15
*************************************************/
void EnsPeriodNotifyFuncion(void)
{
    EnsInformation_t *p_EnsInformation = NULL;

    static EnsSignalStatus ensSignalStatus_bak = 0x00;
    static EnsCanStatus    ensCanStatus_bak = 0x00;
    static uint8_t canBuf_bak[8] = {0};
    static uint8_t waitFlag = 0;
    static uint8_t armClinetStaBak = ARM_CLINET_OFFLINE;
    EnsSignalCheckInt();
    p_EnsInformation = EnsInformationRead();
    EnsSignalCheck();

    if((ENS_STATUS_CHANGED == p_EnsInformation->ensChangedStatus))
    {
        p_EnsInformation->ensChangedStatus = ENS_NOTIFY_NOCHANGED;

        if(ARM_CLINET_ONLINE == g_commonInfo.armClinetStatus)
        {
            TboxSendEnsSignalStatus(p_EnsInformation->ensSignalMode ,p_EnsInformation->ensCanMode, p_EnsInformation->ensCanBuf);
        }
        else if(ARM_CLINET_OFFLINE == g_commonInfo.armClinetStatus)
        {
            waitFlag = 1;
            ensSignalStatus_bak = p_EnsInformation->ensSignalMode;
            ensCanStatus_bak = p_EnsInformation->ensCanMode;
            memcpy(canBuf_bak, p_EnsInformation->ensCanBuf, 8);
        }
    }

    /*客户端从未连接状态变成连接状态，发送一次状态到客户端*/
    if((ARM_CLINET_OFFLINE == armClinetStaBak) && (ARM_CLINET_ONLINE == g_commonInfo.armClinetStatus))
    {
        TboxSendEnsSignalStatus(p_EnsInformation->ensSignalMode ,p_EnsInformation->ensCanMode, p_EnsInformation->ensCanBuf);
    }
    armClinetStaBak = g_commonInfo.armClinetStatus;

    /*客户端未连接时，发生了触发事件，等待客户端连接后，补发消息*/
    if(1 == waitFlag)
    {
        waitFlag = 0;
        TboxSendEnsSignalStatus(ensSignalStatus_bak ,ensCanStatus_bak,canBuf_bak);
    }
}