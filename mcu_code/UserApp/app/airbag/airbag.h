#ifndef  _AIRBAG_H_
#define  _AIRBAG_H_
#include "AppTask.h"
#include "r_can.h"

#define AIRBAG_TEST 0

//分析ENS信号，正常时的值在33000到35000之间跳动，直接读的时CDR9寄存器中的值
//#define ENS_NORMAL_HIGH_LEVEL_WIDTH_LOWLIMIT      37000      //33000           //us单位
//#define ENS_NORMAL_HIGH_LEVEL_WIDTH_TOPLIMIT      38000      //35000           //us单位
#define ENS_NORMAL_HIGH_LEVEL_WIDTH_LOWLIMIT        8890      //PWM的计数次数是 9362，下浮5%=472
#define ENS_NORMAL_HIGH_LEVEL_WIDTH_TOPLIMIT        9830      //PWM的计数次数是 9362，上浮5%=468

#define ENS_TRIG_HIGH_LEVEL_WIDTH_LOWLIMIT          35600      //捕获到的PWM方波计数是37486，减少5%=1886            
#define ENS_TRIG_HIGH_LEVEL_WIDTH_TOPLIMIT          39350      //捕获到的PWM方波计数是37486，增加5%=1864    

#define ENS_NORMAL_NOTIFY_ARM_TIME        10// 100*10 ms
#define ENS_CAN_OFFLINE_TIMEOUT_TIME      9 // 100*9 ms
#define ENS_SIGNAL_OFFLINE_TIMEOUT_TIME   9 // 100*9 ms

#define ETMR_AIRBAG_PWM_INST              2
#define AIRBAG_CAPTURE_CHAN               7

//#define PWM_SIM_ENABLED                   1

#define ETMR_PWM_SIM_INST                 1
#define ETMR_PWM_SIM_CHAN                 4

typedef enum
{
    ENS_SIGNAL_OFFLINE = 0x00,
    ENS_SIGNAL_NORMAL,
    ENS_SIGNAL_TRIG,
}EnsSignalStatus;

typedef enum
{   
    ENS_CAN_OFFLINE = 0x00,
    ENS_CAN_NORMAL,
    ENS_CAN_TRIG,
}EnsCanStatus;

typedef enum
{
    ENS_NOTIFY_NOT_SNED = 0x00,
    ENS_NOTIFY_HAD_SNED,
}EnsNotifyArmStatus;

typedef enum
{
    ENS_STATUS_CHANGED = 0x00,
    ENS_NOTIFY_NOCHANGED,
}EnsChangedStatus;

typedef struct
{
    EnsSignalStatus      ensSignalStatus; /*用于检测判断安全气囊波形信号是否在线和离线*/
    EnsSignalStatus      ensSignalMode;   /*用于标识安全气囊波形信号当前工作状态，在线，离线，触发*/
    EnsCanStatus         ensCanStatus;    /*用于检测判断安全气囊can信号是否在线和离线*/
    EnsCanStatus         ensCanMode;      /*用于标识安全气囊波can信号当前工作状态，在线，离线，触发*/
    uint8_t              ensCanBuf[8];
    EnsNotifyArmStatus   ensSignalNotifyArmStatus;
    EnsNotifyArmStatus   ensCanNotifyArmStatus;
    EnsChangedStatus     ensChangedStatus;
}EnsInformation_t;

void EnsPeriodNotifyFuncion(void);
void EnsInformationRamInit(void);
void EnsCanAnalays(CanPduInfo pduInfo);
void EnsSignalCheckInt(void);
#endif