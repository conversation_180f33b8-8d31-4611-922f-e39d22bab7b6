/**********************************************
      ECallApi.c
描述：此文件实现ECall相关的业务功能
作者：RichardChen
时间：2024.04.28
************************************************/
#include "FreeRTOS.h"
#include "LedApi.h"
#include "ECall.h"
#include "IpcApi.h"
#include "LogApi.h"
#include "PmApi.h"
#include "event.h"
#include "gpio.h"
#include "AppTask.h"
#include "rlin30.h"
#include "CanApi.h"



/************************外部全局变量****************************/
extern GpioInfo g_gpioPowerOnInfoList[];
extern CommonInfo g_commonInfo;
extern GpioControlInfoStruct g_GpioControlInfoStruct[GPIO_CONTROL_ENUM_MAX];
//extern ModulePowerStatus  g_powerModuleStatus = MODULE_POWER_IDLE;
extern int GpioControlHandle(GpioCtrlTypeEnum gpioCtrlTypeEnum, GpioControlInfoStruct gpioControlInfoStruct);

/*************************************************
函数名称: GpioControlECallReInit
函数功能: 重新初始化ECall相关的LED状态
输入参数: 初始化的状态
输出参数: 初始化的状态，0-表示初始化成功
函数返回类型值： 
编写者: zxl
编写日期 :2020/01/12
*************************************************/
int GpioControlECallReInit(GpioControlHearbeatuEnum status)
{
    if(GPIO_CONTROL_HEARTBEAT_SUCCESS == status)
    {
        //由于ECall需要在IPC通讯正常的情况下才能触发，因此需要根据IPC通讯的状态来点亮ECall指示灯
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_ON;
        //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
        //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
        //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
        GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].curStatus = GPIO_STATUS_OFF;
        GpioControlHandle(GPIO_CONTROL_ECALL_SOS_RED_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED]);
    }
    else if(GPIO_CONTROL_HEARTBEAT_TIMEOUT == status)
    {
        //由于ECall需要在IPC通讯正常的情况下才能触发，因此需要根据IPC通讯的状态来点亮ECall指示灯
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_OFF;
        GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].curStatus = GPIO_STATUS_BLINKED;
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
        g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioTimeRecord = 0x00;
        GpioControlHandle(GPIO_CONTROL_ECALL_SOS_RED_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED]);
    }
    return 0;
}

/*************************************************
函数名称: UpdateEcallLedStatus
函数功能: 状态更新函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/28
*************************************************/
int UpdateEcallLedStatus(GpioCtrlTypeEnum gpioCtrlType, GpioStatusTypeEnum gpioStatusType, uint8_t lightOnTimeCnt,uint8_t lightOffTimeCnt)
{
    if(gpioStatusType < GPIO_STATUS_INVALID)
    {
        if((GPIO_CONTROL_ECALL_SOS_GREEN_LED == gpioCtrlType) ||
            (GPIO_CONTROL_ECALL_SOS_RED_LED == gpioCtrlType))
        {
            g_GpioControlInfoStruct[gpioCtrlType].curStatus = gpioStatusType;
            g_GpioControlInfoStruct[gpioCtrlType].ioSetTime = lightOnTimeCnt;
            g_GpioControlInfoStruct[gpioCtrlType].ioResetTime = lightOffTimeCnt;
            g_GpioControlInfoStruct[gpioCtrlType].ioTimeRecord = 0x00;
            GpioControlHandle(gpioCtrlType, g_GpioControlInfoStruct[gpioCtrlType]);
        }
    }
    else
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Gpio Update Status received INVALID gpioStatusType for gpioCtrlType=%d. \r\n", gpioCtrlType);
    }

    return 0;
}

/*************************************************
函数名称: ECallStatusNotifyFunction
函数功能: ECall状态更新通知函数
输入参数: 消息
输出参数: msg
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/24
*************************************************/
void ECallStatusNotifyFunction(Msg msg)
{
    EcallVoiceState  eCallStatus = GetECallStatus(msg);
    
    switch(eCallStatus)
    {
        case ECALL_VOICE_STA_ACTIVE:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call Active.\r\n");
            //to blink the GREEN LED
            if(GPIO_STATUS_BLINKED != g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus)
            {
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
                GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            }

            //to update the global variable which will be used in PM task to manage sleep
            g_commonInfo.ecallStatus = ECALL_STATUS_ACTIVE;
            break;
        }
        case ECALL_VOICE_STA_END:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call Ended.\r\n");
            //to turn ON the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_ON;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            g_commonInfo.ecallStatus = ECALL_STATUS_INACTIVE;
            break;
        }
        case ECALL_VOICE_STA_DIALING:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call Dialing.\r\n");
            //to blink the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            g_commonInfo.ecallStatus = ECALL_STATUS_ACTIVE;
            break;
        }
        case ECALL_VOICE_STA_ALERTING:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call Alerting.\r\n");
            //to blink the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            g_commonInfo.ecallStatus = ECALL_STATUS_ACTIVE;
            break;
        }
        case ECALL_VOICE_STA_INCOMING:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call Incoming.\r\n");
            //to blink the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            g_commonInfo.ecallStatus = ECALL_STATUS_ACTIVE;
            break;
        }
        case ECALL_VOICE_STA_HOLDING:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call Holding.\r\n");
            //to blink the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            g_commonInfo.ecallStatus = ECALL_STATUS_ACTIVE;
            break;
        }

        default:
        {
            //to turn ON the green LED　to indicate the ECall is available again
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_ON;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
           
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "ECall status with NOT supported state:%d, just turn ON green LED.\r\n", eCallStatus);
            g_commonInfo.ecallStatus = ECALL_STATUS_INACTIVE;
            break;
        }
    }
    
    // ECallMuteControl();
}

/*************************************************
函数名称: GetECallStatus
函数功能: 将接收到的ECall的状态转换为GetECallStatus
输入参数: msg
输出参数: EcallVoiceState的枚举类型
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/28
*************************************************/
EcallVoiceState GetECallStatus(Msg msg)
{
    EcallVoiceState eCallStatus = ECALL_VOICE_STA_INVALID;
    
    uint8_t *para = (uint8_t*)msg.lparam;
    if(NULL != para)
    {
        XCallType xcallType = (XCallType)para[0];
        
        if(ECALL_INITIATED == xcallType)
        {
            ArmNotifyECallState  armECallStatus = para[1] + (para[2] << 8);
            switch(armECallStatus)
            {
                case E_QSER_VOICE_ECALL_IND_ALACK_POSITIVE_RECEIVED:
                    eCallStatus = ECALL_VOICE_STA_ACTIVE;
                    break;
                case E_QSER_VOICE_ECALL_IND_ALACK_CLEARDOWN_RECEIVED:
                    eCallStatus = ECALL_VOICE_STA_END;
                    break;
                case E_QSER_VOICE_ECALL_IND_DIALING:
                    eCallStatus = ECALL_VOICE_STA_DIALING;
                    break;
                case E_QSER_VOICE_ECALL_IND_ALERTING :
                    eCallStatus = ECALL_VOICE_STA_ALERTING;
                    break;
                case E_QSER_VOICE_ECALL_IND_ACTIVE:
                    eCallStatus = ECALL_VOICE_STA_ACTIVE;
                    break;
                case E_QSER_VOICE_ECALL_IND_DISCONNECTED:
                    eCallStatus = ECALL_VOICE_STA_END;
                    break;
                case E_QSER_VOICE_ECALL_IND_IMS_DISCONNECTED:
                    eCallStatus = ECALL_VOICE_STA_END;
                    break;
                case E_QSER_VOICE_ECALL_IND_ABNORMAL_HANGUP:
                    eCallStatus = ECALL_VOICE_STA_END;
                    break;
                case E_QSER_VOICE_ECALL_IND_PSAP_CALLBACK_START:
                    eCallStatus = ECALL_VOICE_STA_ACTIVE;
                    break;
                case E_QSER_VOICE_ECALL_IND_PSAP_CALLBACK_IMS_UPDATE_MSD:
                    eCallStatus = ECALL_VOICE_STA_INCOMING;
                    break;
                case E_QSER_VOICE_ECALL_IND_SENDING_START_IN_VOICE:
                    eCallStatus = ECALL_VOICE_STA_ACTIVE;
                    break;
                case E_QSER_VOICE_ECALL_IND_REDIAL_TIMER_OUT:
                    eCallStatus = ECALL_VOICE_STA_END;
                    break;
                default:
                    break;
            }
            
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ECall type=TBOX Initiated, status=%d.\r\n", armECallStatus);
        }
        else if(ECALL_INCOMING == xcallType)
        {
            ArmNotifyIncomeECallState  armECallStatus = para[1] + (para[2] << 8);
            switch(armECallStatus)
            {
                case E_QSER_VOICE_CALL_STATE_INCOMING:
                    eCallStatus = ECALL_VOICE_STA_INCOMING;
                    break;
                case E_QSER_VOICE_CALL_STATE_DIALING:
                    eCallStatus = ECALL_VOICE_STA_DIALING;
                    break;
                case E_QSER_VOICE_CALL_STATE_ALERTING:
                    eCallStatus = ECALL_VOICE_STA_ALERTING;
                    break;
                case E_QSER_VOICE_CALL_STATE_ACTIVE :
                    eCallStatus = ECALL_VOICE_STA_ACTIVE;
                    break;
                case E_QSER_VOICE_CALL_STATE_HOLDING:
                    eCallStatus = ECALL_VOICE_STA_HOLDING;
                    break;
                case E_QSER_VOICE_CALL_STATE_END:
                    eCallStatus = ECALL_VOICE_STA_END;
                    break;
                case E_QSER_VOICE_CALL_STATE_WAITING:
                    eCallStatus = ECALL_VOICE_STA_WAITING;
                    break;
                default:
                    break;
            }
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ECall type=Incoming, status=%d.\r\n", armECallStatus);
        }
    }
    return(eCallStatus);
}

