/*************************************
     ECall.h
描述：定义ECall相关的宏与函数声明等
作者：RichardChen
时间：2024.04.28
***************************************/

#ifndef  _ECALL_API_H_
#define  _ECALL_API_H_

#include "AppTask.h"
#include "LedApi.h"


typedef enum
{
    ECALL_INITIATED = 0x00,
    ECALL_INCOMING,
    BCALL_INITIATED,
    BCALL_INCOMING,
    XCALL_TYPE_INVALID,
}XCallType;

typedef enum
{
    ECALL_VOICE_STA_INCOMING = 0x0000,
    ECALL_VOICE_STA_DIALING,
    ECALL_VOICE_STA_ALERTING,
    ECALL_VOICE_STA_ACTIVE,
    ECALL_VOICE_STA_HOLDING,
    ECALL_VOICE_STA_END,
    ECALL_VOICE_STA_WAITING,
    ECALL_VOICE_STA_IDLE,
    ECALL_VOICE_STA_INVALID,
}EcallVoiceState;

typedef enum
{
    E_QSER_VOICE_ECALL_IND_SENDING_START = 1,
    E_QSER_VOICE_ECALL_IND_SENDING_MSD,
    E_QSER_VOICE_ECALL_IND_LLACK_RECEIVED,
    E_QSER_VOICE_ECALL_IND_ALACK_POSITIVE_RECEIVED,
    E_QSER_VOICE_ECALL_IND_ALACK_CLEARDOWN_RECEIVED,
    E_QSER_VOICE_ECALL_IND_DIALING = 9,
    E_QSER_VOICE_ECALL_IND_ALERTING,
    E_QSER_VOICE_ECALL_IND_ACTIVE,
    E_QSER_VOICE_ECALL_IND_DISCONNECTED,
    E_QSER_VOICE_ECALL_IND_IMS_ACTIVE,
    E_QSER_VOICE_ECALL_IND_IMS_DISCONNECTED,
    E_QSER_VOICE_ECALL_IND_ABNORMAL_HANGUP,
    E_QSER_VOICE_ECALL_IND_IMS_MSD_ACK = 20,
    E_QSER_VOICE_ECALL_IND_IMS_UPDATE_MSD,
    E_QSER_VOICE_ECALL_IND_IMS_IN_BAND_TRANSFER,
    E_QSER_VOICE_ECALL_IND_IMS_MSD_NACK,
    E_QSER_VOICE_ECALL_IND_IMS_SRVCC,
    E_QSER_VOICE_ECALL_IND_ONLY_DEREGISTRATION = 31,
    E_QSER_VOICE_ECALL_IND_MAY_DEREGISTER,
    E_QSER_VOICE_ECALL_IND_PSAP_CALLBACK_START = 40,
    E_QSER_VOICE_ECALL_IND_PSAP_CALLBACK_IMS_UPDATE_MSD,
    
    E_QSER_VOICE_ECALL_IND_SENDING_START_IN_VOICE = 8000,
    E_QSER_VOICE_ECALL_IND_T2_TIMER_OUT = 9000,
    E_QSER_VOICE_ECALL_IND_T5_TIMER_OUT,
    E_QSER_VOICE_ECALL_IND_T6_TIMER_OUT,
    E_QSER_VOICE_ECALL_IND_T7_TIMER_OUT,
    E_QSER_VOICE_ECALL_IND_REDIAL_TIMER_OUT,
    E_QSER_VOICE_ECALL_IND_AUTO_ANS_TIMER_OUT,
    E_QSER_VOICE_ECALL_IND_AUTO_ANS_IMS_TIMER_OUT,
    
    E_QSER_VOICE_ECALL_IND_UNSPECIFIED = 0xffff,

}ArmNotifyECallState;

typedef enum
{
    E_QSER_VOICE_CALL_STATE_INCOMING = 0x0000,
    E_QSER_VOICE_CALL_STATE_DIALING,
    E_QSER_VOICE_CALL_STATE_ALERTING,
    E_QSER_VOICE_CALL_STATE_ACTIVE,
    E_QSER_VOICE_CALL_STATE_HOLDING,
    E_QSER_VOICE_CALL_STATE_END,
    E_QSER_VOICE_CALL_STATE_WAITING,
    E_QSER_VOICE_CALL_INVALID = 0xffff,

}ArmNotifyIncomeECallState;


void ECallStatusNotifyFunction(Msg msg);
int GpioControlECallReInit(GpioControlHearbeatuEnum status);
int UpdateEcallLedStatus(GpioCtrlTypeEnum gpioCtrlType, GpioStatusTypeEnum gpioStatusType, uint8_t lightOnTimeCnt,uint8_t lightOffTimeCnt);
EcallVoiceState GetECallStatus(Msg msg);
// void ECallMuteControl(void);

#endif
