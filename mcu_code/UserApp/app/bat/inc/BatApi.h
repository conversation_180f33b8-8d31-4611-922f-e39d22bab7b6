/*
      BatApi.h
描述：定义内置电池具体业务功能头文件，待实现
作者：廖勇刚
时间：2016.7.5
*/
#ifndef  _BAT_API_H_
#define  _BAT_API_H_

#include "AppTask.h"

/************************宏定义***************************/
#define   CHARGE_START_TIME_COUNT            (5*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)       // 充电前每5分钟检测一次电池温度和电量
#define   CHARGE_NORAML_TIME_COUNT           (5*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)       // 第一阶段充电中每5分钟检测一次电池温度和电量
#define   NORAML_MAX_CHARGE_TIME_COUNT       (10*60*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)   // 第一阶段充电满10个小时或电压已达最大值，进入第二阶段充电
#define   CHARGE_SECOND_TIME_COUNT           (2*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)       // 第二阶段充电中每2分钟检测一次电池温度和电量
#define   SECOND_MAX_CHARGE_TIME_COUNT       (2*60*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)    // 第二阶段充电满2小时，若电压未达最大值，则更新电池寿命
#define   LAST_CHARGE_TIME_COUNT             (3*60*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)    // 上一次充电时间超过3个小时
#define   CHARGE_FULL_TIME_COUNT             (10*60*1000)/(TASK_SELFCHECK_PERIOD_TIME - 1)      // 满电后10分钟检测一次电压

#define   BATTERAY_NTC_TEMP_MAX_VALUE        150    // 电池温度最大值
#define   BATTERAY_NTC_TEMP_MIN_VALUE        -40    // 电池温度最小值

#define   CHARGE_START_MIN_TEMP              -30    // 充电前最小温度
#define   CHARGE_START_MAX_TEMP              80     // 充电前最大温度

#define   CHARGEING_MIN_TEMP                 -30    // 充电中最小温度
#define   CHARGEING_MAX_TEMP                 80     // 充电中最大温度

#define   BAT_DEFAULT_FULL_VLOT              4100   // 默认满电电压 单位为 mV
#define   BAT_SOH_MAX_COUNT                  8      // 到期最大次数
#define   BAT_NO_FULL_MAX_COUNT              5      // 电池寿命检测未满次数

#define   BAT_POWER_OFF_VALUE                3400   // 电池低电关机电压mV
#define   BAT_POWER_LOW_VALUE                3600   // 电池低电重新充电电压mV

#define   BAT_TEMP_HIGH_VALUE                80     // 电池高温故障值
#define   BAT_TEMP_LOW_VALUE                 -30    // 电池低温故障值

/************************数据结构定义***************************/
typedef enum
{
    EVENT_BAT_LOST = 0,                   // 内置电池丢失    
    EVENT_BAT_PLUG,                       // 内置电池插入
}BatStatus;


typedef enum
{
    EVENT_SOH_NOT_EXPIRED = 0,             // 电池寿命未到期
    EVENT_SOH_WARNING,                     // 电池寿命即将到期
    EVENT_SOH_EXPIRED,                     // 电池寿命已到期
} SohStatus;

typedef enum
{
    CONTROL_OFF = 0,                        // 控制关闭
    CONTROL_ON,                             // 控制打开
} ControlStatus;

typedef enum  
{
    TEMP_NORAML = 0,                        // 温度正常
    TEMP_ABNORAML,                          // 温度异常
} TempStatus;

typedef enum
{
    EVENT_CHARGE_IDLE = 0,                   // 电池处于充电关闭中
    EVENT_CHARGE_START,                      // 电池处于充电前
    EVENT_CHARGE_FIRST,                      // 电池处于第一阶段充电中
    EVENT_CHARGE_SECOND,                     // 电池处于第二阶段充电中
    EVENT_CHARGE_FULL,                       // 电池处于充电满中
    EVENT_CHARGE_SOH,                        // 电池处于检测电池寿命中
    EVENT_DISCHARGE_ON,                      // 电池处于放电中
} ChargeStatus;

typedef enum
{
    BAT_NO_ERROR = 0,                        // 执行无错误
    BAT_INPUT_PARA_ERROR,                    // 输入参数错误
 
}BatErrorCode;


typedef struct
{            
    BatStatus   batStatus;                   // 内置电池是否在位
    ChargeStatus chargeStatus;               // 充电状态
    TempStatus   tempStatus;                 // 温度状态
    uint32_t      firstChargeTime;             // 第一阶段充电记录时间
    uint32_t      secondChargeTime;            // 第二阶段充电记录时间
    uint16_t      periodTime;                  // 温度查询记录时间
    uint16_t      batMaxVoltage;               // 记录充电过程中最大电池电压
}BatInfo;

/************************函数接口***************************/
void BatPeriodNotifyFunction(Msg msg);
void BatPowerOnRamInit(void);
void BatEventFunction(Msg msg);
int16_t BatReadTemperature(void);
uint16_t BatReadVoltage(void);
uint16_t ReadBpPlusValue(void);
uint16_t ReadMicValue(void);
#endif

