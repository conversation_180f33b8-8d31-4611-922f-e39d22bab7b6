#pragma once
#ifndef _BAT_TASK_H_
#define _BAT_TASK_H_
#include "queue.h"
#include "task.h"

#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define BAT_TASK_STACK_SIZE 384
#define BAT_TASK_PRIORITY   4

extern TaskHandle_t BATTask_Handle;

void StartBATTask(void);
void BATTask(void* param);
void BatTaskPeriodHook(void);
void BatTaskInitHook(void);
void BatTaskPostInitHook(void);
void BatTaskFunctionHook(Msg msg);

#endif