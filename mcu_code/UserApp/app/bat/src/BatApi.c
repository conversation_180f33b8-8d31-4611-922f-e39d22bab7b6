/*
      BatApi.c
描述：此文件主要是任务具体实现的业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/

#include "BatApi.h"
#include "LogApi.h"
#include "NvApi.h"
#include "CanFtm.h"
#include "adc_driver.h"
#include "event.h"
#include "gpio.h"
#include <stdint.h>
#include <string.h>

/************************外部全局变量****************************/
extern  TboxSelfConfigPara g_nvTboxSelfConfigData;
extern  CommonInfo g_commonInfo;
extern  GpioInfo   g_gpioPowerOnInfoList[];
extern  DtcInfo    g_dtcInfo;

/************************函数接口***************************/
static void BatRxArmStatus(Msg msg);
static void BatRxBatStatus(Msg msg);
static void BatChargeStartHandle(void);
static void BatChargeConditionJudgment(void);

/************************全局变量****************************/
BatInfo g_batInfo;

EventInfo g_batEventFunctionMap[] = 
{
    {EVENT_ID_BAT_PERIOD_NOTIFY,  BatPeriodNotifyFunction},
    {EVENT_ID_ARM_STATUS,         BatRxArmStatus         },
    {EVENT_ID_BAT_STATUS,         BatRxBatStatus         },
};

#define ADC_INST                  (0U)
#define ADC_CH_SEQ_LEN            (4U) /* ADC channel sequence number */
#define RUN_VOLTAGE_DET_ADCH_INDX 0    /*外部电源电压采样*/
#define BAT_NTC_DET_ADCH_INDX     1    /*电池温度采样*/
#define MIC_VOLTAGE_DET_ADCH_INDX 2    /*MIC电压采样*/
#define BAT_VOLTAGE_DET_ADCH_INDX 3    /*电池电压采样*/

// 根据NTC热敏电阻器转换成ADC值对应表，下标0~190对应-40~150摄氏度 根据手册温度-阻值表计算 ADC=4095*Tn/(Tn+R) 取整
uint16_t g_mf5aTempTab[] =
{
    3972,3964,3956,3947,3938,3929,3919,3908,3897,3885,  // -40 ~ -31
    3873,3860,3846,3832,3817,3802,3785,3768,3750,3732,  // -30 ~ -21
    3712,3692,3671,3649,3626,3603,3578,3553,3527,3499,  // -20 ~ -11
    3471,3442,3412,3381,3349,3317,3283,3249,3213,3177,  // -10 ~ -1
    3140,3102,3063,3024,2984,2943,2901,2859,2817,2773,  // 0 ~ 9
    2730,2686,2641,2596,2551,2505,2460,2414,2368,2322,  // 10 ~ 19
    2276,2230,2184,2138,2092,2047,2002,1957,1913,1869,  // 20 ~ 29
    1825,1782,1740,1698,1656,1615,1575,1535,1496,1458,  // 30 ~ 39
    1420,1383,1347,1311,1276,1242,1209,1176,1144,1112,  // 40 ~ 49
    1082,1052,1023, 994, 966, 939, 913, 887, 862, 838,  // 50 ~ 59
     814, 791, 768, 746, 725, 705, 684, 665, 646, 628,  // 60 ~ 69
     610, 592, 575, 559, 543, 528, 513, 498, 484, 470,  // 70 ~ 79
     457, 444, 432, 419, 408, 396, 385, 374, 364, 354,  // 80 ~ 89
     344, 334, 325, 316, 307, 299, 291, 283, 275, 268,  // 90 ~ 99
     260, 253, 246, 240, 233, 227, 221, 215, 209, 204,  // 100 ~ 109
     199, 193, 188, 183, 179, 174, 169, 165, 161, 157,  // 110 ~ 119
     153, 149, 145, 142, 138, 135, 131, 128, 125, 122,  // 120 ~ 129
     119, 116, 113, 110, 108, 105, 102, 100,  98,  95,  // 130 ~ 139
      93,  91,  89,  87,  85,  83,  81,  79,  77,  75,  // 140 ~ 149
      76,                                               // 150
};

/*************************************************
函数名称: ADC0_IRQHandler
函数功能: ADC 采样完成中断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xuhui
编写日期 :2024/03/8
*************************************************/
void ADC0_IRQHandler(void)
{
    int i = 0;
	uint16_t adc_conv_result;

    ADC_DRV_ClearEoseqFlagCmd(ADC_INST);

    for (i = 0; i < ADC_CH_SEQ_LEN; i++)
    {
    	/* read the data */
        adc_conv_result = ADC_DRV_ReadFIFO(ADC_INST);

		switch (i) 
		{
			case RUN_VOLTAGE_DET_ADCH_INDX:
				g_commonInfo.bPplusAdcValue = adc_conv_result;
				break;
			case BAT_NTC_DET_ADCH_INDX:
                g_commonInfo.batTempAdcValue = adc_conv_result;
				break;
			case MIC_VOLTAGE_DET_ADCH_INDX:
				g_commonInfo.micVoltAdcValue = adc_conv_result;
				break;
			case BAT_VOLTAGE_DET_ADCH_INDX:
                g_commonInfo.batVoltAdcValue = adc_conv_result;
				break;
			default:
			break;
		}
    }
}

/*************************************************
函数名称: TriggerADCSampling
函数功能: 触发ADC采样
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xuhui
编写日期 :2024/03/8
*************************************************/
static void TriggerADCSampling(void)
{
    ADC_DRV_Start(ADC_INST);
    #if LOG_SWITCH_CONFIG_DEBUG == LOG_SWITCH_ON
    SystemApiLogPrintf(LOG_INFO_OUTPUT,"ADC Sampling enterd\n");
    #endif
}

/*************************************************
函数名称: ReadBpPlusValue
函数功能: 获取B+ 电压
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xuhui
编写日期 :2024/03/8
*************************************************/
uint16_t ReadBpPlusValue(void)
{
    float tmp = (34700/4096.0)*g_commonInfo.bPplusAdcValue/(91000.0/(91000.0+1000000.0))*0.1020;
    uint16_t vo = (uint16_t)tmp;
    return vo;
}

/*************************************************
函数名称: ReadMicValue
函数功能: 获取Mic 电压
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xuhui
编写日期 :2024/03/8
*************************************************/
uint16_t ReadMicValue(void)
{
    float tmp = (34700/4096.0)*g_commonInfo.micVoltAdcValue/(20.0/(15+20))*0.1020;
    uint16_t vo = (uint16_t)tmp;
    return vo;
}

/*************************************************
函数名称: BatTempDetect
函数功能: 内置电池温度故障检测
          电池NTC温度高于65度定义为电池温度高故障
          电池NTC温度低于-15度定义为电池温度低故障
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/20
*************************************************/
static void BatTempDetect(void)
{
    if(BAT_TEMP_HIGH_VALUE >= BatReadTemperature() && BAT_TEMP_LOW_VALUE <= BatReadTemperature())
    {
        g_batInfo.tempStatus = TEMP_NORAML;
    }
    else
    {
        g_batInfo.tempStatus = TEMP_ABNORAML;
    }
}

/*
 * @brief Get last Max Charge battery voltage from NV
 * @param 无
 * @return Max Charge battery voltage
 */
static uint16_t BatReadNvMaxVoltage(void)
{
    uint16_t  VoltageValue = 0;

    VoltageValue = (g_nvTboxSelfConfigData.batPara.batVoltageH << 8) | g_nvTboxSelfConfigData.batPara.batVoltageL;
    return VoltageValue;
}

/*
 * @brief 内置电池插拔检测
 * @param 无
 * @return 无
 * @note Check By Bat Temperature
 */
static void BatPlugCheck(void)
{
    int16_t value = BatReadTemperature();
    if(value > BATTERAY_NTC_TEMP_MIN_VALUE && g_batInfo.batStatus == EVENT_BAT_LOST)
    {
        g_batInfo.batStatus = EVENT_BAT_PLUG;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat plug in\r\n");
    }
    else if(value == BATTERAY_NTC_TEMP_MIN_VALUE && g_batInfo.batStatus == EVENT_BAT_PLUG)
    {
        g_batInfo.batStatus = EVENT_BAT_LOST;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat plug out\r\n");
    }
}

/*
 * @brief 内置电池充电中温度检测
 * @param 无
 * @return true:温度正常 false:温度异常
 * @note Check Bat Charging Temperature
 */
static bool BatChargingTempCheck(void)
{
    bool result = false;
    if(CHARGEING_MAX_TEMP >= BatReadTemperature() && CHARGEING_MIN_TEMP <= BatReadTemperature())
    {
        result = true;
    }
    else
    {
        result = false;
    }
    return result;
    
}

/*
 * @brief Update Bat Max Voltage
 * @param 无
 * @return 无
 * @note Update Bat Max Voltage， if now voltage > max voltage, update max voltage and reset no full count to 0, else update no full count plus 1
 */
static void UpdateBatMaxVoltage(void)
{
    TboxSelfConfigPara tempConfigPara;
    uint16_t nowBatVoltage = BatReadVoltage();
    uint16_t maxBatVoltage = BatReadNvMaxVoltage();
    nowBatVoltage = nowBatVoltage > g_batInfo.batMaxVoltage ? nowBatVoltage : g_batInfo.batMaxVoltage;
    g_batInfo.batMaxVoltage = 0;
    // 充电完成后电压超过最大电压或者未充满次数超过最大次数，更新最大电压
    if(nowBatVoltage > maxBatVoltage || g_nvTboxSelfConfigData.batPara.NoFullCount >= BAT_NO_FULL_MAX_COUNT)
    {
        g_nvTboxSelfConfigData.batPara.batVoltageH = (uint8_t)(nowBatVoltage >> 8);
        g_nvTboxSelfConfigData.batPara.batVoltageL = (uint8_t)(nowBatVoltage & 0xff);
        if(g_nvTboxSelfConfigData.batPara.NoFullCount >= BAT_NO_FULL_MAX_COUNT)
        {
            g_nvTboxSelfConfigData.batPara.NoFullCount = 0;
            g_nvTboxSelfConfigData.batPara.SohCount++;
        }
        // 这个可用于判断电池是否为新电池
        if(nowBatVoltage >= BAT_DEFAULT_FULL_VLOT)
        {
            g_nvTboxSelfConfigData.batPara.SohCount = 0;
        }
    }
    else
    {
        g_nvTboxSelfConfigData.batPara.NoFullCount++;
    }
    // update nv
    memcpy(&tempConfigPara, &g_nvTboxSelfConfigData, sizeof(TboxSelfConfigPara));
    if(NV_NO_ERROR != NvApiWriteData(NV_ID_SELF_CONFIG, (uint8_t*)&tempConfigPara, sizeof(TboxSelfConfigPara)))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "bat new full vlot write nv fail\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat new full vlot write nv success, volt: %dmV, target volt: %dmV no full count: %d\r\n", nowBatVoltage, maxBatVoltage, g_nvTboxSelfConfigData.batPara.NoFullCount);
    }
}

/*************************************************
函数名称: BatPowerOnRamInit
函数功能: 内置电池开机变量初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
void BatPowerOnRamInit(void)
{
    TboxSelfConfigPara tempConfigPara;

    TriggerADCSampling();
    g_batInfo.chargeStatus = EVENT_CHARGE_IDLE;
    g_batInfo.tempStatus = TEMP_NORAML;
    g_batInfo.firstChargeTime   = 0;
    g_batInfo.secondChargeTime = 0;
    g_batInfo.periodTime  = 0;
    if(BatReadNvMaxVoltage() == 0)
    {
        g_nvTboxSelfConfigData.batPara.batVoltageH = (uint8_t)(BAT_DEFAULT_FULL_VLOT >> 8);
        g_nvTboxSelfConfigData.batPara.batVoltageL = (uint8_t)(BAT_DEFAULT_FULL_VLOT & 0xff);
        memcpy(&tempConfigPara, &g_nvTboxSelfConfigData, sizeof(TboxSelfConfigPara));
        if(NV_NO_ERROR != NvApiWriteData(NV_ID_SELF_CONFIG, (uint8_t*)&tempConfigPara, sizeof(TboxSelfConfigPara)))
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "bat default full vlot write nv fail\r\n");
        }
    }
    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat PowerOn with bat max voltage:%dmV\r\n", BatReadNvMaxVoltage());
    #endif
}

/*************************************************
函数名称: BatChargeControl
函数功能: 内置电池充电控制
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeControl(ControlStatus status)
{
    FtmInfo  *pFtmInfo = FtmInitRead();
    
    if(pFtmInfo->ftmMode == FTM_MODE_ENTER)
    {
        return;
    }

    if(CONTROL_OFF == status)
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BAT_CHARGE_EN], GPIO_OUTPUT_LOW);
    }
    else
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BAT_CHARGE_EN], GPIO_OUTPUT_HIGH);    
    }
}

/*************************************************
函数名称: BatEnterChargeConditionJudgment
函数功能: 内置电池充电条件判断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatEnterChargeConditionJudgment(void)
{
    /*
        B+电源开启 && B+电压正常 && (ACC开启 || ECALL激活 || SVT激活) && 内置电池插入 && 电池温度正常
    */
    if(
        (BPLUS_STATUS_PLUG == g_commonInfo.bplusStatus) 
        && (BPLUS_VOLT_STATUS_NORMAL == g_commonInfo.bplusVoltStatus)
        && ((WORK_STATUS_ACTIVE == g_commonInfo.accStatus) || (ECALL_STATUS_INACTIVE != g_commonInfo.ecallStatus) || (SVT_STATUS_ACTIVE == g_commonInfo.svtStatus)) 
        && (EVENT_BAT_PLUG == g_batInfo.batStatus)
        && (TEMP_NORAML == g_batInfo.tempStatus)
        )
    {
        /*
        电池处于充电关闭中 || 电池处于放电中
        */

        if((EVENT_CHARGE_IDLE == g_batInfo.chargeStatus) || (EVENT_DISCHARGE_ON == g_batInfo.chargeStatus))
        {   
            g_batInfo.chargeStatus = EVENT_CHARGE_START;
            g_batInfo.periodTime = 0;
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat enter charge start\r\n");
            #endif
        }
    }
}

/*************************************************
函数名称: BatExitChargeConditionJudgment
函数功能: 内置电池退出充电条件判断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatExitChargeConditionJudgment(void)
{
    /*
        B+电源关闭 || B+电压异常 || 内置电池丢失 || (acc off &&  ecall off || svt off) || 电池温度异常
    */
    if(
        (BPLUS_STATUS_LOST == g_commonInfo.bplusStatus) 
        || (BPLUS_VOLT_STATUS_ABNORMAL == g_commonInfo.bplusVoltStatus)
        || (EVENT_BAT_LOST == g_batInfo.batStatus) 
        || ((WORK_STATUS_INACTIVE == g_commonInfo.accStatus) && (ECALL_STATUS_INACTIVE == g_commonInfo.ecallStatus) && (SVT_STATUS_INACTIVE == g_commonInfo.svtStatus))
        || (TEMP_ABNORAML == g_batInfo.tempStatus)
       )
    {
        if((EVENT_CHARGE_START == g_batInfo.chargeStatus) || (EVENT_CHARGE_FIRST == g_batInfo.chargeStatus))
        {
            g_batInfo.chargeStatus = EVENT_CHARGE_IDLE;
            BatChargeControl(CONTROL_OFF);
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat charge off with bplusStatus=%d\r\n", g_commonInfo.bplusStatus);
            #endif
        }  
    }
}

/*************************************************
函数名称: BatChargeConditionJudgment
函数功能: 内置电池前置充电条件判断
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeConditionJudgment(void)
{
    BatPlugCheck();
    BatTempDetect();
    BatEnterChargeConditionJudgment();
    BatExitChargeConditionJudgment();
}

/*************************************************
函数名称: BatRxEcallStatus
函数功能: 内置电池接收到ECALL状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatRxArmStatus(Msg msg)
{
    uint8_t *tempBuf = NULL;
    uint8_t armIndex = 0;
    uint8_t armStatus = 0;
    
    if((2 != msg.len)||(NULL == (void *)msg.lparam))
    {
        return;
    }

    tempBuf = (uint8_t *)msg.lparam;
    armIndex = tempBuf[0];
    armStatus = tempBuf[1];
    
    switch(armIndex)
    {
        case TBOX_ECALL_STATUS:
        {
            if(ECALL_STATUS_ACTIVE >= armStatus)
            {
                g_commonInfo.ecallStatus = (EcallStatus)armStatus;
            }
            break;
        }
        case TBOX_SVT_STATUS:
        {
            if(SVT_STATUS_ACTIVE >= armStatus)
            {
                g_commonInfo.tspStatus = (WorkStatus)armStatus;
            }
            break;
        }
        default:
        {
            return;
        }
    }

    BatChargeConditionJudgment();
    return;
}

/*************************************************
函数名称: BatRxBatStatus
函数功能: 内置电池接收到Bat状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/04
*************************************************/
static void BatRxBatStatus(Msg msg)
{
    uint8_t *batStatus = NULL;
    
    if((1 != msg.len)||(NULL == (void *)msg.lparam))
    {
        return;
    }

    batStatus = (uint8_t *)msg.lparam;
    if(EVENT_BAT_LOST == *batStatus)
    {
        g_batInfo.batStatus = EVENT_BAT_LOST;
    }
    else if(EVENT_BAT_PLUG == *batStatus)
    {
        g_batInfo.batStatus = EVENT_BAT_PLUG;
    }
    else
    {
       return;
    }

    BatChargeConditionJudgment();
    return; 
}

/*************************************************
函数名称: BatTransformTemperature
函数功能: 把ADC值转化为内置电池的温度
输入参数: ADC采样值
输出参数:   -40-150度的温度值
函数返回类型值：INT16
编写者: Jason
编写日期 :2017/12/01
*************************************************/
static int16_t BatTransformTemperature(uint16_t adcValue)
{
    int16_t tempMax = sizeof(g_mf5aTempTab)/sizeof(g_mf5aTempTab[0]) - 1;
    int16_t tempMin = 0;
    int16_t tempMid = 0;

    while(adcValue >= g_mf5aTempTab[tempMax])
    {
        tempMid = (tempMax+tempMin)/2;
        if((tempMid == tempMin) || (tempMid == tempMax))
        {
            //find result
            break;
        }

        if (adcValue >= g_mf5aTempTab[tempMid])
        {
            tempMax = tempMid;
        }
        else
        {
            tempMin = tempMid;
        }   
    }
    return tempMid - 40;
}

/*************************************************
函数名称: BatReadTemperature
函数功能: 读取内置电池的温度
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: Jason
编写日期 :2017/12/01
*************************************************/
int16_t BatReadTemperature(void)
{
    return BatTransformTemperature(g_commonInfo.batTempAdcValue);
}

/*************************************************
函数名称: BatReadVoltage
函数功能: 读取内置电池的电压
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: Jason
编写日期 :2017/12/04
*************************************************/
uint16_t BatReadVoltage(void)
{
    uint16_t  VoltageValue = 0;

    /*20220712,zxl,电池电压采用mv单位,在转换值的基础上,额外增加20mV电压*/
    VoltageValue = ((34700/4096)*g_commonInfo.batVoltAdcValue*2)*0.1057;
    return VoltageValue;
}

/*************************************************
函数名称: BatSohCheck
函数功能: 内置电池电池寿命检查
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static SohStatus BatSohCheck(void)
{
    if(0 == g_nvTboxSelfConfigData.batPara.SohCount)
    {
        return EVENT_SOH_NOT_EXPIRED;
    }
    else if(BAT_SOH_MAX_COUNT > g_nvTboxSelfConfigData.batPara.SohCount)
    {
        return EVENT_SOH_WARNING;
    }
    else
    {
        return EVENT_SOH_EXPIRED;  
    }
}

/*************************************************
函数名称: BatSohHandle
函数功能: 内置电池电池寿命处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatSohHandle(void)
{
    char sohStr[3][16] = {"Not Expired", "Warning", "Expired"};
    SohStatus sohStatus = BatSohCheck();

    switch(sohStatus)
    {
        case EVENT_SOH_NOT_EXPIRED:
        {
            //发送给CAN任务报电池寿命未到期
            break;
        }
        case EVENT_SOH_WARNING:
        {
            //发送给CAN任务报电池寿命到期警告
            break;
        }
        case EVENT_SOH_EXPIRED:
        {
            //发送给CAN任务报电池寿命到期故障
            break;
        }
        default:
        {
            break;
        }
    }
    
    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "bat soh notify %s\r\n", sohStr[sohStatus]);
    return;
}

/*************************************************
函数名称: BatChargeStartHandle
函数功能: 内置电池充电前处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeStartHandle(void)
{
    int16_t batTemp = BatReadTemperature();

    //判断充电前温度
    if((CHARGE_START_MIN_TEMP <= batTemp) && (CHARGE_START_MAX_TEMP >= batTemp))
    {
        g_batInfo.chargeStatus = EVENT_CHARGE_FIRST;
        // 开始充电只在此次控制
        g_batInfo.periodTime = 0;
        BatChargeControl(CONTROL_ON);
        #if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat charge start >> temp: %d\r\n", batTemp);
        #endif
    }
    else
    {
        g_batInfo.chargeStatus = EVENT_CHARGE_IDLE;
        BatChargeControl(CONTROL_OFF);
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "bat charge start error >> temp: %d\r\n", batTemp);
        #endif
    }
}

/*************************************************
函数名称: BatChargeStartPeriodHandle
函数功能: 内置电池充电前查询温度周期处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeStartPeriodHandle(void)
{
    // 充电前状态检测
    if((g_batInfo.periodTime++ % CHARGE_START_TIME_COUNT) == 0)
    {
        g_batInfo.periodTime = 0;
        BatChargeStartHandle();
    }
}

/*************************************************
函数名称: BatChargeFirstStepHandle
函数功能: 内置电池普通充电处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeFirstStepHandle(void)
{
    // 每2分钟检测一次充电中的温度和电压
    if(CHARGE_NORAML_TIME_COUNT <= g_batInfo.periodTime++)
    {
        g_batInfo.periodTime = 0;
        if(!BatChargingTempCheck())
        {
            g_batInfo.chargeStatus = EVENT_CHARGE_IDLE;
            BatChargeControl(CONTROL_OFF);
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat first charge off that temp is abnormal\r\n");
            #endif
        }
    }

    return;
}

/*************************************************
函数名称: BatChargeSecondStepHandle
函数功能: 内置电池第二阶段充电处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeSecondStepHandle(void)
{
    g_batInfo.periodTime++;
    g_batInfo.secondChargeTime++;

    // 每2分钟检测一次充电中的温度和电压
    if(CHARGE_SECOND_TIME_COUNT < g_batInfo.periodTime)
    {
        uint16_t nowBatVoltage = BatReadVoltage();
        g_batInfo.periodTime = 0;
        if(!BatChargingTempCheck())
        {
            g_batInfo.chargeStatus = EVENT_CHARGE_IDLE;
            BatChargeControl(CONTROL_OFF);
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat second charge off that temp is abnormal\r\n");
            #endif
        }
        // 记录充电过程中的最大电压
        if(nowBatVoltage >= g_batInfo.batMaxVoltage)
        {
            g_batInfo.batMaxVoltage = nowBatVoltage;
        }
    }
    // 充电时间到，更新电池最大电压,关闭充电
    if(SECOND_MAX_CHARGE_TIME_COUNT <= g_batInfo.secondChargeTime)
    {
        UpdateBatMaxVoltage();
        g_batInfo.chargeStatus = EVENT_CHARGE_FULL;
        g_batInfo.periodTime = 0;
        BatChargeControl(CONTROL_OFF);
        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat second charge finished. stop charge\r\n");
        #endif
    }
}

/*************************************************
函数名称: BatChargeFullHandle
函数功能: 内置电池充满电处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/03
*************************************************/
static void BatChargeFullHandle(void)
{
    g_batInfo.periodTime++;
    if(CHARGE_FULL_TIME_COUNT <= g_batInfo.periodTime)
    {
        g_batInfo.periodTime = 0;
        uint16_t nowBatVoltage = BatReadVoltage();
        if(nowBatVoltage < BAT_POWER_LOW_VALUE)
        {
            g_batInfo.chargeStatus = EVENT_CHARGE_IDLE;
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "bat volt low, maybe should restart charge >> volt: %dmV\r\n", nowBatVoltage);
            #endif
        }
    }
}

/*************************************************
函数名称: BatPeriodNotifyFunction
函数功能: 
输入参数: 周期消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void BatPeriodNotifyFunction(Msg msg)
{
    McuScheduleTimeOutReInit(TASK_ID_BAT, 0x00);
    // 前置充电条件判断，高优先级
    BatChargeConditionJudgment();
    switch(g_batInfo.chargeStatus)
    {
        case EVENT_CHARGE_START:
        {
            BatChargeStartHandle();
            break;
        }
        case EVENT_CHARGE_FIRST:
        {
            BatChargeFirstStepHandle();
            break;
        }
        default:
            break;
    }
    TriggerADCSampling();
}

/*************************************************
函数名称: CanEventFunction
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void BatEventFunction(Msg msg)
{
    uint8_t index = 0;

    for(index = 0; index < (sizeof(g_batEventFunctionMap)/sizeof(g_batEventFunctionMap[0])); index++)
    {
        if(g_batEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_batEventFunctionMap[index].TaskFunctionHook)
            {
                g_batEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}