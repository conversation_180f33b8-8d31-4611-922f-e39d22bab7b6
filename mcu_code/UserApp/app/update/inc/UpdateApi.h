/*
      UpdateTask.h
描述：定义Update具体业务功能，
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _UPDATE_API_H_
#define  _UPDATE_API_H_


#include "event.h"
#include "status.h"
#include "NvApi.h"





/************************宏定义***************************/
#define   REMOTE_UPDATE_DOWNLOAD_LEN                           6
#define   BPLUS_ADC_10V_VALUE                                  10

/************************数据结构定义***************************/
#pragma pack(1)
typedef struct remoteUpdateMessage
{
    uint8_t        updateType;     
    uint16_t       updateNumber;
    uint8_t        ecuId;
    uint8_t        fileId;
    uint8_t        updateStatus;  
}RemoteUpdateMessage;
#pragma pack()


typedef struct updateInfo
{
    UpdateStatus status;
    uint8_t        count;
} UpdateInfo;





/************************函数接口***************************/
void McuSoftReset(void);
void UpdateEventFunction(Msg msg);
void UpdatePeriodNotifyFunction(void);
status_t RefreshUpgradeStatus(uint32_t address,uint32_t data);
#endif

