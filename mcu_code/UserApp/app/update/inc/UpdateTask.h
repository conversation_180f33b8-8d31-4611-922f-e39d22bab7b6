#pragma once
#ifndef _UPDATE_TASK_H_
#define _UPDATE_TASK_H_

#include "AppTask.h"
#include "UpdateApi.h"
#include "task.h"

#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define UPDATE_TASK_STACK_SIZE 384
#define UPDATE_TASK_PRIORITY   5

extern TaskHandle_t UPDATETask_Handle;

void StartUpdateTask(void);
void UpdateTask(void* param);

/************************函数接口***************************/
void UpdateTaskInitHook(void);
void UpdateTaskPostInitHook(void);
void UpdateTaskPeriodHook(void);
void UpdateTaskFunctionHook(Msg msg);

#endif