/*
      UpdateTask.c
描述：此头文件主要定义Update任务函数包括任务初始化函数、任务第二次初始化函数、任务周期性执行函数、任务主功能执行函数。
      任务初始化函数：任务第一次运行对任务的一些初始化操作。
      任务第二次初始化函数：等待其他任务第一次初始化执行完成以后，任务进行第二次执行的函数。
      任务周期性执行函数：任务周期性执行的回调函数。
      任务主功能函数：任务实时运行的回调函数。
作者：廖勇刚
时间：2016.7.5
*/

#include "UpdateTask.h"



/************************外部全局变量****************************/
extern UpdateInfo  g_updateInfo;
extern CommonInfo  g_commonInfo;








/*************************************************
函数名称: UpdateTaskInitHook
函数功能: 升级任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskInitHook(void)
{
    g_updateInfo.status = UPDATE_STATUS_END;
    g_updateInfo.count  = 0;
    g_commonInfo.updateConditionStatus = UPDATE_CONDITION_MEET;
}

/*************************************************
函数名称: UpdateTaskPostInitHook
函数功能: 升级任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskPostInitHook(void)
{

}

/*************************************************
函数名称: UpdateTaskPeriodHook
函数功能: 升级任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskPeriodHook(void)
{
    Msg msg;
    
    msg.event  = EVENT_ID_UPDATA_PERIOD_NOTIFY;
    msg.len    = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_UPDATE, msg);
}

/*************************************************
函数名称: UpdateTaskFunctionHook
函数功能: 升级任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskFunctionHook(Msg msg)
{
    UpdateEventFunction(msg);
}

