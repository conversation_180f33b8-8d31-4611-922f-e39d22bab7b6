/*
      UpdateApi.c
描述：此文件主要是Update任务具体实现的业务功能
作者：廖勇刚
时间：2016.7.5
*/
#include <string.h>
#include "AppTask.h"
#include "UpdateApi.h"
#include "LogApi.h"
#include "LedApi.h"
#include "IpcApi.h"
#include "NvApi.h"
#include "PmApi.h"
#include "BtApi.h"
#include "flash_driver.h"
#include "interrupt_manager.h"
#include "pwm.h"

/************************外部全局变量****************************/
extern CommonInfo            g_commonInfo;
extern GpioInfo              g_gpioPowerOnInfoList[];
extern CarInfo               g_carInfo;
extern StaticDidInfo         g_staticDid;
extern ModulePowerStatus     g_powerModuleStatus;
extern PmInfo                g_pmInfo;
extern GpioControlInfoStruct g_GpioControlInfoStruct[GPIO_CONTROL_ENUM_MAX];

/************************函数接口***************************/

static void UpdateRemoteDownloadFunction(Msg msg);
//static void UpdateRxSystemSupplierIdentifier(Msg msg);
//static void UpdateRxTboxSparePartNumber(Msg msg);
//static void UpdateRxTboxId(Msg msg);
//static void UpdateRxSimNumber(Msg msg);

/************************全局变量****************************/
UpdateInfo  g_updateInfo;
EventInfo g_updateEventFunctionMap[] = 
{
//    {EVENT_ID_UPDATA_PERIOD_NOTIFY,  UpdatePeriodNotifyFunction},
    {EVENT_ID_REMOTE_UPDATE_NOFIFY,  UpdateRemoteDownloadFunction},
//    {EVENT_ID_RX_SUPPLIER_ID,        UpdateRxSystemSupplierIdentifier},
//    {EVENT_ID_RX_SPAREPARTNUMBER,    UpdateRxTboxSparePartNumber},
//    {EVENT_ID_RX_TBOX_ID,            UpdateRxTboxId},
//    {EVENT_ID_RX_SIM_NUMBER,         UpdateRxSimNumber},
};




/*************************************************
函数名称: McuSoftReset
函数功能: 执行MCU软复位功能
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/30
*************************************************/
void McuSoftReset(void)
{
    UpdateStatus status = g_updateInfo.status;
    char updateStatus[UPDATE_STATUS_MAX][32] = {
        "UPDATE_START",
        "UPDATE_END",
        "UPDATE_HARD_RESET",
        "UPDATE_WRITE_FINGER",
        "UPDATE_CHECK_DEPENDENCY",
        "UPDATE_SOFT_RESET",
    };
    if(status >= UPDATE_STATUS_MAX || status < UPDATE_STATUS_START)
    {
        status = UPDATE_STATUS_SOFT_RESET;
    }
    SystemApiLogPrintf(LOG_INFO_OUTPUT, " =================================================\r\n");
    SystemApiLogPrintf(LOG_INFO_OUTPUT, " System Will Go To Soft Reset Because %s\r\n", updateStatus[status]);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, " =================================================\r\n");
    SystemSoftwareReset();
}




/*
    刷写存储在flash中的升级状态标志位
    写入0，相对于boot来说，表示app升级成功，下次preapp启动时读取为0，则自动引导app
    写入3，表示表示告诉boot，启动的时候需要进入更新状态，接收来自模组的固件更新
*/
status_t RefreshUpgradeStatus(uint32_t address,uint32_t data)
{
    uint32_t i = 10;
    status_t status = STATUS_ERROR;
    uint32_t UpgradeState[4] = {data,data,0,0};
    uint32_t readUpdateStatus = 0xFF;

    if (USER_SOFTRESET_COUNT_ADDR == address)
    {
        UpgradeState[1] = ReadValueOfFlashAddress(WAKEUP_SOURCE_ADDR);
        UpgradeState[2] = ReadValueOfFlashAddress(BOOT_VERSION_ADDR);
    }
    do{
        EraseStorageRegion(address);
        status = WriteDataToFlash(address,UpgradeState,16);
        readUpdateStatus = ReadValueOfFlashAddress(address);                                          //读出来检查
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Refresh Addr 0x%X to %d %s\r\n", address, data, data == readUpdateStatus ? "Success" : "Failed");

    }while((readUpdateStatus != data)&&(i--));

    return status;

}


/*************************************************
函数名称: UpdateRxSystemSupplierIdentifier
函数功能: 保存接收的供应商ID
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/07/20
*************************************************/
//static void UpdateRxSystemSupplierIdentifier(Msg msg)
//{
//    CarInfo  carInfo;
//    uint8_t *para = NULL;
//
//    para = (uint8_t *)msg.lparam;
//    memcpy((uint8*)&carInfo,(uint8*)&g_carInfo, sizeof(CarInfo));
//
//    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update rx Supplier id\r\n");
//    #endif
//    if(0 == memcmp(para,carInfo.systemSupplierIdentifier,TBOX_SYSTEM_SUPPIER_IDENTIFIER_NUM))
//    {
//        return;
//    }
//    else
//    {
//        NvErrorCode errorCode = NV_NO_ERROR;
//        memcpy(carInfo.systemSupplierIdentifier,para,TBOX_SYSTEM_SUPPIER_IDENTIFIER_NUM);
//        errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8_t *)&carInfo, sizeof(CarInfo));
//        if(NV_NO_ERROR != errorCode)
//        {
//            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Update rx Supplier id into NV failed:%d\r\n", errorCode);
//        }
//        return;
//    }
//}

///*************************************************
//函数名称: UpdateRxTboxSparePartNumber
//函数功能: 保存接收的T-Box零件号
//输入参数: 无
//输出参数: 无
//函数返回类型值：无
//编写者: zxl
//编写日期 :2022/03/12
//*************************************************/
//static void UpdateRxTboxSparePartNumber(Msg msg)
//{
//    CarInfo  carInfo;
//    uint8_t *para = NULL;
//    NvErrorCode errorCode = NV_NO_ERROR;
//
//    para = (uint8_t *)msg.lparam;
//    if(para == NULL)
//    {
//        return;
//    }
//
//    memcpy((uint8*)&carInfo,  (uint8*)&g_carInfo, sizeof(CarInfo));
//    memcpy(carInfo.tboxSparePartNumber , para, TBOX_SPARE_PARTNUMBERMAX);
//    errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8_t *)&carInfo, sizeof(CarInfo));
//    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update spare part number into NV status:%d\r\n", errorCode);
//}

///*************************************************
//函数名称: UpdateRxTboxId
//函数功能: 保存接收的TBOX Id
//输入参数: 无
//输出参数: 无
//函数返回类型值：无
//编写者: liaoyonggang
//编写日期 :2017/07/20
//*************************************************/
//static void UpdateRxTboxId(Msg msg)
//{
//    CarInfo  carInfo;
//    uint8_t *para = NULL;
//    para = (uint8_t *)msg.lparam;
//
//    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update rx tbox id\r\n");
//    #endif
//
//    memcpy((uint8*)&carInfo, (uint8*)&g_carInfo, sizeof(CarInfo));
//    if(0 == memcmp(para,carInfo.tboxId,TBOX_ID))
//    {
//       return;
//    }
//    else
//    {
//       NvErrorCode errorCode = NV_NO_ERROR;
//       memcpy(carInfo.tboxId,para,TBOX_ID);
//       errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8_t *)&carInfo, sizeof(CarInfo));
//       if(NV_NO_ERROR != errorCode)
//       {
//            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Update rx tbox id into NV failed:%d\r\n", errorCode);
//       }
//       return;
//    }
//}

///*************************************************
//函数名称: UpdateRxSimNumber
//函数功能: 保存接收的sim 卡号
//输入参数: 无
//输出参数: 无
//函数返回类型值：无
//编写者: liaoyonggang
//编写日期 :2017/07/20
//*************************************************/
//static void UpdateRxSimNumber(Msg msg)
//{
//    CarInfo carInfo = {0};
//    uint8_t *para = NULL;
//
//    para = (uint8_t *)msg.lparam;
//
//    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update rx sim number\r\n");
//    #endif
//
//    memcpy((uint8*)&carInfo,  (uint8*)&g_carInfo, sizeof(CarInfo));
//    if(0 == memcmp(para, carInfo.simNumber, TBOX_SIM_NUMBER))
//    {
//       return;
//    }
//    else
//    {
//       NvErrorCode errorCode = NV_NO_ERROR;
//       memcpy(carInfo.simNumber, para, TBOX_SIM_NUMBER);
//       errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8_t *)&carInfo, sizeof(CarInfo));
//       if(NV_NO_ERROR != errorCode)
//       {
//           SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Update rx sim number into NV failed:%d\r\n", errorCode);
//       }
//       return;
//    }
//}

/*************************************************
函数名称: UpdateRestartConfig
函数功能: MCU重启配置函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/11/01
*************************************************/
static void UpdateRestartConfig(void)
{
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_3V3_PWR_SW], GPIO_OUTPUT_LOW);

    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BAT_CHARGE_EN], GPIO_OUTPUT_LOW);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_RST_CTL], GPIO_OUTPUT_LOW);

    for(int index = GPIO_CONTROL_CAN_RED_LED; index < GPIO_CONTROL_ENUM_MAX; index++)
    {
        GpioLedControlUpdateStatusFunction(index,GPIO_STATUS_OFF, 0, 0);
    }
}

/*************************************************
函数名称: UpdatePeriodExecFunction
函数功能: 
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/16
*************************************************/
void UpdatePeriodNotifyFunction(void)
{   
    Msg msg2;
    
    //McuScheduleTimeOutReInit(TASK_ID_UPDATE, 0x00);
    if(UPDATE_STATUS_START == g_updateInfo.status)
    {
        g_updateInfo.count++;
        if(1 == g_updateInfo.count)
        {
            g_pmInfo.workStatus = PM_STATUS_MCU_SOFTRESET;
            msg2.event = MESSAGE_TX_PM_REQUEST;
            msg2.len   = 1;
            msg2.lparam = (uint32_t)&g_pmInfo.workStatus;
            SystemSendMessage(TASK_ID_IPC, msg2);
            BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
        }
        else if(4 == g_updateInfo.count)
        {
            UpdateRestartConfig();
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update download before mcu reset\r\n");
            #endif

             GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_HIGH);
             GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_HIGH);
            // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_RLIN21_SLP_N], GPIO_OUTPUT_LOW);
            // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_HIGH);
            // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN5_STB], GPIO_OUTPUT_HIGH);
            McuSoftReset();
        }
    }
    else if(UPDATE_STATUS_SOFT_RESET == g_updateInfo.status)
    {
        UpdateRestartConfig();
        McuSoftReset();
    }
}

/*************************************************
函数名称: UpdateRemoteDownloadFunction
函数功能: 远程升级下载消息处理
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/30
*************************************************/
static void UpdateRemoteDownloadFunction(Msg msg)
{
    RemoteUpdateMessage *remoteUpdateMessage = NULL;
    uint8_t tempBuf[4];

    if((REMOTE_UPDATE_DOWNLOAD_LEN != msg.len) ||(NULL == (void *)msg.lparam))
    {   
        #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "UpdateRemoteDownloadFunction error\r\n");
        #endif
        
        return;
    }

    remoteUpdateMessage = (RemoteUpdateMessage *)msg.lparam;
    
    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "update Type is %d, update Status is %d\r\n", remoteUpdateMessage->updateType,remoteUpdateMessage->updateStatus);
    #endif
    
    if(UPDATE_STATUS_START == remoteUpdateMessage->updateStatus)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "remote update mcu condition is %d\r\n", g_commonInfo.updateConditionStatus);
        
        //升级条件不满足
        if(UPDATE_CONDITION_NOT_MEET == g_commonInfo.updateConditionStatus)
        {
            tempBuf[0] = 0;
            tempBuf[1] = UPDATE_TYPE_MCU;
            tempBuf[2] = remoteUpdateMessage->ecuId;
            tempBuf[3] = remoteUpdateMessage->fileId;
            
            msg.event = MESSAGE_TX_REMOTE_UPGRADE_RESPONSE;
            msg.len   = 4;
            msg.lparam = (uint32_t)&tempBuf[0];
            SystemSendMessage(TASK_ID_IPC, msg);
            return;
        }
        else
        {
            if(STATUS_SUCCESS != RefreshUpgradeStatus(USER_UPGRADE_STATE_ADDR,3))
            {
                tempBuf[0] = 5;
                tempBuf[1] = UPDATE_TYPE_MCU;
                tempBuf[2] = remoteUpdateMessage->ecuId;
                tempBuf[3] = remoteUpdateMessage->fileId;
            
                msg.event = MESSAGE_TX_REMOTE_UPGRADE_RESPONSE;
                msg.len   = 4;
                msg.lparam = (uint32_t)&tempBuf[0];
                SystemSendMessage(TASK_ID_IPC, msg);
                #if(LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "remote update write status fail\r\n");
                #endif
                return;
            }
            else
            {
                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "remote update write status success\r\n");
                #endif
                g_updateInfo.status = UPDATE_STATUS_START;
                g_commonInfo.tspStatus = TSP_STATUS_ACTIVE;
                return;
            }
        }
    }
}

/*************************************************
函数名称: CanEventFunction
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void UpdateEventFunction(Msg msg)
{
    uint8_t index = 0;

    for(index = 0; index < (sizeof(g_updateEventFunctionMap)/sizeof(g_updateEventFunctionMap[0])); index++)
    {
        if(g_updateEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_updateEventFunctionMap[index].TaskFunctionHook)
            {
                g_updateEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

