/*
      DiagApi.c
描述：此文件主要是诊断，按键和IO检测任务具体实现的业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/
#include <stdlib.h>
#include <string.h>
#include "DiagApi.h"
#include "NvApi.h"
#include "PmApi.h"
#include "BatApi.h"
#include "CanFtm.h" 
#include "UpdateApi.h"
#include "BtApi.h"
#include "airbag.h"
#include "LogApi.h"
#include "event.h"
#include "gpio.h"
#include "CanMsgApi.h"
#include "Can_Cfg.h"

/************************外部全局变量****************************/
extern CommonInfo       g_commonInfo;
extern GpioInfo         g_gpioPowerOnInfoList[];
extern StaticDidInfo    g_staticDid;
extern uint32_t         g_osCurrentTickTime;
extern UpdateInfo       g_updateInfo;
extern EnsInformation_t *EnsInformationRead(void);
extern void TboxSendEnsSignalStatus(EnsSignalStatus siganlSta,EnsCanStatus canSta,uint8_t *canInfo);
extern BatInfo          g_batInfo;

/************************函数接口***************************/
static void DiagDtcReqNotifyCallBackFunction(Msg msg);
static void DiagMicOpenShortCirctSelfCheckNotify(Msg msg);
static void DiagHandShakeNotifyFunction(Msg msg);
static KeyStatus DiagEcallStatus(void);
static KeyStatus DiagBcallStatus(void);

/************************全局变量****************************/
uint8   g_busOffDtc[CAN_MAX_CONTROLLERS] = {0};
DiagInfo g_diagInfo;
static DTCRecordType  dtcDeviceTable[DTC_MAX_INDEX] = {{DTC_DEVICE_IDLE_STATUS,0x00,0x00}};
static BatteryBackGroundCheckStruct  g_batteryBackGroundCheckStruct[BATTERY_TYPE_MAX] = {{DTC_DEVICE_IDLE_STATUS,0x00,0x00}};
static uint8_t  g_dtcSyncToArmTimeCnt = 0x00;
static uint8_t  g_dtcSyncToArmFlag= 0x00;
static uint32_t dtcClientReqClearTimeStamp = 0x00;
uint8_t MainPowerExit_SleepWait = 0x00;   // 主电丢失时，正常工作12分钟再休眠

EventInfo g_diagEventFunctionMap[] = 
{
//    {EVENT_ID_DIAG_PERIOD_NOTIFY,      DiagPeriodNotifyFunction},
    {EVENT_ID_DIAG_DTC_EVENT,          DiagDtcReqNotifyCallBackFunction},
    {EVENT_ID_DIAG_MIC_EVENT,          DiagMicOpenShortCirctSelfCheckNotify}
};

/*************************************************
函数名称: DiagEcallHrDetect
函数功能: 检测ECALL按键硬件是否正常
          ECALL按键低于2.145且高于0.825判断有故障
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/20
*************************************************/
// static void DiagEcallHrDetect(void)
// {
//     //检测到ECALL有故障
//     if((ECALL_FAULT_HIGH_VALUE >= g_commonInfo.eCallAdcValue)&&
//        (ECALL_FAULT_LOW_VALUE <= g_commonInfo.eCallAdcValue))
//     {

//     }
//     else
//     {

//     }
// }

/*************************************************
函数名称: DiagEcallStatus
函数功能: 查询Ecall是否按下
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/9
*************************************************/
static KeyStatus DiagEcallStatus(void)
{
    //uint8_t eCallKeyStatus = PINS_DRV_ReadPins(SOS_KEY_INPUT_GPIO_PORT) & SOS_KEY_INPUT_GPIO_PIN;
    GpioLevel eCallKeyStatus = GPIO_OUTPUT_LOW;
    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_SOS_KEY_INPUT], &eCallKeyStatus);
    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "eCall key status is %d\r\n",eCallKeyStatus);
    if(!eCallKeyStatus)
    {
        return KEY_STATUS_PRESS;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "eCall key status is %d\r\n",eCallKeyStatus);
    }
    else if(eCallKeyStatus)
    {
        return KEY_STATUS_RELEASE;
    }
    else
    {
        return KEY_STATUS_FAULT;
    }
}


/*************************************************
函数名称: DiagBcallStatus
函数功能: 查询Bcall按键是否按下
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/11
*************************************************/
static KeyStatus DiagBcallStatus(void)
{
    //uint8_t eCallKeyStatus = PINS_DRV_ReadPins(SOS_KEY_INPUT_GPIO_PORT) & SOS_KEY_INPUT_GPIO_PIN;
    GpioLevel bCallKeyStatus = GPIO_OUTPUT_LOW;
    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_B_KEY_INPUT], &bCallKeyStatus);
    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "eCall key status is %d\r\n",eCallKeyStatus);
    if(!bCallKeyStatus)
    {
        return KEY_STATUS_PRESS;
    }
    else if(bCallKeyStatus)
    {
        return KEY_STATUS_RELEASE;
    }
    else
    {
        return KEY_STATUS_FAULT;
    }
}
/*************************************************
函数名称: DiagEcallNotifyArm
函数功能: ECALL按键通知ARM
输入参数: KeyEvent event
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/6/25
*************************************************/
static void DiagEcallNotifyArm(KeyEvent event)
{
    Msg msg;
    if(KEY_EVENT_SHORT_RELEASE == event)
    {
        if(ARM_CLINET_ONLINE == g_commonInfo.armClinetStatus)
        {        
#if  (ECALL_AUTO_TRIG_TEST_SWITCH == 1)
        EnsInformation_t *p_EnsInformation = EnsInformationRead();
#if   (ECALL_AIRBAG_SIGNAL_SIM == 1)  
//处理ECall硬线触发信号      
        p_EnsInformation->ensChangedStatus = ENS_NOTIFY_NOCHANGED;
        p_EnsInformation->ensSignalMode = ENS_SIGNAL_TRIG;
        p_EnsInformation->ensSignalStatus = ENS_SIGNAL_NORMAL;
#else   
        p_EnsInformation->ensChangedStatus = ENS_NOTIFY_NOCHANGED;
        p_EnsInformation->ensSignalMode = ENS_SIGNAL_NORMAL;
        p_EnsInformation->ensSignalStatus = ENS_SIGNAL_NORMAL;
#endif
#if   (ECALL_AIRBAG_CAN_SIM == 1)
//处理ECall CAN触发信号
        p_EnsInformation->ensCanMode = ENS_CAN_TRIG;
        memset(p_EnsInformation->ensCanBuf, 0, sizeof(p_EnsInformation->ensCanBuf));
#else 
        p_EnsInformation->ensCanMode = ENS_CAN_NORMAL;
        memset(p_EnsInformation->ensCanBuf, 0, sizeof(p_EnsInformation->ensCanBuf));
#endif
        
            TboxSendEnsSignalStatus(p_EnsInformation->ensSignalMode ,p_EnsInformation->ensCanMode, p_EnsInformation->ensCanBuf);
#endif
        }

    }
    else
    {
        msg.event = MESSAGE_TX_ECALL_REQUEST; 
        msg.len   = 1;
        msg.lparam = (uint32_t)&event;
        SystemSendMessage(TASK_ID_IPC, msg);
    }
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Ecall key notify arm event=%d\r\n", event);
    
}

/*************************************************
函数名称: DiagPowerOnInit
函数功能: 诊断任务开机计数初始化
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/02
*************************************************/
void DiagPowerOnInit(void)
{
    keyId id = KEY_ID_ECALL;

    for(id = KEY_ID_ECALL; id < KEY_ID_MAX; id++)
    {
        g_diagInfo.key[id].pressCount = 0;
    }
}

/*************************************************
函数名称: DiagXcallKeyQuery
函数功能: 查询XCALL按键是否按下
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/02
*************************************************/
DiagErrorCode DiagXcallKeyQuery(keyId id, KeyStatus *status)
{
    DiagErrorCode errorCode = DIAG_NO_ERROR;
    
    if((KEY_ID_MAX <= id)||(NULL == status))
    {
        errorCode = DIAG_INPUT_PARA_ERROR;
        return errorCode;
    }

    switch(id)
    {
        case KEY_ID_ECALL:
        {
            *status = DiagEcallStatus();
            break;
        }
        case KEY_ID_BCALL:
        {
            *status = DiagBcallStatus();
            break;
        }
        default:
            break;
    }
    
    return errorCode;
}

/*************************************************
函数名称: DiagKeyScan
函数功能: 
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/02
*************************************************/
static void DiagKeyScan(void)
{
    KeyStatus status = KEY_STATUS_RELEASE;
    keyId id = KEY_ID_ECALL;
    KeyEvent  event;

    static uint8_t sosTime = 0;

    if(KEY_LONG_TIME_COUNT > sosTime)
    {
        sosTime = KEY_LONG_TIME_COUNT;
    }
    
    for(id = KEY_ID_ECALL; id < KEY_ID_MAX; id++)
    {
        if(DIAG_NO_ERROR != DiagXcallKeyQuery(id, &status))
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "diag Xcall Query is error\r\n"); 
            continue;
        }

        //如果按键释放
        if(KEY_STATUS_RELEASE == status)
        {
            //if(KEY_LONG_TIME_COUNT < g_diagInfo.key[id].pressCount)
            if(sosTime < g_diagInfo.key[id].pressCount)
            {
               //发送长按释放
               if(KEY_ID_ECALL == id)
               {
                   event = KEY_EVENT_LONG_RELEASE;
                   // DiagEcallNotifyArm(event);
                   //to turn OFF ECall red light
                   GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_SOS_LED_RED], GPIO_OUTPUT_LOW);
                   SystemApiLogPrintf(LOG_INFO_OUTPUT,"ecall key long time press relased\r\n");
               }
               else if(KEY_ID_BCALL == id)
               {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,"BCall key long time press relased\r\n");
               }
            }
            //else if((KEY_SHORT_TIME_COUNT < g_diagInfo.key[id].pressCount)&&(KEY_LONG_TIME_COUNT >= g_diagInfo.key[id].pressCount))
            else if((KEY_SHORT_TIME_COUNT < g_diagInfo.key[id].pressCount)&&(sosTime >= g_diagInfo.key[id].pressCount))
            {      
               //发送短按释放
               if(KEY_ID_ECALL == id)
               {
                   event = KEY_EVENT_SHORT_RELEASE;
                   DiagEcallNotifyArm(event);
                   ////to turn OFF ECall red light
                   SystemApiLogPrintf(LOG_INFO_OUTPUT,"ecall key short time press relased\r\n");
               }
               else if(KEY_ID_BCALL == id)
               {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,"BCall key short time press relased\r\n");
               }
            }
            else
            {
               //无
            }
            g_diagInfo.key[id].pressCount = 0;
        }
        else if(KEY_STATUS_PRESS == status)
        {   
            g_diagInfo.key[id].pressCount++;
            if(KEY_SHORT_TIME_COUNT == g_diagInfo.key[id].pressCount)
            {
                //短按按下
                if(KEY_ID_ECALL == id)
                {
                    // event = KEY_EVENT_SHORT_PRESS;
                    // 为使模组改动较小，使用KEY_EVENT_LONG_PRESS传递ecall被按下的状态
                    //event = KEY_EVENT_LONG_PRESS;
                    //DiagEcallNotifyArm(event);
                    
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,"ecall key short time pressed\r\n");
                }
                else if(KEY_ID_BCALL == id)
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,"BCall key short time pressed\r\n");
                }
            }
            //else if(KEY_LONG_TIME_COUNT == g_diagInfo.key[id].pressCount)
            else if(sosTime == g_diagInfo.key[id].pressCount)
            {
                //长按按下
                if(KEY_ID_ECALL == id)
                {
                    event = KEY_EVENT_LONG_PRESS;
                    DiagEcallNotifyArm(event);
                    //to turn ON ECall red light
                    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_SOS_LED_RED], GPIO_OUTPUT_HIGH);
                    //GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_SOS_LED_GREEN], GPIO_OUTPUT_LOW);
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,"ecall key long time pressed and notified ARM.\r\n");
                }
                else if(KEY_ID_BCALL == id)
                {
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,"BCall key long time pressed\r\n");
                }
            }
            else
            {
                /* NULL */
            }
            if (sosTime+1 <= g_diagInfo.key[id].pressCount) {
                g_diagInfo.key[id].pressCount = sosTime+1;
            }
        }
        else
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "diag[%d] Xcall\r\n", id);  
        }
    }
}

/*************************************************
函数名称: DiagWriteDevicesStatus
函数功能: 
输入参数: 当前状态值
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/20
*************************************************/
void DiagWriteDevicesStatus(DtcIndexType dtcIndex, DtcStatus dtcStatus)
{
    Msg msg;
    uint8_t tempBuf[2];

    if (Dem_GetDtcRecordEnableState())
    {
        tempBuf[0] = dtcIndex;
        tempBuf[1] = dtcStatus;
        msg.event  = EVENT_ID_CAN_RX_DTC;
        msg.len    = 0x02;
        msg.lparam = (uint32_t)&tempBuf[0];
        SystemSendMessage(TASK_ID_CAN, msg);

        g_dtcSyncToArmTimeCnt = 0x00;
        g_dtcSyncToArmFlag = 0x01;
    }
}
/*************************************************
函数名称: DiagModuleFailureCheck
函数功能: 写入模组状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/25
*************************************************/
void DiagModuleFailureCheck(DTCDevicesStatus dtcStatus)
{
    if(dtcStatus != dtcDeviceTable[DTC_NETWORK_MODULE_FAILURE_INDEX].status)
    {
        dtcDeviceTable[DTC_NETWORK_MODULE_FAILURE_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
        DiagWriteDevicesStatus(DTC_NETWORK_MODULE_FAILURE_INDEX, DTC_STATUS_NO_FAULT);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Module Fault Check, OK\r\n");
    }
    if(dtcStatus != dtcDeviceTable[DTC_NETWORK_MODULE_FAILURE_INDEX].status)
    {
        dtcDeviceTable[DTC_NETWORK_MODULE_FAILURE_INDEX].status = DTC_DEVICE_ERROR_STATUS;
        DiagWriteDevicesStatus(DTC_NETWORK_MODULE_FAILURE_INDEX, DTC_STATUS_FAULT);
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Module Fault Check, Fault\r\n");
    }
}

/*************************************************
函数名称: DiagWriteBusOffDTC
函数功能: 记录故障码
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/02/09
*************************************************/
void DiagWriteBusOffDTC(void)
{
    DtcInfo* dtcInfo =  GetDtcInfo();

    if (GetCheckDtcEnable(MASK_VOL | MASK_ACC))
    {
        //当前有BUS OFF故障
        if (0x01 == g_busOffDtc[CAN_CHANNEL_1])
        {
            if (DTC_STATUS_NO_FAULT == (dtcInfo->dtc[DTC_CAN_BUS_OFF_INDEX].status & 0x01)) {
                DiagWriteDevicesStatus(DTC_CAN_BUS_OFF_INDEX, DTC_STATUS_FAULT);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "can %d bus off error\r\n", CAN_CHANNEL_1 + 1);
            }
        }
    }

    if(0x00 == g_busOffDtc[CAN_CHANNEL_1])
    {
        //记录无故障
        if(DTC_STATUS_FAULT == (dtcInfo->dtc[DTC_CAN_BUS_OFF_INDEX].status & 0x01))
        {
            DiagWriteDevicesStatus(DTC_CAN_BUS_OFF_INDEX, DTC_STATUS_NO_FAULT);
            SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "can  %d bus off recovery\r\n", CAN_CHANNEL_1 + 1);
        }
    }
}

/*************************************************
函数名称: DiagGnssAntennaShortGndSelfCheck
函数功能: 检测GNSS天线是否短路到地
          MCU P10_7输入电平为低短路到地 
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static int DiagGnssAntennaShortGndSelfCheck(void)
{
    GpioLevel level;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_SHORT], &level);
    if(GPIO_OUTPUT_LOW == level)
    {
        dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].normalStatusCnt = 0x00;
        dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].errorStatusCnt++;

        if(dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].errorStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].errorStatusCnt = 0x00;
            if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].status)
            {
                dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                DiagWriteDevicesStatus(DTC_GNSS_ANT_SHORT_INDEX, DTC_STATUS_FAULT);

                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Gnss Ant Short Circut\r\n");
                #endif
            }
        }
    }
    else
    {
        dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].errorStatusCnt = 0x00;
        dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].normalStatusCnt++;

        if(dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].normalStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].normalStatusCnt = 0x00;
            if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].status)
            {
                dtcDeviceTable[DTC_GNSS_ANT_SHORT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                DiagWriteDevicesStatus(DTC_GNSS_ANT_SHORT_INDEX, DTC_STATUS_NO_FAULT);

                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Gnss Ant Not Short Circut\r\n");
                #endif
            }
        }
    }
    return(level);
}


/*************************************************
函数名称: DiagGnssAntennaOpenCircutSelfCheck
函数功能: 检测GNSS天线是否开路
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static int DiagGnssAntennaOpenCircutSelfCheck(void)
{
    GpioLevel level;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_OPEN], &level);
    if(GPIO_OUTPUT_HIGH == level)
    {
        dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].normalStatusCnt = 0x00;
        dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].errorStatusCnt++;

        if(dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].errorStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].errorStatusCnt = 0x00;
            if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].status)
            {
                dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                DiagWriteDevicesStatus(DTC_GNSS_ANT_OPEN_INDEX, DTC_STATUS_FAULT);

                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Gnss Ant Open Circut.\r\n");
                #endif
            }
        }
    }
    else
    {
        dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].errorStatusCnt = 0x00;
        dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].normalStatusCnt++;

        if(dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].normalStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].normalStatusCnt = 0x00;
            if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].status)
            {
                dtcDeviceTable[DTC_GNSS_ANT_OPEN_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                DiagWriteDevicesStatus(DTC_GNSS_ANT_OPEN_INDEX, DTC_STATUS_NO_FAULT);

                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Gnss Ant Not Open Circut.\r\n");
                #endif
            }
        }
    }

    return 0;
}


/*************************************************
函数名称: DiagBackupBatterySelfCheck
函数功能: 检测电池故障信息
          电池NTC电压低于0.7V，即温度高于65度定义为电池温度高故障
          电池NTC电压高于2.78V，即温度低于-15度定义为电池温度低故障
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
int DiagBackupBatterySelfCheck(void)
{
    // int16_t value = 0;

    /*Backup Battery temperature exceed 80 or lower -30 degree Centigrade,Backup Battery crashd */
    // value = BatReadTemperature();
    // if((value > BAT_TEMP_HIGH_VALUE )||(value < BAT_TEMP_LOW_VALUE))
    // {
    //     dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].normalStatusCnt = 0x00;
    //     dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].errorStatusCnt++;

    //     if(dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].errorStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
    //     {
    //         dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].errorStatusCnt = 0x00;
    //         if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].status)
    //         {
    //             dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].status = DTC_DEVICE_ERROR_STATUS;

    //             #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //             SystemApiLogPrintf(LOG_INFO_OUTPUT, "Backup Bat failed,temp:%d\r\n",value);
    //             #endif
    //         }
    //     }
    // }
    // else
    // {
    //     dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].errorStatusCnt = 0x00;
    //     dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].normalStatusCnt++;

    //     if(dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].normalStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
    //     {
    //         dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].normalStatusCnt = 0x00;
    //         if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].status)
    //         {
    //             dtcDeviceTable[DTC_BAT_NOT_PRESENT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;

    //             #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //             SystemApiLogPrintf(LOG_INFO_OUTPUT, "Backup Bat OK,temp:%d\r\n",value);
    //             #endif
    //         }
    //     }
    // }
    return 0;
}

/*************************************************
函数名称: DiagBackupBatteryCheckVoltage
函数功能: 检测备用电压电压过低
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2023/01/09
*************************************************/
int DiagBackupBatteryCheckVoltage(void)
{
    // uint16_t voltage;
    // voltage = BatReadVoltage();

    // if(voltage < BACKUP_BATTERY_LOW_VOLTAGE_VALUE)    
    // {
    //     dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].normalStatusCnt = 0x00;
    //     dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].errorStatusCnt++;

    //     if(dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].errorStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
    //     {
    //         dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].errorStatusCnt = 0x00;
    //         if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].status)
    //         {
    //             dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].status = DTC_DEVICE_ERROR_STATUS;

    //             #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //             SystemApiLogPrintf(LOG_INFO_OUTPUT, "Backup Bat voltage failed,voltage:%d\r\n",voltage);
    //             #endif
    //         }
    //     }
    // }
    // else
    // {
    //     dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].errorStatusCnt = 0x00;
    //     dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].normalStatusCnt++;

    //     if(dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].normalStatusCnt >= HARDWARE_FAULT_CONFIRM_CNT)
    //     {
    //         dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].normalStatusCnt = 0x00;
    //         if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].status)
    //         {
    //             dtcDeviceTable[DTC_BAT_VOLAGAGE_LOWEST_INDEX].status = DTC_DEVICE_NORMAL_STATUS;

    //             #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //             SystemApiLogPrintf(LOG_INFO_OUTPUT, "Backup Bat voltage OK,voltage:%d\r\n",voltage);
    //             #endif
    //         }
    //     }
    // }
    return 0;        
}

/*************************************************
函数名称: DiagWriteEskCheck
函数功能: 检测写入ESK情况
          
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2023/01/09
*************************************************/
int DiagWriteEskCheck(void)
{
//     uint8_t readesk[TBOX_ESK_LEN] = {0x00};
//     uint8_t eskmaxk[TBOX_ESK_LEN] = {0x00};

//     memset(eskmaxk,0xFF,TBOX_ESK_LEN);
//     NvReadESK(readesk);
    
//     if(memcmp(readesk,eskmaxk,TBOX_ESK_LEN) == 0)
//     {
//         dtcDeviceTable[DTC_WRITE_ESK_FAILED_INDEX].status = DTC_DEVICE_ERROR_STATUS;

//         #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//         //SystemApiLogPrintf(LOG_INFO_OUTPUT, "tbox not write ESK before\r\n");
//         #endif
//     }
//     else
//     {
//         dtcDeviceTable[DTC_WRITE_ESK_FAILED_INDEX].status = DTC_DEVICE_NORMAL_STATUS;

//         #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//         //SystemApiLogPrintf(LOG_INFO_OUTPUT, "tbox wrote ESK before\r\n");
//         #endif
//     }
    return 0 ;
}

/*************************************************
函数名称: DiagWriteVinCheck
函数功能: 检测写入VIN情况
          
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2023/01/09
*************************************************/
int DiagWriteVinCheck(void)
{
    // uint8_t readvin[TBOX_VIN_NUM+1] = {0x00};
    // uint8_t vinmask[TBOX_VIN_NUM] = {0x00};

    // memset(vinmask,0xFF,TBOX_VIN_NUM);
    // NvReadVin(readvin);
    
    // if(memcmp(readvin,vinmask,TBOX_VIN_NUM) == 0)
    // {      
    //     dtcDeviceTable[DTC_WRITE_VIN_FAILED_INDEX].status = DTC_DEVICE_ERROR_STATUS;

    //     #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //     //SystemApiLogPrintf(LOG_INFO_OUTPUT, "tbox wrote Vin failed\r\n");
    //     #endif
    // }
    // else
    // {
    //     dtcDeviceTable[DTC_WRITE_VIN_FAILED_INDEX].status = DTC_DEVICE_NORMAL_STATUS;

    //     #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    //     //SystemApiLogPrintf(LOG_INFO_OUTPUT, "tbox wrote Vin success\r\n");
    //     #endif
    // }
    return 0;
}

/*************************************************
函数名称: DiagMainPowerVoltageSelfCheck
函数功能: 主电源电压周期检查
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/20
*************************************************/
int DiagMainPowerVoltageSelfCheck(void)
{
    uint16_t bPplusValue = ReadBpPlusValue();
    if(0 == bPplusValue)
    {
        return 0;
    }

    /*Main Power Voltae lower than 8v*/
    if(BPLUS_VOL_LOW_VALUE > bPplusValue)
    {
        dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].errorStatusCnt++;
        dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].normalStatusCnt = 0x00;
        if((dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].errorStatusCnt = 0x00;

            if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].status)
            {
                dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                DiagWriteDevicesStatus(DTC_EXT_POWER_VOL_LOW_INDEX, DTC_STATUS_FAULT);
                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power lower,vol:%d\r\n", bPplusValue);
                #endif
            }

            if(DTC_DEVICE_ERROR_STATUS == dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].status)
            {
                dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].status = DTC_DEVICE_IDLE_STATUS;
                DiagWriteDevicesStatus(DTC_EXT_POWER_VOL_HIGH_INDEX, DTC_STATUS_NO_FAULT);
                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power higher vanish,vol:%d\r\n", bPplusValue);
                #endif
            }
        }
    }
    /*Main Power Voltae higher  than 18v*/
    else if(BPLUS_VOL_HIGH_VALUE < bPplusValue)
    {
        dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].errorStatusCnt++;
        dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].normalStatusCnt = 0x00;
        if((dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].errorStatusCnt = 0x00;

            if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].status)
            {
                dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].status = DTC_DEVICE_ERROR_STATUS;

                DiagWriteDevicesStatus(DTC_EXT_POWER_VOL_HIGH_INDEX, DTC_STATUS_FAULT);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power higher,vol:%d\r\n", bPplusValue);

            }

            if(DTC_DEVICE_ERROR_STATUS == dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].status)
            {
                dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].status = DTC_DEVICE_IDLE_STATUS;
                DiagWriteDevicesStatus(DTC_EXT_POWER_VOL_LOW_INDEX, DTC_STATUS_NO_FAULT);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power lower vanish,vol:%d\r\n", bPplusValue);

            }
        }
    }
    else
    {

        dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].normalStatusCnt++;
        dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].errorStatusCnt = 0x00;
        if((dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].normalStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].normalStatusCnt = 0x00;

            if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].status && BPLUS_VOL_HIGH_RECOVER_VALUE > bPplusValue)
            {
                dtcDeviceTable[DTC_EXT_POWER_VOL_HIGH_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                DiagWriteDevicesStatus(DTC_EXT_POWER_VOL_HIGH_INDEX, DTC_STATUS_NO_FAULT);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power high Ok,vol:%d\r\n", bPplusValue);
            }
        }



        dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].normalStatusCnt++;
        dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].errorStatusCnt = 0x00;
        if((dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].normalStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].normalStatusCnt = 0x00;

            if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].status && BPLUS_VOL_LOW_RECOVER_VALUE < bPplusValue)
            {
                dtcDeviceTable[DTC_EXT_POWER_VOL_LOW_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                DiagWriteDevicesStatus(DTC_EXT_POWER_VOL_LOW_INDEX, DTC_STATUS_NO_FAULT);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power low  Ok,vol:%d\r\n", bPplusValue);
            }
        }
    }
    return 0;
}

/************************************************
函数名称: DiagBatDisconnectedSelfCheck
函数功能: 内置电池丢失Diag周期检查
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/01/14
*************************************************/
int DiagBatDisconnectedSelfCheck(void)
{
    if (g_batInfo.batStatus == EVENT_BAT_LOST)
    {
        dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].normalStatusCnt = 0x00;
        dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].errorStatusCnt++;

        if (dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].errorStatusCnt > 1)
        {
            dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].errorStatusCnt = 0x00;
            if (DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].status)
            {
                dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].status = DTC_DEVICE_ERROR_STATUS;
                DiagWriteDevicesStatus(DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX, DTC_STATUS_FAULT);

                #if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bat lost.\r\n");
                #endif
            }
        }
    }
    else
    {
        dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].errorStatusCnt = 0x00;
        dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].normalStatusCnt++;

        if (dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].normalStatusCnt > 1)
        {
            dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].normalStatusCnt = 0x00;
            if (DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].status)
            {
                dtcDeviceTable[DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
                DiagWriteDevicesStatus(DTC_INTERNAL_BATTERY_DISCONNECTED_INDEX, DTC_STATUS_NO_FAULT);

                #if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Bat recover.\r\n");
                #endif
            }
        }
    }
    return 0;
}

/************************************************
函数名称: DiagBatteyBackGroundCheckFunction
函数功能: 后台检测电池电压状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/11
*************************************************/
void DiagBatteyBackGroundCheckFunction(void)
{
    FtmInfo  *pFtmInfo = FtmInitRead();
    uint16_t bPplusValue = ReadBpPlusValue();
    if(BPLUS_VOL_LOW_CAN_OFF >= bPplusValue)
    {
        // Main Power Voltage Low Stop Charge Battery
        g_commonInfo.bplusVoltStatus = BPLUS_VOLT_STATUS_ABNORMAL;
        if(bPplusValue<BPLUS_VOL_LOSS_VAULE)
        {
            g_commonInfo.bplusStatus = BPLUS_STATUS_LOST;
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].errorStatusCnt++;
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].normalStatusCnt = 0x00;
            if((g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
            {
                g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].errorStatusCnt = 0x00;

                if(DTC_DEVICE_ERROR_STATUS != g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].status)
                {
                    /*Main Power Voltage Lowest or Lost, Wait Arm Hnad Shake and Notify Arm Got To Sleep*/
                    if(g_commonInfo.handshakeStatus == HANDSHAKE_SUCESS)
                    {
                        g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].status = DTC_DEVICE_ERROR_STATUS;

                        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power Lowest,shutdwon,vol:%d\r\n",bPplusValue);
                        #endif

                        // BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);

                        MainPowerExit_SleepWait = 0x01;
                        if(pFtmInfo->ftmMode == FTM_MODE_ENTER)
                        {
                            DiagPowerVoltageStatusSend(DIAG_SNED_PM_ENTER_POWER_OFF);
                            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Ftm End,Power Off\r\n");
                        }
                    }
                }
            }
            
            if((BAT_POWER_OFF_VALUE >= BatReadVoltage()))
            {
                g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].errorStatusCnt++;
                g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].normalStatusCnt = 0x00;
                if((g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
                {
                    g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].errorStatusCnt = 0x00;

                    if(DTC_DEVICE_ERROR_STATUS != g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].status)
                    {
                        /*backUp Battery Voltage Lowest or Lost, Wait Arm Hnad Shake and Notify Arm Got To ShutDown*/
                        if(g_commonInfo.handshakeStatus == HANDSHAKE_SUCESS)
                        {
                            g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].status = DTC_DEVICE_ERROR_STATUS;

                            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                            SystemApiLogPrintf(LOG_INFO_OUTPUT, "BckUp Battery Lowest or Lost,vol:%d\r\n",BatReadVoltage());
                            #endif

                            DiagPowerVoltageStatusSend(DIAG_SNED_PM_ENTER_POWER_OFF);
                            // BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
                        }
                    }
                }
            }
        }
        else
        {
            g_commonInfo.bplusStatus = BPLUS_STATUS_PLUG;
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].errorStatusCnt++;
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].normalStatusCnt = 0x00;
            if((g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
            {
                g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].errorStatusCnt = 0x00;

                if(DTC_DEVICE_ERROR_STATUS != g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].status)
                {
                    /*Main Power Voltage Lowest or Lost, Wait Arm Hnad Shake and Notify Arm Got To Sleep*/
                    if(g_commonInfo.handshakeStatus == HANDSHAKE_SUCESS)
                    {
                        g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].status = DTC_DEVICE_IDLE_STATUS;
                        g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_EXIT].errorStatusCnt = 0x00;

                        // DiagPowerVoltageStatusSend(DIAG_SNED_PM_ENTER_SLEEP);
                        // BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
                    }
                }
            }
        }

    }
    else
    {
        // Main Power Voltage Recoverry Continue Charge Battery
        g_commonInfo.bplusStatus = BPLUS_STATUS_PLUG;
        g_commonInfo.bplusVoltStatus = BPLUS_VOLT_STATUS_NORMAL;
        g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].normalStatusCnt++;
        g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].errorStatusCnt = 0x00;
        if((g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].normalStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
        {
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].normalStatusCnt = 0x00;

            if(DTC_DEVICE_ERROR_STATUS == g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].status)
            {
                g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].status = DTC_DEVICE_NORMAL_STATUS;
                // BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_OPEN_ADV);

                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power Lowest or Lost Recovery,vol:%d\r\n", bPplusValue);
                #endif
            }
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].errorStatusCnt = 0x00;
            g_batteryBackGroundCheckStruct[MAIN_POWER_BATTERY_LOWEST].normalStatusCnt = 0x00;
        }

        g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].errorStatusCnt = 0X00;
        g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].normalStatusCnt = 0x00;
        g_batteryBackGroundCheckStruct[BACKUP_BATTERY_LOWEST].status = DTC_DEVICE_IDLE_STATUS;

        MainPowerExit_SleepWait = 0x00;
    }
}

/************************************************
函数名称: DiagBatteyBackGroundSleepWait
函数功能: 外部电压丢失超过12分钟，进入休眠状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/11
*************************************************/
void DiagBatteyBackGroundSleepWait(void)
{
    static uint16_t timeTick = 0x00;

    if(0x01 == MainPowerExit_SleepWait)
    {
        timeTick++;
        if(timeTick == MAIN_POWER_BATTERY_EXIT_SHUT_TIME)
        {
            timeTick = 0;
            MainPowerExit_SleepWait = 0x00; // 允许进入休眠状态
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Main Power Lost, 12min sleep\r\n");
        }
    }
    else
    {
        timeTick = 0x00;
    }
}

/*************************************************
函数名称: DiagMicOpenShortCirctSelfCheckNotify
函数功能: mic 开短路自检，针对奇点 1.0.1的硬件平台，需ARM发起音频，MCU才能检测
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/07
*************************************************/
static void DiagMicOpenShortCirctSelfCheckNotify(Msg msg)
{

}

/*************************************************
函数名称: DiagMicOpenShortCirctSelfCheckPeriod
函数功能: 诊断mic ，周期自检，针对奇点1.0.2 硬件平台，对应安全芯片为信大捷安版本,不需ARM发起音频，MCU自主检测电压即可
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
// static int DiagMicOpenShortCirctSelfCheckPeriod(void)
// {
//     uint16_t micVoltage=0;

//     //大于2.3V开路；1.2V~1.7V为正常接入；短路为0
//     micVoltage = ReadMicValue();
//     if((micVoltage>=1200)&&(micVoltage<=1800))
//     {
//         dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].normalStatusCnt++;
//         dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].errorStatusCnt = 0x00;
//         if((dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].normalStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
//         {
//             dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].normalStatusCnt = 0x00;

//             if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].status)
//             {
//                 dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
//                 DiagWriteDevicesStatus(DTC_MIC_OPEN_CIRCUT_INDEX, DTC_STATUS_NO_FAULT);

//                 #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//                 SystemApiLogPrintf(LOG_INFO_OUTPUT, "Mic Open Circut Check ok,vol:%d\r\n",micVoltage);
//                 #endif
//             }
//         }

//         dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].normalStatusCnt++;
//         dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].errorStatusCnt = 0x00;
//         if((dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].normalStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
//         {
//             dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].normalStatusCnt = 0x00;

//             if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].status)
//             {
//                 dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
//                 DiagWriteDevicesStatus(DTC_MIC_SHORT_CIRCUT_INDEX, DTC_STATUS_NO_FAULT);

//                 #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//                 SystemApiLogPrintf(LOG_INFO_OUTPUT, "Mic Short Circut Check ok,vol:%d\r\n", micVoltage);
//                 #endif
//             }
//         }
//     }
//     else if(micVoltage>2300)
//     {
//         dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].errorStatusCnt++;
//         dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].normalStatusCnt = 0x00;
//         if((dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
//         {
//             dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].errorStatusCnt = 0x00;

//             if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].status)
//             {
//                 dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].status = DTC_DEVICE_ERROR_STATUS;
//                 DiagWriteDevicesStatus(DTC_MIC_OPEN_CIRCUT_INDEX, DTC_STATUS_FAULT);

//                 #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//                 SystemApiLogPrintf(LOG_INFO_OUTPUT, "Mic Open Circut,vol:%d\r\n", micVoltage);
//                 #endif
//             }

//             if(DTC_DEVICE_ERROR_STATUS == dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].status)
//             {
//                 dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
//                 DiagWriteDevicesStatus(DTC_MIC_SHORT_CIRCUT_INDEX, DTC_STATUS_NO_FAULT);
//             }
//         }
//     }
//     else if(micVoltage<500)
//     {
//         dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].errorStatusCnt++;
//         dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].normalStatusCnt = 0x00;
//         if((dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].errorStatusCnt) >= HARDWARE_FAULT_CONFIRM_CNT)
//         {
//             dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].errorStatusCnt = 0x00;

//             if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].status)
//             {
//                 dtcDeviceTable[DTC_MIC_SHORT_CIRCUT_INDEX].status = DTC_DEVICE_ERROR_STATUS;
//                 DiagWriteDevicesStatus(DTC_MIC_SHORT_CIRCUT_INDEX, DTC_STATUS_FAULT);

//                 #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
//                 SystemApiLogPrintf(LOG_INFO_OUTPUT, "Mic Short Circut,vol:%d\r\n", micVoltage);
//                 #endif
//             }

//             if(DTC_DEVICE_ERROR_STATUS == dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].status)
//             {
//                 dtcDeviceTable[DTC_MIC_OPEN_CIRCUT_INDEX].status = DTC_DEVICE_NORMAL_STATUS;
//                 DiagWriteDevicesStatus(DTC_MIC_OPEN_CIRCUT_INDEX, DTC_STATUS_NO_FAULT);
//             }
//         }
//     }

//     return 0;
// }

void DiagDtcDeviceTableInit(void)
{

    for(int i = 0; i < DTC_MAX_INDEX; i++)
    {
        dtcDeviceTable[i].status = DTC_DEVICE_IDLE_STATUS;
        dtcDeviceTable[i].normalStatusCnt = 0x00;
        dtcDeviceTable[i].errorStatusCnt = 0x00;
    }
}
/*************************************************
函数名称: DiagDtcReqSystemInit
函数功能: 更新设备状态到ARM
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
void DiagDtcReqSystemInit(void)
{   
    uint8_t i = 0x00;

    memset(dtcDeviceTable, 0x00, sizeof(dtcDeviceTable));
    for(i = 0x00; i<DTC_MAX_INDEX; i++)
    {
        dtcDeviceTable[i].status = DTC_DEVICE_IDLE_STATUS;
    }
    
    memset(g_batteryBackGroundCheckStruct, 0x00, sizeof(g_batteryBackGroundCheckStruct));
    for(i = 0x00; i<BATTERY_TYPE_MAX; i++)
    {
        g_batteryBackGroundCheckStruct[i].status = DTC_DEVICE_IDLE_STATUS;
    }

}

/*************************************************
函数名称: DiagDtcReqSystemClear
函数功能: 当ECU被诊断仪清除故障码后，ARM端也需要同步更新DTC,上电唤醒后，MCU也需要通知ARM更新一次设备状态
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
void DiagDtcReqSystemClear(void)
{
    NvClearDTC(DEM_DTC_GROUP_ALL_DTCS, DEM_DTC_KIND_ALL_DTCS, DEM_DTC_ORIGIN_PRIMARY_MEMORY);
    DiagDtcNotifyArmClearCheck();
}


/*************************************************
函数名称: DiagDtcNotifyArmStartCheck
函数功能: 开始检测设备，并写DTC
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/26
*************************************************/
void DiagDtcNotifyArmStartCheck(void)
{
    Msg msg = {0};
    uint8_t  bufTemp[10] = {0x00};

    bufTemp[0] = DTC_SYSTEM_REQ;
    bufTemp[1] = DTC_REQ_RESTART_CHECK;
    bufTemp[2] = 0x00;

    msg.event = MESSAGE_TX_TBOX_DTC_RESPONSE;
    msg.len = 3;
    msg.lparam = (uint32_t)&bufTemp[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: DiagDtcNotifyArmStopCheck
函数功能: 关闭arm Dtc检查
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/26
*************************************************/
void DiagDtcNotifyArmStopCheck(void)
{
    Msg msg = {0};
    uint8_t  bufTemp[10] = {0x00};

    bufTemp[0] = DTC_SYSTEM_REQ;
    bufTemp[1] = DTC_REQ_STOP_CHECK;
    bufTemp[2] = 0x00;

    msg.event = MESSAGE_TX_TBOX_DTC_RESPONSE;
    msg.len = 3;
    msg.lparam = (uint32_t)&bufTemp[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}
/*************************************************
函数名称: DiagDtcNotifyArmClearCheck
函数功能: 清除arm Dtc检查
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/26
*************************************************/
void DiagDtcNotifyArmClearCheck(void)
{
    Msg msg = {0};
    uint8_t  bufTemp[10] = {0x00};

    bufTemp[0] = DTC_SYSTEM_REQ;
    bufTemp[1] = DTC_REQ_CLEAR;
    bufTemp[2] = 0x00;

    msg.event = MESSAGE_TX_TBOX_DTC_RESPONSE;
    msg.len = 3;
    msg.lparam = (uint32_t)&bufTemp[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}


/*************************************************
函数名称: DiagDtcStatsSyncToArm
函数功能: MCU 的DTC状态同步到arm
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/26
*************************************************/
void DiagDtcStatsSyncToArm(void)
{
    Msg msg = {0};
    uint8_t  bufTemp[50] = {0x00};
    uint16_t len = 47;

    bufTemp[0] = DTC_SYSTEM_REQ;
    bufTemp[1] = DTC_REQ_SYNC;
    bufTemp[2] = DTC_READ_STATUS_MASK_01;

    Dem_GetAllDTCStatus(&bufTemp[3] ,&len,DTC_READ_STATUS_MASK_01);

    msg.event = MESSAGE_TX_TBOX_DTC_RESPONSE;
    msg.len = len + 3;
    msg.lparam = (uint32_t)&bufTemp[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}


/*************************************************
函数名称: DiagDtcReqNotifyCallBackFunction
函数功能: 诊断DTC 通知函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static void DiagDtcReqNotifyCallBackFunction(Msg msg)
{
    int ret = 0;
    int dtcReturnLen = 0;
    uint8_t bufTemp[200] = {0};
    uint16_t dtcCnt = 0x00;
    uint16_t dtcInfoAllBytelen = 0x00;

    Msg dtcRetMsg = {0};


    uint8_t *para = (uint8_t *)msg.lparam;

    /*Dtc Req Source, DtcReqSource*/
    DtcReqSource dtcReqSource =  para[0];
    DTCReqType dtcReqType = para[1];
    DTCReadStatusMask readStatusMask = para[2];

    switch(dtcReqType)
    {
        case DTC_REQ_RESTART_CHECK:
        {
            DiagDtcDeviceTableInit();
            //通知arm重新检查
            DiagDtcNotifyArmStartCheck();

            bufTemp[2] = DTC_REQ_SUCCESS_STATUS;
            dtcReturnLen = 3;

            dtcClientReqClearTimeStamp = g_osCurrentTickTime;
            break;
        }
        case DTC_REQ_CLEAR:
        {
            if (msg.len > 6)
            {
                uint32 groupOfDTC = para[3] << 24 | para[4] << 16 | para[5] << 8 | para[6];
                uint8 dtcType = para[7];
                uint8 memoryType = para[8];
                ret = NvClearDTC(groupOfDTC, dtcType, memoryType);
                //通知arm清除故障码
                DiagDtcNotifyArmClearCheck();
            }
            else
            {
                ret = -1;
            }
            bufTemp[2] = ret == 0 ?  DTC_REQ_SUCCESS_STATUS : DTC_REQ_FAIL_STATUS;
            dtcReturnLen = 3;
            dtcClientReqClearTimeStamp = g_osCurrentTickTime;
            break;
        }
        case DTC_REQ_READ:
        {
            dtcCnt = 0x00;
            bufTemp[2] = DTC_REQ_SUCCESS_STATUS;
            bufTemp[3] = DTC_AVAILABILITY_MASK_VALUE;
            bufTemp[4] = readStatusMask;
            dtcReturnLen = 7;

            ret = DTC_REQ_SUCCESS_STATUS;
            if(abs(g_osCurrentTickTime - dtcClientReqClearTimeStamp)<=5500)
            {
                ret = DTC_REQ_FAIL_STATUS;
            }

            if(DTC_REQ_SUCCESS_STATUS == ret)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "DTC Mask:0x%02x\r\n", readStatusMask);
                ret = Dem_GetAllDTC(&bufTemp[7],&dtcInfoAllBytelen, readStatusMask, &dtcCnt);
            }

            if(DTC_REQ_SUCCESS_STATUS == ret)
            {
                bufTemp[5] = (dtcCnt>>8)&0xff;
                bufTemp[6] = (dtcCnt>>0)&0xff;
                dtcReturnLen += dtcInfoAllBytelen;
            }
            else
            {
                bufTemp[2] = DTC_REQ_FAIL_STATUS;
            }
            break;
        }
        default:
            break;
    }
    if (dtcReqSource == DTC_CLIENT_REQ)
    {
        bufTemp[0] = para[0];
        bufTemp[1] = para[1];
        dtcRetMsg.event = MESSAGE_TX_TBOX_DTC_RESPONSE;
        dtcRetMsg.len = dtcReturnLen;
        dtcRetMsg.lparam = (uint32_t)&bufTemp[0];
        SystemSendMessage(TASK_ID_IPC, dtcRetMsg);
    }
}



/*************************************************
函数名称: DiagCodeStatuSelfCheck
函数功能: 检测某个节点是否丢失
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2022/07/01
*************************************************/
void DiagCodeStatuSelfCheck(uint8_t index)
{

    CanMsgAnalyCheckCodeIno * canMsgAnalyCodeInfo = (CanMsgAnalyCheckCodeIno * )CanMsgGetCodeStatusInfo();;

    /*节点报文对应的通道是否busOff*/
    if(g_busOffDtc[canMsgAnalyCodeInfo[index].busoffindex])
    {
        dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].errorStatusCnt = 0;
        return;
    }

    if(canMsgAnalyCodeInfo[index].code_online == true)
    {
        dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].errorStatusCnt = 0;
        canMsgAnalyCodeInfo[index].code_online = false;
        if(DTC_DEVICE_NORMAL_STATUS != dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].status)
        {
            dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].status = DTC_DEVICE_NORMAL_STATUS;
            DiagWriteDevicesStatus(canMsgAnalyCodeInfo[index].dtcindex, DTC_STATUS_NO_FAULT);
        }
    }
    else
    {
        dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].errorStatusCnt ++;
        if(dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].errorStatusCnt >= canMsgAnalyCodeInfo[index].timeoutcnt)
        {
            dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].errorStatusCnt = 0;
            if(DTC_DEVICE_ERROR_STATUS != dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].status)
            {
                dtcDeviceTable[canMsgAnalyCodeInfo[index].dtcindex].status = DTC_DEVICE_ERROR_STATUS;
                DiagWriteDevicesStatus(canMsgAnalyCodeInfo[index].dtcindex, DTC_STATUS_FAULT);
            }
        }
    }
}

/*************************************************
函数名称: DiagEcuCodeLostSelfCheck
函数功能: 轮询检查节点是否丢失
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2022/11/24
*************************************************/
void DiagEcuCodeLostSelfCheck()
{
    /*在远程诊断升级，禁止检测ECU节点丢失*/
    if(g_commonInfo.RemoteUpgradeFlag == Remote_UpGrade_Start)
    {
        return;
    }
    /*disable CAN RX*/
    if((g_commonInfo.udsCtrlNormPduStatus & 0x02) == 0x02)
    {
        return;
    }

    if (GetCheckDtcEnable(MASK_VOL | MASK_ACC) || GetCheckDtcEnable(MASK_VOL | MASK_LOW_POWER))
    {
        for(int index = CAN_MSG_RX_HECU_ID_INDEX; index < CAN_MSG_RX_MAX; index++)
        {
            DiagCodeStatuSelfCheck(index);
        }
    }
}


/*************************************************
函数名称: DiagPowerVoltageStatusSend
函数功能: 电压状态通知函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2017/02/16
*************************************************/
int DiagPowerVoltageStatusSend(DiagSendPmStatusEnum diagSendPmStatu)
{
    Msg msg;
    uint8_t  bufTemp[2] = {0x00};

    bufTemp[0] = diagSendPmStatu;
    bufTemp[1] = 0x00;

    msg.event = EVENT_ID_PM_BATTERY_NOTIFY;
    msg.len = 2;
    msg.lparam = (uint32_t)&bufTemp[0];
    SystemSendMessage(TASK_ID_PM, msg);

    return 0;
}

/*************************************************
函数名称: CheckAccEnableSystemReset
函数功能: 检测acc重启系统,当用户30S内反复上下电KL15达到5次时
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/17
*************************************************/
static void CheckAccEnableSystemReset(void)
{
    static WorkStatus lastAcc = WORK_STATUS_INACTIVE;
    static uint8_t accCount = 0;
    static uint32_t timeCount = 0;

    // 如果ACC从INACTIVE变为ACTIVE，增加上下电计数并重置计时
    if (lastAcc == WORK_STATUS_INACTIVE && g_commonInfo.accStatus == WORK_STATUS_ACTIVE)
    {
        accCount++;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "acc count %u in the 30s\r\n",accCount);
    }

    // 每周期递增timeCount，直到300个周期（30秒）
    if (accCount > 0)
    {
        timeCount++;
    }

    if (accCount >= 5 && timeCount <= 300)// 如果在30秒内上下电次数达到5次，触发软重置
    {
        g_updateInfo.status = UPDATE_STATUS_SOFT_RESET;
        accCount = 0;  // 触发重置后，重置计数器
        timeCount = 0; // 重置时间计数器
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "acc count 5 in the 30s, reset system\r\n");
    }

    // 如果30秒过去了，但未达到5次上下电，重置计数器
    if (timeCount > 300)
    {
        accCount = 0; // 重置计数器
        timeCount = 0; // 重新计时
    }

    // 更新上次ACC状态
    lastAcc = g_commonInfo.accStatus;
}

/*************************************************
函数名称: GetCheckDtcEnable
函数功能: 根据掩码获取DTC检查使能
输入参数: mask
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/13
*************************************************/
bool GetCheckDtcEnable(uint8_t mask)
{
    // 如果 stop 位为 1，直接返回 false，表示停止检测
    if (g_commonInfo.dtcEnable.bits.stop == 1)
    {
        return false;
    }

    // 通过掩码检查是否启用 vol、acc、lowPowerMode
    if ((g_commonInfo.dtcEnable.value & mask) == mask)
    {
        return true;
    }

    return false;
}

/*************************************************
函数名称: SetCheckDtcEnable
函数功能: 手动开关dtc检测
输入参数: status
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/02/06
*************************************************/
void SetCheckDtcEnable(bool start)
{
    g_commonInfo.dtcEnable.bits.stop = !start;
}

static uint8_t ProcessEnableCount(bool condition, uint8_t count, uint8_t threshold)
{
    if (condition)
    {
        if (count < threshold)
        {
            count++;
        }
    }
    else
    {
        count = 0; // 条件不满足时重置计数器
    }
    return count;
}
/*************************************************
函数名称: CheckDtcEnable
函数功能: 维持DTC检查使能
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/13
*************************************************/
static void CheckDtcEnable(void)
{
    static uint8_t accCount = 0; // 计数器，用于检测ACC持续时间
    static uint8_t lowPowerModeCount = 0; // 低功耗模式计数器
    static bool dtcEnableLast = false;

    bool volLDtcEr = GetDtcFaultStatus(DTC_EXT_POWER_VOL_LOW_INDEX);
    bool volHDtcEr = GetDtcFaultStatus(DTC_EXT_POWER_VOL_HIGH_INDEX);
    g_commonInfo.dtcEnable.bits.vol = (volLDtcEr || volHDtcEr) ? 0 : 1;

    // 处理ACC状态
    accCount = ProcessEnableCount(g_commonInfo.accStatus, accCount, 20);
    g_commonInfo.dtcEnable.bits.acc = (accCount >= 20) ? 1 : 0;

    // 处理低功耗模式状态
    DynamicDidInfo *dynamicDidInfo = GetDynamicDid();
    lowPowerModeCount = ProcessEnableCount(dynamicDidInfo->lowPowerMode == 0x02, lowPowerModeCount, 20);
    g_commonInfo.dtcEnable.bits.lowPowerMode = (lowPowerModeCount >= 20) ? 1 : 0;

    g_commonInfo.dtcEnable.bits.busOff = GetDtcFaultStatus(DTC_CAN_BUS_OFF_INDEX) ? 0 : 1;

    // 处理握手成功后的DTC使能检测
    if (HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
    {
        bool dtcEnable = GetCheckDtcEnable(MASK_VOL | MASK_ACC);

        if (dtcEnableLast != dtcEnable)
        {
            if (dtcEnable)
            {
                DiagDtcNotifyArmStartCheck();
            }
            else
            {
                DiagDtcNotifyArmStopCheck();
            }
        }
        dtcEnableLast = dtcEnable;
    }
}
/*************************************************
函数名称: CheckDtcClearHistory
函数功能: 检测DTC清除历史故障
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/13
*************************************************/
static void CheckDtcClearHistory(void)
{
    static WorkStatus lastAcc = WORK_STATUS_INACTIVE;
    NvErrorCode errorCode = 0;

    if (lastAcc == WORK_STATUS_INACTIVE && g_commonInfo.accStatus == WORK_STATUS_ACTIVE)
    {
        DtcInfo *dtcInfo = GetDtcInfo();
        for (int i = 0; i < DTC_MAX_INDEX; ++i)
        {
            if (dtcInfo->dtc[i].status == 0x08)
            {
                dtcInfo->dtc[i].accCount ++;
                NvApiWriteData(NV_ID_DTC_INFO + i, (uint8 *)&dtcInfo->dtc[i], sizeof(DtcType));
            }
            else
            {
                dtcInfo->dtc[i].accCount = 0;
            }

            if (dtcInfo->dtc[i].accCount >= 40)
            {
                memset(&dtcInfo->dtc[i],0x00, sizeof(DtcType));
                errorCode = NvApiWriteData(NV_ID_DTC_INFO + i, (uint8 *)&dtcInfo->dtc[i], sizeof(DtcType));
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Clear History DTC index:%d into NV ret = %d\r\n", i, errorCode);
            }
        }

    }
    lastAcc = g_commonInfo.accStatus;
}


/*************************************************
函数名称: DiagMainServicePeriodCheck
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/06
*************************************************/
static void DiagMainServicePeriodCheck(void)
{
    CheckDtcEnable();  //维持DTC检查使能
    CheckDtcClearHistory(); //检查是否需要清除历史故障

    CheckAccEnableSystemReset(); //检查acc触发重启

    /*CAN报文接收检测*/
    MCUCanCheckStatus();

    if (GetCheckDtcEnable(MASK_NULL))
    {
        DiagMainPowerVoltageSelfCheck();
    }

    if (GetCheckDtcEnable(MASK_VOL | MASK_ACC))
    {
        DiagGnssAntennaShortGndSelfCheck();
        DiagGnssAntennaOpenCircutSelfCheck();
        DiagBatDisconnectedSelfCheck();
    }

    DiagBatteyBackGroundCheckFunction();

    if(HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
    {

        g_dtcSyncToArmTimeCnt++;
        if(g_dtcSyncToArmTimeCnt >= DIAG_DTC_UPDATE_ARM_TIMER_EXPIER)
        {
            if(0x01 == g_dtcSyncToArmFlag)
            {
                g_dtcSyncToArmFlag = 0x00;
                DiagDtcStatsSyncToArm();
            }
        }
    }
}


/*************************************************
函数名称: DiagPeriodNotifyFunction
函数功能: 执行诊断周期性包括:按键扫描
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void DiagPeriodNotifyFunction(Msg msg)
{
    //Richard comments below ONE line of code
    //McuScheduleTimeOutReInit(TASK_ID_DIAG, 0x00);
    DiagMainServicePeriodCheck();

}


/*************************************************
函数名称: DiagEventFunction
函数功能: 诊断事件功能执行调度函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void DiagEventFunction(Msg msg)
{
    uint8_t index = 0;

    for(index = 0; index < (sizeof(g_diagEventFunctionMap)/sizeof(g_diagEventFunctionMap[0])); index++)
    {
        if(g_diagEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_diagEventFunctionMap[index].TaskFunctionHook)
            {
                g_diagEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

