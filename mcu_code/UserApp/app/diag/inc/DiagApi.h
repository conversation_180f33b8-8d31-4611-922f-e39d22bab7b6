/*
     DiagTask.h
描述：此文件主要是诊断，按键和IO检测任务具体实现的业务功能头文件，待实现
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _DIAG_API_H_
#define  _DIAG_API_H_

#include <stdbool.h>
#include "AppTask.h"
#include "Dem.h"

/************************宏定义***************************/
// #define KEY_SHORT_TIME_COUNT                5   // 500MS
#define KEY_SHORT_TIME_COUNT                5  // 500MS/TASK_SELFCHECK_PERIOD_TIME
// #define KEY_LONG_TIME_COUNT                 45  // 4500MS
#define KEY_LONG_TIME_COUNT                 20  // 2000MS/TASK_SELFCHECK_PERIOD_TIME
#define ECALL_AUTO_TRIG_TEST_SWITCH         1   //定义ECall测试代码的打开/关闭开关
#define ECALL_AIRBAG_SIGNAL_SIM             0
#define ECALL_AIRBAG_CAN_SIM                1

#define ECALL_FAULT_HIGH_VALUE              2662
#define ECALL_FAULT_LOW_VALUE               1014

#define KEY_PRESS_LOWER_LIMIT               0x0000      //对应0v
#define KEY_PRESS_UPPER_LIMIT               0x0620      //对应0.5v
#define KEY_RELEASE_LOWER_LIMIT             0x0d17      //对应2.7v
#define KEY_RELEASE_UPPER_LIMIT             0x0fff      //对应3.3v

/*hardware go wrong status continuely, than this fault shall be confirmed*/
#define HARDWARE_FAULT_CONFIRM_CNT          10          //5s/(TASK_SELFCHECK_PERIOD_TIME - 1)
#define MAIN_POWER_BATTERY_EXIT_SHUT_TIME   7200        //12m/(TASK_SELFCHECK_PERIOD_TIME - 1)
#define DIAG_DTC_UPDATE_ARM_TIMER_EXPIER    10           //After Write Dtc, There is no  devices Updata status more than 3sec, than update all code to arm
#define BACKUP_BATTERY_LOW_VOLTAGE_VALUE    3500        //3.5v

/************************数据结构定义***************************/
typedef enum
{
    KEY_ID_ECALL = 0,
    KEY_ID_BCALL,
    KEY_ID_MAX,
} keyId;

typedef enum
{
    KEY_STATUS_RELEASE = 0,
    KEY_STATUS_PRESS,
    KEY_STATUS_FAULT,
} KeyStatus;

typedef enum
{
    KEY_EVENT_SHORT_PRESS,                 //按键短按按下
    KEY_EVENT_SHORT_RELEASE,               //按键短按释放
    KEY_EVENT_LONG_PRESS,                  //按键长按按下
    KEY_EVENT_LONG_RELEASE,                //按键长按释放
} KeyEvent;

typedef enum
{
    DIAG_NO_ERROR = 0,                     //诊断执行无错误
    DIAG_INPUT_PARA_ERROR,                 //诊断输入参数错误
} DiagErrorCode;

typedef struct
{
    uint8_t     pressCount;                   // 按键按下计数 单位100ms
} KeyInfo;

typedef struct
{
    KeyInfo key[KEY_ID_MAX];
} DiagInfo;

typedef enum
{
    DTC_STATUS_NO_FAULT = 0x00,
    DTC_STATUS_FAULT,
}DtcStatus;

typedef enum
{
    DTC_SYSTEM_REQ = 0x00,
    DTC_CLIENT_REQ,
}DtcReqSource;

typedef enum
{
    DTC_REQ_RESTART_CHECK = 0x00,
    DTC_REQ_STOP_CHECK,
    DTC_REQ_CLEAR,
    DTC_REQ_READ,
    DTC_REQ_SYNC,
} DTCReqType;

typedef enum
{
    DTC_REQ_SUCCESS_STATUS = 0x00,
    DTC_REQ_FAIL_STATUS,
}DtcReqResultStatus;

typedef enum
{
    DTC_READ_STATUS_MASK_01 = 0x01,    /*Reference ISO14229  DTC STATUS MASK,  testFailed*/
    DTC_READ_STATUS_MASK_08 = 0x08,    /*Reference ISO14229  DTC STATUS MASK,  confirmedDTC*/
    DTC_READ_STATUS_MASK_09 = 0x09,    /*Reference ISO14229  DTC STATUS MASK,  confirmedDTC & testFailed*/
} DTCReadStatusMask;

typedef enum
{
    DTC_DEVICE_NORMAL_STATUS = 0x00,
    DTC_DEVICE_ERROR_STATUS,
    DTC_DEVICE_IDLE_STATUS,
}DTCDevicesStatus;

typedef struct
{
    DTCDevicesStatus status;                       /*devices current status*/
    uint16_t normalStatusCnt;                 /*devices current status Consecutive Count*/
    uint16_t errorStatusCnt;                  /*devices current status Consecutive Count*/
}DTCRecordType;

/*Thes  ENUM check by T-Box System BackGround, not defined by dtc or oem*/
typedef enum
{
    MAIN_POWER_BATTERY_LOWEST = 0x00,
    MAIN_POWER_BATTERY_EXIT,
    BACKUP_BATTERY_LOWEST,
    BATTERY_TYPE_MAX,
}BatteryBackGroundCheckEnum;

/*Thes  ENUM check by T-Box System BackGround, not defined by dtc or oem*/
typedef enum
{
    DIAG_SNED_PM_ENTER_SLEEP = 0x00,
    DIAG_SNED_PM_ENTER_POWER_OFF,
}DiagSendPmStatusEnum;

typedef struct
{
    DTCDevicesStatus status;                       /*devices current status*/
    unsigned char normalStatusCnt;                 /*devices current status Consecutive Count*/
    unsigned char errorStatusCnt;                  /*devices current status Consecutive Count*/
}BatteryBackGroundCheckStruct;

/************************函数接口***************************/
void DiagDtcDeviceTableInit(void);
void DiagPeriodNotifyFunction(Msg msg);
DiagErrorCode DiagXcallKeyQuery(keyId id, KeyStatus *status);
void DiagEventFunction(Msg msg);
void DiagDtcReqSystemClear(void);
void DiagDtcNotifyArmStartCheck(void);
void DiagDtcNotifyArmStopCheck(void);
void DiagDtcNotifyArmClearCheck(void);
void DiagDtcStatsSyncToArm(void);
void DiagDtcReqSystemInit(void);

void DiagModuleFailureCheck(DTCDevicesStatus dtcStatus);
bool GetCheckDtcEnable(uint8_t mask);
void SetCheckDtcEnable(bool start);

void DiagEcuCodeLostSelfCheck();

void DiagWriteDevicesStatus(DtcIndexType dtcIndex, DtcStatus dtcStatus);

int DiagPowerVoltageStatusSend(DiagSendPmStatusEnum diagSendPmStatu);


#endif

