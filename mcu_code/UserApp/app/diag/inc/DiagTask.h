#pragma once
#ifndef _DIAG_TASK_H_
#define _DIAG_TASK_H_

#include "appqueue.h"

#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define DIAG_TASK_STACK_SIZE 768
#define DIAG_TASK_PRIORITY   3

extern TaskHandle_t DIAGTask_Handle;

void StartDIAGTask(void);
void DIAGTask(void* param);

/************************函数接口***************************/
void DiagTaskInitHook(void);
void DiagTaskPostInitHook(void);
void DiagTaskPeriodHook(void);
void DiagTaskFunctionHook(Msg msg);

#endif