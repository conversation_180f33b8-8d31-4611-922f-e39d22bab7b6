#pragma once
#ifndef _LOG_TASK_H_
#define _LOG_TASK_H_

#include "AppTask.h"
#include "LogApi.h"
#include "task.h"


#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define LOG_TASK_STACK_SIZE 512
#define LOG_TASK_PRIORITY   4

extern TaskHandle_t LOGTask_Handle;

void StartLOGTask(void);
void LOGTask(void* param);

/************************函数接口***************************/
void LogTaskInitHook(void);
void LogTaskPostInitHook(void);
void LogTaskPeriodHook(void);
void LogTaskFunctionHook(Msg msg);
#endif