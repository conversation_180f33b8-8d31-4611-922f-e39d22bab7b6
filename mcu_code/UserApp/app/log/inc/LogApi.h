/*
      LogApi.h
描述：定义Log具体业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _LOG_API_H_
#define  _LOG_API_H_

#include "appqueue.h"
#include "event.h"
#include "osif.h"

/************************宏定义***************************/
#define  LOG_HEAD_MAX_SIZE                          50
#define  LOG_MAX_SIZE                               255
#define  LOG_MAX_BUFFER_SIZE                        2048

#define SEC_SOURCE_TYPE_SHIFT                       8
#define SEC_EVENT_QUEUE_LEN                         10  // 安全事件日志队列长度
#define SEC_EVENT_QUEUE_SIZE                        32  // 安全事件日志说明内容长度(需减去2，实际可用30字节)

/************************数据结构定义***************************/
typedef enum
{
    LOG_NO_INIT = 0,               // LOG相关未初始化
    LOG_INIT,                      // LOG相关已初始化
} LogInitFlag;

typedef enum
{
    LOG_NO_ERROR = 0,           //LOG 执行无错误
    LOG_PARA_ERROR,             //lOG 输入参数错误
    LOG_PRINTF_TOO_LOG,         //lOG 打印太长
    LOG_SEND_TO_FAIL,           //发送给LOG任务失败
}LogErrorCode;

typedef struct logQueue
{
    uint8_t  data[LOG_MAX_BUFFER_SIZE];
    uint16_t readPos;
    uint16_t writePos;
    uint16_t spaceLen;
    mutex_t  logMutex;
}LogQueue;

typedef enum secSourceType
{
    SEC_SOURCE_TYPE_MCU,
    SEC_SOURCE_TYPE_ARM,
    SEC_SOURCE_TYPE_MAX,
} SecSourceType;

typedef enum secEventType
{
    SEC_MCU_UDS_ATH_SUC = 0x2000, //UDS安全校验成功
    SEC_MCU_UDS_ATH_FAL = 0x2001, //UDS安全校验失败
} SecEventType;

/************************函数接口***************************/
void LogPeriodNotifyFunction(Msg msg);
void LogInitRamData(void);
void LogPowerOnInit(void);
void LogEventFunction(Msg msg);
LogErrorCode SystemApiLogPrintf(uint8_t level, const char *fmt,...);
LogErrorCode SecurityEventLog(SecEventType type, const char *fmt,...);
void SystemApiLogHexPrintf(uint8_t level, char *prx,uint8_t *hex,int len);
#endif

