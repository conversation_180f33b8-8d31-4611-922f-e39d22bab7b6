/*
      LogApi.c
描述：此文件主要是Log任务具体实现的业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/
#include <stdio.h>
#include <stdarg.h>
#include <stdbool.h>
#include "LogApi.h"
#include "CanApi.h"
#include "NvApi.h"
#include "SystemApi.h"
#include "BatApi.h"
#include "gpio.h"
#include "string.h"
#include "printf.h"
#include "IpcApi.h"
#include "utility_print_config.h"

/************************外部全局变量****************************/
extern  uint32_t            g_osCurrentTickTime;
extern  TboxSelfConfigPara  g_nvTboxSelfConfigData;
extern  GpioInfo            g_gpioPowerOnInfoList[];
extern  CommonInfo          g_commonInfo;
extern  CanDataArmStr       g_canDataArmStr;

/************************内部全局变量****************************/
LogQueue        g_rxLogQueue;
LogInitFlag     g_logInitFlag = LOG_NO_INIT;
QueueHandle_t   g_secEventQueue;
static mutex_t  g_logPrintfMutex;  // 用于保护SystemApiLogPrintf函数的互斥锁

EventInfo g_logEventFunctionMap[] = 
{
    {EVENT_ID_LOG_PERIOD_NOTIFY,  LogPeriodNotifyFunction},
};

static bool IsInISR(void)
{
    // return (0 != __get_IPSR());
    return true; // 暂时屏蔽锁
}

/*************************************************
函数名称: LogConfigUartOutput
函数功能: 配置LOG UART输出，UART2配置为UART模式, SPI配置为GPIO输入模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/28
修改日期 :2016/08/01 增加发送开机的LOG信息和初始化LOG BUFFER
*************************************************/
static void LogConfigUartOutput(void)
{
    //Uart31SendData(LOG_MAX_BUFFER_SIZE - g_rxLogQueue.spaceLen, g_rxLogQueue.data);
    //Richard:to do with SPI transfer
}

/*************************************************
函数名称: LogInitRamData
函数功能: 初始化LOG相关的变量
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/28
*************************************************/
void LogInitRamData(void)
{
    g_rxLogQueue.readPos = 0;
    g_rxLogQueue.writePos = 0;
    g_rxLogQueue.spaceLen = LOG_MAX_BUFFER_SIZE;
    memset(g_rxLogQueue.data, 0x00, LOG_MAX_BUFFER_SIZE);
    g_secEventQueue = xQueueCreate(SEC_EVENT_QUEUE_LEN, SEC_EVENT_QUEUE_SIZE);
    OSIF_MutexCreate(&g_rxLogQueue.logMutex);
    OSIF_MutexCreate(&g_logPrintfMutex);  // 初始化SystemApiLogPrintf互斥锁
}

/*************************************************
函数名称: LogInitHardwareDevice
函数功能: 根据不同的配置初始化硬件设备
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/28
*************************************************/
static void LogInitHardwareDevice(void)
{    
    switch(g_nvTboxSelfConfigData.logAndIpc.logMode)
    {
        case LOG_UART_OUTPUT:
        {
            LogConfigUartOutput();
            break;
        }
        case LOG_ARM_SAVE_OUTPUT:
        case LOG_ARM_NO_SAVE_OUTPUT:
        {
            LogConfigUartOutput();
            break;
        }
        case LOG_NO_OUTPUT:
        default:
        {
            break;
        }
    }
}

/*************************************************
函数名称: LogPowerOnInit
函数功能: 上电初始化LOG相关设备和变量
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/28
修改日期 :2016/08/1  增加LOG初始化标志
*************************************************/
void LogPowerOnInit(void)
{
    LogInitHardwareDevice();
    g_logInitFlag = LOG_INIT;
}

/*************************************************
函数名称: LogWriteQueueBuffer
函数功能: Log写入到LOG队列BUFFER
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/04/17
*************************************************/
queueErrorCode LogWriteQueueBuffer(uint16_t len, uint8_t *buffer)
{
    uint16_t paraPos  = 0;
    
    if(NULL == buffer)
    {
        return QUEUE_PRAR_POINT_NULL;
    }
    
    if(LOG_MAX_BUFFER_SIZE <= len)
    {
        return QUEUE_WRITE_LEN_BEYOND_MAX_SZIE;
    }
    
    // 只有在非中断环境中才加锁
    bool inISR = IsInISR();
    if (!inISR)
    {
        OSIF_MutexLock(&g_rxLogQueue.logMutex, OSIF_WAIT_FOREVER);
    }
    
    if(g_rxLogQueue.spaceLen < len)
    {
        if (!inISR)
        {
            OSIF_MutexUnlock(&g_rxLogQueue.logMutex);
        }
        return QUEUE_WRITE_LEN_BEYOND_SPACE_SZIE;
    }


    for(paraPos = 0; paraPos < len; paraPos++)
    {
        g_rxLogQueue.data[g_rxLogQueue.writePos] = buffer[paraPos];
        g_rxLogQueue.writePos = (g_rxLogQueue.writePos + 1)%LOG_MAX_BUFFER_SIZE;
    }
    g_rxLogQueue.spaceLen = g_rxLogQueue.spaceLen - len;

    if (!inISR)
    {
        OSIF_MutexUnlock(&g_rxLogQueue.logMutex);
    }
    return QUEUE_NO_ERROR;
}

/*************************************************
函数名称: LogReadQueueBuffer
函数功能: Log读取BUFFER数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/04/17
*************************************************/
queueErrorCode LogReadQueueBuffer(uint16_t *len, uint8_t *buffer)
{
    uint16_t writeDataLen = 0;
    uint16_t  paraPos  = 0;
    
    if((NULL == buffer)||(NULL == len))
    {
        return QUEUE_PRAR_POINT_NULL;
    }

    // 只有在非中断环境中才加锁
    bool inISR = IsInISR();
    if (!inISR)
    {
        OSIF_MutexLock(&g_rxLogQueue.logMutex, OSIF_WAIT_FOREVER);
    }
    
    writeDataLen = LOG_MAX_BUFFER_SIZE - g_rxLogQueue.spaceLen;
    //队列的读位置和写位置相等并且空白空间大于0则判断循环队列为空
    if((g_rxLogQueue.readPos == g_rxLogQueue.writePos) && (0 == writeDataLen))
    {        
        if (!inISR)
        {
            OSIF_MutexUnlock(&g_rxLogQueue.logMutex);
        }
        return QUEUE_IS_EMPTY;
    }
    if(IPC_SEND_LOG_FRAME_MIN_LEN > writeDataLen)
    {
        if (!inISR)
        {
            OSIF_MutexUnlock(&g_rxLogQueue.logMutex);
        }
        return QUEUE_IS_EMPTY;
    }


    if(IPC_SEND_LOG_FRAME_LEN <= writeDataLen)
    {
        *len = IPC_SEND_LOG_FRAME_LEN;
    }
    else
    {
        *len = writeDataLen;
    }

    for(paraPos = 0; paraPos < *len; paraPos++)
    {
        buffer[paraPos] = g_rxLogQueue.data[g_rxLogQueue.readPos];
        g_rxLogQueue.readPos = (g_rxLogQueue.readPos + 1)%LOG_MAX_BUFFER_SIZE;
    }
    g_rxLogQueue.spaceLen = g_rxLogQueue.spaceLen + *len;

    if (!inISR)
    {
        OSIF_MutexUnlock(&g_rxLogQueue.logMutex);
    }
    return QUEUE_NO_ERROR;
}

/*************************************************
函数名称: SystemApiLogPrintf
函数功能: 系统打印LOG发送函数
输入参数: LOG等级, 可变形参
输出参数: 执行错误类型
函数返回类型值：
           LOG_NO_ERROR--- LOG 执行无错误
           LOG_PARA_ERROR ---lOG 输入参数错误
           LOG_PRINTF_TOO_LOG ---lOG 打印太长 
           LOG_SEND_TO_FAIL --- 发送给LOG任务失败
编写者: liaoyonggang
编写日期 :2016/07/27
*************************************************/
LogErrorCode SystemApiLogPrintf(uint8_t level, const char *fmt, ...)
{
    LogErrorCode errorCode = LOG_NO_ERROR;
    uint16_t n = 0;
    uint16_t headLen = 0;
    va_list args;
    static char buffer[LOG_MAX_SIZE];  // 静态缓冲区，使用互斥锁保护
    char headbuffer[LOG_HEAD_MAX_SIZE];
    static uint8_t logFlag = 0;
    TboxSystemTimeStruct *pTimeStruct = TboxSystemTimeInfoRead();
    static uint32_t logCount = 0; // 用于LOG初始化之前，记录LOG打印次数

    // 只有在非中断环境中才加锁保护静态缓冲区
    bool inISR = IsInISR();
    if (!inISR)
    {
        OSIF_MutexLock(&g_logPrintfMutex, OSIF_WAIT_FOREVER);
    }

    if (logFlag == 1)
    {
        if (!inISR)
        {
            OSIF_MutexUnlock(&g_logPrintfMutex);
        }
        return LOG_SEND_TO_FAIL;
    }

    if (g_logInitFlag == LOG_NO_INIT) 
    {
        memset(buffer, 0x00, LOG_MAX_SIZE);
        memset(headbuffer, 0x00, LOG_HEAD_MAX_SIZE);

        switch (level) 
        {
            case LOG_INFO_OUTPUT:
                headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%04X][I]:", logCount++);
                break;
            case LOG_WARING_OUTPUT:
                headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%04X][W]:", logCount++);
                break;
            case LOG_ERROR_OUTPUT:
                headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%04X][E]:", logCount++);
                break;
            case LOG_ISR_OUTPUT:
                headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%04X][R]:", logCount++);
                break;
            default:
                if (!inISR)
                {
                    OSIF_MutexUnlock(&g_logPrintfMutex);
                }
                return LOG_PARA_ERROR;
        }

        // 先复制头部信息到缓冲区
        memcpy(buffer, headbuffer, headLen);

        va_start(args, fmt);
        n = vsnprintf(buffer + headLen, LOG_MAX_SIZE - headLen, fmt, args);
        va_end(args);

        if ((n + headLen) >= LOG_MAX_SIZE)
        {
            logFlag = 0;
            logCount--;
            if (!inISR)
            {
                OSIF_MutexUnlock(&g_logPrintfMutex);
            }
            return LOG_PRINTF_TOO_LOG;
        }
        LogWriteQueueBuffer(n + headLen, (uint8_t*)buffer);
        PRINTF("%s",buffer);
        logFlag = 0;
        if (!inISR)
        {
            OSIF_MutexUnlock(&g_logPrintfMutex);
        }
        return errorCode;
    }

    if (g_nvTboxSelfConfigData.logAndIpc.logMode == LOG_NO_OUTPUT)
    {
        logFlag = 0;
        if (!inISR)
        {
            OSIF_MutexUnlock(&g_logPrintfMutex);
        }
        return errorCode;
    }

    if ((g_nvTboxSelfConfigData.logAndIpc.logLevel & level) == level) 
    {
        memset(buffer, 0x00, LOG_MAX_SIZE);
        memset(headbuffer, 0x00, LOG_HEAD_MAX_SIZE);
        switch (g_nvTboxSelfConfigData.logAndIpc.logMode) 
        {
            case LOG_UART_OUTPUT:
            case LOG_ARM_SAVE_OUTPUT:
            case LOG_ARM_NO_SAVE_OUTPUT:
                switch (level) 
                {
                    case LOG_INFO_OUTPUT:
                        headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%02d:%02d:%02d:%03d][I]:", pTimeStruct->currentTime.hour, pTimeStruct->currentTime.minute, pTimeStruct->currentTime.second, xTaskGetTickCount() % 1000);
                        break;
                    case LOG_WARING_OUTPUT:
                        headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%02d:%02d:%02d:%03d][W]:", pTimeStruct->currentTime.hour, pTimeStruct->currentTime.minute, pTimeStruct->currentTime.second, xTaskGetTickCount() % 1000);
                        break;
                    case LOG_ERROR_OUTPUT:
                        headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%02d:%02d:%02d:%03d][E]:", pTimeStruct->currentTime.hour, pTimeStruct->currentTime.minute, pTimeStruct->currentTime.second, xTaskGetTickCount() % 1000);
                        break;
                    case LOG_ISR_OUTPUT:
                        headLen = snprintf(headbuffer, LOG_HEAD_MAX_SIZE, "[%02d:%02d:%02d:%03d][R]:", pTimeStruct->currentTime.hour, pTimeStruct->currentTime.minute, pTimeStruct->currentTime.second, xTaskGetTickCount() % 1000);
                        break;
                    default:
                        logFlag = 0;
                        if (!inISR)
                        {
                            OSIF_MutexUnlock(&g_logPrintfMutex);
                        }
                        return LOG_PARA_ERROR;
                }
                break;
            default:
                logFlag = 0;
                if (!inISR)
                {
                    OSIF_MutexUnlock(&g_logPrintfMutex);
                }
                return LOG_PARA_ERROR;
        }

        // 先复制头部信息到缓冲区
        memcpy(buffer, headbuffer, headLen);

        va_start(args, fmt);
        n = vsnprintf(buffer + headLen, LOG_MAX_SIZE - headLen, fmt, args);
        va_end(args);

        if ((n + headLen) >= LOG_MAX_SIZE)
        {
            logFlag = 0;
            if (!inISR)
            {
                OSIF_MutexUnlock(&g_logPrintfMutex);
            }
            return LOG_PRINTF_TOO_LOG;
        }

        buffer[n + headLen] = '\0';

        if (g_nvTboxSelfConfigData.logAndIpc.logMode == LOG_UART_OUTPUT) 
        {
            // PRINTF("%s", buffer);
            LINFlexD_UART_DRV_SendDataBlocking(UART_PORT_LOG_OUTPUT, (uint8_t*)buffer, n + headLen, 1000);
        } 
        else 
        {
            LogWriteQueueBuffer(n + headLen, (uint8_t*)buffer);
            // PRINTF("%s", buffer);
            LINFlexD_UART_DRV_SendDataBlocking(UART_PORT_LOG_OUTPUT, (uint8_t*)buffer, n + headLen, 1000);
        }
    }

    logFlag = 0;
    if (!inISR)
    {
        OSIF_MutexUnlock(&g_logPrintfMutex);
    }
    return errorCode;
}

/*************************************************
函数名称: SecurityEventLog
函数功能: 安全事件LOG发送函数
输入参数: 安全事件类型, 可变形参（形参最终长度需要小于30字节，用于事件说明）
输出参数: 执行错误类型
函数返回类型值：
           LOG_NO_ERROR--- LOG 执行无错误
           LOG_PARA_ERROR ---lOG 输入参数错误
           LOG_PRINTF_TOO_LOG ---lOG 打印太长 
           LOG_SEND_TO_FAIL --- 发送给LOG任务失败
编写者: SUNHANG
编写日期 :2024/03/28
*************************************************/
LogErrorCode SecurityEventLog(SecEventType type, const char *fmt,...)
{
    LogErrorCode errorCode = LOG_NO_ERROR;
    va_list args;
    char temp[SEC_EVENT_QUEUE_SIZE]; //temp[0]为消息长度  temp[1，2]为事件类型
    uint16_t len = 0;

    memset(temp, 0x00, SEC_EVENT_QUEUE_SIZE);
    temp[1] = (type >> 8) & 0xFF;
    temp[2] = type & 0xFF;
    va_start(args, fmt);
    len = vsprintf(temp + 3, fmt, args);
    va_end(args);

    if(len >= SEC_EVENT_QUEUE_SIZE - 3)
    {
        errorCode = LOG_PRINTF_TOO_LOG;
        return errorCode;
    }
    temp[0] = len + 2;

    if(pdPASS != xQueueSend(g_secEventQueue, temp, 0))
    {
        errorCode = LOG_SEND_TO_FAIL;
    }
    return errorCode;
}

/*************************************************
函数名称: SecurityLogSend
函数功能: 发送安全事件LOG到ARM
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: SUNHANG
编写日期 :2024/03/28
*************************************************/
static void SecurityLogSend(void)
{
    Msg msg;
    uint8_t temp[SEC_EVENT_QUEUE_SIZE]; //temp[0]为消息长度  temp[1]为事件类型

    if(HANDSHAKE_FAIL == g_commonInfo.handshakeStatus)
    {
        return;
    }

    if(IsQueueSpaceEnough(TASK_ID_IPC, SEC_EVENT_QUEUE_SIZE) != QUEUE_NO_ERROR)
    {
        return;
    }

    if(pdTRUE == xQueueReceive(g_secEventQueue, temp, 0))
    {
        msg.event = MESSAGE_TX_MCU_SECURITY_EVENT;
        msg.len = temp[0];
        msg.lparam = (uint32_t)&temp[1];
        SystemSendMessage(TASK_ID_IPC, msg);
    }
}

/*************************************************
函数名称: LogSendArmLog
函数功能: 发送LOG到ARM128字节
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/01
*************************************************/
static void LogSendArmLog(void)
{
    Msg msg;
    uint8_t temp[IPC_SEND_LOG_FRAME_LEN];
    uint16_t len = 0;

    if(HANDSHAKE_FAIL == g_commonInfo.handshakeStatus)
    {
        return;
    }

    if(IsQueueSpaceEnough(TASK_ID_IPC, IPC_SEND_LOG_FRAME_LEN) != QUEUE_NO_ERROR)
    {
        return;
    }

    memset(temp, 0x00, IPC_SEND_LOG_FRAME_LEN);
    if(QUEUE_NO_ERROR != LogReadQueueBuffer(&len, temp))
    {
        return;
    }

    msg.event  = MESSAGE_TX_MCU_LOG;
    msg.len    = len;
    msg.lparam = (uint32_t)&temp[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: SystemStatusLogPrintf
函数功能: 系统状态信息周期性日志打印（不包含电压信息，避免与BatVoltLogPrintf重复）
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2024/12/19
*************************************************/
static void SystemStatusLogPrintf(void)
{
    static uint8_t  count = 0;

    count++;
    if(count >= 50)
    {
        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT,
            "SysStatus - Ftm:%u,Acc:%u,Rc:%u,FreeHeap:%.2fKB\r\n",
            g_commonInfo.ftmErrorCount,
            g_commonInfo.accStatus,
            g_commonInfo.remoteControlStatus,
            (float)xPortGetFreeHeapSize() / 1024
        );
        #endif

        count = 0;
    }
}

/*************************************************
函数名称: PmCanLogFail
函数功能: CAN通信状态周期性日志打印
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/10/23
修改日期 :2024/12/19 重构，拆分非CAN相关日志到SystemStatusLogPrintf
*************************************************/
static void PmCanLogFail(void)
{
    static uint8_t  count = 0;
    static uint32_t lastCanCount = 0;

    count++;
    if(count >= 50)
    {
        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT,
            "CANStatus - Tot:%u,Inc:%u,Lost:%u,ArmRx:%u,RxTxDiff:%u\r\n",
            g_commonInfo.canTotalNum,
            g_commonInfo.canTotalNum - lastCanCount,
            g_commonInfo.canLostNum,
            g_canDataArmStr.countFromArm,
            g_canDataArmStr.countFromArm - g_canDataArmStr.countMcuOut
        );
        #endif

        count = 0;
        lastCanCount = g_commonInfo.canTotalNum;
    }
}

/**
 * @brief B+ 电压、MIC 电压、电池电压、电池温度 周期打印
 * 
 * @param void 
 * @return void 
 */
void BatVoltLogPrintf(void)
{
    static uint8_t  count = 0;
    count++;
    if(count >= 50)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "B+ Volt: %dmv,Mic Volt: %dmv,Bat Volt: %dmv,Bat Temp: %d\r\n",ReadBpPlusValue(),ReadMicValue(),BatReadVoltage(),BatReadTemperature());
        count = 0;
    }
}

/*************************************************
函数名称: LogPeriodNotifyFunction
函数功能: LOG周期性调用函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/28
修改日期 :2024/12/19 添加SystemStatusLogPrintf调用
*************************************************/
void LogPeriodNotifyFunction(Msg msg)
{
    //McuScheduleTimeOutReInit(TASK_ID_LOG, 0x00);
    LogSendArmLog();
    SecurityLogSend();
    PmCanLogFail();
    SystemStatusLogPrintf();
    BatVoltLogPrintf();
}

/*************************************************
函数名称: CanEventFunction
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void LogEventFunction(Msg msg)
{
    uint8_t index = 0;

    for(index = 0; index < (sizeof(g_logEventFunctionMap)/sizeof(g_logEventFunctionMap[0])); index++)
    {
        if(g_logEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_logEventFunctionMap[index].TaskFunctionHook)
            {
                g_logEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

void SystemApiLogHexPrintf(uint8_t level, char *prx, uint8_t *hex, int len)
{
    if (hex == NULL || prx == NULL || len <= 0)
    {
        SystemApiLogPrintf(level, "HexPrintf invalid parameters\r\n");
        return;
    }

    uint8_t logBuf[200] = {0};  // 保持原来的 logBuf 大小
    const int MAX_BYTES_PER_LINE = 99;  // 99 字节 * 2 HEX 字符 = 198，占满 logBuf

    SystemApiLogPrintf(level, "%s len(%d):", prx, len);  // 先打印前缀

    for (int i = 0; i < len; i += MAX_BYTES_PER_LINE)
    {
        int chunkLen = (len - i > MAX_BYTES_PER_LINE) ? MAX_BYTES_PER_LINE : (len - i);

        SystemApiHexToStr(&hex[i], chunkLen, logBuf);  // 直接转换，无额外处理
        SystemApiLogPrintf(level, "%s\r\n", logBuf);  // 逐行打印转换后的字符串
    }
}

