/*
     LedApi.h
描述：定义LED显示具体业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _LED_API_H_
#define  _LED_API_H_

#include "AppTask.h"
#include "gpio.h"


#define GPIO_CONTROL_PERIOD_TIME_TICK          100   /*ms*/

/*HAND SHAKE led status time*/
#define GPIO_HANDSHAKE_LED_RED_SET_TIME        10    /*ioRecord Updata per 100ms*/
#define GPIO_HANDSHAKE_LED_RED_RESET_TIME      10    /*ioRecord Updata per 100ms*/
#define GPIO_HANDSHAKE_LED_GREEN_SET_TIME      20    /*ioRecord Updata per 100ms*/
#define GPIO_HANDSHAKE_LED_GREEN_RESET_TIME    20    /*ioRecord Updata per 100ms*/

/*4g module  led status time*/
#define GPIO_MODULE_LED_RED_SET_TIME           10    /*ioRecord Updata per 100ms*/
#define GPIO_MODULE_LED_RED_RESET_TIME         10    /*ioRecord Updata per 100ms*/
#define GPIO_MODULE_LED_GREEN_SET_TIME         10    /*ioRecord Updata per 100ms*/
#define GPIO_MODULE_LED_GREEN_RESET_TIME       10    /*ioRecord Updata per 100ms*/
#define GPIO_MODULE_TSP_LED_GREEN_SET_TIME     25    /*ioRecord Updata per 100ms*/
#define GPIO_MODULE_TSP_LED_GREEN_RESET_TIME   05    /*ioRecord Updata per 100ms*/

/*gnss module led status time*/
#define GPIO_GNSS_LED_RED_SET_TIME             10    /*ioRecord Updata per 100ms*/
#define GPIO_GNSS_LED_RED_RESET_TIME           10    /*ioRecord Updata per 100ms*/
#define GPIO_GNSS_LED_GREEN_SET_TIME           10    /*ioRecord Updata per 100ms*/
#define GPIO_GNSS_LED_GREEN_RESET_TIME         10    /*ioRecord Updata per 100ms*/
#define GPIO_GNSS_CRASHED_LED_RED_SET_TIME     25    /*ioRecord Updata per 100ms*/
#define GPIO_GNSS_CRASHED_LED_RED_RESET_TIME   05    /*ioRecord Updata per 100ms*/

//SOS led status time*/
#define GPIO_SOS_LED_RED_SET_TIME             5    /*ioRecord Updata per 100ms*/
#define GPIO_SOS_LED_RED_RESET_TIME           5    /*ioRecord Updata per 100ms*/
#define GPIO_SOS_LED_GREEN_SET_TIME           5    /*ioRecord Updata per 100ms*/
#define GPIO_SOS_LED_GREEN_RESET_TIME         5    /*ioRecord Updata per 100ms*/


//to define below ADB mode MACRO will trigger compile the version with ADB trigger
#define ARM_ADB_MODE    1


// reset后等待一会儿再拉低powerkey
#define MODULE_RESET_PKON_INTERVAL          (1000+TASK_LEDGPIO_PERIOD_TIME)/TASK_LEDGPIO_PERIOD_TIME
//ARM power ON time should be > 500ms, but according to the testing result on the real chip,
//it was found that the Power ON time should be at least > 800ms, so put 1000ms here to leave 200ms buffer  
#if ARM_ADB_MODE
#define MODULE_POWER_ON_TIME                (7500+TASK_LEDGPIO_PERIOD_TIME)/TASK_LEDGPIO_PERIOD_TIME
#else
#define MODULE_POWER_ON_TIME                (1000+TASK_LEDGPIO_PERIOD_TIME)/TASK_LEDGPIO_PERIOD_TIME
#endif

//ARM power OFF time should be > 6000ms
#define MODULE_POWER_OFF_TIME               (6100+TASK_LEDGPIO_PERIOD_TIME)/TASK_LEDGPIO_PERIOD_TIME
//to define the time for the Module Wake up 
#define MODULE_WAKE_UP_LASTING_TIME         (500+TASK_LEDGPIO_PERIOD_TIME)/TASK_LEDGPIO_PERIOD_TIME

typedef enum
{
    MODULE_POWER_IDLE = 0,
    MODULE_POWER_ON_GPRS_PWR_EN_LOW,
    MODULE_POWER_ON_ONOFF_HIGH,
    MODULE_POWER_ON_ONOFF_LOW,
    MODULE_POWER_ON_TRANSLATOR_EN_LOW,
    MODULE_POWER_ON_UART_INIT,             /*模块开机,低速串口开机会打印日志干扰数据接收*/
    MODULE_POWER_OFF_TRANSLATOR_EN_HIGH,
    MODULE_POWER_OFF_ONOFF_HIGH,
    MODULE_POWER_OFF_ONOFF_LOW,
    MODULE_POWER_OFF_GPRS_PWR_EN_HIGH,
    MODULE_POWER_OFF_WIFI_ON,
    MODULE_POWER_WAKEUP_ARM_HIGH,
    MODULE_POWER_WAKEUP_ARM_LOW,
    MODULE_RESET_HIGH,
    MODULE_RESET_LOW,
}ModulePowerStatus;


typedef enum
{
    GPIO_CONTROL_HEARTBEAT_SUCCESS = 0x00,
    GPIO_CONTROL_HEARTBEAT_TIMEOUT,
}GpioControlHearbeatuEnum;


typedef enum
{
    GPIO_STATUS_OFF = 0x00,
    GPIO_STATUS_ON,
    GPIO_STATUS_BLINKED,
    GPIO_STATUS_FLASH,
    GPIO_STATUS_INVALID,
}GpioStatusTypeEnum;


typedef enum
{
    GPIO_CONTROL_CAN_RED_LED = 0x00,
    GPIO_CONTROL_CAN_GREEN_LED,
    GPIO_CONTROL_MODULE_RED_LED,
    GPIO_CONTROL_MODULE_GREEN_LED,
    GPIO_CONTROL_GNSS_RED_LED,
    GPIO_CONTROL_GNSS_GREEN_LED,
    GPIO_CONTROL_ECALL_SOS_GREEN_LED,
    GPIO_CONTROL_ECALL_SOS_RED_LED,
    GPIO_CONTROL_FDC_IO,
    GPIO_CONTROL_GPS_PHY_LED,
    GPIO_CONTROL_ENUM_MAX,
}GpioCtrlTypeEnum;

typedef struct
{
    GpioCtrlTypeEnum pairedCtrlType;    // 对应的另一GPIO控制类型
    GpioStatusTypeEnum curStatus;
    GpioStatusTypeEnum preStatus;
    uint8_t ioRemap;
    uint8_t ioSetTime;
    uint8_t ioResetTime;
    uint8_t ioTimeRecord;
}GpioControlInfoStruct;


/************************函数接口***************************/
ModulePowerStatus GetPowerModuleStatus(void);
void GpioControlPeriodNotifyFunction(Msg msg);
int  GpioControlHandle(GpioCtrlTypeEnum gpioCtrlTypeEnum, GpioControlInfoStruct gpioControlInfoStruct);

int GpioLedControlFirstInit(void);
int GpioLedControlUpdateStatusFunction(GpioCtrlTypeEnum gpioCtrlType, GpioStatusTypeEnum gpioStatusType, uint8_t lightOnTimeCnt, uint8_t lightOffTimeCnt);
void LedEventFunction(Msg msg);
void ChangeLEDState(uint16_t ledIndex, GpioLevel state);
void CheckECallLEDState(void);

#endif

