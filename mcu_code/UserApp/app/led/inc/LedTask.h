#pragma once
#ifndef _LED_TASK_H_
#define _LED_TASK_H_

#include "AppTask.h"
#include "LedApi.h"
#include "task.h"

#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define LED_TASK_STACK_SIZE 384
#define LED_TASK_PRIORITY   4

extern TaskHandle_t LEDTask_Handle;

void StartLEDTask(void);
void LEDTask(void* param);


/************************函数接口***************************/
void LedTaskInitHook(void);
void LedTaskPostInitHook(void);
void LedTaskPeriodHook(void);
void LedTaskFunctionHook(Msg msg);

#endif