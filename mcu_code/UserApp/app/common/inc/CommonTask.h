#pragma once
#ifndef _COMMON_TASK_H_
#define _COMMON_TASK_H_
#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#include "airbag.h"

#define COMMON_TASK_STACK_SIZE (512)
#define COMMON_TASK_PRIORITY   4

extern TaskHandle_t CommonTask_Handle;

void StartCommonTask(void);
_Noreturn void CommonTask(void* param);
void CommonTaskInitHook(void);

#define CAN_UPLOAD_TEST 0
#if  (CAN_UPLOAD_TEST == 1)
void CanLogSimulationTest(void);
#endif // CAN_UPLOAD_TEST

#if (AIRBAG_TEST == 1)
void airBagSigDisplay(UINT32 counter);
#endif 

#endif