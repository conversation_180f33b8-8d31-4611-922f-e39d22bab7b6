#pragma once
#ifndef _CAN_DATA_UPLOAD_TASK_H_
#define _CAN_DATA_UPLOAD_TASK_H_
#ifdef WINDOWS_SIM
#define PRINTF printf
#endif
#define CAN_DATA_UPLOAD_TASK_STACK_SIZE 1152
#define CAN_DATA_UPLOAD_TASK_PRIORITY   3

#define CAN_DATA_UPLOAD_MASTER_MODE 
#define SPI_INST_HANDLE        3
//#define SPI_TRANS_LENGTH       146
extern TaskHandle_t CANDataUploadTask_Handle;

void StartCANDataUploadTask(void);
void CANDataUploadTask(void* param);
void CANDataUploadTaskInitHook(void);

#ifdef CAN_DATA_UPLOAD_MASTER_MODE
#define CAN_DATA_TRANSFER   SPI_DRV_MasterTransferBlocking
#define GET_TRANS_STATUS    SPI_DRV_MasterGetTransferStatus
#else
#define CAN_DATA_TRANSFER   SPI_DRV_SlaveTransferBlocking
#define GET_TRANS_STATUS    SPI_DRV_SlaveGetTransferStatus
#endif

#endif    //_CAN_DATA_UPLOAD_TASK_H_