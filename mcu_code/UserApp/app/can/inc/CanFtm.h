/*
      CanFtm.h
描述：此文件主要是通过CAN进行产线测试任务具体实现的业务功能
作者：廖勇刚
时间：2017.6.16
*/


#ifndef  _CAN_FTM_H_
#define  _CAN_FTM_H_


#include "IpcApi.h"
#include "Platform_Types.h"



/************************宏定义***************************/
#define   CAN_REQUEST_ID                    0x640
#define   CAN_RESPONSE_ID                   0x641 
#define   CAN_TEST_REQUEST_ID               0x643
#define   CAN_TEST_RESPONSE_ID              0x642

#define   CAN_FRAME_LEN                     8


//PC--->MCU消息ID
#define   MESSAGE_RX_MCU_CMD                         (uint16_t)0x08ff     
#define   MESSAGE_RX_ARM_CMD                         (uint16_t)0x02ff     
#define   MESSAGE_RX_DUT_CMD                         (uint16_t)0x3001


//MCU--->PC消息ID
#define   MESSAGE_TX_PC_CMD                          (uint16_t)0x1001    // PC 接收命令
//MCU --> 底板
#define   MESSAGE_TX_DUT_CMD                         (uint16_t)0x3001 


#define   FTM_MAX_SIZE_LEN                            200

#define   MCU_ICT_KEY_INDEX                           2
#define   MCU_ICT_AIRBAG_INDEX                        3
#define   MCU_ICT_ON_INDEX                            4
#define   MCU_ICT_ACC_INDEX                           5
#define   MCU_ICT_DITGIT_INDEX                        6
#define   MCU_ICT_ANALOG_INDEX                        7
#define   MCU_ICT_RTC_WAKEUP_INDEX                    0x0b
#define   MCU_ICT_CAN1_WAKEUP_INDEX                   0x0c
#define   MCU_ICT_CAN2_WAKEUP_INDEX                   0x0d



#define   MCU_PC_KEY_INDEX                            6
#define   MCU_PC_AIRBAG_INDEX                         7
#define   MCU_PC_ON_INDEX                             8
#define   MCU_PC_ACC_INDEX                            9
#define   MCU_PC_DITGIT_INDEX                         10
#define   MCU_PC_ANALOG_INDEX                         11
#define   MCU_PC_CAN1_RESULT_INDEX                    0x0c
#define   MCU_PC_CAN2_RESULT_INDEX                    0x0d
#define   MCU_PC_GPS_ANT_OPEN_INDEX                   0x16
#define   MCU_PC_GPS_ANT_SHORT_INDEX                  0x17
#define   MCU_PC_WAKEUP_SOURCE_INDEX                  0x20
#define   MCU_PC_SUPPLIER_ID_INDEX                    0x80  
#define   MCU_PC_TBOX_MODEL_INDEX                     0x81
#define   MCU_PC_TBOX_ID_INDEX                        0x82
#define   MCU_PC_SIM_NUMBER_INDEX                     0x83
#define   MCU_FTM_TEST_LIN_ID                         36



/************************数据结构定义***************************/
typedef enum
{
    FTM_TYPE_CAN = 0,
    FTM_TYPE_UART,
}FtmType;

typedef enum
{
    MESSAGE_ID_RX_MCU_CMD = 0,
    MESSAGE_ID_RX_ARM_CMD,
    MESSAGE_ID_RX_DUT_CMD,
    MESSAGE_ID_RX_CANFTM_MAX,
} MessageRxCanFtmIndex;



typedef enum
{
    MESSAGE_ID_TX_PC_CMD = 0,
    MESSAGE_ID_TX_DUT_CMD,
    MESSAGE_ID_TX_CANFTM_MAX,
} MessageTxCanFtmIndex;

typedef enum
{
    FTM_RX_MCU_MODE_INDEX  = 1, //装备模式
    FTM_RX_MCU_ECALL_INDEX ,
    FTM_RX_MCU_AIRBAG_INDEX ,
    FTM_RX_MCU_ON_INDEX ,
    FTM_RX_MCU_ACC_INDEX ,
    FTM_RX_MCU_DIGIT_INPUT_INDEX ,
    FTM_RX_MCU_DIGIT_OUTPUT1_INDEX ,
    FTM_RX_MCU_DIGIT_OUTPUT2_INDEX ,
    FTM_RX_MCU_HU_INDEX ,
    FTM_RX_MCU_LED_GPS_INDEX ,
    FTM_RX_MCU_LED_ANTITHEFT_INDEX ,//防盗灯0x0B--11
    FTM_RX_MCU_LED_ECALL_INDEX ,
    FTM_RX_MCU_SECURITY_CHIP_INDEX ,
    FTM_RX_MCU_BT_VERSION_CHECK_INDEX,
    FTM_RX_MCU_BT_CONNECT_INFO_INDEX,
    FTM_RX_MCU_BT_SPEED_INDEX,//蓝牙速率测试
    FTM_RX_MCU_MIC_INDEX,
    FTM_RX_MCU_LINE_INDEX,  //LINE+输出测试
    FTM_RX_MCU_SPEAKER_INDEX,//扬声器测试
    FTM_RX_MCU_BAT_INFO_INDEX,//电池温度和电压测试
    FTM_RX_MCU_BAT_CHARGE_INDEX,//电池充放电测试
    FTM_RX_MCU_UART32_INDEX,
    FTM_RX_MCU_GPS_ANT_OPEN_INDEX,//  = 0x17,
    FTM_RX_MCU_GPS_ANT_SHORT_INDEX ,
    FTM_RX_MCU_SYSTEM_SLEEP_INDEX ,
    FTM_RX_MCU_BT_WAKEUP_INDEX ,
    FTM_RX_MCU_RTC_WAKEUP_INDEX ,
    FTM_RX_MCU_CAN2_WAKEUP_INDEX ,
    FTM_RX_MCU_CAN3_WAKEUP_INDEX ,
    FTM_RX_MCU_CAN4_WAKEUP_INDEX ,
    FTM_RX_MCU_CAN5_WAKEUP_INDEX ,
    FTM_RX_MCU_REQUEST_WAKEUP_INDEX ,//查询唤醒源结果  0X20
    FTM_RX_MCU_TBOXID_WRITE_INDEX,
    FTM_RX_MCU_SUPPLIERID_WRITE_INDEX,
    FTM_RX_MCU_TBOXNO_WRITE_INDEX,
    FTM_RX_MCU_SIMNUMBER_WRITE_INDEX,
    FTM_RX_MCU_LTE_ANT_OPEN_INDEX,// 
    FTM_RX_MCU_LTE_ANT_SHORT_INDEX ,
    FTM_RX_MCU_MIC_OPEN_INDEX,
    FTM_RX_MCU_MIC_SHORT_INDEX,
    FTM_RX_MCU_ECALL_WAKEUP_INDEX = 0x2B,
    FTM_RX_MCU_ECU_ID_READ_INDEX,
    FTM_RX_MCU_TEST_NAVIGATE_INDEX = 0x29,
    FTM_RX_MCU_READ_INFORMATION = 0x2c,
    FTM_RX_MCU_LIN = 0x2D,
    FTM_RX_MCU_WRITE_NAVIGATE_INDEX = 0x2E,
    FTM_RX_MCU_READ_SOFT_VERSION = 0x2F,
    FTM_RX_MCU_READ_HARD_VERSION = 0x30,
    FTM_RX_MCU_READ_BOOTLOADER_VERSION = 0x31,
    FTM_RX_MCU_READ_ECU_NAME = 0x32,
    FTM_RX_MCU_READ_BLE_MAC = 0x33,
    FTM_RX_MCU_MODULE_WAKEUP_PIN_INDEX = 0x34,
    FTM_RX_MCU_LED_TEST_INDEX = 0x35,
    FTM_RX_MCU_EXIT_FTM = 0x36,
    FTM_RX_MCU_READ_KL30 = 0x37,
    FTM_RX_MCU_GPS_ANT_NORMAL_INDEX = 0x38,
    FTM_RX_MCU_LTE_ANT_NORMAL_INDEX = 0x39,
    FTM_RX_MCU_SPAREPARTNUMBER_WRITE_INDEX = 0x3A,    /*20220312,zxl,增加产写写入零件号的功能*/
    
    FTM_RX_MCU_PROJECT_WRITE_INDEX         = 0x3B,      /*前晨新增产线写入项目名(车型)*/
    FTM_RX_MCU_ICCID_WRITE_INDEX           = 0x3C,      /*前晨新增产线写入ICCID*/
    FTM_RX_MCU_ECUMANUFACTURINGDATE_WRITE_INDEX = 0x3D, /*前晨新增产线写入软件生产日期*/
    FTM_RX_MCU_ECUINSTALLATIONDATE_WRITE_INDEX  = 0x3E, /*前晨新增产线写入软件更新日期*/
    FTM_RX_MCU_MAX_INDEX,
}FtmRxMcuIndex;

typedef enum
{
    FTM_TX_PC_MODE_INDEX=1,
    FTM_TX_PC_ECALL_INDEX,
    FTM_TX_PC_AIRBAG_INDEX,
    FTM_TX_PC_ON_INDEX,
    FTM_TX_PC_ACC_INDEX,
    FTM_TX_PC_DIGIT_INPUT_INDEX,

    FTM_TX_PC_SECURITY_CHIP_INDEX = 0x11,
    FTM_TX_PC_BT_VERSION_CHECK_INDEX,
    FTM_TX_PC_UART32_INDEX = 0x15, 
    FTM_TX_PC_BT_CONNECT_INFO_INDEX = 0x1A,
    FTM_TX_PC_BT_SPEED_INDEX ,//蓝牙速率测试
    FTM_TX_PC_SPI_INDEX = 0x20,
    
    FTM_TX_PC_GPS_ANT_SHORT_INDEX = 0x26,
    FTM_TX_PC_GPS_ANT_OPEN_INDEX,
    FTM_TX_PC_BAT_TEMP_INDEX,//电池温度测试
    FTM_TX_PC_WAKE_UP_SOURCE = 0x2C,
    FTM_TX_PC_PCBASN_WRITE_INDEX = 0x2D,
    FTM_TX_PC_TBOXID_WRITE_INDEX = 0x2f,
    FTM_TX_PC_SUPPLIERID_WRITE_INDEX,
    FTM_TX_PC_TBOXNO_WRITE_INDEX,
    FTM_TX_PC_SIMNUMBER_WRITE_INDEX,
    FTM_TX_PC_LTE_ANT_OPEN_INDEX  = 0x3a,
    FTM_TX_PC_LTE_ANT_SHORT_INDEX = 0x3b, 
    FTM_TX_PC_MIC_OPEN_INDEX = 0x3c,
    FTM_TX_PC_MIC_SHORT_INDEX = 0x3d,
    FTM_TX_PC_NVAIGATE_TEST = 0x3e,
    FTM_TX_PC_LIN_INDEX = 0x3F,
    FTM_TX_PC_READINFO_INDEX = 0x43,
    FTM_TX_PC_NAVIGATE_WRITE_RESULT_INDEX = 0x45,
    FTM_TX_PC_SOFT_VERSION_INDEX = 0x47,
    FTM_TX_PC_HARD_VERSION_INDEX = 0x48,
    FTM_TX_PC_BOOTLOADER_VERSION_INDEX = 0x49,
    FTM_TX_PC_ECU_NAME_INDEX = 0x4A,
    FTM_TX_PC_BLE_MAC_INDEX = 0x53,
    FTM_TX_PC_MODULE_WAKEUP_PIN_INDEX = 0x54,
    FTM_TX_PC_KL30_INDEX = 0x5F,   /*读取KL30的电压值*/
    MCU_PC_GPS_ANT_NORMAL_INDEX = 0x60,   /*GPS天线正常*/
    MCU_PC_LTE_ANT_NORMAL_INDEX = 0x61,   /*LTE天线正常*/
    MCU_PC_SPAREPARTNUMBER_INDEX = 0x62,   /*20220312,zxl,零件号写入结果*/

    MCU_PC_PROJECT_INDEX         = 0x63,   /*写入项目名(车型)结果*/
    MCU_PC_ICCID_INDEX           = 0x64,   /*写入ICCID结果*/
    MCU_PC_ECUMANUFACTURINGDATE_INDEX  = 0x65,   /*写入软件生产结果*/
    MCU_PC_ECUINSTALLATIONDATE_INDEX   = 0x66,   /*写入软件更新结果*/
    FTM_TX_PC_MAX_INDEX,
}FtmTxMcuToPcIndex;

typedef enum
{
    FTM_TX_DUT_ECALL_INDEX = 1,
    FTM_TX_DUT_AIRBAG_INDEX,
    FTM_TX_DUT_ON_INDEX,
    FTM_TX_DUT_ACC_INDEX,
    FTM_TX_DUT_DIGIT_INPUT_INDEX,
    FTM_TX_DUT_DIGIT_OUTPUT1_INDEX,
    FTM_TX_DUT_DIGIT_OUTPUT2_INDEX,
    FTM_TX_DUT_HU_INDEX,
    FTM_TX_DUT_LED_GPS_INDEX,
    FTM_TX_DUT_LED_ANTITHEFT_INDEX,
    FTM_TX_DUT_LED_ECALL_INDEX,

    FTM_TX_DUT_MIC_INDEX = 0x11,
    FTM_TX_DUT_LINE_INDEX,
    FTM_TX_DUT_SPEAKER_INDEX,
    FTM_TX_DUT_CHARGE_INDEX,
    FTM_TX_DUT_SYSTEM_SLEEP_INDEX,
    FTM_TX_DUT_WAKEUP_BT_INDEX,
    FTM_TX_DUT_WAKEUP_RTC_INDEX,
    FTM_TX_DUT_WAKEUP_CAN3_INDEX,
    FTM_TX_DUT_WAKEUP_CAN2_INDEX,
    FTM_TX_DUT_WAKEUP_CAN4_INDEX,
    FTM_TX_DUT_WAKEUP_CAN5_INDEX,
    FTM_TX_DUT_UPLOAD_INDEX,//PC-->DUT  上报采集电压
    FTM_TX_DUT_GPSANT_OPEN = 0x1d,
    FTM_TX_DUT_GPSANT_SHORT = 0x1e,
    FTM_TX_DUT_LTEANT_OPEN = 0x21,
    FTM_TX_DUT_LTEANT_SHORT = 0x22,
    FTM_TX_DUT_MIC_OPEN = 0x23,
    FTM_TX_DUT_MIC_SHORT = 0x24,
    FTM_TX_DUT_WAKEUP_ECALL_INDEX = 0x28,
    FTM_TX_DUT_LED_TEST_INDEX = 0x2A,
    FTM_TX_DUT_CAN2_INT_TEST_INDEX = 0x2C,
    FTM_TX_DUT_CAN3_INT_TEST_INDEX,
    FTM_TX_DUT_CAN4_INT_TEST_INDEX,
    FTM_TX_DUT_CAN5_INT_TEST_INDEX,
    FTM_TX_DUT_MAX_INDEX,
}FtmTxMcuToDutIndex;

typedef enum
{
    FTM_TX_ARM_MODE_INDEX = 1,
    FTM_TX_ARM_MIC_INDEX =0x09,
    FTM_TX_ARM_LINE_INDEX,
    FTM_TX_ARM_SPEAKER_INDEX,
    FTM_TX_ARM_WAKEUP_SOURCE_INDEX = 0x12,
    FTM_TX_ARM_SLEEP = 0x13 , //通知睡眠
    FTM_TX_ARM_MODIFY_WIFI = 0x14,//通知ARM修改wifi信息
    FTM_TX_ARM_NAVIGATE_TEST = 0x22,
    FTM_TX_ARM_SET_MODULE_WAKEUP_PIN_INDEX = 0x2E,
    FTM_TX_ARM_MAX_INDEX,
}FtmTxMcuToArmIndex;



typedef struct
{
    FtmRxMcuIndex index;
    pIpcRxCallBack FtmMcuCallBack;
}FtmRxMcuInfo;


typedef struct messageRxCanFtmInfo           
{
    MessageRxCanFtmIndex   index;              //消息索引值
    uint16_t                 id;                 //消息ID
    FixedFlag              fixedFlag;          //消息是否定长
    MessageAttribute       attribute;          //消息属性
    pIpcRxCallBack         FtmRxCallBack;   //消息接收回调函数
} MessageRxFtmInfo;

typedef struct ipcCanTx
{
    uint16_t len;
    uint8_t  buffer[FTM_MAX_SIZE_LEN];
}CanIpcTx;

typedef struct ipcCanRx
{
    uint16_t len;
    uint8_t  buffer[FTM_MAX_SIZE_LEN];
}CanIpcRx;

typedef enum
{
    FTM_MODE_EXIT = 0,
    FTM_MODE_ENTER,
    FTM_MODE_WAKEUP,
    FTM_MODE_SEND_SEED,
    FTM_MODE_CALC_KEY,
}ModeStatus;

typedef enum
{
    FTM_SUCCESS = 0,
    FTM_FAILED,
}FtmExcuteResult;

typedef enum
{
    FTM_ECALL_KEY_PRESS = 0,
    FTM_ECALL_KEY_RELEASE,
    FTM_ECALL_FAUTL,
}FtmEcallButton;

typedef enum
{
    SEMI_FINISHED_PRODUCT = 0,
    FULL_FINISHED_PRODUCTS,
}FtmTestStage;


typedef enum
{
    FTM_CMD_HIGH = 0,
    FTM_CMD_LOW,
}FtmLevelCmd;

typedef enum
{
    FTM_NULL_WAKEUP = 0,
    FTM_RTC_WAKEUP,
    FTM_CAN_WAKEUP,
    FTM_ECALL_WAKEUP,
    FTM_BT_WAKE_UP,
    FTM_NOT_WAKEUP_TEST,
    FTM_NOT_WAKEUP_BT_SIGNAL,
}FtmWakeupType;

typedef enum
{
    FTM_ALIVE_WAKE_INIT_BIT     = 0x00,
    FTM_ALIVE_WAKE_BLE_BIT      = 0x01,
    FTM_ALIVE_WAKE_RTC_BIT      = 0x02,
    FTM_ALIVE_WAKE_SENSOR_BIT   = 0x04,
    FTM_ALIVE_WAKE_CAN1_BIT     = 0x08,
    FTM_ALIVE_WAKE_CAN2_BIT     = 0x10,
    FTM_ALIVE_WAKE_CAN3_BIT     = 0x20,
    FTM_ALIVE_WAKE_CAN4_BIT     = 0x40,
    FTM_ALIVE_WAKE_CAN5_BIT     = 0x80,
}FtmAliveWakeBit;

typedef enum
{
    FTM_INS_DRIVE_FORWARD_TESTING  = 0x01,
    FTM_INS_DRIVE_FORWARD_TESTED   = 0x02,
    FTM_INS_DRIVE_BACKWARD_TESTING = 0x04,
    FTM_INS_DRIVE_BACKWARD_TESTED  = 0x08,
    FTM_INS_DRIVE_TEST_STARTED = 0x10,
    FTM_INS_DRIVE_SUCCESS = 0x20,
    FTM_INS_DRIVE_FAIL    = 0x40,
}FtmINSTest;

typedef void (*ftmTestCallBack)(uint8_t index, uint8_t *buf, uint8_t len);

typedef struct canFtmInfo
{
    AckFlag ackFlag;                          //是否需要应答标志
    uint16_t  retransmissionTimeout;            //超时重传时间
    uint16_t  retransmissionCount;              //超时重传次数
    RxStatus rxStatus;                        //IPC UART串口接收数据状态
    uint16_t  txTid;                            //发送流水号
    uint16_t  rxTid;                            //接收流水号
    CanIpcRx   ipcRx;                         //从ARM接收的长度和数据  
    CanIpcTx   ipcTx;                         //发送给ARM长度和数据
    FtmWakeupType  wakeupType;                //ftm唤醒类型
    ModeStatus     ftmMode;                   //ftm模式
    FtmTestStage   testStage;                 //ftm测试阶段
    uint8_t          ftmCurrentTxIndex;         //ftm当前发送的index测试值
    uint8_t          ftmCurrentRxIndex;         //ftm当前接收的index测试值
    ftmTestCallBack ftmTestCbk;               //ftm测试时使用的回调函数
    boolean        ftmLedTestEvent;
    FtmAliveWakeBit   aliveWakeBit;
    boolean           aliveWakeEvent;
    boolean           armEnterFtm;            /*ARM是否进入装备模式，若ARM进入装备模式，则为TRUE*/
    FtmINSTest        ftmINSTest;             /*惯导测试方案采用后台测试方案，进入装备模式后，主动开始测试，PC下发该测试项时仅查询结果即可*/
} FtmInfo;

typedef enum
{
    MESSAGE_ID_RX_ARM_MCU = 0,
    MESSAGE_ID_RX_ARM_PC,
    MESSAGE_ID_RX_ARM_DUT,
    MESSAGE_ID_RX_UARTFTM_MAX,
} MessageRxUartFtmIndex;

/************************函数接口***************************/
void FtmTxMsgArmToPc(uint8_t *para, uint16_t len);
void FtmMainRxMsgFunction(void);
void FtmTxPcCanResult(uint8_t channel,uint8_t *buf);
void FtmCanPeriodFunction(void);
void FtmUartPeriodFunction(void);
void FtmCanIsrRxFunction(uint8_t len, uint8_t *buf);
void FtmMcuTxToPcResult(uint8_t index, uint8_t result);
ipcErrorCode FtmCanTxPackageFrameData(Msg msg);

FtmInfo *FtmInitRead(void);
void FtmInitRamData(void);
void FtmRxMcuReadModuleWakeUpPin(uint8_t* para, uint16_t len);
void FtmNavigateTestModeSet(uint8_t value);
void FtmRxLinResponse(uint8_t* data);
void FtmWakeupTestIntHandle(FtmAliveWakeBit wakeBit);


#endif

