/*
      CanTask.h
描述：定义CAN TASK回调函数
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _CAN_TASK_H_
#define  _CAN_TASK_H_

#include "appqueue.h"

#define CAN_TASK_STACK_SIZE 768
#define CAN_TASK_PRIORITY   4

extern TaskHandle_t CANTask_Handle;

void StartCANTask(void);
void CANTask(void* param);





/************************函数接口***************************/
void CanTaskInitHook(void);
void CanTaskPostInitHook(void);
void CanTaskPeriodHook(void);
void CanTaskFunctionHook(Msg msg);
#endif

