/*
 * RemoteControlTask.h
 *
 * 远程控制任务，用于处理车辆锁车和限速功能
 */
#ifndef REMOTE_CONTROL_TASK_H
#define REMOTE_CONTROL_TASK_H

#include <stdint.h>

/* 远程控制的CAN ID */
#define REMOTE_CONTROL_FEEDBACK_ID 0x1802A709 // 用于接收车辆控制反馈的ID

/* 锁车/解锁的车辆反馈值 */
#define VEHICLE_ALREADY_LOCKED   2
#define VEHICLE_ALREADY_UNLOCKED 3
#define VEHICLE_UNLOCK_COMPLETED 5
#define VEHICLE_LOCK_COMPLETED   6

/* 限速的车辆反馈值 */
#define VEHICLE_NO_SPEED_LIMIT     0
#define VEHICLE_SPEED_LIMIT_ACTIVE 3

/* 反馈队列的消息结构 */
typedef struct
{
    uint8_t lockStatus;       // Byte1 - 锁车状态
    uint8_t speedLimitStatus; // Byte2 - 限速状态
} RemoteControlFeedbackMsg;

/* 队列参数 */
#define REMOTE_CONTROL_QUEUE_SIZE 10

/* 函数原型声明 */
void RemoteControlTask_Init(void);
void RemoteControlTask_Process(void);
void RemoteControlTask_SendFeedback(uint8_t lockStatus, uint8_t speedLimitStatus);
void ProcessVehicleControlFeedback(uint8_t lockStatus, uint8_t speedLimitStatus);
void RemoteControlMsgInit(void);
void RemoteControlEnterSleepHandle(void);
void RemoteControlWakeupHandle(void);

#endif /* REMOTE_CONTROL_TASK_H */
