/*
      CanApi.h
描述：定义Can任务具体业务功能头文件，待实现
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _CAN_API_H_
#define  _CAN_API_H_

#include <stdint.h>
#include "Platform_Types.h"
#include "appqueue.h"
#include "event.h"
#include "r_can.h"

/************************宏定义***************************/
#define     CAN_PACKAGE_DATA_LEN                15
#define     CAN_PACKAGE_DATA_NUM                20

#define     REMOTE_CONTROL_MASK_NUM             10
#define     REMOTE_CONTROL_MAX_CMD_LEN          64
#define     REMOTE_CONTROL_TX_CAN_CMD_LEN       10
#define     REMOTE_CONTROL_TIMEOUT_WAIT_TICK   300   /*如果当前远控需要执行较长时间，需MCU及时通知ARM，由ARM更新超时信息*/
#define     REMOTE_CONTROL_SEND_CNT_MAX          5
#define     PEPS_AUTH_CAN_FRAME_LEN              8
#define     PEPS_AUTH_CAN_TIMEOUT               50   /*peps认证超时时间 10*50 = 500ms*/
#define     PEPS_AUTH_CAN_OPTYPE              0x00
#define     ECU_PERIOD_50MS_TIMEOUT             25   /*25*10ms*/
#define     ECU_PERIOD_100MS_TIMEOUT            50   /*50*10ms*/
#define     CALLING_PARKING_TIMEOUT             600  /*600*10, 6s超时后结束召唤动作*/
#define     VOLUNTARY_POWER_SHUTDOWN_TIME_INIT  (540000)  /*开高压后主动下电倒计时初始时间90min(1.5h)，函数执行周期是10ms*/
#define     PEPS_REPONSE_FRAME_LEN              8

#define SWITCH_OFF        0
#define SWITCH_ON         1
#define LOG_TEST_SWITCH   1

#define MCU_OK                  0
#define MCU_ERROR               -1
#define MCU_PARA_ERROR          -2
#define MCU_BUSY                -3
#define MCU_PENDING             -4
#define MCU_NOT_SUPPORT         -5
#define MCU_QUEUE_FULL          -6
#define MCU_QUEUE_EMPTY         -7
#define MCU_TIME_OUT            -8
#define MCU_MALLOC_FAIL         -9
#define MCU_CREATE_TASK_FAIL    -10
#define MCU_CREATE_QUEUE_FAIL   -11
#define MCU_CREATE_MUTEX_FAIL   -12
#define MCU_INIT_UART_FAIL      -13
#define MCU_IPC_SEND_FAIL       -14
#define MCU_CRC_ERROR           -15
#define MCU_OUT_OF_RANGE        -16
#define MCU_NOT_EXIST           -17
#define MCU_READ_FILE_FAIL      -18
#define MCU_WRITE_FILE_FAIL     -19
#define MCU_BUF_NOT_ENOUGH      -20
#define MCU_IPC_NOT_CONNECT     -21
#define MCU_APN_CREATE_FAIL     -22
#define MCU_APN_DIAG_FAIL       -23
#define MCU_APN_ALREADY_CREATED -24
#define MCU_APN_PROFILE_FAIL    -25
#define MCU_AT_CMD_FAIL         -26
#define MCU_CREATE_TIMER_FAIL   -27
#define MCU_CREATE_FILE_FAIL    -28
#define MCU_SIM_NOT_READY       -29
#define MCU_START_NAVI_FAIL     -30
#define MCU_NAVI_PARA_FAIL      -31

#define CAN_ALL_DATA_LENGTH     8
#define CAN_DATA_MERGE_MAX_NUM  10
#define CAN_INVALID_CAN_ID    0xFFFFFF
#define SystemTick_t      uint64_t
#define SystemTime_t      uint64_t

#define CAN_DATA_LENGTH         8
#define CAN_SEND_ID_MAX_NUM     13 /* 支持的最大CAN报文数量(数量+1) */
#define CAN_SEND_MAX_DATA_COUNT 8  /* 一轮最多发送的数据条数，驾驶行为分析报文需要6条 */

// 配置广播报文
// 时间及终端状态报文
#define CAN_SEND_ID_1       0x180AA7A3
#define CAN_SEND_CH_1       CAN_CHANNEL_2
#define CAN_SEND_TYPE_1     CAN_TYPE_CYCLE
#define CAN_SEND_INTERVAL_1 500 // 500 ms
#define CAN_SEND_DFT_DATA_1 0, 0, 0, 0, 0, 0, 0, 0

// 纬度报文
#define CAN_SEND_ID_2       0x180BA7A3
#define CAN_SEND_CH_2       CAN_CHANNEL_2
#define CAN_SEND_TYPE_2     CAN_TYPE_CYCLE
#define CAN_SEND_INTERVAL_2 500 // 500 ms
#define CAN_SEND_DFT_DATA_2 0, 0, 0, 0, 0, 0, 0, 0

// 经度报文
#define CAN_SEND_ID_3       0x180CA7A3
#define CAN_SEND_CH_3       CAN_CHANNEL_2
#define CAN_SEND_TYPE_3     CAN_TYPE_CYCLE
#define CAN_SEND_INTERVAL_3 500 // 500 ms
#define CAN_SEND_DFT_DATA_3 0, 0, 0, 0, 0, 0, 0, 0

// VIN码报文，周期 3S 发送 17 个字节的VIN 码信息，注：每包报文间隔时间 1s，发完三包共需 3s。
#define CAN_SEND_ID_4       0x1803A709
#define CAN_SEND_CH_4       CAN_CHANNEL_2
#define CAN_SEND_TYPE_4     CAN_TYPE_CYCLE
#define CAN_SEND_INTERVAL_4 1000 // 1000 ms
#define CAN_SEND_DFT_DATA_4 0, 0, 0, 0, 0, 0, 0, 0

// 企标远控外发报文配置


// 远程锁车和限速报文
#define CAN_SEND_ID_5       0x180109A7
#define CAN_SEND_CH_5       CAN_CHANNEL_2
#define CAN_SEND_TYPE_5     CAN_TYPE_EVENT
#define CAN_SEND_INTERVAL_5 500                       // 500 ms
#define CAN_SEND_DFT_DATA_5 0, 0xFF, 0, 0xFF, 0, 0, 0, 0 // Byte1=0xFF(不请求锁车解锁), Byte2=0(未远程限制)

// 天气报文 上电后先不外发，平台下发后再开始发
#define CAN_SEND_ID_6       0x181FA7A3
#define CAN_SEND_CH_6       CAN_CHANNEL_2
#define CAN_SEND_TYPE_6     CAN_TYPE_EVENT
#define CAN_SEND_INTERVAL_6 60000 // 1分钟
#define CAN_SEND_DFT_DATA_6 0, 0, 0, 0, 0, 0, 0, 0

// 驾驶行为分析报文
#define CAN_SEND_ID_7              0x180EA7A3
#define CAN_SEND_CH_7              CAN_CHANNEL_2
#define CAN_SEND_TYPE_7            CAN_TYPE_CYCLE // 改为周期型，上电即发送
#define CAN_SEND_SIGNAL_INTERVAL_7 10             // 10ms间隔
#define CAN_SEND_INTERVAL_7        60000          // 1分钟循环
#define CAN_SEND_DFT_DATA_7        0, 0, 0, 0, 0, 0, 0, 0

// 历史能耗排名（分时）报文
#define CAN_SEND_ID_8              0x180FA7A3 // 第一帧
#define CAN_SEND_ID_8_1            0x1810A7A3 // 第二帧
#define CAN_SEND_ID_8_2            0x1811A7A3 // 第三帧
#define CAN_SEND_ID_8_3            0x1812A7A3 // 第四帧
#define CAN_SEND_ID_8_4            0x1813A7A3 // 第五帧
#define CAN_SEND_CH_8              CAN_CHANNEL_2
#define CAN_SEND_TYPE_8            CAN_TYPE_CYCLE // 改为周期型，上电即发送
#define CAN_SEND_SIGNAL_INTERVAL_8 10             // 10ms间隔
#define CAN_SEND_INTERVAL_8        600000         // 10分钟循环
#define CAN_SEND_DFT_DATA_8        0, 0, 0, 0, 0, 0, 0, 0

// 历史能耗排名（分日）报文
#define CAN_SEND_ID_9              0x1814A7A3 // 第一帧
#define CAN_SEND_ID_9_1            0x1815A7A3 // 第二帧
#define CAN_SEND_ID_9_2            0x1816A7A3 // 第三帧
#define CAN_SEND_ID_9_3            0x1817A7A3 // 第四帧
#define CAN_SEND_ID_9_4            0x1818A7A3 // 第五帧
#define CAN_SEND_ID_9_5            0x1824A7A3 // 第六帧
#define CAN_SEND_CH_9              CAN_CHANNEL_2
#define CAN_SEND_TYPE_9            CAN_TYPE_CYCLE // 改为周期型，上电即发送
#define CAN_SEND_SIGNAL_INTERVAL_9 10             // 10ms间隔
#define CAN_SEND_INTERVAL_9        600000         // 10分钟循环
#define CAN_SEND_DFT_DATA_9        0, 0, 0, 0, 0, 0, 0, 0

// 历史能耗排名（分月）报文
#define CAN_SEND_ID_10              0x1819A7A3 // 第一帧
#define CAN_SEND_ID_10_1            0x181AA7A3 // 第二帧
#define CAN_SEND_ID_10_2            0x181BA7A3 // 第三帧
#define CAN_SEND_ID_10_3            0x181CA7A3 // 第四帧
#define CAN_SEND_ID_10_4            0x181DA7A3 // 第五帧
#define CAN_SEND_ID_10_5            0x1825A7A3 // 第六帧
#define CAN_SEND_CH_10              CAN_CHANNEL_2
#define CAN_SEND_TYPE_10            CAN_TYPE_CYCLE // 改为周期型，上电即发送
#define CAN_SEND_SIGNAL_INTERVAL_10 10             // 10ms间隔
#define CAN_SEND_INTERVAL_10        600000         // 10分钟循环
#define CAN_SEND_DFT_DATA_10        0, 0, 0, 0, 0, 0, 0, 0

// 历史能耗排名（分年）报文
#define CAN_SEND_ID_11              0x1826A7A3 // 第一帧
#define CAN_SEND_ID_11_1            0x1827A7A3 // 第二帧
#define CAN_SEND_CH_11              CAN_CHANNEL_2
#define CAN_SEND_TYPE_11            CAN_TYPE_CYCLE // 改为周期型，上电即发送
#define CAN_SEND_SIGNAL_INTERVAL_11 10             // 10ms间隔
#define CAN_SEND_INTERVAL_11        600000         // 10分钟循环
#define CAN_SEND_DFT_DATA_11        0, 0, 0, 0, 0, 0, 0, 0

// 车辆能耗排行榜报文
#define CAN_SEND_ID_12              0x1820A7A3 // 第一帧
#define CAN_SEND_ID_12_1            0x1821A7A3 // 第二帧
#define CAN_SEND_CH_12              CAN_CHANNEL_2
#define CAN_SEND_TYPE_12            CAN_TYPE_CYCLE // 改为周期型，上电即发送
#define CAN_SEND_SIGNAL_INTERVAL_12 10             // 10ms间隔
#define CAN_SEND_INTERVAL_12        600000         // 10分钟循环
#define CAN_SEND_DFT_DATA_12        0, 0, 0, 0, 0, 0, 0, 0

typedef enum
{
    BROADCAST_MSG_INDEX0 = 0,     // 0x180AA7A3 广播报文0
    BROADCAST_MSG_INDEX1,         // 0x180BA7A3 广播报文1
    BROADCAST_MSG_INDEX2,         // 0x180CA7A3 广播报文2
    BROADCAST_MSG_INDEX3,         // 0x1803A709 广播报文3
    REMOTE_LOCK_SPEED_INDEX = 4,  // 远程锁车和限速
    WEATHER_INFO_INDEX,           // 天气信息
    DRIVING_BEHAVIOR_INDEX,       // 驾驶行为分析
    HOURLY_RANK_INDEX,           // 分时能耗排名
    DAILY_RANK_INDEX,            // 分日能耗排名
    MONTHLY_RANK_INDEX,          // 分月能耗排名
    YEARLY_RANK_INDEX,           // 分年能耗排名
    VEHICLE_RANK_INDEX           // 车辆能耗排行榜
} ExternalMsgGroupIndex_en;

#define CAN_DTC_DEFAULT   0
#define CAN_DTC_LEVEL_1   1
#define CAN_DTC_LEVEL_2   2
#define CAN_DTC_LEVEL_3   3

#define INI_1E0COUNTER  0
#define SET_1E0COUNTER  1

#define INI_COUNTER  0
#define SET_COUNTER  1
#define GET_COUNTER  2

#define ECALL_CONTROL_MAX_CMD_LEN           2
#define ECALL_CONTROL_TX_CAN_CMD_LEN        10

#define CAN_DATA_MAX_LENGTH         8
#define CAN_NM_TABLE_VALID_MAX_NUM  50   /*This Value can modify, the max number is 125*/
#define CAN_DIAG_FUNCTION_ADDR      0x7DF

#define CAN_TP_RX_DIAG_MYSELF       0x0B
#define CAN_TP_RX_DIAG_OTHER        11//0x01

#define CAN_TP_TX_DIAG_MYSELF       0x03
#define CAN_TP_TX_DIAG_OTHER        0x00

#define CAN_PROTOCOL_LOW_TASK_MIN_PERIOD         1   /*ms*/
#define CAN_PROTOCOL_LOW_TASK_SYSTEM_PERIOD     10   /*ms*/

#define DIAG_CANID_MASK     0x700
#define DIAG_EXT_CANID_MAX  0x18D00000

/*函数调用周期为10ms，因此时间实际为500*10 = 5000ms = 5s*/
/*昌河OBD OSEK标准要求:正常建环4~5s后允许释放网络，进入Limphome4~5s后允许释放网络，网络释放4~6s后允许关闭CAN通道，因此取5s时间覆盖上述三个时间要求*/
#define OSEK_NM_USER_HANDLE_TIME       (520)
#define OSEK_NM_USER_HANDLE_TIME_DELAY (100)

#define OSEK_NM_CANID_MIN    0x400
#define OSEK_NM_CANID_MAX    0x47F

#define CAN0_TP_TX_HRH   0
#define CAN0_NM_TX_HRH   1
#define CAN1_TP_TX_HRH   2
#define CAN1_NM_TX_HRH   3
#define CAN0_TP_RX_ID    1
#define CAN1_TP_RX_ID    2

// Dcm_TboxUdsProtocol_TboxUdsConnection_RxCfg
#define CAN_PHY_DCM_RX_PDU_ID  0
#define CAN_FUN_DCM_RX_PDU_ID  1

#define CAN_TP_RX_DATA_MAX_LENGTH  250
#define CAN_TP_TX_DATA_MAX_LENGTH  2100//544

#define DIAG_QUEUE_LENGTH          256
#define DIAG_QUEUE_MSG_MAX_LEN     16

#define  OBD_DIAGREQUESTPHYADDR 0x98DB33F1
#define  OBD_DIAGRESPONSEADDR   0x98DAF100
#define  OBD_DIAGFCADDR         0x98DA00F1

#define DIAG_LOCAL     0
#define DIAG_REMODE    1
#define DIAG_OBD       2

#define CAN_PROTOCOL_LOW_TASK_MIN_PERIOD         1   /*ms*/
#define CAN_PROTOCOL_LOW_TASK_SYSTEM_PERIOD     10   /*ms*/


#define DIAG_CANID_MASK     0x700
#define DIAG_EXT_CANID_MAX  0x18D00000

/*函数调用周期为10ms，因此时间实际为500*10 = 5000ms = 5s*/
/*昌河OBD OSEK标准要求:正常建环4~5s后允许释放网络，进入Limphome4~5s后允许释放网络，网络释放4~6s后允许关闭CAN通道，因此取5s时间覆盖上述三个时间要求*/
#define OSEK_NM_USER_HANDLE_TIME       (520)
#define OSEK_NM_USER_HANDLE_TIME_DELAY (100)

#define OSEK_NM_CANID_MIN    0x400
#define OSEK_NM_CANID_MAX    0x47F

#define CAN0_TP_TX_HRH   0
#define CAN0_NM_TX_HRH   1
#define CAN1_TP_TX_HRH   2
#define CAN1_NM_TX_HRH   3
#define CAN0_TP_RX_ID    1
#define CAN1_TP_RX_ID    2

// Dcm_TboxUdsProtocol_TboxUdsConnection_RxCfg
#define CAN_PHY_DCM_RX_PDU_ID  0
#define CAN_FUN_DCM_RX_PDU_ID  1

#define CAN_TP_RX_DATA_MAX_LENGTH  250
#define CAN_TP_TX_DATA_MAX_LENGTH  2100//544

#define DIAG_QUEUE_LENGTH          256
#define DIAG_QUEUE_MSG_MAX_LEN     16

#define  OBD_DIAGREQUESTPHYADDR 0x98DB33F1
#define  OBD_DIAGRESPONSEADDR 0x98DAF100
#define  OBD_DIAGFCADDR 0x98DA00F1

#define DIAG_LOCAL     0
#define DIAG_REMODE    1
#define DIAG_OBD       2

/************************数据结构定义***************************/
typedef struct
{
    unsigned int  id;
    unsigned char channel;
    unsigned char len;
    unsigned char data[CAN_DATA_MAX_LENGTH];
}CanMsg_s;

/*LogDebug funcion type*/
typedef void(*SdkLogPrintfCallBack_t)(unsigned char level, const char *fmt,...);

/*Normal CAN Frame function type*/
typedef void (*pCanNormalMsgSendCallBack_t)(CanMsg_s *canMsg);

/*NM CAN Frame function type*/
typedef void (*pCanNmMsgSendCallBack_t)(unsigned char ch, unsigned int canId, unsigned char dlc, unsigned char *dataBuf);

/*Diag CAN Data function type*/
typedef void (*pCanDiagDataSend_t)(unsigned char ch, unsigned int canId, unsigned char dlc, unsigned char *dataBuf);

/*Remote Diag Reponse  CAN Data function type*/
typedef void (*pCanReDiagReponseDataSend_t)(unsigned char dlc, unsigned char *dataBuf);

/*Diag Contrller callback function type. controlllerIndex为0~4, startOrStop:0--stop, 1--start*/
typedef void (*pCanControllerCallBack_t)(unsigned char controlllerIndex, unsigned char startOrStop);

typedef enum
{
    HANDLE_REQUEST_INIT = 0x00,
    HANDLE_REQUEST_ALIVE,
    HANDLE_REQUEST_RELEASE,
    HANDLE_REQUEST_CAN_DISABLE,
    HANDLE_REQUEST_CAN_ENABLE,
}OsekNm_HandleType;

typedef enum
{
    CAN_UDS_NO_INIT = 0,
    CAN_UDS_INIT,
}CanUdsStatus;

typedef struct
{
    char   writePos;
    char   readPos;
    uint8_t  spacePos;
    uint32_t countFromArm;
    uint32_t countMcuOut;
    uint8_t  CanRxFromArm[CAN_PACKAGE_DATA_NUM][CAN_PACKAGE_DATA_LEN];  /*用一个二维数组来做can发送缓冲区*/
}CanDataArmStr;

typedef enum
{
    CAN_TYPE_EVENT,
    CAN_TYPE_CYCLE,
}CanSendType_en;

typedef enum
{
    DATA_MERGE_NONE,
    DATA_WAIT_MERGE,
    DATA_WAIT_AND_MERGE,
    DATA_MERGE_LAST,
}DataMergeInfo_en;

typedef enum
{
    CAN_INTEL = 1,
    CAN_MOTOROLA_LSB,
    CAN_MOTOROLA_MSB,
    CAN_MOTOROLA_SEQ,
}CanByteOrder_en;

typedef enum
{
    BOARD_CAN1 = 1,
    BOARD_CAN2,
    BOARD_CAN3,
    BOARD_CAN4,
    BOARD_CAN5,
    BOARD_CAN_ALL,
} BoardCanChannel;

typedef enum
{
    BOARD_CAN_NO_BUSOFF = 0,
    BOARD_CAN_BUSOFF,
} BoardCanBusOffState_en;

typedef enum
{
    CTRL_DATA_RMTCTRL_CMD = 1,
    CTRL_DATA_RMTCTRL_RES,
    CTRL_DATA_AUTH_REQ,
    CTRL_DATA_AUTH_RES,
    CTRL_DATA_CHECK_BUSY,
    CTRL_DATA_BUSY_RES,
}CtrlDataType_en;

typedef enum
{
    SET_3D5COUNTER ,
    GET_3D5COUNTER ,
}Counter3D5Type;

typedef struct
{
    uint32_t canId;        // CAN ID，多条时，可以相同，也可以不同
    uint8_t dlc;           // 数据长度
    uint8_t canData[CAN_DATA_LENGTH]; // 发送数据，可以一轮发送多条数据
}SendDataInfo_s;

typedef struct
{
    boolean      sendEnable; //事件型表示是否发送，周期型表示里面的事件数据是否发送
    uint16_t     sendCount;  //发送剩余次数，仅针对事件型数据，事件型数据同一时间同一ID只发送一个，值为65535时表示一直发送
    uint8_t      timeOutCnt; //超时次数，当有条件限制不能发送时，最多等待次数:sendCount * 3
    uint8_t      canCh : 4;  //CAN通道
    uint8_t      byteOrder : 4;  //数据排列格式
    uint8_t      sendType;       //event or cycle等，事件型上电或唤醒后不发，等ARM更新后再发
    uint8_t      canCount;       //sendData中有多少条CAN数据，为0也表示有1条数据（兼容以前的业务应用）
    uint8_t      canIndex;       //下一条应该发送sendData中第几条数据，canCount大于1有效
    boolean      needWaitGroup;  //true:需要等待组间间隔
    uint16_t     signalInterval; //单轮数据内的时间间隔，canCount大于1有效
    uint32_t     totalInterval;  //总间隔时间，单位ms，即一轮发完后，下一轮隔多久再发
    SystemTime_t lastTick;       //上次发送时间
    SendDataInfo_s sendData[CAN_SEND_MAX_DATA_COUNT]; //发送数据，可以一轮发送多条数据
    uint8_t        canDataBak[CAN_DATA_LENGTH];       //备份事件型数据前的发送数据
    uint8_t        defaultData[CAN_DATA_LENGTH];
    uint8_t        bakIndex; //备份的是第几条数据（下标）
} CanSendDataInfo_s;

//PS：使用本文件中的接口发送merge数据时，可能有很小的概率会因为cnt改变而发送失败
typedef struct
{
    uint32_t canId;
    uint32_t canCh;
    uint8_t  sendType;
    uint16_t sendCnt;
    uint8_t  startBit;
    uint8_t  bitLen;
    //针对同时发送多个事件型CAN数据的情况，更新第二个时，将merge设为TRUE
    //注意，第二个与第一个的发送属性必须相同，如发送次数
    uint8_t merge;
    //当需要merge两条数据时，如果组装和发送分别在ARM和MCU端完成，则第二条下发前，第一条有可能已经开始发送
    //为避免此情况，增加waitMerge变量，在第一条中设定，该变量只在组装发送分开实现的情况下有用
    uint8_t  waitMerge;
    uint32_t data;
} CanUpdDataInfo_s;

typedef struct
{
    uint8_t sendType;
    uint8_t startBit;
    uint8_t bitLen;
    uint8_t mergeInfo;  //DataMergeInfo_en
    uint32_t data;
}PassSingle_s;

#pragma pack(1)
typedef struct
{
    uint8_t  dataType; //CtrlDataType_en
    uint8_t  canCh;
    uint16_t sendCnt;
    uint8_t  single;
    uint32_t canId;
    // 如果一组内的CANID有相同的，指示用第几个，默认为0的话只搜索第一个
    uint8_t  canIdIndex;
    union
    {
        PassSingle_s singleData;
        uint8_t      allData[CAN_DATA_LENGTH];
    } u;
} CanSendPassInfo_s;
#pragma pack()

#pragma pack(1)
typedef struct
{
    uint8_t dataType;
    uint8_t canCh;
    uint8_t reserved[2];
    uint32_t canId;
}CanCheckBusyInfo_s;
#pragma pack()

typedef enum
{
    OSEKNM_INIT_MODE = 0x00,
    OSEKNM_NORMAL_MODE,
    OSEKNM_LIMPHOME_MODE,
}Nm_Mode;

typedef struct
{
    Nm_StateType Nm_State;
    unsigned long aliveTimeStamp;
}SysNmInfo;

typedef void (*OsekNm_UserHandleCbk) (void);

typedef struct
{
    OsekNm_HandleType    readyHandle;
    OsekNm_UserHandleCbk cbk;
    unsigned short timeRemain;
    unsigned char  handleEvent;
    unsigned char  Started;
    unsigned char  requested;
    unsigned char  busSleeped;
    Nm_Mode   nmStatus;    /*0x00: 初始化， 0x01:正常模式， 0x02:LimpHome模式*/
    SysNmInfo NmInfo;
    pCanNmMsgSendCallBack_t canSendCallBack;
    unsigned int  canNmValidTable[CAN_NM_TABLE_VALID_MAX_NUM];
    unsigned char canNmValidTableNum;
    unsigned int  canNmSelfAddr;   /*OSEK NM Self Net Addr*/
    unsigned char canNmChannel;    /*OSEK NM CAN Bus Channel*/
}OsekNm_UserHandleType;

/************************函数接口***************************/
void CanUdsInit(void);
void CanSendInfoInit(void);
void CanPeriodUdsHandle(void);
void CanPeriodNetHandle(void);
void Update3D1FrameData(void);
void CanEventFunction(Msg msg);
void CanSendPassIpcCb(Msg msg);
void CanTransmitDataMcuOut(void);
void CanTransmitDataFromArm(void);
void CanPeriodNotifyFunction(Msg msg);
void CanNetStatusNotifyFunction(WorkStatus status);
#ifdef CAN_ENABLE_OSEKNM
void OsekNetworkStatusSet(uint8_t sta);
void OsekNm_UserHandleInit(void);
void OsekNm_UserHandleEvenSet(OsekNm_HandleType type);
#else
void AutoNm_NetworkReleae(void);
void AutoNm_NetworkRequest(void);
void AutoNm_NetworkCanShutDown(void);
void AutoNm_NetworkCanEnable(void);
#endif

boolean CanCheckSendBusy(uint32_t canId, uint8_t canCh);
int8_t CanUpdateAuthResponse(uint32_t id,uint8_t* data,uint8_t datalen);
uint8_t CanGetCurentFailureLevel(void);
int ReadObdBoxConfig(uint8_t* data);
int CanUpdateSendData(CanUpdDataInfo_s *updData);
int CanSend(BoardCanChannel channel, uint32_t canId, uint8_t dlc, uint8_t *buf);
int CanUpdateAllData(uint32_t canId, uint8_t canCh, uint16_t sendCnt, uint8_t *data, uint8_t canIdIndex);
int CanProtocolSdkTpDataSend(uint8_t channel, uint32_t canId, uint64_t length, uint8_t *buf);
uint32_t DiagagosticFunctionReqestAddress(void);
uint32_t DiagagosticPhysicalRequestAddress(void);
uint32_t DiagagosticPhysicalResponseAddress(void);
SystemTick_t McuCalcOsTickDiff(SystemTick_t lastTick, SystemTick_t nowTick);
SystemTick_t McuCalcOsRevertTick(SystemTick_t curTick, SystemTick_t diffTick);
WorkStatus CanGetAccStatus(void);
BoardCanBusOffState_en BoardCanBusOffStateGet(BoardCanChannel channel);
int CanSetSendCountToZero(uint32_t canId, uint8_t canCh);
void CANAllExitStandby(void);
void CANAllEnterStandby(void);
void WakeupAllCANByCANIf(void);
#endif