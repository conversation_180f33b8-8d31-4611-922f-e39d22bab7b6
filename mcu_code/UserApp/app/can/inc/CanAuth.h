/*
      CanAuth.h
描述：此文件主要是Tbox与PEPS认证 具体实现的业务功能
作者：ben
时间：2019.6.11
*/


#ifndef  _CAN_AUTH_H_
#define  _CAN_AUTH_H_

#include "AppTask.h"
#include "IpcApi.h"

/************************函数接口***************************/
void AES_SubBytes(uint8_t state[4][4]);
void AES_ShiftRows(uint8_t state[4][4]);
uint8_t AES_FFmul(uint8_t a ,uint8_t b);
void AES_MixColumns(uint8_t state[4][4]);
void AES_AddRoundKey(uint8_t state[4][4],uint8_t k[4][4]);
void AES_KeyExpansion(uint8_t key1[4],uint8_t w[11][4][4]);
void AES_Cipher(uint8_t data[16]);

#endif  /* _CAN_MSG_API_H_ */
