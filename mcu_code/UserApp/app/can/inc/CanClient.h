/*
      CanClient.h
描述：此文件主要是ECU升级和诊断文件，包括ECU列表如下
      EVCAN(CAN1)  ---MCU
      CHCAN(CAN2)---ESP
      BDCAN(CAN3) ---IHU
      DCAN(CAN4) ---TBOX
作者：廖勇刚
时间：2018.05.21
*/

#ifndef  _CAN_CLIENT_H_
#define  _CAN_CLIENT_H_
#include "FreeRTOS.h"
#include "ComStack_Types.h"
#include "PduR_Dcm.h"
#include "AppTask.h"
#include "rtc.h"
#include "r_can.h"
#include "CanApi.h"




/************************宏定义***************************/
#define  TBOX_CLIENT_RX_BUFFER                 450
#define  TBOX_CLIENT_TX_BUFFER                 300


#define TBOX_PHYSICAL_REQUEST_ID                0x00000722
#define TBOX_FUNCTION_REQUEST_ID                0x000007DF
#define TBOX_RESPONSE_ID                        0x0000072A

/************************数据结构定义***************************/
typedef enum
{
    /*DCAN(CAN4)*/
    ECU_TYPE_TBOX,

    ECU_TYPE_MAX,
}EcuType;

typedef enum
{
    ECU_ID_PHY = 0,
    ECU_ID_FUN,        
}IdType_en;

typedef struct
{
    IdType_en idType;
    unsigned char channel;
    unsigned long txId;
    unsigned long rxId;
    unsigned char txData[CAN_TP_TX_DATA_MAX_LENGTH];
    unsigned char rxData[CAN_TP_RX_DATA_MAX_LENGTH];
    unsigned long txLen;
    unsigned long rxLen;
    unsigned char remoteDiagFlag;   // true:远程诊断中 false:无远程诊断
    unsigned char remoteDiagTBox;   // 是否是远程诊断T-Box true:是 false:远程诊断其他ECU
    unsigned char localDiagTBox;    // 是否是本地诊断T-Box
    uint32_t  reqcanId;             // 请求CAN id，最高位为1:扩展帧， 最高位为0:标准帧*/
    uint32_t  rescanId;             // 响应CAN id，最高位为1:扩展帧， 最高位为0:标准帧*/
} CanTpInfo;

typedef struct ecuIdInfo
{
    uint32_t requestPhy;
    uint32_t responsePhy;
    uint32_t requestFun;
}EcuIdInfo;

/************************函数接口***************************/
uint8_t CanRxPduHrhSearchValid(uint8_t hrhIdOfPdu, uint32_t canId);
uint8_t CanRxPduHrhSearch(uint8_t hrhIdOfPdu, uint32_t canId,  uint16_t *pduIdIndex);
void  CanClientTransmit(PduIdType CanTpTxSduId, PduInfoType  PduInfo);
void CanClientRxDiagFrame(uint8_t channel, CanPduInfo pduInfo);
void CanRxDiagFrameHandle(void);
void CanRemoteDiagEcuResponse(uint8_t channel, uint32_t canId,uint8_t *data,uint32_t len);

void CanDiagInitQueue(void);

#endif

