/*
      CanMsgApi.h
描述：定义CAN Message具体业务功能，
作者：sword zheng
时间：2016.12.6
*/

#ifndef  _CAN_MSG_API_H_
#define  _CAN_MSG_API_H_

#include "AppTask.h"
#include "IpcApi.h"
#include "r_can.h"
#include "osif.h"

/***************************宏定义***************************/
#define CAN_MSG_RT_INFO_LENGTH          (118)
#define CAN_DATA_CRC_TYPE               0       /* 0：CRC8 1：CRC32 默认使用CRC8，根据需要再切换CRC32 */ 

#if (CAN_DATA_CRC_TYPE == 0)
#define CAN_DATA_CRC_LEN                    1       /* CRC字节数 */
#elif (CAN_DATA_CRC_TYPE == 1)
#define CAN_DATA_CRC_LEN                    4       /* CRC字节数 */
#endif

#define CAN_FRAME_DATA_COUNT            8
#define CAN_LOG_FRAME_LENGTH            18
#define CAN_LOG_PACKAGE_LEN             144
#define CAN_LOG_ARM_LEN                 (CAN_LOG_PACKAGE_LEN + CAN_DATA_CRC_LEN + 1)   /* 数据位 + CRC位 + 帧头 = 146*/
#define CAN_LOG_MAX_COUNT               25
#define CAN_LOG_HEAD_POS                0
#define CAN_LOG_CRC_POS                 (CAN_LOG_PACKAGE_LEN + 1)  //145

#define TBOX_FIXEDCODE_LEN               16
#define TBOX_REPONSE_TOYAL_LEN           8
#define TBOX_RESPONSE_KEY_LEN            6

#define CAN_ANALYZE_ROW_COUNT               100
#define CAN_ANALYZE_ROW_COLUMN_COUNT        14

/*DCS*/
#define DCS_TIMEOUT_START           1
#define DCS_TIMEOUT_END             0
/*IC*/
#define IC_TIMEOUT_START           1
#define IC_TIMEOUT_END             0

/***************************外部变量***************************/
extern mutex_t g_mutexCANLog;

/***************************函數指針***************************/
typedef void(*pCanMsgAnalysisCB)(uint8_t index,uint8_t len,uint8_t* data);

/***************************函數定義***************************/
typedef enum /*时代新安项目只有can4*/
{
    CHANNEL_CH_CAN = CAN_CHANNEL_1,
    CHANNEL_EV_CAN = CAN_CHANNEL_1,
    CHANNEL_BD_CAN = CAN_CHANNEL_1,
    CHANNEL_DIAG_CAN = CAN_CHANNEL_1,
}CanChannel;

typedef enum /*国标 CAN ID  */
{
    CAN_MSG_RX_HECU_ID = 0x2B7,
    CAN_MSG_RX_BCM_ID  = 0x237,
    CAN_MSG_RX_BMS_ID  = 0x0BA,
    CAN_MSG_RX_MCU_ID  = 0x0C9,
    CAN_MSG_RX_DCDC_ID = 0x3B2,
    CAN_MSG_RX_ABS_ID  = 0x118,
    CAN_MSG_RX_IC_ID   = 0x120,
    CAN_MSG_RX_APU_ID  = 0x0D3
}GbRxCanId;

typedef enum
{
    CAN_MSG_RX_BCM_ID_033  = 0x0033,
    CAN_MSG_RX_VBU_ID_031  = 0x0031,
}GbRxAuthCanId;

typedef enum
{
    CAN_MSG_TX_BCM_ID_034  = 0x0034,
    CAN_MSG_TX_VBU_ID_036  = 0x0036,
}GbRxAuthRepCanId;

/*国标 CAN ID index  */
typedef enum
{
    CAN_MSG_RX_HECU_ID_INDEX = 0,
    CAN_MSG_RX_BCM_ID_INDEX,
    CAN_MSG_RX_BMS_ID_INDEX,
    CAN_MSG_RX_MCU_ID_INDEX,
    CAN_MSG_RX_DCDC_ID_INDEX,
    CAN_MSG_RX_ABS_ID_INDEX,
    CAN_MSG_RX_IC_ID_INDEX,
    CAN_MSG_RX_APU_ID_INDEX,
    CAN_MSG_RX_MAX,
}GbCanIdIndex;

typedef enum
{
    CAN_MSG_RX_BCM_ID_033_INDEX  = 0,
    CAN_MSG_RX_VBU_ID_031_INDEX ,
    CAN_MSG_RX_AUTH_MAX
}
CanAuthCanIdIndex;

typedef enum
{
    AUTH_SUCCESS           = 0x00,
    AUTH_FAIL              = 0x5F,
    AUTH_BUSY              = 0xF0,
    AUTH_SETTLEMENT_RESULT = 0xF5,
}AuthResponseStatus;

typedef enum
{
    TBOX_IS_SPARE = 0,
    TBOX_IS_BUSY,
}TboxAuthStatus;

typedef enum
{
    SEND_SUCCESS = 0,
    SEND_FAUIL,
}TboxRspSendStatus;


typedef struct 
{
    GbCanIdIndex index;     /* 消息索引值 */
    GbRxCanId    rxid;      /* 接收消息ID */
    CanChannel   rxchannel; /* 接收can通道 */
}CanMsgAnalysisInfo;

typedef struct 
{
    bool     code_online;   /*节点在线状态*/
    uint8_t  dtcindex;      /*故障码索引值*/
    uint8_t  channel;       /*报文的通道*/
    uint8_t  busoffindex;   /*对应通道busoff故障码索引值*/
    uint16_t timeoutcnt;    /*节点丢失计数阀值*/
}CanMsgAnalyCheckCodeIno;

typedef struct
{
    CanAuthCanIdIndex index;
    CanChannel        rxchannel;
    GbRxAuthCanId     rxid;
    GbRxAuthRepCanId  txid;
}TboxAuthInfo;

typedef struct
{
    uint8_t readPos;
    uint8_t writePos;
    uint8_t spaceLen;
    uint8_t frameCount;
    uint8_t data[CAN_LOG_MAX_COUNT][CAN_LOG_ARM_LEN];
    uint8_t dmaCompelte;
}CanLogInfo;

typedef struct
{
    uint8_t readpos;
    uint8_t writepos;
    uint8_t spacelen;
    uint8_t data[CAN_ANALYZE_ROW_COUNT][CAN_ANALYZE_ROW_COLUMN_COUNT];
}CanAnalyzeQueue;

/************************函数接口***************************/
void CanLogInitBuf(void);
void CanLogWriteBuf(uint8_t checksum, CanPduInfo pduInfo);
void WriteVehicleInfoToAnalyze(uint8_t channel, CanPduInfo pduInfo);
void CanMesgAES128(uint8_t index,uint8_t len,uint8_t *challengecode);
void CanLogWriteTxBuf(uint8_t channel, uint32_t canId, uint8_t canDlc, uint8_t *buf);
bool CanLogReadBuf(uint8_t* logBuf);
int  CanMsgAnalyzeData(uint8_t channel, CanPduInfo pduInfo);

CanMsgAnalyCheckCodeIno* CanMsgGetCodeStatusInfo(void);

#endif  /* _CAN_MSG_API_H_ */
