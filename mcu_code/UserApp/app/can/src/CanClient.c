/*
      CanClient.c
描述：此文件主要是ECU升级文件，包括汽车ECU升级流程
作者：廖勇刚
时间：2016.12.02
*/
#include <string.h>
#include "CanClient.h"
#include "CanApi.h"
#include "r_can.h"
#include "appqueue.h"
#include "CanIf_Cbk.h"
#include "IpcApi.h"
#include "LogApi.h"


/************************外部全局变量****************************/
extern CommonInfo g_commonInfo;
extern Can_HardwareObjectType Can_HardwareObjectConfigData[];
extern CanTpInfo   g_canTpInfo;

/************************全局变量****************************/
uint8_t   g_clientRxInfo[TBOX_CLIENT_RX_BUFFER];
uint8_t   g_clientTxInfo[TBOX_CLIENT_TX_BUFFER];
uint16_t  g_clientRxLen = 0;
DoubleQueue g_diagDoubleQueue;
const EcuIdInfo   g_ecuIdInfo[ECU_TYPE_MAX] =  
{
    {TBOX_PHYSICAL_REQUEST_ID,TBOX_RESPONSE_ID, TBOX_FUNCTION_REQUEST_ID},   /*TBOX*/
};

/*************************************************
函数名称: CanDiagInitQueue
函数功能: 初始化诊断二维数据
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2019/05/29
*************************************************/
void CanDiagInitQueue(void)
{
    CreateDoubleQueue(&g_diagDoubleQueue);
}


/*************************************************
函数名称: CanRxPduHrhSearch
函数功能: 查询接收的CAN ID是否是当前TP合法配置的ID
输入参数: uint8_t hrhIdOfPdu
          uint32_t canId
          uint16_t *pduIdIndex
输出参数: uint8_t
函数返回类型值：0---代表查询成功
                1---代表查询失败
编写者: liaoyonggang
编写日期 :2018/06/07
*************************************************/
uint8_t CanRxPduHrhSearch(uint8_t hrhIdOfPdu, uint32_t canId,  uint16_t *pduIdIndex)
{
    uint8_t  result = 1;

    switch(hrhIdOfPdu)
    {
        case RX_EVCAN_CAN1_TP:
        {
            *pduIdIndex = RX_EVCAN_CAN1_TP;
            g_canTpInfo.rescanId = canId;
            // g_canTpInfo.canCh = 3;
            result = 0;
            break;
        }
        case RX_DCAN_CAN4_TP:
        {
            *pduIdIndex = RX_DCAN_CAN4_TP;
            g_canTpInfo.rescanId = canId;
            // g_canTpInfo.canCh = 3;
            result = 0;
            break;
        }
        default:
            break;
    }

    return result;
}

/*************************************************
函数名称: CanRxPduHrhSearchVaid
函数功能: 查询接收的CAN ID是否是当前HRH合法配置的ID
输入参数: uint8_t hrhIdOfPdu
          uint32_t canId
输出参数: uint8_t
函数返回类型值：0---代表查询成功
                1---代表查询失败
编写者: liaoyonggang
编写日期 :2018/06/07
修改者: zxl 新增注释
注释： 这个函数用于过滤T-Box在D CAN收到的所有诊断请求报文和响应，对T-Box自身来说，收到的是诊断报文，对其他ECU来说，收到的诊断响应报文
*************************************************/
uint8_t CanRxPduHrhSearchValid(uint8_t hrhIdOfPdu, uint32_t canId)
{
    uint8_t  result = 1;

    switch(hrhIdOfPdu)
    {
        case RX_EVCAN_CAN1_HRH:
        {
            /*当T-Box未执行远程诊断时，接受来自can网络的诊断请求报文和诊断响应报文，那么此时肯定是T-Box被诊断仪本地诊断*/
            if(DiagagosticPhysicalRequestAddress() == canId) 
            {
                g_canTpInfo.idType = ECU_ID_PHY;
                g_canTpInfo.reqcanId = canId;
                g_canTpInfo.localDiagTBox = TRUE;
                g_canTpInfo.remoteDiagFlag = FALSE;
                result = 0;
                break;
            }
            else if(DiagagosticFunctionReqestAddress() == canId)  
            {
                g_canTpInfo.idType = ECU_ID_FUN;
                g_canTpInfo.reqcanId = canId;
                g_canTpInfo.localDiagTBox = TRUE;
                g_canTpInfo.remoteDiagFlag = FALSE;
                result = 0;
                break;
            }

            /*当T-Box执行远程诊断时才接受来自can网络的诊断报文*/
        //    for(index = 0x700; index < 0x7DF; index++)
            if(((canId & 0x700) == 0x700)&&(TRUE == g_canTpInfo.remoteDiagFlag))
            {
                    result = 0;
                    break;
            }
        }
        default:
            break;
    }    

    return result;
}

/*************************************************
函数名称: CanClientTransmit
函数功能: 发送数据到TP
输入参数: 接收消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/05
*************************************************/
void CanClientTransmit(PduIdType CanTpTxSduId, PduInfoType  PduInfo)
{
    PduR_DcmTransmit(CanTpTxSduId,&PduInfo);
}

/*************************************************
函数名称: CanClientRxDiagFrame
函数功能: 接收到诊断报文
输入参数: 接收消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/09/30
*************************************************/
void CanClientRxDiagFrame(uint8_t hrhIdOfPdu, CanPduInfo pduInfo)
{
    uint8_t  ret = 0;
    uint16_t count = 0;
    uint8_t tempBuf[14] = {0};

    /*过滤无用的诊断报文，防止队列溢出*/
    ret = CanRxPduHrhSearchValid(hrhIdOfPdu, pduInfo.id);
    if(0 != ret)
    {
        return;
    }

    tempBuf[count++] = hrhIdOfPdu;
    tempBuf[count++] = (uint8_t)(pduInfo.id >> 24);
    tempBuf[count++] = (uint8_t)(pduInfo.id >> 16);
    tempBuf[count++] = (uint8_t)(pduInfo.id >> 8);
    tempBuf[count++] = (uint8_t)pduInfo.id;
    tempBuf[count++] = (uint8_t)pduInfo.dlc;
    memcpy(tempBuf + count, pduInfo.data, pduInfo.dlc);
    count = count + pduInfo.dlc;

    SystemApiWriteRowData(&g_diagDoubleQueue,tempBuf);
    return;
}

/*************************************************
函数名称: CanRxDiagFrameHandle
函数功能: CAN处理诊断报文
输入参数: 接收消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/09/30
*************************************************/
void CanRxDiagFrameHandle(void)
{    
    uint8_t Hrh = 0;
    uint8_t id[4] = {0};
    uint16_t count = 0;
    CanPduInfo pduInfo;
    uint8_t tempData[TBOX_DIAG_COLUMN_COUNT] = {0};

    if(CanTp_PollFilledStatusRead() == TRUE)
    {
        return;
    }

    if(QUEUE_NO_ERROR != SystemApiReadRowData(&g_diagDoubleQueue,tempData))
    {
        return;
    }

    Hrh = tempData[count++];
    id[0] = tempData[count++];
    id[1] = tempData[count++];
    id[2] = tempData[count++];
    id[3] = tempData[count++];
    pduInfo.id = (uint32_t)((id[0] << 24) + (id[1] << 16) + (id[2] << 8) + id[3]);
    pduInfo.dlc = tempData[count++];
    memcpy(pduInfo.data, tempData + count, pduInfo.dlc);

    CanIf_RxIndication(Hrh, pduInfo.id, pduInfo.dlc, pduInfo.data);
}

/*************************************************
函数名称: CanRemoteDiagEcuResponse
函数功能: 远程诊断ECU响应
输入参数: 接收的消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/03/23
*************************************************/
void CanRemoteDiagEcuResponse(uint8_t channel, uint32_t canId,uint8_t *data,uint32_t len)
{
    Msg msg;
    uint8_t tempBuf[TBOX_CLIENT_RX_BUFFER];

    if(TBOX_CLIENT_RX_BUFFER < (len + 7))
    {
        return;
    }

    tempBuf[0] = channel;
    tempBuf[1] = (canId>>24)&0xff;
    tempBuf[2] = (canId>>16)&0xff;
    tempBuf[3] = (canId>>8)&0xff;
    tempBuf[4] = (canId)&0xff;
    tempBuf[5] = (uint8_t)(len >> 8);
    tempBuf[6] = (uint8_t)len;
    memcpy(tempBuf + 7, data, len);

    msg.event = MESSAGE_TX_VEHICLE_REMOTE_DIAGNOSIS;
    msg.len   = len + 7;
    msg.lparam = (uint32_t)&tempBuf[0];

    SystemApiLogHexPrintf(LOG_INFO_OUTPUT, "Remote Diag Ecu Response:",tempBuf,msg.len);
    SystemSendMessage(TASK_ID_IPC, msg);
}


