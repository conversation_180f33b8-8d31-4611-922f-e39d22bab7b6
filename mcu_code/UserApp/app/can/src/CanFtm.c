#include "CanFtm.h"
#include "NvApi.h"
#include "pwm.h"
#include "r_can.h"
#include "DiagApi.h"
#include "LedApi.h"
#include "LogApi.h"
#include "PmApi.h"
#include "BatApi.h"
#include "gpio.h"
#include "BtApi.h"
#include "Rte_Dcm.h"
#include "SystemApi.h"
#include "rtc.h"
#include <stdio.h>
#include <string.h>

/************************外部全局变量****************************/
extern GpioInfo             g_gpioPowerOnInfoList[];
extern PmInfo               g_pmInfo;
extern CommonInfo           g_commonInfo;
extern TboxSelfConfigPara   g_nvTboxSelfConfigData;
extern CarInfo              g_carInfo;
FtmTestStage                g_FtmTestStage = SEMI_FINISHED_PRODUCT;
extern StaticDidInfo        g_staticDid;
extern uint8_t              g_canRxData[];
extern NvItemInfo           g_nvInfoMap[NV_MAX_NUMBER];
extern BackupRamInfo        g_backupRamInfo;

/************************函数接口***************************/
static void FtmCanMsgRxMcuCmd(uint8_t *para, uint16_t len);
static void FtmCanMsgRxArmCmd(uint8_t *para, uint16_t len);
static void FtmCanMsgRxIctCmd(uint8_t *para, uint16_t len);
//static void FtmUartMsgRxMcu(uint8_t *para, uint16_t len);
//static void FtmUartMsgRxDut(uint8_t *para, uint16_t len);
static ipcErrorCode FtmEncodeEscapeData(uint8_t* pEscapeData, uint16_t len, uint8_t *pOutData, uint16_t *outLen);
static ipcErrorCode FtmDecodeEscapeData(uint8_t *pEscapeData, uint16_t len, uint8_t* pOutData, uint16_t *outLen);
static void FtmCanRxMsgTxAckMsg(void);
static void FtmClearAckInfo(FtmInfo *ftmInfo);
static void FtmSetupAckInfo(FtmInfo *ftmInfo);
static void FtmCanRxNadAckFunction(uint8_t *tempBuffer, uint16_t *tempLen);
//static void FtmUartRxNadAckFunction(uint8_t *tempBuffer, uint16_t *tempLen);
//static ipcErrorCode FtmUartTxPackageFrameData(Msg msg);
static ipcErrorCode FtmCanMsgRxFunction(Msg msg);

/************************全局变量****************************/
FtmInfo       g_canFtmInfo;
FtmWakeupType g_ftmWakeup = FTM_NULL_WAKEUP;

MessageRxFtmInfo g_rxCanFtmMessageInfoMap[MESSAGE_ID_RX_CANFTM_MAX] =
    {
        {MESSAGE_ID_RX_MCU_CMD, MESSAGE_RX_MCU_CMD, FLAG_NOT_FIXED_LEN, {0x4001}, FtmCanMsgRxMcuCmd},
        {MESSAGE_ID_RX_ARM_CMD, MESSAGE_RX_ARM_CMD, FLAG_NOT_FIXED_LEN, {0x4001}, FtmCanMsgRxArmCmd},
        {MESSAGE_ID_RX_DUT_CMD, MESSAGE_RX_DUT_CMD, FLAG_NOT_FIXED_LEN, {0x4001}, FtmCanMsgRxIctCmd}};

MessageTxInfo g_txCanFtmMessageInfoMap[MESSAGE_ID_TX_CANFTM_MAX] =
    {
        {MESSAGE_ID_TX_PC_CMD, MESSAGE_TX_PC_CMD, FLAG_WAIT_ACK, FLAG_NOT_FIXED_LEN, NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01},
        {MESSAGE_ID_TX_DUT_CMD, MESSAGE_TX_DUT_CMD, FLAG_WAIT_ACK, FLAG_NOT_FIXED_LEN, NORAML_MESSAGE, NO_ENCRYPT_MODE, 0x01}};

/*************************************************
函数名称: FtmCanInitRamData
函数功能: 初始化装备命令任务全局变量
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2017/06/16
*************************************************/
FtmInfo *FtmInitRead(void)
{
    return &g_canFtmInfo;
}

/*************************************************
函数名称: FtmCanInitRamData
函数功能: 初始化装备命令任务全局变量
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
void FtmInitRamData(void)
{
    g_canFtmInfo.ackFlag               = FLAG_NOT_WATI_ACK;
    g_canFtmInfo.retransmissionTimeout = 0;
    g_canFtmInfo.retransmissionCount   = 0;
    g_canFtmInfo.rxStatus              = STATUS_RX_IDLE;
    g_canFtmInfo.txTid                 = 0;
    g_canFtmInfo.rxTid                 = 0;
    g_canFtmInfo.ipcRx.len             = 0;
    memset(g_canFtmInfo.ipcRx.buffer, 0x00, FTM_MAX_SIZE_LEN);
    g_canFtmInfo.ipcTx.len = 0;
    memset(g_canFtmInfo.ipcTx.buffer, 0x00, FTM_MAX_SIZE_LEN);
    g_canFtmInfo.ftmCurrentRxIndex = 0x00;
    g_canFtmInfo.ftmCurrentTxIndex = 0x00;
    g_canFtmInfo.ftmMode           = FTM_MODE_EXIT;
    g_canFtmInfo.ftmTestCbk        = NULL;
    g_canFtmInfo.wakeupType        = FTM_NULL_WAKEUP;
    g_canFtmInfo.ftmLedTestEvent   = FALSE;
    g_canFtmInfo.aliveWakeBit      = FTM_ALIVE_WAKE_INIT_BIT;
    g_canFtmInfo.aliveWakeEvent    = TRUE;
    g_canFtmInfo.armEnterFtm       = FALSE;
    g_canFtmInfo.ftmINSTest        = 0x00;
}

/*************************************************
函数名称: CanFtmSendData
函数功能: 通过CAN发送装备命令
输入参数: len ---发送数据长度
          buf ---发送数据内容
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/19
*************************************************/
static void FtmCanSendData(uint16_t len, uint8_t *buf)
{
    uint8_t count   = 0;
    uint8_t lastLen = 0;
    uint8_t i       = 0;
    uint8_t tempBuf[8];

    if (NULL == buf)
    {
        return;
    }

    count   = len / CAN_FRAME_LEN;
    lastLen = len % CAN_FRAME_LEN;

    if (0 < count)
    {
        for (i = 0; i < count; i++)
        {
            memcpy(tempBuf, &buf[i * 8], 8);
            MCUCanSendFtmData(CAN_CHANNEL_1, CAN_RESPONSE_ID, 8, tempBuf);
        }
    }

    // 最后一帧
    if (0 != lastLen)
    {
        memcpy(tempBuf, &buf[i * 8], lastLen);
        MCUCanSendFtmData(CAN_CHANNEL_1, CAN_RESPONSE_ID, lastLen, tempBuf);
    }
    else
    {
        memset(tempBuf, 0x00, 8);
        MCUCanSendFtmData(CAN_CHANNEL_1, CAN_RESPONSE_ID, 0, tempBuf);
    }
}

/*************************************************
函数名称: FtmDecodeEscapeData
函数功能: Ipc解码转义字节 0xFB 0X02--->0XFC
                          0XFB 0X01--->0XFB
输入参数: 转义前参数指针和长度  转义后参数指针和长度
输出参数: 转义结果
函数返回类型值：
           IPC_NO_ERROR --- 转义执行正确
           IPC_INPUT_PARA_ERROR--- 参数输入错误
           IPC_FRAME_LEN_TOO_LONG ---消息报文长度过长
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static ipcErrorCode FtmDecodeEscapeData(uint8_t *pEscapeData, uint16_t len, uint8_t *pOutData, uint16_t *outLen)
{
    ipcErrorCode errorCode  = IPC_NO_ERROR;
    EscapeFlag   escapeFlag = FLAG_NOT_ESCAPE;
    uint16_t       i          = 0;
    uint16_t       tempLen    = 0;

    // 输入参数合法性检查
    if ((NULL == pEscapeData) || (FTM_MAX_SIZE_LEN < len) || (NULL == pOutData) || (NULL == outLen))
    {
        errorCode = IPC_INPUT_PARA_ERROR;
        return errorCode;
    }

    memset(pOutData, 0x00, FTM_MAX_SIZE_LEN);

    for (i = 0; i < len; i++)
    {
        // 检测到转义字节0xfb
        if (IPC_ESCAPE_DATA == pEscapeData[i])
        {
            escapeFlag = FLAG_ESCAPE;
        }
        else if ((0x02 == pEscapeData[i]) || (0x01 == pEscapeData[i]))
        {
            if (escapeFlag == FLAG_ESCAPE)
            {
                if (0x02 == pEscapeData[i])
                {
                    pOutData[tempLen] = IPC_MESSAGE_IDENTIFIER;
                }
                else
                {
                    pOutData[tempLen] = IPC_ESCAPE_DATA;
                }
                escapeFlag = FLAG_NOT_ESCAPE;
                tempLen++;
            }
            else
            {
                pOutData[tempLen] = pEscapeData[i];
                tempLen++;
            }
        }
        else
        {
            pOutData[tempLen] = pEscapeData[i];
            tempLen++;
        }
    }

    *outLen = tempLen;

    if (FTM_MAX_SIZE_LEN < *outLen)
    {
        errorCode = IPC_FRAME_LEN_TOO_LONG;
    }

    return errorCode;
}

/*************************************************
函数名称: FtmEncodeEscapeData
函数功能: Ipc编码转义字节 0XFC--->0xFB 0X02
                          0XFB--->0XFB 0X01
输入参数: 转义前参数指针和长度  转义后参数指针和长度
输出参数: 转义结果
函数返回类型值：
           IPC_NO_ERROR --- 转义执行正确
           IPC_INPUT_PARA_ERROR--- 参数输入错误
编写者: liaoyonggang
编写日期 :2017/06/29
*************************************************/
static ipcErrorCode FtmEncodeEscapeData(uint8_t *pEscapeData, uint16_t len, uint8_t *pOutData, uint16_t *outLen)
{
    ipcErrorCode errorCode = IPC_NO_ERROR;
    uint16_t       i         = 0;
    uint16_t       tempLen   = 0;

    // 输入参数合法性检查
    if ((NULL == pEscapeData) || (FTM_MAX_SIZE_LEN < len) || (NULL == pOutData) || (NULL == outLen))
    {
        errorCode = IPC_INPUT_PARA_ERROR;
        return errorCode;
    }

    memset(pOutData, 0x00, FTM_MAX_SIZE_LEN - 1);
    tempLen = 0;

    for (i = 0; i < len; i++)
    {
        if ((IPC_MESSAGE_IDENTIFIER != pEscapeData[i]) && (IPC_ESCAPE_DATA != pEscapeData[i]))
        {
            pOutData[tempLen] = pEscapeData[i];
            tempLen++;
        }
        // 检测到转义字节0xfc
        else if (IPC_MESSAGE_IDENTIFIER == pEscapeData[i])
        {
            pOutData[tempLen] = IPC_ESCAPE_DATA;
            tempLen++;
            pOutData[tempLen] = 0x02;
            tempLen++;
        }
        // 检测到转义字节0xfb
        else
        {
            pOutData[tempLen] = IPC_ESCAPE_DATA;
            tempLen++;
            pOutData[tempLen] = 0x01;
            tempLen++;
        }
    }

    *outLen = tempLen;
    return errorCode;
}

/*************************************************
函数名称: CanFtmRxMsgTxAckMsg
函数功能: MCU回PC端ACK消息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmCanRxMsgTxAckMsg(void)
{
    uint8_t mcuAckData[5] = {0xfc, 0x08, 0x00, 0x08, 0xfc};

    MCUCanSendFtmData(CAN_CHANNEL_1, CAN_RESPONSE_ID, 5, mcuAckData);
}

/*************************************************
函数名称: FtmClearAckInfo
函数功能: 串口装备接收到应答相关消息后，清除ACK相关信息
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmClearAckInfo(FtmInfo *ftmInfo)
{
    if (NULL == ftmInfo)
    {
        return;
    }

    ftmInfo->ackFlag               = FLAG_NOT_WATI_ACK;
    ftmInfo->retransmissionCount   = 0;
    ftmInfo->retransmissionTimeout = 0;
    ftmInfo->ipcTx.len             = 0;
    memset(ftmInfo->ipcTx.buffer, 0x00, FTM_MAX_SIZE_LEN);
}

/*************************************************
函数名称: CanFtmSetupAckInfo
函数功能: Ftm发送需要回应ACK消息后，置位相应ACK状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmSetupAckInfo(FtmInfo *ftmInfo)
{
    if (NULL == ftmInfo)
    {
        return;
    }

    ftmInfo->ackFlag               = FLAG_WAIT_ACK;
    ftmInfo->retransmissionCount   = 0;
    ftmInfo->retransmissionTimeout = 0;
}

/*************************************************
函数名称: FtmCanRxNadAckFunction
函数功能: CAN装备接收到应答帧处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmCanRxNadAckFunction(uint8_t *tempBuffer, uint16_t *tempLen)
{
    // 如果是DUT ACK应答
    if ((0xfc == tempBuffer[0]) &&
        (0x30 == tempBuffer[1]) &&
        (0x00 == tempBuffer[2]) &&
        (0x01 == tempBuffer[3]) &&
        (0xfc == tempBuffer[4]))
    {
        FtmClearAckInfo(&g_canFtmInfo);
    }
    else
    {
        *tempLen = 0;
        memset(tempBuffer, 0x00, FTM_MAX_SIZE_LEN);
    }
}

/*************************************************
函数名称: FtmCanTxPackageFrameData
函数功能: 执行发送前的命令组包和发送过程
输入参数: 接收事件 长度 和具体参数
输出参数: ipcErrorCode
函数返回类型值：
              IPC_NO_ERROR --- 组包执行成功，并发送
              IPC_INPUT_PARA_ERROR ---输入参数错误
              IPC_ID_ERROR --- 消息报文ID错误
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
ipcErrorCode FtmCanTxPackageFrameData(Msg msg)
{
    ipcErrorCode         errorCode = IPC_NO_ERROR;
    MessageTxCanFtmIndex txIndex   = MESSAGE_ID_TX_PC_CMD;
    MessageAttribute     txAttribute;
    uint8_t                txBuffer[FTM_MAX_SIZE_LEN];
    uint8_t               *pTempBuffer = NULL;
    uint16_t               i           = 0;
    uint16_t               len         = 0;
    uint8_t                checkByte   = 0;

    for (txIndex = MESSAGE_ID_TX_PC_CMD; txIndex < MESSAGE_ID_TX_CANFTM_MAX; txIndex++)
    {
        if (g_txCanFtmMessageInfoMap[txIndex].id == msg.event)
        {
            break;
        }
    }

    // 未查询到对应的消息ID
    if (MESSAGE_ID_TX_CANFTM_MAX == txIndex)
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm tx event error is 0x%x\r\n", msg.event);
#endif

        errorCode = IPC_ID_ERROR;
        return errorCode;
    }

    memset(txBuffer, 0x00, FTM_MAX_SIZE_LEN);
    memset(g_canFtmInfo.ipcTx.buffer, 0x00, FTM_MAX_SIZE_LEN);
    g_canFtmInfo.ipcTx.len = 0;

    // 发送事件需要等待ACK
    if (FLAG_WAIT_ACK == g_txCanFtmMessageInfoMap[txIndex].ackFlag)
    {
        FtmSetupAckInfo(&g_canFtmInfo);
    }

    // 发送属性赋值
    txAttribute.bit.len            = msg.len;
    txAttribute.bit.encryptionMode = g_txCanFtmMessageInfoMap[txIndex].encryptMode;
    txAttribute.bit.type           = g_txCanFtmMessageInfoMap[txIndex].type;
    txAttribute.bit.ackFlag        = g_txCanFtmMessageInfoMap[txIndex].ackFlag;
    txAttribute.bit.reserved       = 0;

    txBuffer[MESSAGE_HEAD_ID_HIGH] = (uint8_t)(msg.event >> 8);
    txBuffer[MESSAGE_HEAD_ID_LOW]  = (uint8_t)msg.event;

    txBuffer[MESSAGE_HEAD_ATTRIBUTE_HIGH] = (uint8_t)(txAttribute.byte >> 8);
    txBuffer[MESSAGE_HEAD_ATTRIBUTE_LOW]  = (uint8_t)txAttribute.byte;

    if ((0xffff == g_canFtmInfo.txTid) || (0 == txAttribute.bit.ackFlag))
    {
        g_canFtmInfo.txTid = 0;
    }
    else
    {
        g_canFtmInfo.txTid = g_canFtmInfo.txTid + 1;
    }

    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm send tid: %d\r\n", g_canFtmInfo.txTid);

    txBuffer[MESSAGE_HEAD_TID_HIGH] = (uint8_t)(g_canFtmInfo.txTid >> 8);
    txBuffer[MESSAGE_HEAD_TID_LOW]  = (uint8_t)g_canFtmInfo.txTid;

    len = 6;

    if (0 != msg.len)
    {
        if (0 == msg.lparam)
        {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm tx para error\r\n");
#endif

            errorCode = IPC_INPUT_PARA_ERROR;
            return errorCode;
        }
        else
        {
            pTempBuffer = (uint8_t *)msg.lparam;
            for (i = 0; i < msg.len; i++)
            {
                txBuffer[6 + i] = pTempBuffer[i];
            }
            len = len + msg.len;
        }
    }

    // 异或校验码计算
    errorCode = IpcXorCalculation(txBuffer, len, &checkByte);
    if (IPC_NO_ERROR != errorCode)
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm xor check fail\r\n");
#endif

        return errorCode;
    }
    txBuffer[len] = checkByte;
    len           = len + 1;

    // 发送数据进行转义
    errorCode = FtmEncodeEscapeData(txBuffer, len, &g_canFtmInfo.ipcTx.buffer[1], &g_canFtmInfo.ipcTx.len);
    if (IPC_NO_ERROR != errorCode)
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm tx encode error\r\n");
#endif

        return errorCode;
    }

    g_canFtmInfo.ipcTx.buffer[0]                          = IPC_MESSAGE_IDENTIFIER;
    g_canFtmInfo.ipcTx.buffer[g_canFtmInfo.ipcTx.len + 1] = IPC_MESSAGE_IDENTIFIER;
    g_canFtmInfo.ipcTx.len                                = g_canFtmInfo.ipcTx.len + 2;

    FtmCanSendData(g_canFtmInfo.ipcTx.len, g_canFtmInfo.ipcTx.buffer);
    return errorCode;
}

/*************************************************
函数名称: FtmMcuTxToPcBuf
函数功能: 发送给pc检测状态
输入参数: 检测类型索引和检测结果
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/03/31
*************************************************/
static void FtmMcuTxToPcBuf(uint8_t index, uint8_t *buf, uint8_t len)
{
    Msg   msg;
    uint8_t tempBuf[100] = {0x00};

    if (len > sizeof(tempBuf))
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->pc cmd:0x%02x, over len:%d\r\n", index, len);
        return;
    }

    tempBuf[0] = index;
    memcpy(&tempBuf[1], buf, len);
    msg.event  = MESSAGE_TX_PC_CMD;
    msg.len    = len + 1;
    msg.lparam = (uint32_t)tempBuf;
    FtmCanTxPackageFrameData(msg);

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->pc cmd:0x%02x, para:0x%02x\r\n", tempBuf[0], tempBuf[1]);
}

/*************************************************
函数名称: FtmMcuTxToPcResult
函数功能: 发送给pc检测状态
输入参数: 检测类型索引和检测结果
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
void FtmMcuTxToPcResult(uint8_t index, uint8_t result)
{
    uint8_t buf[1] = {0x00};

    buf[0] = result;

    FtmMcuTxToPcBuf(index, buf, sizeof(buf));
}

/*************************************************
函数名称: FtmMcuTxToDutCmd
函数功能: 发送给底板命令
输入参数: 检测类型索引和检测结果
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmMcuTxToDutCmd(uint8_t index, uint8_t param)
{
    uint8_t buf[2];
    Msg   msg;

    buf[0]     = index;
    buf[1]     = param;
    msg.event  = MESSAGE_TX_DUT_CMD;
    msg.len    = 0x02;
    msg.lparam = (uint32_t)buf;
    FtmCanTxPackageFrameData(msg);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->dut cmd:0x%02x, para:0x%02x\r\n", index, param);
}

/*************************************************
函数名称: FtmMcuTxToArmCmd
函数功能: 发送给arm命令
输入参数: 检测类型索引和检测结果
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/01
*************************************************/
static void FtmMcuTxToArmCmd(uint8_t index, uint8_t param)
{
    uint8_t buf[2];
    Msg   msg;

    buf[0]     = index;
    buf[1]     = param;
    msg.event  = MESSAGE_TX_ARM_FTM_CMD;
    msg.len    = 0x02;
    msg.lparam = (uint32_t)buf;

    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: NotifyArmSleep
函数功能: 通知ARM睡眠
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/01
*************************************************/
static void NotifyArmSleep(void)
{
    g_pmInfo.workStatus      = PM_STATUS_ENTER_NORAML_SLEEP;
    g_pmInfo.entersleepCount = 5;
    g_commonInfo.tspStatus   = WORK_STATUS_INACTIVE;

    FtmMcuTxToArmCmd(FTM_TX_ARM_SLEEP, g_pmInfo.workStatus);

#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm enter sleep test sucess\r\n");
#endif
}

/*************************************************
函数名称: NotifyArmShutDown
函数功能: 通知ARM关机
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/11/03
*************************************************/
static void NotifyArmShutDown(void)
{
    FtmMcuTxToArmCmd(FTM_TX_ARM_SLEEP, PM_STATUS_DEEP_SLEEP);
}

/*************************************************
函数名称: FtmSecurityAlgorithm
函数功能: 装备测试安全算法
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/04/13
*************************************************/
static uint32_t FtmSecurityAlgorithm(uint32_t Seed)
{
    uint8_t i = 0x00;

    for (i = 0; i < 35; i++)
    {
        if (Seed & 0x80000000u)
        {
            Seed = Seed << 1;
            Seed = Seed ^ 0x6454424f;
        }
        else
        {
            Seed = Seed << 1;
        }
    }

    return Seed;
}

/*************************************************
函数名称: FtmRxMcuSetTboxTime
函数功能: 设置T-Box系统时间
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/04/13
*************************************************/
// static void FtmSetTboxTime(uint8_t *para, uint16_t len)
// {
// }

/*************************************************
函数名称: FtmWakeUpTestReady
函数功能: 唤醒测试准备，在进入装备模式时调用
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: BenYuLong
编写日期：2020/08/13
*************************************************/
static void FtmWakeUpTestReady(void)
{
    uint8_t tempBuf[2] = {0x00};

/*
    // INTP5   中断检测设置  边缘检测      下降沿检测  BLE Status2_IND
    MKP5            = 0U;
    RFP5            = 0U;
    TBP5            = 0U;
    FCLA0CTL5_INTPL = 0x02;
    PU8 |= 1 << 1;
    P8 &= ~(1 << 1);
*/
    TboxSystemRtcWakeupSet(0, 0, 0, 5, RTC_WAKEUP_SECOND);


    // 清除RTC时钟中断标志
    memset(tempBuf, 0x00, 2);
    tempBuf[0] = PCF85063_REG_CTRL2;
    tempBuf[1] = 0x80;
    //McuI2cMasterSend(RTC_SLAVE_ADDR, 2, tempBuf);
}

/*************************************************
函数名称: FtmRxMcuMode
函数功能: 设置生产模式
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuMode(uint8_t *para, uint16_t len)
{
    uint8_t         btReset[] = "AT+SOFT_RST=1\r\n";
    uint8_t         buf[8]    = {0x00};
    uint32_t        calcKey     = 0;
    uint32_t        getKey      = 0;
    static uint32_t seed        = 0;
    static uint8_t  flag        = 0;
    FtmInfo      *pFtmInfo    = FtmInitRead();
    BtServices   *pBtServices = BtModuleServiceRead();

    if (FTM_MODE_EXIT == para[0])
    {
        pFtmInfo->ftmMode = FTM_MODE_EXIT;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm test mode exit\r\n");
    }
    else if (FTM_MODE_ENTER == para[0])
    {
        if (1 == flag)
        {
            pFtmInfo->ftmMode   = FTM_MODE_ENTER;
            pFtmInfo->testStage = para[1];

            // if (BT_MODULE_SLAVE_TYPE == pBtServices->btModuleType)
            // {
            //     BtModuleHandShakeSend(HANDSHAKE_SUCESS, 0x00);
            //     BtTxUartCmd((char *)btReset);
            // }

            FtmMcuTxToArmCmd(FTM_RX_MCU_MODE_INDEX, FTM_MODE_ENTER);

            FtmMcuTxToPcResult(FTM_TX_PC_MODE_INDEX, FTM_SUCCESS);
            //McuRLin21Init();
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BAT_CHARGE_EN], GPIO_OUTPUT_LOW);
            /*when time information len byte is error,do not set rtc time*/
            TboxSystemTimeSet(para[2], para[3], 0x00, para[4], para[5], para[6], para[7], NET_TYPE_TIME);
            FtmWakeUpTestReady();
            /*进入装备模式后，默认设置惯导输入车速为60*/
            // SpeedPwmInit(60);
        }
        else
        {
            FtmMcuTxToPcResult(FTM_TX_PC_MODE_INDEX, FTM_FAILED);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm test mode fail\r\n");
        }
    }
    else if (FTM_MODE_WAKEUP == para[0])
    {
        /* NULL */
    }
    else if (FTM_MODE_SEND_SEED == para[0])
    {
        pFtmInfo->txTid = 0;
        pFtmInfo->rxTid = 0;
        if (pFtmInfo->ftmMode == FTM_MODE_ENTER)
        {
            pFtmInfo->ftmMode = FTM_MODE_ENTER;
        }
        else
        {
            pFtmInfo->ftmMode = FTM_MODE_EXIT;
        }

        // seed = rand();
        seed   = 0x534F4957;  // SOIW
        buf[0] = 0x03;
        buf[1] = (uint8_t)(seed >> 24);
        buf[2] = (uint8_t)(seed >> 16);
        buf[3] = (uint8_t)(seed >> 8);
        buf[4] = (uint8_t)(seed);
        FtmMcuTxToPcBuf(FTM_TX_PC_MODE_INDEX, buf, 5);
    }
    else if (FTM_MODE_CALC_KEY == para[0])
    {
        calcKey = FtmSecurityAlgorithm(seed);
        getKey  = (uint32)(para[1] << 24) + (para[2] << 16) + (para[3] << 8) + para[4];
        if (calcKey == getKey)
        {
            flag   = 1;
            buf[0] = 0x04;
            buf[1] = FTM_SUCCESS;
            FtmMcuTxToPcBuf(FTM_TX_PC_MODE_INDEX, buf, 2);
        }
        else
        {
            flag   = 0;
            buf[0] = 0x04;
            buf[1] = FTM_FAILED;
            FtmMcuTxToPcBuf(FTM_TX_PC_MODE_INDEX, buf, 2);
        }
    }
}
/*************************************************
函数名称: FtmRxDutEcallKey
函数功能: 接收eCall按键处理信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/02
*************************************************/
static void FtmRxMcuEcallKey(uint8_t *para, uint16_t len)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    // DiagXcallKeyQuery(index, &status);
    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_SOS_KEY_INPUT], &level);
    /*The Key was pressed*/
    if (0x00 == para[0])
    {
        if (GPIO_OUTPUT_LOW == level)
        {
            ret = FTM_SUCCESS;
        }
        else
        {
            ret = FTM_FAILED;
        }
        FtmMcuTxToDutCmd(FTM_TX_DUT_ECALL_INDEX, FTM_CMD_LOW);
    }
    else if (1 == para[0])
    {
        if (GPIO_OUTPUT_HIGH == level)
        {
            ret = FTM_SUCCESS;
            FtmMcuTxToPcResult(FTM_TX_PC_ECALL_INDEX, ret);
        }
        else
        {
            ret = FTM_FAILED;
        }
    }

    if (FTM_FAILED == ret)
    {
        FtmMcuTxToPcResult(FTM_TX_PC_ECALL_INDEX, ret);
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm eCall key level %d, test ret %d\r\n", level, ret);
}

/*************************************************
函数名称: FtmRxMcuAirbag
函数功能: 接收安全气囊控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/02
*************************************************/
static void FtmRxMcuAirbag(uint8_t *para, uint16_t len)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_AIRBAG_INPUT], &level);
    if (0x00 == para[0])
    {
        if (GPIO_OUTPUT_HIGH == level)
        {
            /*airbag high level test success, notify dut change airbag level to low level and continue airbag low level test*/
            ret = FTM_SUCCESS;
        }
        else
        {
            /*airbag high level test fail, notify pc airbag test fail*/
            ret = FTM_FAILED;
        }
        FtmMcuTxToDutCmd(FTM_TX_DUT_AIRBAG_INDEX, FTM_CMD_LOW);
    }
    else if (0x01 == para[0])
    {
        /*airbag low level test success, notify pc airbag test success*/
        if (GPIO_OUTPUT_LOW == level)
        {
            ret = FTM_SUCCESS;
            FtmMcuTxToPcResult(FTM_TX_PC_AIRBAG_INDEX, ret);
        }
        else
        {
            /*airbag low level test fail, notify pc airbag test fail*/
            ret = FTM_FAILED;
        }
    }

    if (FTM_FAILED == ret)
    {
        FtmMcuTxToPcResult(FTM_TX_PC_AIRBAG_INDEX, ret);
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm airbag level %d, test ret %d\r\n", level, ret);
}

/*************************************************
函数名称: FtmRxMcuAcc
函数功能: 接收Acc控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuAcc(uint8_t *para, uint16_t len)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ACC], &level);
    if (0x00 == para[0])
    {
        if (GPIO_OUTPUT_LOW == level)
        {
            ret = FTM_SUCCESS;
        }
        else
        {
            ret = FTM_FAILED;
        }
        FtmMcuTxToDutCmd(FTM_TX_DUT_ACC_INDEX, FTM_CMD_LOW);
    }
    else if (0x01 == para[0])
    {
        if (GPIO_OUTPUT_HIGH == level)
        {
            ret = FTM_SUCCESS;
            FtmMcuTxToPcResult(FTM_TX_PC_ACC_INDEX, ret);
        }
        else
        {
            ret = FTM_FAILED;
        }
    }

    if (FTM_FAILED == ret)
    {
        FtmMcuTxToPcResult(FTM_TX_PC_ACC_INDEX, ret);
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm acc level %d, test ret %d\r\n", level, ret);
}

/*************************************************
函数名称: FtmRxMcuFchgStaInput
函数功能: 接收快充检测输入信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/03/10
*************************************************/
static void FtmRxMcuFchgStaInput(uint8_t *para, uint16_t len)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_FCHG_STA_INPUT], &level);
    if (0x00 == para[0])
    {
        if (GPIO_OUTPUT_HIGH == level)
        {
            ret = FTM_SUCCESS;
        }
        else
        {
            ret = FTM_FAILED;
        }
        FtmMcuTxToDutCmd(FTM_TX_DUT_DIGIT_INPUT_INDEX, FTM_CMD_LOW);  // Multiplex digital input command
    }
    else if (0x01 == para[0])
    {
        if (GPIO_OUTPUT_LOW == level)
        {
            ret = FTM_SUCCESS;
            FtmMcuTxToPcResult(FTM_TX_PC_DIGIT_INPUT_INDEX, ret);  // Multiplex digital input command
        }
        else
        {
            ret = FTM_FAILED;
        }
    }

    if (FTM_FAILED == ret)
    {
        FtmMcuTxToPcResult(FTM_TX_PC_DIGIT_INPUT_INDEX, ret);  // Multiplex digital input command
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm FCHG STA level %d, test ret %d\r\n", level, ret);
}

/*************************************************
函数名称: FtmRxMcuSchgStaInput
函数功能: 接收慢充检测输入信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/03/10
*************************************************/
static void FtmRxMcuSchgStaInput(uint8_t *para, uint16_t len)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_SCHG_STA_INPUT], &level);
    if (0x00 == para[0])
    {
        if (GPIO_OUTPUT_HIGH == level)
        {
            ret = FTM_SUCCESS;
        }
        else
        {
            ret = FTM_FAILED;
        }
        FtmMcuTxToDutCmd(FTM_TX_DUT_ON_INDEX, FTM_CMD_LOW);  // Multiplex ON command
    }
    else if (0x01 == para[0])
    {
        if (GPIO_OUTPUT_LOW == level)
        {
            ret = FTM_SUCCESS;
            FtmMcuTxToPcResult(FTM_TX_PC_ON_INDEX, ret);  // Multiplex ON command
        }
        else
        {
            ret = FTM_FAILED;
        }
    }

    if (FTM_FAILED == ret)
    {
        FtmMcuTxToPcResult(FTM_TX_PC_ON_INDEX, ret);  // Multiplex ON command
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm SCHG STA level %d, test ret %d\r\n", level, ret);
}

/*************************************************
函数名称: FtmRxMcuAccOutCtl
函数功能: 接收ACC_OUT输出信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/03/06
*************************************************/
static void FtmRxMcuAccOutCtl(uint8_t *para, uint16_t len)
{
    if (0x00 == para[0])
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ACC_OUT_CTL], GPIO_OUTPUT_HIGH);
        FtmMcuTxToDutCmd(FTM_TX_DUT_DIGIT_OUTPUT1_INDEX, FTM_CMD_HIGH);  // Multiplex digital output 1 command
    }
    else if(0x01 == para[0])
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ACC_OUT_CTL], GPIO_OUTPUT_LOW);
        FtmMcuTxToDutCmd(FTM_TX_DUT_DIGIT_OUTPUT1_INDEX, FTM_CMD_LOW);  // Multiplex digital output 1 command
    }
}

/*************************************************
函数名称: FtmRxMcuHu
函数功能: 接收MUTE输出信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuHu(uint8_t *para, uint16_t len)
{
    if (0x00 == para[0])
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_MUTE_OUTPUT], GPIO_OUTPUT_HIGH);
        FtmMcuTxToDutCmd(FTM_TX_DUT_HU_INDEX, FTM_CMD_HIGH);
    }
    else if (0x01 == para[0])
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_MUTE_OUTPUT], GPIO_OUTPUT_LOW);
        FtmMcuTxToDutCmd(FTM_TX_DUT_HU_INDEX, FTM_CMD_LOW);
    }
    else
    {
        /* NULL */
    }
}

/*************************************************
函数名称: FtmRxMcuLedGps
函数功能: 接收GPS的LED控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuLedGps(uint8_t *para, uint16_t len)
{
    if (0x00 == para[0])
    {
        PwmChannelInit(PWM_LED_CHANNEL_GPS_PHY, 0);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GPS_PHY_LED, GPIO_STATUS_ON, 0x00, 0x00);

        // Tbox输出会反相，底板检测到高电平
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_GPS_INDEX, FTM_CMD_HIGH);
    }
    else if (0x01 == para[0])
    {
        PwmChannelInit(PWM_LED_CHANNEL_GPS_PHY, 100);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GPS_PHY_LED, GPIO_STATUS_ON, 0x00, 0x00);

        // Tbox输出会反相，底板检测到高电平
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_GPS_INDEX, FTM_CMD_LOW);
    }
    else
    {
        /* NULL */
    }
}
/*************************************************
函数名称: FtmRxMcuLedAntiTheft
函数功能: 接收防盗LED控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuLedAntiTheft(uint8_t *para, uint16_t len)
{
    if (0x00 == para[0])
    {
        // 输出会反相，底板检测到高电平
        // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_LED10], GPIO_OUTPUT_LOW);
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_ANTITHEFT_INDEX, FTM_CMD_HIGH);
    }
    else if (0x01 == para[0])
    {
        // 输出会反相，底板检测到高电平
        // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_LED10], GPIO_OUTPUT_HIGH);
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_ANTITHEFT_INDEX, FTM_CMD_LOW);
    }
    else
    {
        /* NULL */
    }
}

/*************************************************
函数名称: FtmRxMcuLedEcall
函数功能: 接收Ecall的LED控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuLedEcall(uint8_t *para, uint16_t len)
{
    if (0x00 == para[0])
    {
        PwmChannelInit(PWM_LED_CHANNEL_9, 100);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_ECALL_SOS_GREEN_LED, GPIO_STATUS_ON, 0x00, 0x00);
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_ECALL_INDEX, 0);
    }
    else if (0x01 == para[0])
    {
        PwmChannelInit(PWM_LED_CHANNEL_9, 0);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_ECALL_SOS_GREEN_LED, GPIO_STATUS_ON, 0x00, 0x00);
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_ECALL_INDEX, 1);
    }
    else
    {
        /* NULL */
    }
}

/*************************************************
函数名称: FtmRxMcuReadModuleWakeUpPin
函数功能: 接收ARM唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
void FtmRxMcuReadModuleWakeUpPin(uint8_t *para, uint16_t len)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_WAKEUP_OUT], &level);
    if (0x00 == para[0])
    {
        if (GPIO_OUTPUT_HIGH == level)
        {
            ret = FTM_SUCCESS;
        }
        else
        {
            ret = FTM_FAILED;
        }
        FtmMcuTxToArmCmd(FTM_TX_ARM_SET_MODULE_WAKEUP_PIN_INDEX, FTM_CMD_LOW);
    }
    else if (0x01 == para[0])
    {
        if (GPIO_OUTPUT_LOW == level)
        {
            ret = FTM_SUCCESS;
            FtmMcuTxToPcResult(FTM_TX_PC_MODULE_WAKEUP_PIN_INDEX, ret);
        }
        else
        {
            ret = FTM_FAILED;
        }
    }

    if (FTM_FAILED == ret)
    {
        FtmMcuTxToPcResult(FTM_TX_PC_MODULE_WAKEUP_PIN_INDEX, ret);
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm module wakeup level %d, test ret %d\r\n", level, ret);
}

/*************************************************
函数名称: FtmRxMcuBt
函数功能: 接收蓝牙检测控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuBtReadVersion(void)
{
    uint8_t btVersionBuf[] = {"AT+VERION=?\r\n"};

    BtTxUartCmd((char *)btVersionBuf);

    g_canFtmInfo.ftmTestCbk        = (ftmTestCallBack)&FtmMcuTxToPcBuf;
    g_canFtmInfo.ftmCurrentTxIndex = FTM_TX_PC_BT_VERSION_CHECK_INDEX;
    g_canFtmInfo.ftmCurrentRxIndex = FTM_RX_MCU_BT_VERSION_CHECK_INDEX;
}

/*************************************************
函数名称: FtmRxMcuBt
函数功能: 接收蓝牙检测控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuBtReadMacAddr(void)
{
    uint8_t btDeviceId[] = {"AT+DEVID=?\r\n"};

    BtTxUartCmd((char *)btDeviceId);

    g_canFtmInfo.ftmTestCbk        = (ftmTestCallBack)&FtmMcuTxToPcBuf;
    g_canFtmInfo.ftmCurrentTxIndex = FTM_TX_PC_BLE_MAC_INDEX;
    g_canFtmInfo.ftmCurrentRxIndex = FTM_RX_MCU_READ_BLE_MAC;
}

/*************************************************
函数名称: FtmRxMcuBtConn
函数功能: 连接蓝牙模块
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuBtConn(uint8_t *para, uint16_t len)
{
    char        btMac[15];                              // = "AT+CON_16=0C61CF399308,1000\r\n";//len :31
    const uint8_t scanDevOn[]   = "AT+START_SCAN=1\r\n";  // len 19
    const uint8_t scanClose[]   = "AT+SCAN_RES=0\r\n";    // len 17
    const uint8_t btDevidBuf[]  = {"AT+DEVID=?\r\n"};
    uint8_t      *pFtmBtHead    = NULL;
    uint8_t       btTempBuf[40] = {0x00};

    BtServices *pBtServices = BtModuleServiceRead();

    if (BT_MODULE_MASTER_TYPE == pBtServices->btModuleType)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm pc bt connect info:%s\r\n", para);

        /*PC 返回的PC所使用蓝牙从模块信息如下格式: MAC=0C61CF386C8F:PASS=112233445566*/
        if ((NULL == para) || (0x00 == len))
        {
            return;
        }

        pFtmBtHead = (uint8_t *)strstr((char *)para, "MAC=");
        if (pFtmBtHead)
        {
            FtmMcuTxToPcResult(FTM_TX_PC_BT_CONNECT_INFO_INDEX, FTM_SUCCESS);
            memcpy(btMac, pFtmBtHead + 4, 12);
            memset(btTempBuf, 0x00, sizeof(btTempBuf));
            sprintf((char *)btTempBuf, "AT+ADVFLITER=%s,0000\r\n", btMac);
            BtTxUartCmd((char *)btTempBuf);
            // BtSendData(btTempBuf, strlen(btTempBuf));
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm pc bt len %d, mac:%s\r\n", strlen((char *)btTempBuf), btTempBuf);

            BtTxUartCmd((char *)scanDevOn);
            // BtSendData(scanDevOn, strlen(scanDevOn));
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm pc bt len %d, mac:%s\r\n", strlen((char *)scanDevOn), scanDevOn);

            // BtTxUartCmd(scanClose);
            // BtSendData(scanDevOn, strlen(scanDevOn));
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm pc bt len %d, cmd:%s\r\n", strlen((char *)scanClose), scanClose);

            memset(btTempBuf, 0x00, sizeof(btTempBuf));
            sprintf((char *)btTempBuf, "AT+CON_16=%s,1000\r\n", btMac);
            BtTxUartCmd((char *)btTempBuf);
            // BtSendData(btTempBuf, strlen(btTempBuf));
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm pc bt len %d, mac:%s\r\n", strlen((char *)btTempBuf), btTempBuf);
        }
        else
        {
            FtmMcuTxToPcResult(FTM_TX_PC_BT_CONNECT_INFO_INDEX, FTM_FAILED);
        }
        g_canFtmInfo.ftmCurrentTxIndex = 0x00;
        g_canFtmInfo.ftmCurrentRxIndex = 0x00;
    }
    else if (BT_MODULE_SLAVE_TYPE == pBtServices->btModuleType)
    {
        /*Send Read Bt Devid cmd to uart, and wait for bt return*/
        btTempBuf[0] = BT_MODULE_PASS_DATA_TYPE_NORMAL;
        strcat((char *)&btTempBuf[1], (char *)btDevidBuf);
        BtModulePassReqTxEvent(btTempBuf, strlen((char *)btDevidBuf) + 1);
        g_canFtmInfo.ftmTestCbk        = (ftmTestCallBack)&FtmMcuTxToPcBuf;
        g_canFtmInfo.ftmCurrentTxIndex = FTM_TX_PC_BT_CONNECT_INFO_INDEX;
        g_canFtmInfo.ftmCurrentRxIndex = FTM_RX_MCU_BT_CONNECT_INFO_INDEX;
    }
}

/*************************************************
函数名称: FtmRxMcuBtSpeed
函数功能: 蓝牙速率测试
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
// static void FtmRxMcuBtSpeedJidanMaster(void)
// {
//     uint8_t    buf[16];
//     char     scanDevOff[] = "AT+START_SCAN=0\r\n";  // len 19
//     FtmInfo *pFtmInfo = FtmInitRead();

//     if (FTM_MODE_ENTER != pFtmInfo->ftmMode)
//     {
//         SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ftm BTSpeed return\r\n");
//         return;
//     }

//     sprintf((char *)buf, "AT+SEND=%s\r\n", "ABCDEF");
//     BtTxUartCmd((char *)buf);
//     SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm bt test speed:%S\r\n", buf);
//     // 关闭蓝牙扫描
//     BtTxUartCmd(scanDevOff);
// }

/*************************************************
函数名称: FtmRxMcuBtSpeed
函数功能: 蓝牙速率测试
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuBtSpeedSlave(void)
{
    uint8_t sendBuf[5] = {0x00};
    sendBuf[0]         = 0xAB;
    sendBuf[1]         = 0xCD;
    sendBuf[2]         = 0xEF;
    sendBuf[3]         = 0xEF;

    BtTxUartCmd((char *)sendBuf);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm bt send data\r\n");
}

/*************************************************
函数名称: FtmRxMcuMic
函数功能: 接收Mic检测控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
// static void FtmRxMcuMic(void)
// {
// }

/*************************************************
函数名称: FtmRxMcuLine
函数功能: 接收Line+输出控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
// static void FtmRxMcuLine(void)
// {
// }
/*************************************************
函数名称: FtmRxMcuSpeaker
函数功能: 接收喇叭控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
// static void FtmRxMcuSpeaker(void)
// {
// }

/*************************************************
函数名称: FtmRxMcuBatTemp
函数功能: 接收电池温度控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuBatTemp(void)
{
    uint8_t  buf[5];
    int16_t  value  = -30;
    uint16_t batVol = 0x00;

    value = BatReadTemperature();
    if (value < 0)
    {
        buf[0] = 1;  // 负数
        value  = 0 - value;
        buf[1] = (uint8_t)value;
    }
    else
    {
        buf[0] = 0;
        buf[1] = (uint8_t)value;
    }

    batVol = BatReadVoltage();
    buf[2] = (batVol >> 8) & 0xff;
    buf[3] = (batVol) & 0xff;

    FtmMcuTxToPcBuf(FTM_TX_PC_BAT_TEMP_INDEX, buf, 4);

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm bat temperature:%d, vol %d\r\n", value, batVol);
}

/*************************************************
函数名称: FtmRxMcuBatCharge
函数功能: 接收电池充电测试控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuBatCharge(uint8_t *para, uint16_t len)
{
    if (0x00 == para[0])
    {
        // 充电打开
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BAT_CHARGE_EN], GPIO_OUTPUT_HIGH);
        FtmMcuTxToDutCmd(FTM_TX_DUT_CHARGE_INDEX, 0x01);
    }
    else if (0x01 == para[0])
    {
        // 充电关闭
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BAT_CHARGE_EN], GPIO_OUTPUT_LOW);
        FtmMcuTxToDutCmd(FTM_TX_DUT_CHARGE_INDEX, 0x02);
    }
    else
    {
        /* NULL */
    }

#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm bat charge %d\r\n", para[0]);
#endif
}

/*************************************************
函数名称: FtmRxMcuGpsAntOpen
函数功能: 接收GPS天线开路控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuGpsAntOpen(void)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_OPEN], &level);
    if (GPIO_OUTPUT_HIGH == level)
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(FTM_TX_PC_GPS_ANT_OPEN_INDEX, ret);
    FtmMcuTxToDutCmd(FTM_TX_DUT_GPSANT_OPEN, FTM_CMD_LOW);
}

/*************************************************
函数名称: FtmRxMcuGpsAntShort
函数功能: 接收GPS天线短路控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuGpsAntShort(void)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_SHORT], &level);
    if (GPIO_OUTPUT_LOW == level)
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(FTM_TX_PC_GPS_ANT_SHORT_INDEX, ret);
    FtmMcuTxToDutCmd(FTM_TX_DUT_GPSANT_SHORT, FTM_CMD_LOW);
}

/*************************************************
函数名称: FtmRxMcuGpsAntNormal
函数功能: 接收GPS天线正常控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
**************************unused***********************/
static void FtmRxMcuGpsAntNormal(void)
{
    GpioLevel       shortLevel = GPIO_OUTPUT_LOW;
    GpioLevel       openLevel  = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret        = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_OPEN], &openLevel);
    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_SHORT], &shortLevel);
    if ((GPIO_OUTPUT_HIGH == shortLevel) && (GPIO_OUTPUT_LOW == openLevel))
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(MCU_PC_GPS_ANT_NORMAL_INDEX, ret);
}

/*************************************************
函数名称: FtmRxMcuLteAntOpen
函数功能: 接收4g天线开路控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuLteAntOpen(void)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_LTE_ANT_OPEN], &level);
    if (GPIO_OUTPUT_HIGH == level)
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(FTM_TX_PC_LTE_ANT_OPEN_INDEX, ret);
    FtmMcuTxToDutCmd(FTM_TX_DUT_LTEANT_OPEN, FTM_CMD_LOW);
}

/*************************************************
函数名称: FtmRxMcuLteAntShort
函数功能: 接收lte天线短路控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuLteAntShort(void)
{
    GpioLevel       level = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret   = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_LTE_ANT_SHORT], &level);
    if (GPIO_OUTPUT_LOW == level)
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(FTM_TX_PC_LTE_ANT_SHORT_INDEX, ret);
    FtmMcuTxToDutCmd(FTM_TX_DUT_LTEANT_SHORT, FTM_CMD_LOW);
}

/*************************************************
函数名称: FtmRxMcuLteAntNormal
函数功能: 接收lte天线正常控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
**************************unused***********************/
static void FtmRxMcuLteAntNormal(void)
{
    GpioLevel       shortLevel = GPIO_OUTPUT_LOW;
    GpioLevel       openLevel  = GPIO_OUTPUT_LOW;
    FtmExcuteResult ret        = FTM_FAILED;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_LTE_ANT_OPEN], &openLevel);
    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_LTE_ANT_SHORT], &shortLevel);
    if ((GPIO_OUTPUT_HIGH == shortLevel) && (GPIO_OUTPUT_LOW == openLevel))
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(MCU_PC_LTE_ANT_NORMAL_INDEX, ret);
}

/*************************************************
函数名称: FtmRxMcuMicShort
函数功能: 接收Mic短路控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuMicShort(void)
{
    uint16_t          vv  = 0;
    FtmExcuteResult ret = FTM_FAILED;

    vv = ReadMicValue();
    if (vv < 500)
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(FTM_TX_PC_MIC_SHORT_INDEX, ret);
    FtmMcuTxToDutCmd(FTM_TX_DUT_MIC_SHORT, FTM_CMD_LOW);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "FTM MIC short vol %d, ret is %d\r\n", vv, ret);
}

/*************************************************
函数名称: FtmRxMcuMicOpen
函数功能: 接收Mic开路控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuMicOpen(void)
{
    uint16_t          vv  = 0;
    FtmExcuteResult ret = FTM_FAILED;

    vv = ReadMicValue();
    if (vv >= 2300)
    {
        ret = FTM_SUCCESS;
    }
    else
    {
        ret = FTM_FAILED;
    }

    FtmMcuTxToPcResult(FTM_TX_PC_MIC_OPEN_INDEX, ret);
    FtmMcuTxToDutCmd(FTM_TX_DUT_MIC_OPEN, FTM_CMD_LOW);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "FTM MIC open vol %d, ret is %d\r\n", vv, ret);
}

/*************************************************
函数名称: FtmRxMcuUart32
函数功能: 接收Uart32检测信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/25
*************************************************/
void FtmRxMcuUart32(void)
{
    // uint8_t i;
    // uint8_t buf[8];

    // for (i = 0; i < 8; i++)
    // {
    //     buf[i] = 0xa0 + i;
    // }
    /*增加日志保证延时*/
    //Uart32SendData(8, buf);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm uart32 \r\n");
    //Uart32SendData(8, buf);
}

/*************************************************
函数名称: FtmRxMcuModuleWakeup
函数功能: 接收模块唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
// static void FtmRxMcuModuleWakeup(void)
// {
//     // 通知ARM睡眠
//     NotifyArmSleep();
// }

/*************************************************
函数名称: FtmRxMcuRtcWakeup
函数功能: 接收RTC唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuRtcWakeup(void)
{
    uint8_t    tempBuf  = 0x00;
    //uint8_t    buf[6]   = {0};
    uint8_t    wakeSrc  = 0;
    FtmInfo *pFtmInfo = FtmInitRead();

    if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_RTC_BIT)
    {
        RTC_DRV_GetCurrentTimeDate(0, &tempBuf);
        // RtcReadRegister(PCF85063_REG_CTRL2, &tempBuf, 1);
        if (0x40 == (tempBuf & 0x40))
        {
            // 清除RTC使能标志
            // buf[0] = PCF85063_REG_CTRL2;
            // buf[1] = 0x05;
            //McuI2cMasterSend(RTC_SLAVE_ADDR, 2, buf);

            // 清空RTC唤醒时钟配置
            // buf[0] = PCF85063_REG_AS;
            // buf[1] = 0x80;
            // buf[2] = 0x80;
            // buf[3] = 0x80;
            // buf[4] = 0x80;
            // buf[5] = 0x80;
            //McuI2cMasterSend(RTC_SLAVE_ADDR, 6, buf);

            wakeSrc = TBOX_WAKEUP_RTC;
        }
        else
        {
            wakeSrc = TBOX_WAKEUP_SENSOR;
        }
    }
    else
    {
        wakeSrc = TBOX_WAKEUP_BPLUS;
    }
    pFtmInfo->aliveWakeEvent = FALSE;

    FtmMcuTxToPcResult(FTM_TX_PC_WAKE_UP_SOURCE, wakeSrc);
}

/*************************************************
函数名称: FtmWakeupTestIntHandle
函数功能: Ftm唤醒测试中断处理，在中断中调用
输入参数: 中断类型
输出参数: 无
函数返回类型值：无
编写者: BenYuLong
编写日期: 2020/08/12
*************************************************/
void FtmWakeupTestIntHandle(FtmAliveWakeBit wakeBit)
{
    FtmInfo *pFtmInfo = FtmInitRead();

    if (FTM_MODE_ENTER != pFtmInfo->ftmMode)
    {
        return;
    }

    pFtmInfo->aliveWakeBit |= wakeBit;
    pFtmInfo->aliveWakeEvent = TRUE;

    if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_CAN2_BIT)
    {
        //MKP2 = 1U;
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_LOW);
    }
    if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_CAN4_BIT)
    {
        //MKP4 = 1U;
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_LOW);
    }
    if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_CAN5_BIT)
    {
        //MKP12 = 1U;
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN5_STB], GPIO_OUTPUT_LOW);
    }
}

/*************************************************
函数名称: FtmRxSystemSleepTest
函数功能: 接收系统休眠测试信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: BenYuLong
编写日期：2020/08/13
*************************************************/
static void FtmRxSystemSleepTest(void)
{
    const uint8_t scanDevOn[] = "AT+START_SCAN=1\r\n";  // len 19
    const uint8_t openAdv[]   = "AT+SOFT_RST=1\r\n";
    BtServices *pBtServices = BtModuleServiceRead();

    FtmMcuTxToDutCmd(FTM_TX_DUT_SYSTEM_SLEEP_INDEX, 0x00);
    NotifyArmSleep();
    g_ftmWakeup = FTM_CAN_WAKEUP;

    // if (BT_MODULE_MASTER_TYPE == pBtServices->btModuleType)
    // {
    //     BtTxUartCmd((char *)scanDevOn);
    // }
    // else if (BT_MODULE_SLAVE_TYPE == pBtServices->btModuleType)
    // {
    //     /*BLE软复位后默认打开广播*/
    //     BtTxUartCmd((char *)openAdv);
    // }
}

/*************************************************
函数名称: FtmRxMcuBtWakeup
函数功能: 接收bt唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuBtWakeup(void)
{
    const uint8_t scanDevOff[] = "AT+START_SCAN=0\r\n";  // len 19
    const uint8_t closeAdv[]   = "AT+ADV=0\r\n";
    BtServices *pBtServices  = BtModuleServiceRead();
    FtmInfo    *pFtmInfo     = FtmInitRead();
    uint8_t       wakeSrc      = 0;

    if (BT_MODULE_MASTER_TYPE == pBtServices->btModuleType)
    {
        BtTxUartCmd((char *)scanDevOff);
    }
    else if (BT_MODULE_SLAVE_TYPE == pBtServices->btModuleType)
    {
        BtTxUartCmd((char *)closeAdv);
    }

    if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_BLE_BIT)
    {
        wakeSrc = TBOX_WAKEUP_BLE;
    }
    else
    {
        wakeSrc = TBOX_WAKEUP_BPLUS;
    }
    pFtmInfo->aliveWakeEvent = FALSE;

    FtmMcuTxToPcResult(FTM_TX_PC_WAKE_UP_SOURCE, wakeSrc);
}

/*************************************************
函数名称: FtmRxMcuCan2Wakeup
函数功能: 接收CAN2唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuCan2Wakeup(void)
{
    FtmInfo *pFtmInfo = FtmInitRead();
/*
    // INTP2 P0_5  1nd Input CAN2 Rx  中断检测设置  边缘检测  上升沿检测
    MKP2            = 1U;
    RFP2            = 0U;
    TBP2            = 0U;
    FCLA0CTL2_INTPL = 0x01;
    PU0 |= (1 << 5);
    P0 &= ~(1 << 5);
    MKP2 = 0U;
*/
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_HIGH);

    FtmMcuTxToDutCmd(FTM_TX_DUT_CAN2_INT_TEST_INDEX, 0);

    pFtmInfo->aliveWakeEvent = FALSE;
    pFtmInfo->aliveWakeBit |= FTM_ALIVE_WAKE_CAN2_BIT;
}

/*************************************************
函数名称: FtmRxMcuCan3Wakeup
函数功能: 接收CAN3唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/03
*************************************************/
static void FtmRxMcuCan3Wakeup(void)
{
}

/*************************************************
函数名称: FtmRxMcuCan4Wakeup
函数功能: 接收CAN4唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuCan4Wakeup(void)
{
    FtmInfo *pFtmInfo = FtmInitRead();
/*
    // INTP2 P0_9  1nd Input CAN4 Rx  中断检测设置  边缘检测  上升沿检测
    MKP4            = 1U;
    RFP4            = 0U;
    TBP4            = 0U;
    FCLA0CTL4_INTPL = 0x01;
    PU0 |= (1 << 9);
    P0 &= ~(1 << 9);
    MKP4 = 0U;
*/
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_HIGH);

    FtmMcuTxToDutCmd(FTM_TX_DUT_CAN4_INT_TEST_INDEX, 0);

    pFtmInfo->aliveWakeEvent = FALSE;
    pFtmInfo->aliveWakeBit |= FTM_ALIVE_WAKE_CAN4_BIT;
}

/*************************************************
函数名称: FtmRxMcuCan5Wakeup
函数功能: 接收CAN5唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuCan5Wakeup(void)
{
    FtmInfo *pFtmInfo = FtmInitRead();

/*
    // INTP2 P0_13  1nd Input CAN5 Rx  中断检测设置  边缘检测  上升沿检测
    MKP12           = 1U;
    RFP12           = 0U;
    TBP12           = 0U;
    FCLA0CTL4_INTPH = 0x01;
    PU0 |= (1 << 13);
    P0 &= ~(1 << 13);

    MKP12 = 0U;
*/
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN5_STB], GPIO_OUTPUT_HIGH);

    FtmMcuTxToDutCmd(FTM_TX_DUT_CAN5_INT_TEST_INDEX, 0);

    pFtmInfo->aliveWakeEvent = FALSE;
    pFtmInfo->aliveWakeBit |= FTM_ALIVE_WAKE_CAN5_BIT;
}

/*************************************************
函数名称: FtmRxMcuCan5Wakeup
函数功能: 接收CAN5唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuCanWakeupPostHandle(void)
{
    uint8_t    wakeSrc  = 0;
    FtmInfo *pFtmInfo = FtmInitRead();

    if ((pFtmInfo->aliveWakeEvent == TRUE) && (pFtmInfo->aliveWakeBit & 0xF0))
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "canwake bit:0x%02x\r\n", pFtmInfo->aliveWakeBit);
        pFtmInfo->aliveWakeEvent = FALSE;
        if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_CAN2_BIT)
        {
            wakeSrc = TBOX_WAKEUP_CAN2;
        }
        if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_CAN4_BIT)
        {
            wakeSrc = TBOX_WAKEUP_CAN3;
        }
        if (pFtmInfo->aliveWakeBit & FTM_ALIVE_WAKE_CAN5_BIT)
        {
            wakeSrc = TBOX_WAKEUP_CAN4;
        }
        FtmMcuTxToPcResult(FTM_TX_PC_WAKE_UP_SOURCE, wakeSrc);
    }
}

/*************************************************
函数名称: FtmRxMcuCan5Wakeup
函数功能: 接收CAN5唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
static void FtmRxMcuEcallWakeup(void)
{
}

/*************************************************
函数名称: FtmRxMcuEcuID
函数功能: 接收CAN5唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
void FtmRxMcuNavigateTest(uint8_t *para, uint16_t len)
{
    uint8_t    buf[2]   = {0x00};
    FtmInfo *pFtmInfo = FtmInitRead();

    if (FTM_INS_DRIVE_SUCCESS == (pFtmInfo->ftmINSTest & FTM_INS_DRIVE_SUCCESS))
    {
        buf[0] = FTM_SUCCESS;
    }
    else
    {
        buf[0] = FTM_FAILED;
    }

    FtmMcuTxToPcBuf(FTM_TX_PC_NVAIGATE_TEST, buf, 1);
}

/*************************************************
函数名称: FtmRxMcuWriteTboxCfg
函数功能: 公司生产上位机对T-Box功能进行配置
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2022/03/12
*************************************************/
void FtmRxMcuWriteTboxCfg(uint8_t *para, uint16_t len)
{
    TboxSelfConfigPara tboxSelfConfigPara = {0};
    uint8_t buf[8] = {0x00};
    uint8_t ret = 0x00;
    Msg msg;

    TboxSelfConfigPara *tboxSelfConfig = GetTboxSelfConfigData();
    memcpy((uint8 *)&tboxSelfConfigPara, (uint8 *)tboxSelfConfig, sizeof(TboxSelfConfigPara));
    // para[0]具体含义查看NvApi.h定义的 enum TboxCfgType
    /*这个地方由上位机进行配置下发, T-Box进行配置信息写入*/
    tboxSelfConfigPara.tboxCfgType = para[0];
    ret        = NvApiWriteData(NV_ID_SELF_CONFIG, (uint8_t *)&tboxSelfConfigPara, sizeof(TboxSelfConfigPara));

    buf[0]     = FTM_TX_PC_NAVIGATE_WRITE_RESULT_INDEX;
    buf[1]     = ret;
    buf[2]     = tboxSelfConfig->tboxCfgType;
    msg.event  = MESSAGE_TX_PC_CMD;
    msg.len    = 0x03;
    msg.lparam = (uint32_t)buf;
    FtmCanTxPackageFrameData(msg);

#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm write tboxcfg is 0x%02x\r\n", tboxSelfConfig->tboxCfgType);
#endif
}

/*************************************************
函数名称: FtmRxMcuRequestWakeup
函数功能: 接收查询唤醒源控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/23
*************************************************/
static void FtmRxMcuRequestWakeup(void)
{
    NvApiReadData(NV_ID_BACKUP_RAM, (uint32_t *)&g_backupRamInfo, g_nvInfoMap[NV_ID_BACKUP_RAM].nvValidLen);

    // 通知ARM由MCU返回结果
    FtmMcuTxToArmCmd(FTM_TX_ARM_WAKEUP_SOURCE_INDEX, g_backupRamInfo.mcuWakeupSource);

#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm Request Wakeup is %d\r\n", g_backupRamInfo.mcuWakeupSource);
#endif
}

/*************************************************
函数名称: FtmRxLin
函数功能: 接收Lin通信检测
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/07/19
*************************************************/
static void FtmRxLin(uint8_t *para, uint16_t len)
{
    //RLin21SendData(LIN_CMD_RX_DATA, MCU_FTM_TEST_LIN_ID, 8, NULL);
}

/*************************************************
函数名称: LinRxFtm
函数功能: Lin接收到通信检测消息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: benyulong
编写日期 :2019/08/05
*************************************************/
void FtmRxLinResponse(uint8_t *data)
{
    uint8_t buf[2] = {0};
    Msg     msg;

    buf[0] = FTM_TX_PC_LIN_INDEX;
    if ((0x01 == data[0]) &&
        (0x02 == data[1]) &&
        (0x03 == data[2]) &&
        (0x04 == data[3]) &&
        (0x05 == data[4]) &&
        (0x06 == data[5]) &&
        (0x07 == data[6]) &&
        (0x08 == data[7]))
    {
        buf[1] = FTM_SUCCESS;
    }
    else
    {
        buf[1] = FTM_FAILED;
    }
    msg.event  = MESSAGE_TX_PC_CMD;
    msg.len    = 0x02;
    msg.lparam = (uint32_t)buf;
    FtmCanTxPackageFrameData(msg);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm test Lin %d\r\n", buf[1]);
}

/*************************************************
函数名称: FtmRxMcuReadTest
函数功能: 接收读取设备信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/30
*************************************************/
static void FtmRxMcuReadSoftVersion(uint8_t *para, uint16_t len)
{
    uint8_t tempBuf[20] = {0x00};
    uint8_t mcuVer      = 0x00;
    uint8_t armVer      = 0x00;

    Rte_ReadSystemSupplierEcuSoftWareVersionNumber(tempBuf);
    mcuVer     = tempBuf[5];
    armVer     = tempBuf[7];
    tempBuf[5] = mcuVer / 10 % 10 + 0x30;
    tempBuf[6] = mcuVer / 1 % 10 + 0x30;
    tempBuf[7] = '.';
    tempBuf[8] = armVer / 10 % 10 + 0x30;
    tempBuf[9] = armVer / 1 % 10 + 0x30;

    FtmMcuTxToPcBuf(FTM_TX_PC_SOFT_VERSION_INDEX, tempBuf, strlen((char *)tempBuf));
}

/*************************************************
函数名称: FtmRxMcuReadTest
函数功能: 接收读取设备信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/30
*************************************************/
static void FtmRxMcuReadHardVersion(uint8_t *para, uint16_t len)
{
    uint8_t tempBuf[20] = {0x00};
    uint8_t mainVer     = 0x00;
    uint8_t subVer      = 0x00;

    Rte_ReadSystemSupplierEcuHardWareVersionNumber(tempBuf);
    mainVer    = tempBuf[5];
    subVer     = tempBuf[7];
    tempBuf[5] = mainVer / 10 % 10 + 0x30;
    tempBuf[6] = mainVer / 1 % 10 + 0x30;
    tempBuf[7] = '.';
    tempBuf[8] = subVer / 10 % 10 + 0x30;
    tempBuf[9] = subVer / 1 % 10 + 0x30;

    FtmMcuTxToPcBuf(FTM_TX_PC_HARD_VERSION_INDEX, tempBuf, strlen((char *)tempBuf));
}

/*************************************************
函数名称: FtmRxMcuReadTest
函数功能: 接收读取设备信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/30
*************************************************/
static void FtmRxMcuReadBootLoaderVersion(uint8_t *para, uint16_t len)
{
    uint8_t tempBuf[TBOX_BOOT_VERSION_LEN] = {0x00};

    Rte_ReadSystemSupplierEcuBootVersionNumber(tempBuf);

    FtmMcuTxToPcBuf(FTM_TX_PC_BOOTLOADER_VERSION_INDEX, tempBuf, strlen((char *)tempBuf));
}

/*************************************************
函数名称: FtmRxMcuReadTest
函数功能: 接收读取设备信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/30
*************************************************/
static void FtmRxMcuReadECUName(uint8_t *para, uint16_t len)
{
    uint8_t tempBuf[10] = {0x00};

    Rte_ReadSystemName(tempBuf);

    FtmMcuTxToPcBuf(FTM_TX_PC_ECU_NAME_INDEX, tempBuf, sizeof(tempBuf));
}

/*************************************************
函数名称: FtmRxMcuLedTest
函数功能:
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/07/23
*************************************************/
static void FtmRxMcuLedTest(uint8_t *para, uint16_t len)
{
    uint8_t    ledIndicate = 0x00;
    FtmInfo *pFtmInfo    = FtmInitRead();

    pFtmInfo->ftmLedTestEvent = TRUE;

    ledIndicate = para[0];
    switch (ledIndicate)
    {
    /*电源指示灯测试*/
    case 0x00: {
        break;
    }
    /*IPC红色指示灯测试*/
    case 0x01: {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_RED_LED, GPIO_STATUS_ON, 0x00, 0x00);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_GREEN_LED, GPIO_STATUS_OFF, 0x00, 0x00);
        break;
    }
    /*IPC绿色指示灯测试*/
    case 0x02: {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_RED_LED, GPIO_STATUS_OFF, 0x00, 0x00);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_GREEN_LED, GPIO_STATUS_ON, 0x00, 0x00);
        break;
    }
    /*网络红色指示灯测试*/
    case 0x03: {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_MODULE_RED_LED, GPIO_STATUS_ON, 0x00, 0x00);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_MODULE_RED_LED, GPIO_STATUS_OFF, 0x00, 0x00);
        break;
    }
    /*网络绿色指示灯测试*/
    case 0x04: {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_MODULE_RED_LED, GPIO_STATUS_OFF, 0x00, 0x00);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_MODULE_RED_LED, GPIO_STATUS_ON, 0x00, 0x00);
        break;
    }
    /*GPS红色指示灯测试*/
    case 0x05: {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GNSS_RED_LED, GPIO_STATUS_ON, 0x00, 0x00);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GNSS_GREEN_LED, GPIO_STATUS_OFF, 0x00, 0x00);
        break;
    }
    /*GPS绿色指示灯测试*/
    case 0x06: {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GNSS_RED_LED, GPIO_STATUS_OFF, 0x00, 0x00);
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GNSS_GREEN_LED, GPIO_STATUS_ON, 0x00, 0x00);
        break;
    }
    /*退出指示灯测试*/
    case 0x0F: {
        pFtmInfo->ftmLedTestEvent = FALSE;
        if (g_commonInfo.handshakeStatus == HANDSHAKE_SUCESS)
        {
//            GpioControlHandShakeSend(GPIO_CONTROL_HEARTBEAT_SUCCESS);
        }
        else
        {
//            GpioControlHandShakeSend(GPIO_CONTROL_HEARTBEAT_TIMEOUT);
        }
        break;
    }
    }

    if (pFtmInfo->ftmLedTestEvent == TRUE)
    {
        FtmMcuTxToDutCmd(FTM_TX_DUT_LED_TEST_INDEX, ledIndicate);
    }
}

/*************************************************
函数名称: FtmRxMcuLedTest
函数功能:
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/11/03
*************************************************/
static void FtmRxMcuExitFtm(uint8_t *para, uint16_t len)
{
    // DiagPowerVoltageStatusSend(DIAG_SNED_PM_ENTER_POWER_OFF);
    NotifyArmShutDown();
}

/*************************************************
函数名称: FtmRxMcuReadKL30
函数功能:
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2021/10/20
*************************************************/
static void FtmRxMcuReadKL30(void)
{
    uint8_t  buf[2] = {0x00};
    uint16_t batVol = 0x00;

    batVol = ReadBpPlusValue(); /*转换得到mV单位*/
    buf[0] = (batVol >> 8) & 0xff;
    buf[1] = (batVol) & 0xff;

    FtmMcuTxToPcBuf(FTM_TX_PC_KL30_INDEX, buf, 2);
}

/*************************************************
函数名称: FtmTxPcCanResult
函数功能: 反馈PC CAN收发测试结果
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/07/12
*************************************************/
void FtmTxPcCanResult(uint8_t channel, uint8_t *buf)
{
    uint8_t i;
    uint8_t tmpBuf[8];

    for (i = 0; i < 8; i++)
    {
        tmpBuf[i] = buf[7 - i];
    }

    MCUCanSendFtmData(channel, CAN_TEST_RESPONSE_ID, 8, tmpBuf);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm test can %d\r\n", channel);
}

/*************************************************
函数名称: FtmRxSystemSupplierIdentifier
函数功能: 接收供应商ID
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/07/19
*************************************************/
// static void FtmRxSystemSupplierIdentifier(uint8_t *para, uint16_t len)
// {
//     Msg msg;

//     FtmMcuTxToPcResult(FTM_TX_PC_SUPPLIERID_WRITE_INDEX, FTM_SUCCESS);
//     msg.event  = EVENT_ID_RX_SUPPLIER_ID;
//     msg.len    = len;
//     msg.lparam = (uint32_t)para;
//     SystemSendMessage(TASK_ID_UPDATE, msg);
// }

/*************************************************
函数名称: FtmRxTboxSparePartNumber
函数功能: 接收T-Box零件号写入请求
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2022/03/12
*************************************************/
static void FtmRxTboxSparePartNumber(uint8_t *para, uint16_t len)
{
    Msg   msg;
    uint8 ret = 0x00;

    if (para == NULL || len == 0)
    {
        ret = FTM_FAILED;
    }
    else
    {
        ret        = FTM_SUCCESS;
        msg.event  = EVENT_ID_RX_SPAREPARTNUMBER;
        msg.len    = len;
        msg.lparam = (uint32_t)para;
        SystemSendMessage(TASK_ID_UPDATE, msg);
    }

    FtmMcuTxToPcResult(MCU_PC_SPAREPARTNUMBER_INDEX, ret);
}

/*************************************************
函数名称: FtmRxMcuTboxId
函数功能: 接收TBOX ID
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/07/19
*************************************************/
static void FtmRxMcuTboxId(uint8_t *para, uint16_t len)
{
    uint8_t  buf[20] = {0x30};
    uint32_t tboxSn  = 0x00;
    uint8_t  year    = 0x00;
    uint8_t  month   = 0x00;
    uint8_t  day     = 0x00;
    Msg    msg;

    /*清除故障码*/
    DiagDtcReqSystemClear();

    // 通知ARM,修改完wifi信息后，由ARM返回PC成功消息
    memcpy(buf + 1, para, len);
    buf[0]     = FTM_TX_ARM_MODIFY_WIFI;
    msg.event  = MESSAGE_TX_ARM_FTM_CMD;
    msg.len    = len + 1;
    msg.lparam = (uint32_t)buf;
    SystemSendMessage(TASK_ID_IPC, msg);

#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm tbox id result is %d\r\n", buf[1]);
#endif

    tboxSn     = (para[8] - 0x30) * 1000 + (para[9] - 0x30) * 100 + (para[10] - 0x30) * 10 + (para[11] - 0x30) * 1;
    year       = (para[0] - 0x30) * 10 + (para[1] - 0x30) * 1;
    month      = (para[2] - 0x30) * 10 + (para[3] - 0x30) * 1;
    day        = (para[4] - 0x30) * 10 + (para[5] - 0x30) * 1;
    buf[0]     = (tboxSn >> 24);
    buf[1]     = (tboxSn >> 16);
    buf[2]     = (tboxSn >> 8);
    buf[3]     = (tboxSn >> 0);
    buf[4]     = 0x30;
    buf[5]     = 0x30;
    buf[6]     = 0x30;
    buf[7]     = 0x30;
    buf[8]     = para[6];
    buf[9]     = para[7];
    buf[10]    = (year - 19) % 255;
    buf[11]    = month;
    buf[12]    = day;
    msg.event  = EVENT_ID_RX_TBOX_ID;
    msg.len    = 13;
    msg.lparam = (uint32_t)buf;
    SystemSendMessage(TASK_ID_UPDATE, msg);
}

/*************************************************
函数名称: FtmRxSimNumber
函数功能: 接收sim卡号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/07/19
*************************************************/
static void FtmRxSimNumber(uint8_t *para, uint16_t len)
{
    Msg msg;

    msg.event  = EVENT_ID_RX_SIM_NUMBER;
    msg.len    = len;
    msg.lparam = (uint32_t)para;
    SystemSendMessage(TASK_ID_UPDATE, msg);

    FtmMcuTxToPcResult(FTM_TX_PC_SIMNUMBER_WRITE_INDEX, FTM_SUCCESS);
#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "ftm sim number test\r\n");
#endif
}

/**********************成品***************************
函数名称: FtmRxMcuReadInformation
函数功能: 接收读取设备信息
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/30
*************************************************/
static void FtmRxMcuReadInformation(int index)
{
    Msg msg;
    // uint8_t tempBuf[70] = {0x00};
    // uint8_t readBuf[20] = {0x00};
    char   tempBuf[70] = {0x00};
    char   readBuf[20] = {0x00};
    uint8_t  len         = 0x00;
    uint8_t  tempLen     = 0x00;
    uint32_t tboxSn      = 0x00;

    tempBuf[0] = FTM_TX_PC_READINFO_INDEX;

    strcat(tempBuf, "VC=");
    Rte_ReadSystemSupplierIdentifier((uint8 *)readBuf);
    strcat(tempBuf, readBuf);
    strcat(tempBuf, ":");

    strcat(tempBuf, "PN=");
    Rte_ReadVehicleManufacturerSparePartNumber((uint8 *)readBuf);
    strcat(tempBuf, readBuf);
    strcat(tempBuf, ":");

    strcat(tempBuf, "SN=");
    tempLen = strlen(tempBuf);
    Rte_ReadEcuSerialNumber((uint8 *)readBuf);
    /*标签：year*/
    tempBuf[tempLen + 0] = (readBuf[10] + 19) / 10 % 10 + 0x30;
    tempBuf[tempLen + 1] = (readBuf[10] + 19) / 1 % 10 + 0x30;
    /*标签：month*/
    tempBuf[tempLen + 2] = readBuf[11] / 10 % 10 + 0x30;
    tempBuf[tempLen + 3] = readBuf[11] / 1 % 10 + 0x30;
    /*标签：day*/
    tempBuf[tempLen + 4] = readBuf[12] / 10 % 10 + 0x30;
    tempBuf[tempLen + 5] = readBuf[12] / 1 % 10 + 0x30;
    /*标签：生产线号*/
    tempBuf[tempLen + 6] = readBuf[8];
    tempBuf[tempLen + 7] = readBuf[9];
    /*标签: 流水号*/
    tboxSn                = (uint32_t)(readBuf[0] << 24) + (readBuf[1] << 16) + (readBuf[2] << 8) + (readBuf[3]);
    tempBuf[tempLen + 8]  = tboxSn / 1000 % 10 + 0x30;
    tempBuf[tempLen + 9]  = tboxSn / 100 % 10 + 0x30;
    tempBuf[tempLen + 10] = tboxSn / 10 % 10 + 0x30;
    tempBuf[tempLen + 11] = tboxSn / 1 % 10 + 0x30;

    len = strlen(tempBuf);

    msg.event  = MESSAGE_TX_PC_CMD;
    msg.len    = len;
    msg.lparam = (uint32_t)tempBuf;
    FtmCanTxPackageFrameData(msg);

    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "ftm read info:%s\r\n", tempBuf);
}

/*************************************************
函数名称: FtmCanMsgRxMcuCmd
函数功能: CAN接收到MCU命令处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmCanMsgRxMcuCmd(uint8_t *para, uint16_t len)
{
    uint8_t    cmd      = 0;
    FtmInfo *pFtmInfo = FtmInitRead();

    if (NULL == para)
    {
        return;
    }

    cmd = para[0];
#if (LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "pc->mcu cmd:0x%02x, para:0x%02x\r\n", cmd, para[1]);
#endif

    if (FTM_RX_MCU_MODE_INDEX != cmd)
    {
        if (FTM_MODE_ENTER != pFtmInfo->ftmMode)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "pc->mcu cmd not ftm mode,%d\r\n", pFtmInfo->ftmMode);
            return;
        }
    }

    switch (cmd)
    {
    case FTM_RX_MCU_MODE_INDEX: {
        FtmRxMcuMode(&para[1], len);
        break;
    }
    case FTM_RX_MCU_ECALL_INDEX: {
        FtmRxMcuEcallKey(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_AIRBAG_INDEX: {
        FtmRxMcuAirbag(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_ACC_INDEX: {
        FtmRxMcuAcc(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_DIGIT_INPUT_INDEX: {
        FtmRxMcuFchgStaInput(&para[1], 1);  // Multiplex digital input command
        break;
    }
    case FTM_RX_MCU_DIGIT_OUTPUT1_INDEX: {
        FtmRxMcuAccOutCtl(&para[1], 1);  // Multiplex digital output 1 command
        break;
    }
    case FTM_RX_MCU_DIGIT_OUTPUT2_INDEX: {
        break;
    }
    case FTM_RX_MCU_HU_INDEX: {
        FtmRxMcuHu(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_LED_GPS_INDEX: {
        break;
    }
    case FTM_RX_MCU_LED_ANTITHEFT_INDEX: {
        break;
    }
    case FTM_RX_MCU_LED_ECALL_INDEX: {
        FtmRxMcuLedEcall(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_BT_VERSION_CHECK_INDEX: {
        FtmRxMcuBtReadVersion();
        return;
    }
    case FTM_RX_MCU_BT_CONNECT_INFO_INDEX: {
        FtmRxMcuBtConn(&para[1], len - 1);
        return;
    }
    case FTM_RX_MCU_BT_SPEED_INDEX: {
        FtmRxMcuBtSpeedSlave();
        break;
    }
    case FTM_RX_MCU_BAT_INFO_INDEX: {
        FtmRxMcuBatTemp();
        break;
    }
    case FTM_RX_MCU_BAT_CHARGE_INDEX: {
        FtmRxMcuBatCharge(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_UART32_INDEX: {
        FtmRxMcuUart32();
        break;
    }
    case FTM_RX_MCU_GPS_ANT_OPEN_INDEX: {
        FtmRxMcuGpsAntOpen();
        break;
    }
    case FTM_RX_MCU_GPS_ANT_SHORT_INDEX: {
        FtmRxMcuGpsAntShort();
        break;
    }
    case FTM_RX_MCU_GPS_ANT_NORMAL_INDEX: {
        FtmRxMcuGpsAntNormal();
        break;
    }
    case FTM_RX_MCU_SYSTEM_SLEEP_INDEX: {
        FtmRxSystemSleepTest();
        break;
    }
    case FTM_RX_MCU_BT_WAKEUP_INDEX: {
        FtmRxMcuBtWakeup();
        break;
    }
    case FTM_RX_MCU_RTC_WAKEUP_INDEX: {
        FtmRxMcuRtcWakeup();
        break;
    }
    case FTM_RX_MCU_CAN3_WAKEUP_INDEX: {
        FtmRxMcuCan3Wakeup();
        break;
    }
    case FTM_RX_MCU_CAN2_WAKEUP_INDEX: {
        FtmRxMcuCan2Wakeup();
        break;
    }
    case FTM_RX_MCU_CAN4_WAKEUP_INDEX: {
        FtmRxMcuCan4Wakeup();
        break;
    }
    case FTM_RX_MCU_CAN5_WAKEUP_INDEX: {
        FtmRxMcuCan5Wakeup();
        break;
    }
    case FTM_RX_MCU_REQUEST_WAKEUP_INDEX: {
        FtmRxMcuRequestWakeup();
        break;
    }
    case FTM_RX_MCU_TBOXID_WRITE_INDEX: {
        FtmRxMcuTboxId(&para[1], len - 1);
        break;
    }
    case FTM_RX_MCU_SIMNUMBER_WRITE_INDEX: {
        FtmRxSimNumber(&para[1], len - 1);
        break;
    }
    case FTM_RX_MCU_READ_INFORMATION: {
        FtmRxMcuReadInformation(&para[1]);
        break;
    }
    case FTM_RX_MCU_LTE_ANT_OPEN_INDEX: {
        FtmRxMcuLteAntOpen();
        break;
    }
    case FTM_RX_MCU_LTE_ANT_SHORT_INDEX: {
        FtmRxMcuLteAntShort();
        break;
    }
    case FTM_RX_MCU_LTE_ANT_NORMAL_INDEX: {
        FtmRxMcuLteAntNormal();
        break;
    }
    case FTM_RX_MCU_MIC_OPEN_INDEX: {
        FtmRxMcuMicOpen();
        break;
    }
    case FTM_RX_MCU_MIC_SHORT_INDEX: {
        FtmRxMcuMicShort();
        break;
    }
    case FTM_RX_MCU_ECALL_WAKEUP_INDEX: {
        FtmRxMcuEcallWakeup();
        break;
    }
    case FTM_RX_MCU_TEST_NAVIGATE_INDEX: {
        FtmRxMcuNavigateTest(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_WRITE_NAVIGATE_INDEX: {
        FtmRxMcuWriteTboxCfg(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_LIN: {
        FtmRxLin(&para[1], len - 1);
        break;
    }
    case FTM_RX_MCU_READ_SOFT_VERSION: {
        FtmRxMcuReadSoftVersion(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_READ_HARD_VERSION: {
        FtmRxMcuReadHardVersion(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_READ_BOOTLOADER_VERSION: {
        FtmRxMcuReadBootLoaderVersion(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_READ_ECU_NAME: {
        FtmRxMcuReadECUName(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_READ_BLE_MAC: {
        FtmRxMcuBtReadMacAddr();
        break;
    }
    case FTM_RX_MCU_MODULE_WAKEUP_PIN_INDEX: {
        // 在IPC.API接口文件中使用
        // FtmRxMcuReadModuleWakeUpPin(&para[1],1);
        break;
    }
    case FTM_RX_MCU_LED_TEST_INDEX: {
        FtmRxMcuLedTest(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_EXIT_FTM: {
        FtmRxMcuExitFtm(&para[1], 1);
        break;
    }
    case FTM_RX_MCU_READ_KL30: {
        FtmRxMcuReadKL30();
        break;
    }
    case FTM_RX_MCU_SPAREPARTNUMBER_WRITE_INDEX: {
        FtmRxTboxSparePartNumber(&para[1], len - 1);
        break;
    }
    case FTM_RX_MCU_ON_INDEX: {
        FtmRxMcuSchgStaInput(&para[1], 1);  // Multiplex ON command
        break;
    }
    case FTM_RX_MCU_SECURITY_CHIP_INDEX:
    case FTM_RX_MCU_MIC_INDEX:
    case FTM_RX_MCU_LINE_INDEX:
    case FTM_RX_MCU_SPEAKER_INDEX:

    case FTM_RX_MCU_SUPPLIERID_WRITE_INDEX:
    case FTM_RX_MCU_TBOXNO_WRITE_INDEX:
    default: {
        break;
    }
    }
    return;
}

/*************************************************
函数名称: FtmCanMsgRxArmCmd
函数功能: CAN接收到ARM命令处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmCanMsgRxArmCmd(uint8_t *para, uint16_t len)
{
    Msg msg;

    if (NULL == para)
    {
        return;
    }

    msg.event  = MESSAGE_TX_ARM_FTM_CMD;
    msg.len    = len;
    msg.lparam = (uint32_t)para;
    SystemSendMessage(TASK_ID_IPC, msg);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "pc->arm:cmd:0x%02x, para:0x%02x\r\n", para[0], para[1]);
    return;
}

/*************************************************
函数名称: FtmCanMsgRxIctCmd
函数功能: CAN接收到数据采集板命令处理函数
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static void FtmCanMsgRxIctCmd(uint8_t *para, uint16_t len)
{
}

/*************************************************
函数名称: UartFtmMsgRxMcu
函数功能: DUT发送MCU处理命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/08/02
*************************************************/
// static void FtmUartMsgRxMcu(uint8_t *para, uint16_t len)
// {
// }

/*************************************************
函数名称: FtmTxMsgArmToPc
函数功能: ARM转发送PC处理命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/16
*************************************************/
void FtmTxMsgArmToPc(uint8_t *para, uint16_t len)
{
    FtmInfo *pFtmInfo = FtmInitRead();

    if (NULL == para)
    {
        return;
    }

    pFtmInfo->armEnterFtm = TRUE;
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm->pc:cmd:0x%02x, buf len:%d\r\n", para[0], len - 1);
    FtmMcuTxToPcBuf(para[0], &para[1], len - 1);
}

/*************************************************
函数名称: FtmUartMsgRxDut
函数功能: ARM转发送Dut处理命令
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/06/16
*************************************************/
// static void FtmUartMsgRxDut(uint8_t *para, uint16_t len)
// {
//     Msg msg;

//     if (NULL == para)
//     {
// #if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
//         SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm dut para error\r\n");
// #endif
//         return;
//     }

//     msg.event  = MESSAGE_TX_DUT_CMD;
//     msg.len    = len;
//     msg.lparam = (uint32_t)para;
//     FtmCanTxPackageFrameData(msg);
// }

/*************************************************
函数名称: FtmCanMsgRxFunction
函数功能: 接收到具体时间进行分发和处理
输入参数: 输入数据指针和长度和参数
输出参数: 执行结果
函数返回类型值：
           IPC_NO_ERROR --- 执行正确
           IPC_ID_ERROR---  消息ID错误
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
static ipcErrorCode FtmCanMsgRxFunction(Msg msg)
{
    MessageRxCanFtmIndex rxIndex = MESSAGE_ID_RX_MCU_CMD;

    for (rxIndex = MESSAGE_ID_RX_MCU_CMD; rxIndex < MESSAGE_ID_RX_CANFTM_MAX; rxIndex++)
    {
        if (g_rxCanFtmMessageInfoMap[rxIndex].id == msg.event)
        {
            if (NULL != g_rxCanFtmMessageInfoMap[rxIndex].FtmRxCallBack)
            {
                g_rxCanFtmMessageInfoMap[rxIndex].FtmRxCallBack((uint8_t *)msg.lparam, msg.len);
            }
            break;
        }
    }
    // Richard added below code
    return IPC_NO_ERROR;
}

/*************************************************
函数名称: FtmMainRxMsgFunction
函数功能: CAN装备接收到消息帧处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
void FtmMainRxMsgFunction(void)
{
    uint8_t                checkByte                = 0;
    uint16_t               msgId                    = 0;
    uint16_t               msgAttribute             = 0;
    uint16_t               rxTid                    = 0;
    uint8_t                buffer[FTM_MAX_SIZE_LEN] = {0};
    uint16_t               len                      = 0;
    Msg                  msg;
    MessageRxCanFtmIndex rxIndex = MESSAGE_ID_RX_MCU_CMD;

    if (STATUS_RX_HANDLE != g_canFtmInfo.rxStatus)
    {
        return;
    }

    // 消息帧解析失败
    if (IPC_NO_ERROR != FtmDecodeEscapeData(&g_canFtmInfo.ipcRx.buffer[1], g_canFtmInfo.ipcRx.len - 2, buffer, &len))
    {
        g_canFtmInfo.rxStatus = STATUS_RX_IDLE;
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm rx decode frame error\r\n");
#endif
        return;
    }
    g_canFtmInfo.rxStatus = STATUS_RX_IDLE;

    IpcXorCalculation(buffer, len - 1, &checkByte);

    // 如果校验成功
    if (checkByte == buffer[len - 1])
    {
        msgId        = (uint16_t)(buffer[0] << 8) + buffer[1];
        msgAttribute = (uint16_t)(buffer[2] << 8) + buffer[3];
        rxTid        = (uint16_t)(buffer[4] << 8) + buffer[5];

        for (rxIndex = MESSAGE_ID_RX_MCU_CMD; rxIndex < MESSAGE_ID_RX_CANFTM_MAX; rxIndex++)
        {
            if (g_rxCanFtmMessageInfoMap[rxIndex].id == msgId)
            {
                break;
            }
        }

        if (MESSAGE_ID_RX_CANFTM_MAX == rxIndex)
        {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm rx error id\r\n");
#endif
            return;
        }

        if (((0xffff == g_canFtmInfo.rxTid) && (0 == rxTid)) || (g_canFtmInfo.rxTid == rxTid - 1) || (1 == rxTid) ||
            ((0x0000 == (msgAttribute & 0x4000)) && (0 == rxTid)))  // 正常流水号
        {
            // 后续适配长报文
            msg.event  = msgId;
            msg.len    = msgAttribute & 0x3ff;
            msg.lparam = (uint32_t)&buffer[6];

            if ((0x4000 == (msgAttribute & 0x4000)))
            {
                g_canFtmInfo.rxTid = rxTid;
                FtmCanRxMsgTxAckMsg();
            }
            FtmCanMsgRxFunction(msg);
        }
        else if (g_canFtmInfo.rxTid == rxTid)  // 重复流水号
        {
            FtmCanRxMsgTxAckMsg();
#if (LOG_SWITCH_CONFIG_WARING == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "can ftm rx event is 0x%x, repeat tid is 0x%x\r\n", msgId, rxTid);
#endif
        }
        else
        {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm rx event is 0x%x, error tid is 0x%x\r\n", msgId, rxTid);
#endif
        }
    }
    return;
}

/*************************************************
函数名称: FtmRxMcuEcuID
函数功能: 接收CAN5唤醒控制信号
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: Young
编写日期 :2018/07/23
*************************************************/
void FtmNavigateTestModeSet(uint8_t value)
{
    FtmInfo *pFtmInfo = FtmInitRead();

    pFtmInfo->ftmINSTest = value;
}

/*************************************************
函数名称: FtmInsTestBackGround
函数功能: 装备模式后台处理惯导测试
输入参数:
输出参数:
函数返回类型值:
编写者: zxl
编写日期:2020/11/03
*************************************************/
void FtmNavigateTestBackGround(void)
{
    FtmInfo *pFtmInfo = FtmInitRead();

    if (TRUE == pFtmInfo->armEnterFtm)
    {
        /*Testing not started*/
        if (FTM_INS_DRIVE_TEST_STARTED == (pFtmInfo->ftmINSTest & FTM_INS_DRIVE_TEST_STARTED))
        {
            /*低电平，前进。测试车速120*/
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_IO_FWD_IO], GPIO_OUTPUT_LOW);
            SpeedPwmInit(120);
            /*通知ARM进行惯导里程增加的测试判断*/

            pFtmInfo->ftmINSTest = (pFtmInfo->ftmINSTest | FTM_INS_DRIVE_FORWARD_TESTING);
            pFtmInfo->ftmINSTest &= (~FTM_INS_DRIVE_TEST_STARTED);
            FtmMcuTxToArmCmd(FTM_TX_ARM_NAVIGATE_TEST, pFtmInfo->ftmINSTest);
        }

        /*INS drive forward been tested,but backward not been tested*/
        if (FTM_INS_DRIVE_FORWARD_TESTED == (pFtmInfo->ftmINSTest & FTM_INS_DRIVE_FORWARD_TESTED))
        {
            /*高电平，后退。测试车速120*/
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_IO_FWD_IO], GPIO_OUTPUT_HIGH);
            SpeedPwmInit(120);
            /*通知ARM进行惯导里程降低的测试判断*/
            pFtmInfo->ftmINSTest += FTM_INS_DRIVE_BACKWARD_TESTING;
            pFtmInfo->ftmINSTest &= (~FTM_INS_DRIVE_FORWARD_TESTED);
            FtmMcuTxToArmCmd(FTM_TX_ARM_NAVIGATE_TEST, pFtmInfo->ftmINSTest);
        }
    }
}

/*************************************************
函数名称: FtmCanPeriodFunction 1ms
函数功能: 接收到具体时间进行分发和处理
输入参数: 输入数据指针和长度和参数
输出参数: 执行结果
函数返回类型值：
           IPC_NO_ERROR --- 执行正确
           IPC_ID_ERROR---  消息ID错误
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
void FtmCanPeriodFunction(void)
{
    static uint8_t count    = 0;
    FtmInfo       *pFtmInfo = FtmInitRead();

    if (FTM_MODE_ENTER != pFtmInfo->ftmMode)
    {
        return;
    }

    FtmRxMcuCanWakeupPostHandle();
    FtmNavigateTestBackGround();

    if (50 >= count++)  // 50ms
    {
        return;
    }
    count = 0;

    // 在等待DUT侧的ACK应答
    if (FLAG_WAIT_ACK == g_canFtmInfo.ackFlag)
    {
        g_canFtmInfo.retransmissionTimeout++;
        if (g_nvTboxSelfConfigData.logAndIpc.retransmissionCount == g_canFtmInfo.retransmissionTimeout)
        {
#if (LOG_SWITCH_CONFIG_WARING == LOG_SWITCH_ON)
            // SystemApiLogPrintf(LOG_WARING_OUTPUT, "can ftm retry tx is %d\r\n", g_canFtmInfo.retransmissionCount);
#endif
            g_canFtmInfo.retransmissionTimeout = 0;
            g_canFtmInfo.retransmissionCount++;

            // 超时重发业务命令
            if (g_nvTboxSelfConfigData.logAndIpc.retransmissionCount >= g_canFtmInfo.retransmissionCount)
            {
                FtmCanSendData(g_canFtmInfo.ipcTx.len, g_canFtmInfo.ipcTx.buffer);
            }
            else
            {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                // SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm retry tx 3 time fail\r\n");
#endif
                FtmClearAckInfo(&g_canFtmInfo);
            }
        }
    }
}

/*************************************************
函数名称: FtmCanIsrRxFunction
函数功能: CAN装备接收处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 无
编写者: liaoyonggang
编写日期 :2017/06/16
*************************************************/
void FtmCanIsrRxFunction(uint8_t len, uint8_t *buf)
{
    uint8_t i = 0;

    if ((NULL == buf) || (0 == len) || (CAN_FRAME_LEN < len))
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can ftm Isr Rx para error:%d\r\n", len);
#endif
        return;
    }

    for (i = 0; i < len; i++)
    {
        if (IPC_MESSAGE_IDENTIFIER == buf[i])
        {
            if (STATUS_RX_IDLE == g_canFtmInfo.rxStatus)
            {
                g_canFtmInfo.rxStatus  = STATUS_RX_ONGOING;
                g_canFtmInfo.ipcRx.len = 1;
                memset(g_canFtmInfo.ipcRx.buffer, 0x00, FTM_MAX_SIZE_LEN);
                g_canFtmInfo.ipcRx.buffer[0] = buf[i];
                continue;
            }
            else
            {
                g_canFtmInfo.rxStatus                             = STATUS_RX_IDLE;
                g_canFtmInfo.ipcRx.buffer[g_canFtmInfo.ipcRx.len] = buf[i];
                g_canFtmInfo.ipcRx.len++;
                if (5 == g_canFtmInfo.ipcRx.len)
                {
                    FtmCanRxNadAckFunction(g_canFtmInfo.ipcRx.buffer, &g_canFtmInfo.ipcRx.len);
                }
                else  // 非应答帧
                {
                    if (2 == g_canFtmInfo.ipcRx.len)
                    {
                        if ((IPC_MESSAGE_IDENTIFIER == g_canFtmInfo.ipcRx.buffer[0]) || (IPC_MESSAGE_IDENTIFIER == g_canFtmInfo.ipcRx.buffer[1]))
                        {
                            g_canFtmInfo.ipcRx.buffer[1] = 0x00;
                            g_canFtmInfo.rxStatus        = STATUS_RX_ONGOING;
                            g_canFtmInfo.ipcRx.len       = 1;
                        }
                        return;
                    }
                    g_canFtmInfo.rxStatus = STATUS_RX_HANDLE;
                }
                continue;
            }
        }
        else
        {
            if (STATUS_RX_ONGOING == g_canFtmInfo.rxStatus)
            {
                g_canFtmInfo.ipcRx.len = g_canFtmInfo.ipcRx.len + 1;
                if (FTM_MAX_SIZE_LEN < g_canFtmInfo.ipcRx.len)
                {
                    g_canFtmInfo.rxStatus  = STATUS_RX_IDLE;
                    g_canFtmInfo.ipcRx.len = 0;
                    memset(g_canFtmInfo.ipcRx.buffer, 0x00, FTM_MAX_SIZE_LEN);
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                    SystemApiLogPrintf(LOG_ISR_OUTPUT, "can ftm rx len more than 200\r\n");
#endif
                    return;
                }
                g_canFtmInfo.ipcRx.buffer[g_canFtmInfo.ipcRx.len - 1] = buf[i];
            }
            else
            {
                g_canFtmInfo.rxStatus  = STATUS_RX_IDLE;
                g_canFtmInfo.ipcRx.len = 0;
                memset(g_canFtmInfo.ipcRx.buffer, 0x00, FTM_MAX_SIZE_LEN);
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_ISR_OUTPUT, "can ftm rx status error\r\n");
#endif
            }
        }
    }
}
