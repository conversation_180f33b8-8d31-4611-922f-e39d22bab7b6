/*
 * RemoteControlTask.c
 *
 * 远程控制任务，用于处理车辆锁车和限速功能
 */
#include "RemoteControlTask.h"
#include <string.h>
#include "CanApi.h"
#include "LogApi.h"
#include "NvApi.h"

/* 全局队列句柄 */
static QueueHandle_t xRemoteControlQueue = NULL;

/*
 * 初始化远程控制任务
 */
void RemoteControlTask_Init(void)
{
    // 初始化消息队列
    xRemoteControlQueue = xQueueCreate(REMOTE_CONTROL_QUEUE_SIZE, sizeof(RemoteControlFeedbackMsg));
    if (xRemoteControlQueue == NULL)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Failed to create Remote Control queue\r\n");
        return;
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Remote Control module initialized\r\n");
}

/*
 * 初始化远程控制外发CAN报文
 * 该函数处理锁车和限速外发CAN报文的初始化
 */
void RemoteControlMsgInit(void)
{
    CanUpdDataInfo_s canUpdInfo = {0};

    // 锁车和限速报文初始化
    const CarInfo *carInfo = GetCanInfo();

    bool needContinuousSend = false; // ★ 只要锁车或限速有一项需要外发，就置 true

    /* -------- 基本信息初始化 -------- */
    canUpdInfo.canId    = CAN_SEND_ID_5;
    canUpdInfo.canCh    = CAN_SEND_CH_5;
    canUpdInfo.sendType = CAN_SEND_TYPE_5;
    canUpdInfo.merge    = true; // 字段按位合并
    canUpdInfo.sendCnt  = 0;    // ★ 默认先假设“不用持续发”

    /* -------- 锁车指令处理 -------- */
    switch (carInfo->lockCarStatus)
    {              /* 00=无, 01=锁车, 02=解锁 */
        case 0x00: /* 无操作 → 字节置 FF */
        {
            canUpdInfo.startBit = 8;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0xFF;
            CanUpdateSendData(&canUpdInfo);
            break;
        }
        case 0x01: /* 锁车 → 0xAA */
        {
            canUpdInfo.startBit = 8;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0xAA;
            CanUpdateSendData(&canUpdInfo);
            needContinuousSend = true; // ★ 需要持续外发
            break;
        }
        case 0x02: /* 解锁 → 0x55 */
        {
            canUpdInfo.startBit = 8;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0x55;
            CanUpdateSendData(&canUpdInfo);
            needContinuousSend = true; // ★
            break;
        }
        default:
        {
            break;
        }
    }

    /* -------- 限速指令处理 -------- */
    switch (carInfo->speedLimitStatus)
    {              /* 00=无, 01=限速, 02=解限 */
        case 0x00: /* 无限速 → 字节2=0x00, 字节3=0xFF */
        {
            canUpdInfo.startBit = 16;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0x00;
            CanUpdateSendData(&canUpdInfo);

            canUpdInfo.startBit = 24;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0xFF;
            CanUpdateSendData(&canUpdInfo);
            break;
        }
        case 0x01: /* 限速请求 */
        {
            canUpdInfo.startBit = 16;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0x01;
            CanUpdateSendData(&canUpdInfo);

            canUpdInfo.startBit = 24;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = carInfo->speedLimitValue;
            CanUpdateSendData(&canUpdInfo);

            needContinuousSend = true; // ★
            break;
        }
        case 0x02: /* 解除限速 */
        {
            canUpdInfo.startBit = 16;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0x02;
            CanUpdateSendData(&canUpdInfo);

            canUpdInfo.startBit = 24;
            canUpdInfo.bitLen   = 8;
            canUpdInfo.data     = 0xFF;
            CanUpdateSendData(&canUpdInfo);

            needContinuousSend = true; // ★
            break;
        }
        default:
        {
            break;
        }
    }

    /* -------- 统一设置 sendCnt -------- */
    canUpdInfo.sendCnt = needContinuousSend ? UINT16_MAX : 0;
    CanUpdateSendData(&canUpdInfo);
    memset(&canUpdInfo, 0, sizeof(CanUpdDataInfo_s));
}

// 休眠回调
/** 休眠回调函数
 * 该函数在系统进入休眠时调用
 */
void RemoteControlEnterSleepHandle(void)
{
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Remote Control module entering sleep\r\n");
    // 上电或唤醒时先不发送发送天气报文
    CanSetSendCountToZero(CAN_SEND_ID_6, CAN_SEND_CH_6);
}

// 唤醒回调
/** 唤醒回调函数
 * 该函数在系统唤醒时调用
 */
void RemoteControlWakeupHandle(void)
{
    RemoteControlMsgInit();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Remote Control module waking up\r\n");
}

/*
 * 发送反馈消息到队列
 * 该函数由CAN接收处理程序调用
 */
void RemoteControlTask_SendFeedback(uint8_t lockStatus, uint8_t speedLimitStatus)
{
    RemoteControlFeedbackMsg msg;

    // 准备消息
    msg.lockStatus       = lockStatus;
    msg.speedLimitStatus = speedLimitStatus;

    // 将消息发送到队列，不阻塞
    if (xRemoteControlQueue != NULL)
    {
        if (xQueueSendFromISR(xRemoteControlQueue, &msg, NULL) != pdPASS)
        {
            // 队列可能已满，记录错误
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Failed to send message to Remote Control queue\r\n");
        }
    }
}

/*
 * 处理队列中的远程控制消息
 * 该函数应该由任务周期性调用
 */
void RemoteControlTask_Process(void)
{
    RemoteControlFeedbackMsg msg;

    // 检查队列中是否有消息，不阻塞
    if (xRemoteControlQueue != NULL && xQueueReceive(xRemoteControlQueue, &msg, 0) == pdTRUE)
    {
        // 处理收到的消息
        ProcessVehicleControlFeedback(msg.lockStatus, msg.speedLimitStatus);
    }
}

/*
 * 处理车辆控制反馈
 * 该函数处理反馈消息并更新状态
 */
void ProcessVehicleControlFeedback(uint8_t lockStatus, uint8_t speedLimitStatus)
{
    CarInfo         *carInfo    = GetCanInfo();
    NvErrorCode      errorCode  = NV_NO_ERROR;
    bool             needSave   = false;
    CanUpdDataInfo_s canUpdInfo = {0};

    // 初始化CAN消息结构
    canUpdInfo.canId    = CAN_SEND_ID_5;
    canUpdInfo.canCh    = CAN_SEND_CH_5;
    canUpdInfo.sendType = CAN_SEND_TYPE_5;
    canUpdInfo.merge    = true; // 后续更新需要合并

    // 输出传入的锁车和限速状态以及carInfo中的状态
    // SystemApiLogPrintf(LOG_INFO_OUTPUT,
    //                    "ProcessVehicleControlFeedback: lockStatus = 0x%02X, speedLimitStatus = 0x%02X, "
    //                    "carInfo->lockCarStatus = 0x%02X, carInfo->speedLimitStatus = 0x%02X\r\n",
    //                    lockStatus,
    //                    speedLimitStatus,
    //                    carInfo->lockCarStatus,
    //                    carInfo->speedLimitStatus);

    // 处理锁车反馈
    // 需要锁车，并且反馈锁车完成时，将lockCarStatus设置为0，并停止外发
    if (0x01 == carInfo->lockCarStatus && lockStatus == VEHICLE_LOCK_COMPLETED)
    {
        // 车辆已完成锁车，停止发送锁车请求
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Vehicle locked, stopping lock request\r\n");
        needSave               = true;
        carInfo->lockCarStatus = 0;
        canUpdInfo.startBit    = 8;    // Byte1 (从Byte0开始)的起始位
        canUpdInfo.bitLen      = 8;    // 整个字节
        canUpdInfo.data        = 0xFF; // 设置为0xFF
        canUpdInfo.sendCnt = 0 == carInfo->lockCarStatus && 0 == carInfo->speedLimitStatus ? 0 : UINT16_MAX; // 停止发送
        CanUpdateSendData(&canUpdInfo);
    }
    // 需要解锁，并且反馈解锁完成时，将lockCarStatus设置为0，并停止外发
    else if (0x02 == carInfo->lockCarStatus && lockStatus == VEHICLE_UNLOCK_COMPLETED)
    {
        // 车辆已完成解锁，停止发送解锁请求
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Vehicle unlocked, stopping unlock request\r\n");
        needSave               = true;
        carInfo->lockCarStatus = 0;
        canUpdInfo.startBit    = 8;    // Byte1 (从Byte0开始)的起始位
        canUpdInfo.bitLen      = 8;    // 整个字节
        canUpdInfo.data        = 0xFF; // 设置为0xFF
        canUpdInfo.sendCnt = 0 == carInfo->lockCarStatus && 0 == carInfo->speedLimitStatus ? 0 : UINT16_MAX; // 停止发送
        CanUpdateSendData(&canUpdInfo);
    }

    // 处理限速反馈
    // 需要限速，并且反馈限速完成时，将speedLimitStatus设置为0，并停止外发
    if (0x01 == carInfo->speedLimitStatus && speedLimitStatus == VEHICLE_SPEED_LIMIT_ACTIVE)
    {
        // 车辆已进入限速状态，停止发送限速请求
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Vehicle speed limited, stopping speed limit request\r\n");
        needSave                  = true;
        carInfo->speedLimitStatus = 0;
        canUpdInfo.startBit       = 16;   // Byte2 (从Byte0开始)的起始位
        canUpdInfo.bitLen         = 8;    // 整个字节
        canUpdInfo.data           = 0x00; // 设置为0x00
        canUpdInfo.sendCnt = 0 == carInfo->lockCarStatus && 0 == carInfo->speedLimitStatus ? 0 : UINT16_MAX; // 停止发送
        CanUpdateSendData(&canUpdInfo);
    }
    // 需要解限速，并且反馈解限速完成时，将speedLimitStatus设置为0，并停止外发
    else if (0x02 == carInfo->speedLimitStatus && speedLimitStatus == VEHICLE_NO_SPEED_LIMIT)
    {
        // 车辆已取消限速，停止发送取消限速请求
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Vehicle speed limit canceled, stopping request\r\n");
        needSave                  = true;
        carInfo->speedLimitStatus = 0;
        canUpdInfo.startBit       = 16;   // Byte2 (从Byte0开始)的起始位
        canUpdInfo.bitLen         = 8;    // 整个字节
        canUpdInfo.data           = 0x00; // 设置为0x00
        canUpdInfo.sendCnt = 0 == carInfo->lockCarStatus && 0 == carInfo->speedLimitStatus ? 0 : UINT16_MAX; // 停止发送
        CanUpdateSendData(&canUpdInfo);
    }

    // 如果状态有变化，保存到NV存储
    if (needSave)
    {
        errorCode = NvApiWriteData(NV_ID_CAR_INFO, (uint8_t *) carInfo, sizeof(CarInfo));
        if (NV_NO_ERROR != errorCode)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Failed to save CarInfo to NV memory: %d\r\n", errorCode);
        }
    }
}
