/*
      CanMsgApi.c
描述：此文件主要是CAN Message 任务具体实现的业务功能
作者：sword zheng
时间：2016.12.06
*/
#include <string.h>
#include "FreeRTOS.h"
#include "CanMsgApi.h"
#include "NvApi.h"
#include "event.h"
#include "gpio.h"
#include "r_can.h"
#include "CanApi.h"
#include "CanFtm.h"
#include "CanClient.h"
#include "Can_Cfg.h"
#include "CanAuth.h"
#include "LogApi.h"
#include "CommonTask.h"
#include "SystemApi.h"
#include "CanNm_Cbk.h"
#include "RemoteControlTask.h"

/************************外部全局变量****************************/
extern uint8_t                   g_dmaCompelte;
extern CommonInfo                g_commonInfo;
extern uint32_t                  g_osCurrentTickTime;
extern GpioInfo                  g_gpioPowerOnInfoList[];
extern uint8_t                   g_w[11][4][4];
extern uint8_t                   g_state[4][4];
extern uint32_t                  D_data[2];
extern uint32_t                  key1[4];
extern uint8_t                   sBox[256];

/************************全局变量****************************/
uint8_t             g_txTempBuf[CAN_LOG_ARM_LEN] = {0};
CanLogInfo          g_canLogInfo;
mutex_t             g_mutexCANLog;


CanMsgAnalysisInfo g_CanMsgAnalysisInfo[CAN_MSG_RX_MAX] =
{
        {CAN_MSG_RX_HECU_ID_INDEX, CAN_MSG_RX_HECU_ID, CHANNEL_BD_CAN},
        {CAN_MSG_RX_BCM_ID_INDEX,  CAN_MSG_RX_BCM_ID,  CHANNEL_BD_CAN},
        {CAN_MSG_RX_BMS_ID_INDEX,  CAN_MSG_RX_BMS_ID,  CHANNEL_BD_CAN},
        {CAN_MSG_RX_MCU_ID_INDEX,  CAN_MSG_RX_MCU_ID,  CHANNEL_BD_CAN},
        {CAN_MSG_RX_DCDC_ID_INDEX, CAN_MSG_RX_DCDC_ID, CHANNEL_BD_CAN},
        {CAN_MSG_RX_ABS_ID_INDEX,  CAN_MSG_RX_ABS_ID,  CHANNEL_BD_CAN},
        {CAN_MSG_RX_IC_ID_INDEX,   CAN_MSG_RX_IC_ID,   CHANNEL_BD_CAN},
        {CAN_MSG_RX_APU_ID_INDEX,  CAN_MSG_RX_APU_ID,  CHANNEL_BD_CAN},
};

CanMsgAnalyCheckCodeIno g_canMsgAnalyCodeInfo[CAN_MSG_RX_MAX]=
{
        {false, DTC_LOST_COM_WITH_HECU_INDEX, CHANNEL_BD_CAN, CAN_CHANNEL_1, 1000},
        {false, DTC_LOST_COM_WITH_BCM_INDEX,  CHANNEL_BD_CAN, CAN_CHANNEL_1, 500},
        {false, DTC_LOST_COM_WITH_BMS_INDEX,  CHANNEL_BD_CAN, CAN_CHANNEL_1, 500},
        {false, DTC_LOST_COM_WITH_MCU_INDEX,  CHANNEL_BD_CAN, CAN_CHANNEL_1, 500},
        {false, DTC_LOST_COM_WITH_DCDC_INDEX, CHANNEL_BD_CAN, CAN_CHANNEL_1, 1000},
        {false, DTC_LOST_COM_WITH_ABS_INDEX,  CHANNEL_BD_CAN, CAN_CHANNEL_1, 500},
        {false, DTC_LOST_COM_WITH_IC_INDEX,   CHANNEL_BD_CAN, CAN_CHANNEL_1, 1000},
        {false, DTC_LOST_COM_WITH_APU_INDEX,  CHANNEL_BD_CAN, CAN_CHANNEL_1, 500},

};

TboxAuthInfo g_canMsgAuthInfo[CAN_MSG_RX_AUTH_MAX] =
{
//    {CAN_MSG_RX_BCM_ID_033_INDEX, CHANNEL_BDCAN, CAN_MSG_RX_BCM_ID_033, CAN_MSG_TX_BCM_ID_034},
//    {CAN_MSG_RX_VBU_ID_031_INDEX, CHANNEL_BDCAN, CAN_MSG_RX_VBU_ID_031, CAN_MSG_TX_VBU_ID_036},
};

/*************************************************
函数名称: CanMsgTpmsCodeRecIdCfgInit
函数功能: 根据车型配置TPMS节点的报文接收ID
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: PC
编写日期 :2023/02/13
*************************************************/
void CanMsgTpmsCodeRecIdCfgInit(void )
{
}

/*************************************************
函数名称: CanLogInitBuf
函数功能: 初始化相关LOG函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2018/11/13
*************************************************/
void CanLogInitBuf(void)
{
    uint8_t i = 0;
    uint8_t j = 0;

    g_canLogInfo.readPos       = 0;
    g_canLogInfo.writePos      = 0;
    g_canLogInfo.spaceLen      = CAN_LOG_MAX_COUNT;
    g_canLogInfo.frameCount    = 0;
    g_commonInfo.canReportInit = FALSE;

    for (i = 0; i < CAN_LOG_MAX_COUNT; i++)
    {
        g_canLogInfo.data[i][CAN_LOG_HEAD_POS] = 0xbb;
        for (j = 1; j < CAN_LOG_ARM_LEN; j++)
        {
            g_canLogInfo.data[i][j] = 0x00;
        }
    }
}

/*************************************************
函数名称: CanLogWriteBuf
函数功能: can日志写BUF内容
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2018/11/13
*************************************************/
void CanLogWriteBuf(uint8_t checksum, CanPduInfo pduInfo)
{
#ifndef CAN_UPLOAD_TEST
    if (FALSE == g_commonInfo.canReportInit)
    {
        return;
    }
#endif

    if (0 != g_canLogInfo.spaceLen)
    {
        // 保护g_canLogInfo，防止正在更新的时候读取
        OSIF_MutexLock(&g_mutexCANLog, OSIF_WAIT_FOREVER);
        memcpy(&g_canLogInfo.data[g_canLogInfo.writePos][g_canLogInfo.frameCount * CAN_LOG_FRAME_LENGTH + 1], &pduInfo, CAN_LOG_FRAME_LENGTH);
        if (7 > g_canLogInfo.frameCount)
        {
            g_canLogInfo.frameCount = g_canLogInfo.frameCount + 1;
        }
        else
        {
            g_canLogInfo.writePos   = (g_canLogInfo.writePos + 1) % CAN_LOG_MAX_COUNT;
            g_canLogInfo.frameCount = 0;
            g_canLogInfo.spaceLen   = g_canLogInfo.spaceLen - 1;
        }
        OSIF_MutexUnlock(&g_mutexCANLog);
    }
    else
    {
        g_commonInfo.canLostNum = g_commonInfo.canLostNum + 1;
    }
}

/*************************************************
函数名称: CanLogWriteTxBuf
函数功能: can日志自发写BUF内容
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/11/26
*************************************************/
void CanLogWriteTxBuf(uint8_t channel, uint32_t canId, uint8_t canDlc, uint8_t *buf)
{
    CanPduInfo pduInfo;

#ifndef CAN_UPLOAD_TEST
    if (FALSE == g_commonInfo.canReportInit)
    {
        return;
    }
#endif

    pduInfo.channel = channel;
    pduInfo.time    = g_osCurrentTickTime;
    pduInfo.id      = canId;
    pduInfo.dlc     = canDlc;
    memcpy(pduInfo.data, buf, 8);

    if (0 != g_canLogInfo.spaceLen)
    {
        OSIF_MutexLock(&g_mutexCANLog, OSIF_WAIT_FOREVER);
        memcpy(&g_canLogInfo.data[g_canLogInfo.writePos][g_canLogInfo.frameCount * CAN_LOG_FRAME_LENGTH + 1], &pduInfo, CAN_LOG_FRAME_LENGTH);
        if (CAN_FRAME_DATA_COUNT - 1 > g_canLogInfo.frameCount)
        {
            g_canLogInfo.frameCount = g_canLogInfo.frameCount + 1;
        }
        else
        {
            g_canLogInfo.writePos   = (g_canLogInfo.writePos + 1) % CAN_LOG_MAX_COUNT;
            g_canLogInfo.frameCount = 0;
            g_canLogInfo.spaceLen   = g_canLogInfo.spaceLen - 1;
        }
        OSIF_MutexUnlock(&g_mutexCANLog);
    }
    else
    {
        g_commonInfo.canLostNum = g_commonInfo.canLostNum + 1;
    }
}

/*************************************************
函数名称: CanLogReadBuf
函数功能: can日志读取BUF内容
输入参数: logBuf-足够容纳一帧CAN日志的buffer(不少于CAN_LOG_ARM_LEN)
输出参数: logBuf-如果读取成功，保存着从CAN日志存储空间中拷贝出来的一帧CAN日志内容
函数返回类型值：bool-true表示读到了有效的CAN日志;false表示没有读到有效的日志
编写者: RichardChen
编写日期 :2024/03/13
*************************************************/
bool CanLogReadBuf(uint8_t *logBuf)
{
    /*CAN LOG循环队列为空*/
    uint8_t i        = 0;
    bool  logValid = false;
    uint32_t readWaitTime = 5;
    status_t lockState = STATUS_SUCCESS;
#if (CAN_DATA_CRC_TYPE == 1)
    uint32_t crcCal = 0;
#endif

    if (NULL != logBuf)
    {
        // 保护读取g_canLogInfo的操作，防止正在读取的时候被更新
        lockState = OSIF_MutexLock(&g_mutexCANLog, readWaitTime);
        if(STATUS_SUCCESS == lockState)
        {
            if (CAN_LOG_MAX_COUNT > g_canLogInfo.spaceLen)
            {
                #if (CAN_DATA_CRC_TYPE == 0)
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS]  = SystemCalCrc8(&g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_HEAD_POS], CAN_LOG_ARM_LEN - CAN_DATA_CRC_LEN);
                #elif (CAN_DATA_CRC_TYPE == 1)
                crcCal = SystemCalCrc32(&g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_HEAD_POS], CAN_LOG_ARM_LEN - CAN_DATA_CRC_LEN);
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS]  = (uint8_t)(crcCal >> 24);
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS + 1] = (uint8_t)(crcCal >> 16);
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS + 2] = (uint8_t)(crcCal >> 8);
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS + 3] = (uint8_t)(crcCal);
                #endif
                // memcpy(g_txTempBuf, &g_canLogInfo.data[g_canLogInfo.readPos], CAN_LOG_ARM_LEN);
                memcpy(logBuf, &g_canLogInfo.data[g_canLogInfo.readPos], CAN_LOG_ARM_LEN);

                // Uart32SendData(CAN_LOG_ARM_LEN, g_txTempBuf);
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_HEAD_POS] = 0xbb;
                g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS]  = 0xbb;
                g_canLogInfo.readPos                                      = (g_canLogInfo.readPos + 1) % CAN_LOG_MAX_COUNT;
                g_canLogInfo.spaceLen                                     = g_canLogInfo.spaceLen + 1;
                g_commonInfo.canTotalNum                                  = g_commonInfo.canTotalNum + 8;
                logValid                                                  = true;
            }
            else
            {
                if (2 < g_commonInfo.canIdleCount)
                {
                    if (0 < g_canLogInfo.frameCount)
                    {
                        for (i = g_canLogInfo.frameCount * CAN_LOG_FRAME_LENGTH + 1; i < CAN_LOG_ARM_LEN - 1; i++)
                        {
                            g_canLogInfo.data[g_canLogInfo.readPos][i] = 0xff;
                        }
                        #if (CAN_DATA_CRC_TYPE == 0)
                        g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS]  = SystemCalCrc8(&g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_HEAD_POS], CAN_LOG_ARM_LEN - CAN_DATA_CRC_LEN);
                        #elif (CAN_DATA_CRC_TYPE == 1)
                        crcCal = CanFrameCrc32(&g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_HEAD_POS], CAN_LOG_ARM_LEN - CAN_DATA_CRC_LEN);
                        g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS]  = (uint8_t)(crcCal >> 24);
                        g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS + 1] = (uint8_t)(crcCal >> 16);
                        g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS + 2] = (uint8_t)(crcCal >> 8);
                        g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_CRC_POS + 3] = (uint8_t)(crcCal);
                        #endif
                        // memcpy(g_txTempBuf, &g_canLogInfo.data[g_canLogInfo.readPos], CAN_LOG_ARM_LEN);
                        memcpy(logBuf, &g_canLogInfo.data[g_canLogInfo.readPos], CAN_LOG_ARM_LEN);
                        // Uart32SendData(CAN_LOG_ARM_LEN, g_txTempBuf);
                        g_canLogInfo.data[g_canLogInfo.readPos][CAN_LOG_HEAD_POS] = 0xbb;
                        g_commonInfo.canTotalNum                                  = g_commonInfo.canTotalNum + g_canLogInfo.frameCount;
                        g_canLogInfo.frameCount                                   = 0;
                        logValid                                                  = true;
                    }
                }
            }
        }
        else
        {
            logValid = false;
        }
        OSIF_MutexUnlock(&g_mutexCANLog);
    }
    return logValid;
}

/*************************************************
函数名称: CanMsgAnalyzeData
函数功能: CAN信号解析相关状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/08/06
修改：benyulong 2018/08/13
*************************************************/
int CanMsgAnalyzeData(uint8_t channel, CanPduInfo pduInfo)
{
    uint8_t     hrhIdOfPdu = 0xff;
    FtmInfo     *pFtmInfo = FtmInitRead();

    /* Check if this is a remote control feedback message */
    if (REMOTE_CONTROL_FEEDBACK_ID == pduInfo.id)
    {
        /* Extract feedback data */
        uint8_t lockStatus = pduInfo.data[1];      // Byte1 - 锁车状态
        uint8_t speedLimitStatus = pduInfo.data[2]; // Byte2 - 限速状态

        /* Send feedback to queue for asynchronous processing */
        RemoteControlTask_SendFeedback(lockStatus, speedLimitStatus);
        return 0;
    }

    if (0x700 == (pduInfo.id & 0x700))
    {
        hrhIdOfPdu = RX_EVCAN_CAN1_HRH;
        CanClientRxDiagFrame(hrhIdOfPdu, pduInfo);
        return 0;
    }

    if ((CAN_REQUEST_ID == pduInfo.id) && (CAN_CONTROLLER_A == channel))
    {
        FtmCanIsrRxFunction(pduInfo.dlc, pduInfo.data);
        return 0;
    }

    if (FTM_MODE_ENTER == pFtmInfo->ftmMode)
    {
        if ((CAN_TEST_REQUEST_ID == pduInfo.id) && (CAN_CONTROLLER_A != channel))
        {
            FtmTxPcCanResult(channel, pduInfo.data);
        }
    }

    return 0;
}

/*************************************************
函数名称: CanAuthBusyResPonse
函数功能: 回复T-Box忙碌
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: PC
编写日期 :2022/08/08

*************************************************/
void CanAuthBusyResPonse(uint32_t *id)
{
    uint8_t sendindex = 0;
    uint8_t sendkey[TBOX_REPONSE_TOYAL_LEN] = {0};
    
    sendindex = 0;
    sendkey[sendindex++] = 0x0;
    sendkey[sendindex++] = AUTH_BUSY;
    memset(&sendkey[sendindex],0xFF,TBOX_RESPONSE_KEY_LEN);
    // CanUpdateAuthResponse(id,sendkey,sizeof(sendkey));
    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Tbox Statu is busy\r\n");
}

/*************************************************
函数名称: CanReadESK
函数功能: 获取ESK
输入参数: data：用以接收ESK的指针
输出参数: 无
函数返回类型值：无
编写者: PC
编写日期 :2022/08/09
*************************************************/
uint8_t CanReadESK(uint32_t id, uint8_t* data)
{
    uint8_t result = E_OK;
    uint8_t sendkey[TBOX_REPONSE_TOYAL_LEN] = {0};
    uint8_t eskmask[TBOX_ESK_LEN] = {0};

    memset(eskmask,0xFF,TBOX_ESK_LEN);
    // Rte_ReadESK(data);
    // if(0 == memcmp(data,eskmask,TBOX_ESK_LEN))//未写入过ESK，为无效值
    // {
    //     return E_NOT_OK;
    // }
    
    return E_OK;
}

/*************************************************
函数名称: CanMesgAES128
函数功能: 计算密码
输入参数: index; 对应ECU的索引值
          len:挑战码长度
          challengecode：接收的认证报文（挑战码）
输出参数: 无
函数返回类型值：无
编写者: PC
编写日期 :2022/08/09
*************************************************/
void CanMesgAES128(uint8_t index, uint8_t len, uint8_t *challengecode)
{
    uint32_t RepId =0x00;
    uint8_t eskcode[TBOX_ESK_LEN]={0};
    uint8_t data_f[TBOX_FIXEDCODE_LEN]={0};
    uint8_t sendkey[TBOX_REPONSE_TOYAL_LEN] = {0};
    uint8_t fixcode[8] ={0x11,0x11,0x11,0x11,0x11,0x11,0x11,0x11};

    RepId = g_canMsgAuthInfo[index].txid; 

    /*获取ESK*/
    if (E_OK != CanReadESK(RepId,eskcode))
    {
        sendkey[0] = 0x00;
        sendkey[1] = AUTH_FAIL;
        memset(&sendkey[2],0x00,TBOX_RESPONSE_KEY_LEN);
        CanUpdateAuthResponse(RepId,sendkey,sizeof(sendkey));
        return;
    }      
    memcpy(data_f,challengecode,len);
    memcpy(&data_f[len],fixcode,sizeof(fixcode));
    
    /*计算密码并响应*/
    AES_KeyExpansion(eskcode,g_w);
    AES_Cipher(data_f);  
    sendkey[0] = 0x0;
    sendkey[1] = AUTH_SUCCESS;
    memcpy(&sendkey[2],data_f,TBOX_RESPONSE_KEY_LEN);
    CanUpdateAuthResponse(RepId,sendkey,sizeof(sendkey));
    
}

/*************************************************
函数名称: CreateMsgAnalyzeQueue
函数功能: 创建二维数组循环队列，初始化相应的队列参数
输入参数: 循环队列指针
输出参数: queueErrorCode
函数返回类型值：
         QUEUE_NO_ERROR---执行正确
         QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
编写者: pc
编写日期 :2022/09/23
*************************************************/

queueErrorCode CreateMsgAnalyzeQueue(CanAnalyzeQueue* canAnalyzeQueue)
{
    uint8_t i = 0;
    
    if(NULL == canAnalyzeQueue)
    {
        return QUEUE_PRAR_POINT_NULL;
    }
    canAnalyzeQueue->readpos = 0;
    canAnalyzeQueue->writepos = 0;
    canAnalyzeQueue->spacelen = CAN_ANALYZE_ROW_COUNT;

    for(i=0;i<CAN_ANALYZE_ROW_COUNT;i++)
    {
        memset(&canAnalyzeQueue->data[i][0],0xff,CAN_ANALYZE_ROW_COLUMN_COUNT);
    }

    return QUEUE_NO_ERROR;
}


/*************************************************
函数名称: CanMsgUpdateCodeStatu
函数功能: 检测到接受节点报文并更新在线状态标志
输入参数: 节点对应下标
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2022/07/01
*************************************************/
void CanMsgUpdateCodeStatu(uint8_t index)
{
    g_canMsgAnalyCodeInfo[index].code_online = true;

}

/*************************************************
函数名称: CanMsgGetCodeStatusInfo
函数功能: 获取节点信息
输入参数: 无
输出参数: 节点信息
函数返回类型值：无
编写者: pc
编写日期 :2022/09/23
*************************************************/
CanMsgAnalyCheckCodeIno * CanMsgGetCodeStatusInfo(void)
{
    return g_canMsgAnalyCodeInfo;
}

/*************************************************
函数名称: CanMsgAnlyxeWriteQueue
函数功能: 向循环队列写一行数据
输入参数: 循环队列指针，待写入数据
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE---循环队列满
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
编写者: pc
编写日期 :2022/09/23
*************************************************/
queueErrorCode CanMsgAnlyxeWriteQueue(CanAnalyzeQueue* analyzequeue, uint8_t *buf)
{
    queueErrorCode errorCode = QUEUE_NO_ERROR;

    if((NULL == buf) || (NULL == analyzequeue))
    {
        return QUEUE_PRAR_POINT_NULL;
    }

    if(analyzequeue->spacelen != 0)
    {
        memcpy(&analyzequeue->data[analyzequeue->writepos][0],buf,CAN_ANALYZE_ROW_COLUMN_COUNT);
        analyzequeue->writepos =  (analyzequeue->writepos+1)%CAN_ANALYZE_ROW_COUNT;
        analyzequeue->spacelen = analyzequeue->spacelen - 1;
    }
    else
    {
         errorCode = QUEUE_WRITE_LEN_BEYOND_SPACE_SZIE;
    }
    
    return errorCode;
}

/*************************************************
函数名称: CanMsgAnlyxeReadQueue
函数功能: 向循环队列读一行数据
输入参数: 循环队列指针，待写入数据
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE---循环队列满
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
编写者: pc
编写日期 :2022/09/23
*************************************************/
queueErrorCode CanMsgAnlyxeReadQueue(CanAnalyzeQueue* analyzequeue, uint8_t *buf)
{
    queueErrorCode errorCode = QUEUE_NO_ERROR;

    if((NULL == buf) || (NULL == analyzequeue))
    {
        return QUEUE_PRAR_POINT_NULL;
    }

    if(CAN_ANALYZE_ROW_COUNT == analyzequeue->spacelen)
    {
        return QUEUE_IS_EMPTY;
    }
    else
    {
        memcpy(buf,&analyzequeue->data[analyzequeue->readpos][0],CAN_ANALYZE_ROW_COLUMN_COUNT);
        analyzequeue->readpos = (analyzequeue->readpos + 1)%CAN_ANALYZE_ROW_COUNT;
        analyzequeue->spacelen = analyzequeue->spacelen + 1;
    }
    return errorCode;
    
}

/*************************************************
函数名称: WriteVehicleInfoToAnalyze
函数功能: 将MCU需要处理解析的CAN报文进行处理
输入参数: channel：通道
          pduInfo：can报文相关信息
输出参数: void
函数返回类型值：无
编写者: pc
编写日期 :2022/08/22
*************************************************/
void WriteVehicleInfoToAnalyze(uint8_t channel, CanPduInfo pduInfo)
{

    for(int i = CAN_MSG_RX_HECU_ID_INDEX; i < CAN_MSG_RX_MAX; i++)// check ECU code lost
    {
        if(g_CanMsgAnalysisInfo[i].rxid == pduInfo.id)
        {   
            CanMsgUpdateCodeStatu(i); 
            break;
        }
     }

//    for(int j =CAN_MSG_RX_BCM_ID_033_INDEX;j<CAN_MSG_RX_AUTH_MAX;j++)// BCM VBU Auth
//    {
//        if(g_canMsgAuthInfo[j].rxid ==pduInfo.id )
//        {
//            CanMesgAES128(j,pduInfo.dlc,pduInfo.data);
//            break;
//        }
//    }
}
