/*
     CanApi.c
描述：此文件主要是CAN任务具体实现的业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/
#include "CanApi.h"
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "CanClient.h"
#include "CanFtm.h"
#include "CanMsgApi.h" /*add by sword with 2017.01.09 support for can msg manage */
#include "Can_Cfg.h"
#include "Dem.h"
#include "LogApi.h"
#include "NvApi.h"
#include "gpio.h"
#include "r_can.h"
#if defined(CAN_ENABLE_OSEKNM)
#include "OsekNm.h"
#elif defined(CAN_ENABLE_AUTOSAR_NM)
#include "CanNm.h"
#elif defined(CAN_ENABLE_NO_NM)
/* 无网管模式下不需要包含网络管理头文件 */
#else
/* 默认情况下包含AUTOSAR NM头文件 */
#include "CanNm.h"
#endif
#include "BLELin.h"
#include "CanIf.h"
#include "CanSM_EcuM.h"
#include "CanSM_SchM.h"
#include "ComM.h"
#include "Dcm.h"
#include "DcmDsl_MsgManage.h"
#include "Dcm_Cbk.h"
#include "Dcm_Internal.h"
#include "DiagApi.h"
#include "EcuM.h"
#include "Nm.h"
#include "PmApi.h"
#include "RemoteControlTask.h"
#include "SchM_ComM.h"
#include "SystemApi.h"
#include "event.h"

/************************外部全局变量****************************/
extern bool          g_armresetdisable;
extern uint8_t       g_busOffDtc[];
extern uint8_t       g_canRxData[];
extern uint8_t       g_clientTxInfo[];
extern const uint8_t g_crcTable[];
extern uint32_t      g_AliveOstick;
extern uint32_t      g_osCurrentTickTime;
extern LinInfo       g_linInfo;
extern CommonInfo    g_commonInfo;
extern GpioInfo      g_gpioPowerOnInfoList[];
extern RxQueue       g_taskRxQueue[QUEUE_ID_MAX];
extern uint8_t       g_ensureFlag;

/************************静态函数接口***************************/
static void CanRxDtcHandle(Msg msg);
static void CanPeriodSendData(void);
static void SetChecksumCrc(uint8_t *buf);
static void SetCheckCounter(uint8_t *buf);
static void RemoteControlServiceReqEvent(uint8_t *data, uint32_t dataLen);
static bool IntelCheckBitValid(uint8_t startBit, uint8_t bitLen);
static bool MotLsbCheckBitValid(uint8_t startBit, uint8_t bitLen);
static bool MotMsbCheckBitValid(uint8_t startBit, uint8_t bitLen);
static int CanUpdateData(CanUpdDataInfo_s *updData, uint8_t *canData);
static int CanUpdateMode(CanUpdDataInfo_s *updData, uint8_t *canData, uint8_t canIndex);
static int MotLsbCanUpdateData(CanUpdDataInfo_s *updData, uint8_t *canData);
static int MotMsbCanUpdateData(CanUpdDataInfo_s *updData, uint8_t *canData);

/************************静态变量定义***************************/
#ifdef CAN_ENABLE_OSEKNM
static OsekNm_UserHandleType g_OsekNmUserHandle;
#endif
static uint64_t g_currentTimeTickMs = 0x00;
static uint64_t g_vinSendNum        = 0;

/************************变量定义******************************/
bool                        g_wakeupFlag  = FALSE;
uint32_t                    g_AliveOstick = 0x00;
uint32_t                    g_AccOnOstick = 0x00;
CanTpInfo                   g_canTpInfo;
CanUdsStatus                g_udsStatus                = CAN_UDS_NO_INIT;
pCanDiagDataSend_t          g_CanDiagDataSend          = NULL;
SdkLogPrintfCallBack_t      g_SdkLogPrintfCallBack     = NULL;
pCanControllerCallBack_t    g_CanControllerCallBack    = NULL;
pCanReDiagReponseDataSend_t g_CanReDiagReponseDataSend = NULL;
CanDataArmStr               g_canDataArmStr            = {0, 0, CAN_PACKAGE_DATA_NUM, 0, 0};

EventInfo g_canEventFunctionMap[] =
{
        {EVENT_ID_CAN_PERIOD_NOTIFY, CanPeriodNotifyFunction},
        {EVENT_ID_CAN_RX_DTC, CanRxDtcHandle},
        {EVENT_ID_CAN_RX_IPC_REMOTE_CONTROL, CanSendPassIpcCb},
};

static CanSendDataInfo_s g_canSendDataInfo[CAN_SEND_ID_MAX_NUM] =
{
    {
        .sendEnable = true,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_1,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_1,
        .canCount = 1,
        .canIndex = 0,
        .signalInterval = CAN_SEND_INTERVAL_1,
        .totalInterval = CAN_SEND_INTERVAL_1,
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_1,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_1}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_1},
        .defaultData = {CAN_SEND_DFT_DATA_1},
        .bakIndex = 0
    },

    {
        .sendEnable = true,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_2,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_2,
        .canCount = 1,
        .canIndex = 0,
        .signalInterval = CAN_SEND_INTERVAL_2,
        .totalInterval = CAN_SEND_INTERVAL_2,
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_2,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_2}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_2},
        .defaultData = {CAN_SEND_DFT_DATA_2},
        .bakIndex = 0
    },

    {
        .sendEnable = true,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_3,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_3,
        .canCount = 1,
        .canIndex = 0,
        .signalInterval = CAN_SEND_INTERVAL_3,
        .totalInterval = CAN_SEND_INTERVAL_3,
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_3,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_3}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_3},
        .defaultData = {CAN_SEND_DFT_DATA_3}
    },

    {
        .sendEnable = true,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_4,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_TYPE_CYCLE,
        .canCount = 1,
        .canIndex = 0,
        .signalInterval = CAN_SEND_INTERVAL_4,
        .totalInterval = CAN_SEND_INTERVAL_4,
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_4,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_4}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_4},
        .defaultData = {CAN_SEND_DFT_DATA_4}
    },

    /* 企标远控外发报文配置 */

    /* 远程锁车和限速报文 */
    {
        .sendEnable = false,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_5,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_5, // 使用宏定义
        .canCount = 1,
        .canIndex = 0,
        .signalInterval = CAN_SEND_INTERVAL_5,
        .totalInterval = CAN_SEND_INTERVAL_5,
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_5,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_5}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_5},
        .defaultData = {CAN_SEND_DFT_DATA_5}
    },

    /* 天气报文 */
    {
        .sendEnable = false, /* 上电后不发送，等收到平台指令后再发送 */
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_6,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_6, // 使用宏定义
        .canCount = 1,
        .canIndex = 0,
        .signalInterval = CAN_SEND_INTERVAL_6,
        .totalInterval = CAN_SEND_INTERVAL_6,
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_6,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_6}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_6},
        .defaultData = {CAN_SEND_DFT_DATA_6}
    },    /* 驾驶行为分析报文 */
    {
        .sendEnable = true,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_7,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_7, // 改为周期型，上电即发送
        .canCount = 7, /* 7条数据 */
        .canIndex = 0,
        .needWaitGroup = false,
        .signalInterval = CAN_SEND_SIGNAL_INTERVAL_7, /* 10ms间隔 */
        .totalInterval = CAN_SEND_INTERVAL_7, /* 1分钟循环 */
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            },
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            },
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            },
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            },
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            },
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            },
            {
                .canId = CAN_SEND_ID_7,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_7}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_7},
        .defaultData = {CAN_SEND_DFT_DATA_7}
    },    /* 历史能耗排名（分时）报文 */
    {
        .sendEnable = true,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_8,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_TYPE_CYCLE,
        .canCount = 5, /* 5条数据 */
        .canIndex = 0,
        .needWaitGroup = false,
        .signalInterval = CAN_SEND_SIGNAL_INTERVAL_8, /* 10ms间隔 */
        .totalInterval = CAN_SEND_INTERVAL_8, /* 10分钟循环 */
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_8,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_8}
            },
            {
                .canId = CAN_SEND_ID_8_1,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_8}
            },
            {
                .canId = CAN_SEND_ID_8_2,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_8}
            },
            {
                .canId = CAN_SEND_ID_8_3,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_8}
            },
            {
                .canId = CAN_SEND_ID_8_4,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_8}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_8},
        .defaultData = {CAN_SEND_DFT_DATA_8}
    },

    /* 历史能耗排名（分日）报文 */
    {
        .sendEnable = false,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_9,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_9,
        .canCount = 6, /* 6条数据 */
        .canIndex = 0,
        .needWaitGroup = false,
        .signalInterval = CAN_SEND_SIGNAL_INTERVAL_9, /* 10ms间隔 */
        .totalInterval = CAN_SEND_INTERVAL_9, /* 10分钟循环 */
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_9,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_9}
            },
            {
                .canId = CAN_SEND_ID_9_1,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_9}
            },
            {
                .canId = CAN_SEND_ID_9_2,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_9}
            },
            {
                .canId = CAN_SEND_ID_9_3,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_9}
            },
            {
                .canId = CAN_SEND_ID_9_4,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_9}
            },
            {
                .canId = CAN_SEND_ID_9_5,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_9}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_9},
        .defaultData = {CAN_SEND_DFT_DATA_9}
    },

    /* 历史能耗排名（分月）报文 */
    {
        .sendEnable = false,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_10,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_10,
        .canCount = 6, /* 6条数据 */
        .canIndex = 0,
        .needWaitGroup = false,
        .signalInterval = CAN_SEND_SIGNAL_INTERVAL_10, /* 10ms间隔 */
        .totalInterval = CAN_SEND_INTERVAL_10, /* 10分钟循环 */
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_10,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_10}
            },
            {
                .canId = CAN_SEND_ID_10_1,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_10}
            },
            {
                .canId = CAN_SEND_ID_10_2,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_10}
            },
            {
                .canId = CAN_SEND_ID_10_3,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_10}
            },
            {
                .canId = CAN_SEND_ID_10_4,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_10}
            },
            {
                .canId = CAN_SEND_ID_10_5,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_10}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_10},
        .defaultData = {CAN_SEND_DFT_DATA_10}
    },

    /* 历史能耗排名（分年）报文 */
    {
        .sendEnable = false,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_11,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_11,
        .canCount = 2, /* 2条数据 */
        .canIndex = 0,
        .needWaitGroup = false,
        .signalInterval = CAN_SEND_SIGNAL_INTERVAL_11, /* 10ms间隔 */
        .totalInterval = CAN_SEND_INTERVAL_11, /* 10分钟循环 */
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_11,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_11}
            },
            {
                .canId = CAN_SEND_ID_11_1,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_11}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_11},
        .defaultData = {CAN_SEND_DFT_DATA_11}
    },

    /* 车辆能耗排行榜报文 */
    {
        .sendEnable = false,
        .sendCount = 0,
        .timeOutCnt = 0,
        .canCh = CAN_SEND_CH_12,
        .byteOrder = CAN_INTEL,
        .sendType = CAN_SEND_TYPE_12,
        .canCount = 2, /* 2条数据 */
        .canIndex = 0,
        .needWaitGroup = false,
        .signalInterval = CAN_SEND_SIGNAL_INTERVAL_12, /* 10ms间隔 */
        .totalInterval = CAN_SEND_INTERVAL_12, /* 10分钟循环 */
        .lastTick = 0,
        .sendData = {
            {
                .canId = CAN_SEND_ID_12,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_12}
            },
            {
                .canId = CAN_SEND_ID_12_1,
                .dlc = 8,
                .canData = {CAN_SEND_DFT_DATA_12}
            }
        },
        .canDataBak = {CAN_SEND_DFT_DATA_12},
        .defaultData = {CAN_SEND_DFT_DATA_12}
    }
};


/*************************************************
函数名称: CanGetCurrentTboxstatu
函数功能: 获取当前tbox的状态
输入参数: 无
输出参数: 当前tbox状态
函数返回类型值：无
编写者: PC
编写日期 :2022/08/09
*************************************************/
TboxAuthStatus CanGetCurrentTboxstatu(void)
{
    uint8_t i = 0;
     for(i = 0; i < CAN_SEND_ID_MAX_NUM; i++)
     {
         if(g_canSendDataInfo[i].sendEnable == true)//有事件型报文正在发送
         {
            return TBOX_IS_BUSY;
         }
     }
     return TBOX_IS_SPARE;
}

/*************************************************
函数名称: CanUdsInit
函数功能: CAN UDS协议栈初始化
输入参数: 接收消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/15
*************************************************/
void CanUdsInit(void)
{
    CanDiagInitQueue();
    Can_Init();
    CanIf_Init(NULL);
    CanTp_Init();
    Dcm_Init();
    CanSM_Init();
    ComM_Init();
    McuCanInit();
    Nm_Init();
    #if defined(CAN_ENABLE_OSEKNM)
    /**
     * @brief OSEK NM模式初始化
     *
     * 初始化OSEK网络管理模块和用户处理模块
     */
    // OsekNm_Init();
    // OsekNm_UserHandleInit();
    #elif defined(CAN_ENABLE_AUTOSAR_NM)
    /**
     * @brief AUTOSAR NM模式初始化
     *
     * 初始化AUTOSAR CAN网络管理模块
     */
    CanNm_Init(NULL);
    #elif defined(CAN_ENABLE_NO_NM)
    /**
     * @brief 无网管模式初始化
     *
     * 在无网络管理模式下，不进行网络管理相关的初始化
     */
    /* 无网管模式下的空实现 */
    #else
    /**
     * @brief 默认情况（未定义任何网管模式）
     *
     * 如果没有定义任何网络管理模式，则使用AUTOSAR NM作为默认
     */
    CanNm_Init(NULL);
    #endif
    // ECallControlInit();
    g_udsStatus = CAN_UDS_INIT;
}


/*************************************************
函数名称: CanSendInfoInit
函数功能: CAN 周期发送数据初始化
输入参数:
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2024/12/18
*************************************************/
void CanSendInfoInit(void)
{
    // 初始化远控外发报文
    RemoteControlMsgInit();

    // 其他初始化代码可以在这里添加
}


/*************************************************
函数名称: CanRxDtcHandle
函数功能: CAN接收故障状态
输入参数: 接收消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/08/04
修改日期 :2016/10/18  增加故障码索引值
*************************************************/
static void CanRxDtcHandle(Msg msg)
{
    uint8_t *tempData  = NULL;
    uint8_t  dtcIndex  = 0;
    uint8_t  dtcStatus = 0;

    tempData = (uint8_t *)msg.lparam;

    dtcIndex  = tempData[0];
    dtcStatus = tempData[1];

    if ((DTC_MAX_INDEX <= dtcIndex) || (DTC_STATUS_FAULT < dtcStatus))
    {
        return;
    }

    // 写入故障码
    Dem_WriteDTC(dtcIndex, dtcStatus);
}

/*************************************************
函数名称: CanTransmitDataFromArm
函数功能: ARM发送CAN数据MCU透传给ECU(这个函数和CanRxTransmitData一样，只是这个函数把收到的数据先放到缓冲区，再发送)
          这个函数只负责把DMA中的数据存到缓冲区
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/29
*************************************************/
void CanTransmitDataFromArm(void)
{
    FtmInfo *pFtmInfo = FtmInitRead();

    if (FTM_MODE_ENTER == pFtmInfo->ftmMode)
    {
        if ((g_canRxData[0] == 0xa0) && (g_canRxData[7] == 0xa7) && (g_canRxData[3] == 0xa3))
        {
            FtmMcuTxToPcResult(FTM_TX_PC_UART32_INDEX, FTM_SUCCESS);
        }
        memset(g_canRxData, 0x00, CAN_PACKAGE_DATA_LEN);
        return;
    }

    if (0 != g_canDataArmStr.spacePos)
    {
        memcpy((void *)(&g_canDataArmStr.CanRxFromArm[(uint8_t)g_canDataArmStr.writePos][0]), (void *)g_canRxData, CAN_PACKAGE_DATA_LEN);
        memset(g_canRxData, 0, CAN_PACKAGE_DATA_LEN);
        g_canDataArmStr.writePos     = (g_canDataArmStr.writePos + 1) % (CAN_PACKAGE_DATA_NUM);
        g_canDataArmStr.spacePos     = g_canDataArmStr.spacePos - 1;
        g_canDataArmStr.countFromArm = (g_canDataArmStr.countFromArm + 1) % 0xffffffff;
    }

    if (CAN_SEND_OUT_DISABLE == g_commonInfo.canSendOutStat)
    {
        g_canDataArmStr.writePos = 0;
        g_canDataArmStr.readPos  = 0;
        g_canDataArmStr.spacePos = CAN_PACKAGE_DATA_NUM;
    }
}

/*************************************************
函数名称: CanTxTransmitDataMcuOut
函数功能: 把缓冲区中的数据发出去
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/29
*************************************************/
void CanTransmitDataMcuOut(void)
{
    uint8_t  canChannnel = 0;
    uint32_t canId       = 0;
    uint8_t  count       = 0;
    uint8_t  buf[8]      = {0};
    uint8_t  tempBuf[15] = {0};
    uint8_t  i           = 0;
    uint8_t  crc         = 0;

    if (CAN_PACKAGE_DATA_NUM == g_canDataArmStr.spacePos || CAN_SEND_OUT_DISABLE == g_commonInfo.canSendOutStat)
    {
        return;
    }

    if (g_canDataArmStr.readPos != g_canDataArmStr.writePos)
    {
        // _DI();
        memcpy(tempBuf, &g_canDataArmStr.CanRxFromArm[(uint8_t)g_canDataArmStr.readPos][0], 15);
        g_canDataArmStr.readPos  = (g_canDataArmStr.readPos + 1) % CAN_PACKAGE_DATA_NUM;
        g_canDataArmStr.spacePos = g_canDataArmStr.spacePos + 1;
        // _EI();

        canChannnel = tempBuf[0];
        canId       = (uint32_t)(tempBuf[1] << 24) + (tempBuf[2] << 16) + (tempBuf[3] << 8) + tempBuf[4];
        count       = tempBuf[5];
        for (i = 0; i < 8; i++)
        {
            buf[i] = tempBuf[6 + i];
        }
        crc = SystemCalCrc8(tempBuf, 14);

        if ((9 > count) && (crc == tempBuf[14]))
        {
            if (CAN_CHANNEL_4 < canChannnel)
            {
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can single channel is % error\r\n", canChannnel);
                return;
            }
            MCUCanSendData(canChannnel, canId, count, buf);
            g_canDataArmStr.countMcuOut = (g_canDataArmStr.countMcuOut + 1) % 0xffffffff;
        }
    }
}

/*************************************************
函数名称: CanNetStatusNotifyFunction
函数功能: 底层通知ARM 当前CAN 网络状态
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2019/10/30
*************************************************/
void CanNetStatusNotifyFunction(WorkStatus status)
{
    Msg msg;
    uint8_t buf[4] = {0x00};

    buf[0] = status;

    if (HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
    {
        msg.event  = MESSAGE_TX_ARM_CAN_NET_NOTIFY;
        msg.len    = 1;
        msg.lparam = (uint32_t)&buf[0];
        SystemSendMessage(TASK_ID_IPC, msg);
    }
}

/*************************************************
函数名称: McuCalcOsRevertTick
函数功能: 计算某个时间点前的tick
输入参数: curTick - 当前时间点，diffTick - 时间差
输出参数: 无
函数返回类型值： 计算所有时间Tick
编写者: zhengyong
编写日期 :2020/11/16
*************************************************/
SystemTick_t McuCalcOsRevertTick(SystemTick_t curTick, SystemTick_t diffTick)
{
    SystemTick_t tickMax = (SystemTick_t)-1;
    SystemTick_t prevTick = 0;

    if(curTick >= diffTick)
    {
        prevTick = curTick - diffTick;
    }
    else
    {
        prevTick = tickMax - diffTick + curTick;
    }

    return prevTick;
}

/*************************************************
函数名称: McuCalcOsTickDiff
函数功能: 计算OsTick时间差
输入参数: lastTick - 上次tick, nowTick - 当前tick
输出参数: 无
函数返回类型值： diff - tick差
编写者: zhengyong
编写日期 :2020/11/16
*************************************************/
SystemTick_t McuCalcOsTickDiff(SystemTick_t lastTick, SystemTick_t nowTick)
{
    SystemTick_t diff = 0;
    SystemTick_t tickMax = (SystemTick_t)-1;

    if(nowTick >= lastTick)
    {
        diff = nowTick - lastTick;
    }
    else
    {
        diff = tickMax - lastTick + nowTick;
    }

    return diff;
}

/*************************************************
函数名称: CanGetAccStatus
函数功能: 查询车辆ACC状态
输入参数: 无
输出参数: 无
函数返回类型值：ACC状态值
编写者 zhengyong
编写日期 :2021/01/29
*************************************************/
WorkStatus CanGetAccStatus(void)
{
    return (WorkStatus)g_commonInfo.accStatus;
}

/*************************************************
函数名称: CanSend
函数功能: Can发送报文
输入参数: channel, canId, dlc, buf
输出参数: 无
函数返回类型值 MCU_OK：发送成功，MCU_ERROR：发送失败
编写者 BenYuLong
编写日期: 2021/01/29
*************************************************/
int CanSend(BoardCanChannel channel, uint32_t canId, uint8_t dlc, uint8_t *buf)
{
    uint8_t           ret           = MCU_OK;
    static TickType_t lastErrorTime = 0;
    TickType_t        currentTime;
    const TickType_t  ERROR_LOG_INTERVAL = pdMS_TO_TICKS(5000);  // 5秒间隔

    /*发送报文*/
    if (TRUE != MCUCanSendData(channel, canId, dlc, buf))
    {
        currentTime = xTaskGetTickCount();

        // 第一次错误或距离上次打印超过5秒则打印日志
        if (lastErrorTime == 0 || (currentTime - lastErrorTime) >= ERROR_LOG_INTERVAL)
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "CAN send failed\r\n");
            lastErrorTime = currentTime;
        }
        ret = MCU_ERROR;
    }

    if ((ret != MCU_OK))
    {
        return MCU_ERROR;
    }
    return MCU_OK;
}

/*************************************************
函数名称: SetChecksumCrc
函数功能: 设置的Checksum Crc
输入参数: buf:发送的数据指针
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期 :2024/10/17
*************************************************/
void SetChecksumCrc(uint8_t *buf)
{
    uint8_t i = 0, j = 0;
    uint8_t crc = 0;

    for(i = 0; i < 7; ++ i)
    {
        crc ^= buf[i];
        for(j = 0; j < 8; ++ j)
        {
            if((crc & 0x80) != 0)
            {
                crc = (crc << 1) ^ 0x2F;
            }
            else
            {
                crc = (crc << 1);
            }
        }
    }
    buf[7] = ~crc;
}


/*************************************************
函数名称: CanBusoffUpdateSendTime
函数功能: 当发生busoff并恢复后，更新报文发送时间，第一时间发送报文
输入参数: 发送的报文索引值
输出参数: 无
函数返回类型值：无
编写者 pc
编写日期 :2022/10/17
*************************************************/
int CanBusoffUpdateSendTime(uint8_t index)
{
    // 边界检查
    if (index >= CAN_SEND_ID_MAX_NUM)
    {
        return MCU_PARA_ERROR;
    }

    SystemTick_t             systime;
    static uint8_t           busoff_flags[CAN_SEND_ID_MAX_NUM] = {0};
    CanIf_ChannelGetModeType canStatus                         = CANIF_GET_OFFLINE;

    // 检查返回值
    if (E_OK != CanIf_GetPduMode(g_canSendDataInfo[index].canCh, &canStatus))
    {
        return MCU_ERROR;
    }

    /*对应通道存在busoff*/
    if (canStatus != CANIF_GET_TX_ONLINE && canStatus != CANIF_GET_ONLINE)
    {
        busoff_flags[index] = 1;
    }

    /*发生busoff 并已恢复,更新发送时间*/
    if (((canStatus == CANIF_GET_TX_ONLINE) || (canStatus == CANIF_GET_ONLINE)) && busoff_flags[index] == 1)
    {
        systime                           = g_osCurrentTickTime;
        g_canSendDataInfo[index].lastTick = McuCalcOsRevertTick(systime, g_canSendDataInfo[index].signalInterval);
        busoff_flags[index]               = 0;
    }

    return MCU_OK;
}

// 处理VIN码报文特殊情况
static bool CanHandleVinMessage(uint32_t index, SystemTick_t curTick)
{
    // 暂不判断时间，上电即发送，但是无效的VIN码不发送
    if (3 == index)
    {
        CarInfo *carInfo = GetCanInfo();
        uint8_t zeroVin[TBOX_VIN_LEN] = {0};
        uint8_t zeroCharVin[TBOX_VIN_LEN] = {'0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0'};

        // 检查VIN码是否为全0字节
        if (0 == memcmp(carInfo->vinCode, zeroVin, TBOX_VIN_LEN))
        {
            return false;
        }

        // 检查VIN码字符串长度是否为0（空字符串），使用strnlen限制最大长度
        if (0 == strnlen((char *)carInfo->vinCode, TBOX_VIN_LEN))
        {
            return false;
        }

        // 检查VIN码是否为17个'0'字符
        if (0 == memcmp(carInfo->vinCode, zeroCharVin, TBOX_VIN_LEN))
        {
            return false;
        }
    }
    return true;
    // 下标3是VIN码报文，需要上电后3分钟才开始发送
    if (FALSE == g_wakeupFlag && 3 == index)
    {
        SystemTick_t diffTick = McuCalcOsTickDiff(g_canSendDataInfo[index].lastTick, curTick);
        if (diffTick < 180000)
        {
            return false;
        }
        g_wakeupFlag = TRUE;
    }
    return true;
}

// 处理发送结果
static void CanHandleSendResult(uint32_t index, int sendResult)
{
    if (MCU_OK == sendResult)
    {
        // 如果是VIN码报文则更新计数
        if (CAN_SEND_ID_4 == g_canSendDataInfo[index].sendData->canId)
        {
            g_vinSendNum++;
        }

        // 如果是0x180AA7A3报文（时间及终端状态报文），则更新Life值
        if (CAN_SEND_ID_1 == g_canSendDataInfo[index].sendData->canId)
        {
            // 获取当前Life值并自增
            uint8_t currentLife = g_canSendDataInfo[index].sendData->canData[7];
            g_canSendDataInfo[index].sendData->canData[7] = currentLife + 1;
        }
    }
}

// 更新事件型报文的计数和状态
static void CanUpdateEventStatus(uint32_t index)
{
    // 只有发送了事件型数据即sendEnable为TRUE时，才计数
    if (!g_canSendDataInfo[index].sendEnable)
    {
        return;
    }

    if (0 < g_canSendDataInfo[index].sendCount && g_canSendDataInfo[index].sendCount != UINT16_MAX)
    {
        g_canSendDataInfo[index].sendCount--;
    }

    // 发送完成后的处理，下面代码已被注释，需要时再实现
    if (0 == g_canSendDataInfo[index].sendCount)
    {
        // TODO: 实现恢复逻辑
        // 目前这部分逻辑已被注释掉
    }
}

/*************************************************
函数名称: CanPeriodSendData
函数功能: Can周期性处理CAN报文发送任务1ms
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
static void CanPeriodSendData(void)
{
    uint32_t     i        = 0;
    SystemTick_t curTick  = g_osCurrentTickTime;
    SystemTick_t diffTick = 0;
    static uint32_t errorCount = 0;

    for (i = 0; i < CAN_SEND_ID_MAX_NUM; i++)
    {
        // 检查是否已到达列表末尾
        if (CAN_INVALID_CAN_ID == g_canSendDataInfo[i].sendData->canId)
        {
            break;
        }

        // 检查事件型CAN是否需要发送
        if (CAN_TYPE_EVENT == g_canSendDataInfo[i].sendType
        && (!g_canSendDataInfo[i].sendEnable || 0 == g_canSendDataInfo[i].sendCount))
        {
            continue;
        }

        // 检查发送通道是否busoff并处理
        CanBusoffUpdateSendTime(i);

        // 检查是否满足发送间隔要求
        diffTick = McuCalcOsTickDiff(g_canSendDataInfo[i].lastTick, curTick);
        if (g_canSendDataInfo[i].canCount > 1)
        {
            // 多条消息的情况
            if (g_canSendDataInfo[i].needWaitGroup)
            {
                // 组间等待状态，检查组间时间间隔
                if (diffTick < g_canSendDataInfo[i].totalInterval)
                {
                    continue;
                }
            }
            else
            {
                // 组内消息发送状态，检查组内时间间隔
                if (diffTick < g_canSendDataInfo[i].signalInterval)
                {
                    continue;
                }
            }
        }
        else
        {
            // 单条消息的情况，只检查signalInterval
            if (diffTick < g_canSendDataInfo[i].signalInterval)
            {
                continue;
            }
        }

        // 当事件型报文超时时，清除发送计数和发送使能
        if (0 < g_canSendDataInfo[i].timeOutCnt)
        {
            g_canSendDataInfo[i].timeOutCnt--;
            if (0 == g_canSendDataInfo[i].timeOutCnt && g_canSendDataInfo[i].sendCount != UINT16_MAX)
            {
                g_canSendDataInfo[i].sendCount  = 0;
                g_canSendDataInfo[i].sendEnable = false;
                SystemApiLogPrintf(LOG_WARING_OUTPUT,
                                   "CAN[ID:0x%x CH:%d] timeout, clear send count and enable\r\n",
                                   g_canSendDataInfo[i].sendData->canId,
                                   g_canSendDataInfo[i].canCh);
            }
        }

        // 处理VIN码报文特殊情况
        if (!CanHandleVinMessage(i, curTick))
        {
            continue;
        }

        // 更新最后发送时间
        g_canSendDataInfo[i].lastTick = curTick;

        // 发送报文
        int sendResult = MCU_ERROR;
        if (g_canSendDataInfo[i].canCount > 1)
        {
            // 发送当前索引的数据
            sendResult = CanSend(g_canSendDataInfo[i].canCh,
                                 g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canId,
                                 CAN_DATA_LENGTH,
                                 g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData);

            // 处理多条数据的索引和组状态
            if (g_canSendDataInfo[i].canIndex == g_canSendDataInfo[i].canCount - 1)
            {
                // 如果是最后一条数据，更新索引为0，并设置下一组的发送时间
                g_canSendDataInfo[i].canIndex      = 0;
                g_canSendDataInfo[i].needWaitGroup = true;

                SystemApiLogPrintf(LOG_INFO_OUTPUT,
                                   "CAN[0x%x CH:%d] Group complete, Data:%X%X%X%X%X%X%X%X, Next:%dms\r\n",
                                   g_canSendDataInfo[i].sendData[0].canId,
                                   g_canSendDataInfo[i].canCh,
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[0],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[1],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[2],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[3],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[4],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[5],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[6],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[7],
                                   g_canSendDataInfo[i].totalInterval);
            }
            else
            {
                // 如果不是最后一条数据，更新索引
                g_canSendDataInfo[i].canIndex++;
                g_canSendDataInfo[i].needWaitGroup = false;

                SystemApiLogPrintf(LOG_DEBUG_OUTPUT,
                                   "CAN[0x%x CH:%d] Data[%d/%d], Data:%X%X%X%X%X%X%X%X, Interval:%dms\r\n",
                                   g_canSendDataInfo[i].sendData[0].canId,
                                   g_canSendDataInfo[i].canCh,
                                   g_canSendDataInfo[i].canIndex,
                                   g_canSendDataInfo[i].canCount,
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[0],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[1],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[2],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[3],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[4],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[5],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[6],
                                   g_canSendDataInfo[i].sendData[g_canSendDataInfo[i].canIndex].canData[7],
                                   g_canSendDataInfo[i].totalInterval);
            }
        }
        else
        {
            // 兼容旧版本，只有一条数据时直接发送
            sendResult = CanSend(g_canSendDataInfo[i].canCh,
                                 g_canSendDataInfo[i].sendData[0].canId,
                                 CAN_DATA_LENGTH,
                                 g_canSendDataInfo[i].sendData[0].canData);
            if(MCU_OK == sendResult)
            {
                errorCount = 0;
            }
            else
            {
                errorCount++;
                if(errorCount >= 100)
                {
                    Can_BusOff_Handler(g_canSendDataInfo[i].canCh);
                    errorCount = 0;
                }
            }
        }

        // 处理发送结果
        CanHandleSendResult(i, sendResult);

        // 更新事件型报文的计数和状态
        CanUpdateEventStatus(i);
    }
}

/*************************************************
函数名称: IntelCheckBitValid
函数功能: 检查偏移位和位长度是否合法
输入参数: startBit - 起始位， bitLen - 位长
输出参数: 无
函数返回类型值：TRUE - 合法，FALSE - 不合法
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
static bool IntelCheckBitValid(uint8_t startBit, uint8_t bitLen)
{
    if(CAN_DATA_LENGTH * 8 <= startBit || 0 == bitLen)
    {
        return false;
    }
    if(CAN_DATA_LENGTH * 8 - startBit < bitLen)
    {
        return false;
    }
    return true;
}

/*************************************************
函数名称: MotLsbCheckBitValid
函数功能: 检查偏移位和位长度是否合法
输入参数: startBit - 起始位， bitLen - 位长
输出参数: 无
函数返回类型值：TRUE - 合法，FALSE - 不合法
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
static bool MotLsbCheckBitValid(uint8_t startBit, uint8_t bitLen)
{
    uint8_t startByte = 0;
    uint8_t byteStartBit = 0;

    if(CAN_DATA_LENGTH * 8 <= startBit || 0 == bitLen)
    {
        return false;
    }

    startByte = startBit >> 3;
    byteStartBit = startBit & 0x07;

    //8 - byteStartBit为起始位所有字节的有效位，再加上前面的位长度，即为可容纳位长度
    if(8 - byteStartBit + startByte * 8 < bitLen)
    {
        return false;
    }

    return true;
}

/*************************************************
函数名称: MotMsbCheckBitValid
函数功能: 检查偏移位和位长度是否合法
输入参数: startBit - 起始位， bitLen - 位长
输出参数: 无
函数返回类型值：TRUE - 合法，FALSE - 不合法
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
static bool MotMsbCheckBitValid(uint8_t startBit, uint8_t bitLen)
{
    uint8_t startByte = 0;
    uint8_t byteStartBit = 0;

    if(CAN_DATA_LENGTH * 8 <= startBit || 0 == bitLen)
    {
        return false;
    }

    startByte = startBit >> 3;
    byteStartBit = startBit & 0x07;

    //byteStartBit + 1为起始位所有字节的有效位，再加上后面的位长度，即为可容纳位长度
    if(byteStartBit + 1 + (CAN_DATA_LENGTH - startByte - 1) * 8 < bitLen)
    {
        return false;
    }

    return true;
}

/**
 * @brief  在 Intel 小端格式下，按位更新 8 字节 CAN 数据
 *
 * @param[in]      updData  待更新的信号描述（起始位、位长、数据等）
 * @param[in,out]  canData  8 字节 CAN 原始数据缓存
 *
 * @retval MCU_OK          更新成功
 * @retval MCU_PARA_ERROR  参数非法或位域越界
 */
static int CanUpdateData(CanUpdDataInfo_s *updData, uint8_t *canData)
{
    /*----------------------------- 参数校验 -----------------------------*/
    if ((updData == NULL) || (canData == NULL))
    {
        return MCU_PARA_ERROR;
    }

    /* 检查位域是否合法：0 ≤ startBit ≤ 63，1 ≤ bitLen ≤ 32 等 */
    if (!IntelCheckBitValid(updData->startBit, updData->bitLen))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Bit offset not valid(%u‑%u)\r\n", updData->startBit, updData->bitLen);
        return MCU_PARA_ERROR;
    }

    /*----------------------------- 初始变量 -----------------------------*/
    uint8_t  curByte    = (uint8_t) (updData->startBit >> 3U);   /* 起始字节索引               */
    uint8_t  bitOffset  = (uint8_t) (updData->startBit & 0x07U); /* 字节内偏移 (0‑7)        */
    uint8_t  remainBits = updData->bitLen;                       /* 剩余待写位数               */
    uint32_t remainData = updData->data;                         /* 待写入的原始数据           */

    /*----------------------------- 分段写入 -----------------------------*/
    while (remainBits > 0U)
    {
        /* 1) 计算本轮可写位数：首段要考虑 bitOffset，其余满 8 位 */
        uint8_t curBitLen =
            (uint8_t) ((remainBits < (uint8_t) (8U - bitOffset)) ? remainBits : (uint8_t) (8U - bitOffset));

        /* 2) 构造本段掩码（curBitLen 个 1）并提取、对齐段数据 */
        uint32_t segMask  = (1UL << curBitLen) - 1UL; /* 低 curBitLen 位为 1 */
        uint8_t  segData  = (uint8_t) ((remainData & segMask) << bitOffset);
        uint8_t  byteMask = (uint8_t) (segMask << bitOffset); /* 写入位置对应掩码   */

        /* 3) 清除旧位并写入新数据 */
        canData[curByte] = (uint8_t) ((canData[curByte] & (uint8_t) (~byteMask)) | segData);

        /* 4) 更新循环变量 */
        remainData >>= curBitLen; /* 剔除已写入的低位 */
        remainBits = (uint8_t) (remainBits - curBitLen);
        bitOffset  = 0U; /* 后续字节从 bit0 开始写 */
        curByte++;       /* Intel 小端：向更高地址扩展 */
    }

    return MCU_OK;
}


/*************************************************
函数名称: MotLsbCanUpdateData
函数功能: 更新CAN报文数据
输入参数: updData - 本次更新的数据信息
输出参数: canData - 更新后的数据及其他原数据
函数返回类型值：MCU_OK - 更新成功，MCU_PARA_ERROR - 配置有错
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
static int MotLsbCanUpdateData(CanUpdDataInfo_s *updData, uint8_t *canData)
{
    uint8_t curByte = 0;
    uint8_t byteStartBit = 0;
    uint8_t curBitLen = 0;
    uint8_t remainBitLen = 0;
    uint8_t remainData = 0;
    uint8_t temp = 0;
    uint32_t mask = 0;
    uint32_t i = 0;

    if(!MotLsbCheckBitValid(updData->startBit, updData->bitLen))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Bit offset not valid(%u-5u)\r\n", updData->startBit, updData->bitLen);
        return MCU_PARA_ERROR;
    }

    curByte = updData->startBit >> 3;
    byteStartBit = updData->startBit & 0x07;
    curBitLen = 8 - byteStartBit;
    remainBitLen = updData->bitLen;
    remainData = updData->data;

    do
    {
        if(remainBitLen < curBitLen)
        {
            curBitLen = remainBitLen;
        }
        //计算当前要更新数据掩码，获得更新数据在目标字节中的位数据
        mask = 0;
        for(i = 0; i < curBitLen; i++)
        {
            mask = (mask << 1) + 1;
        }
        temp = (uint8_t)(remainData & mask) << byteStartBit;

        //清除目标字节中的要更新的位
        mask <<= byteStartBit;
        canData[curByte] = canData[curByte] & (~mask);

        //合并数据
        canData[curByte] |= temp;

        //更新剩余未合并数据，前面已经校验过，这里不再考虑翻转
        remainBitLen -= curBitLen;
        remainData >>= byteStartBit;
        curByte--;
        //除起始位所在字节外，其他字节起始位都是0，长度为8
        byteStartBit = 0;
        curBitLen = 8;
    }while(remainBitLen > 0);

    return MCU_OK;
}

/*************************************************
函数名称: MotMsbCanUpdateData
函数功能: 更新CAN报文数据
输入参数: updData - 本次更新的数据信息
输出参数: canData - 更新后的数据及其他原数据
函数返回类型值：MCU_OK - 更新成功，MCU_PARA_ERROR - 配置有错
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
static int MotMsbCanUpdateData(CanUpdDataInfo_s *updData, uint8_t *canData)
{
    uint8_t curByte = 0;
    uint8_t byteStartBit = 0;
    uint8_t curBitLen = 0;
    uint8_t remainBitLen = 0;
    uint32_t remainData = 0;
    uint8_t temp = 0;
    uint32_t mask = 0;
    uint32_t i = 0;

    if(!MotMsbCheckBitValid(updData->startBit, updData->bitLen))
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Bit offset not valid(%u-5u)\r\n", updData->startBit, updData->bitLen);
        return MCU_PARA_ERROR;
    }

    curByte = updData->startBit >> 3;
    byteStartBit = updData->startBit & 0x07;
    curBitLen = byteStartBit + 1;
    remainBitLen = updData->bitLen;
    remainData = updData->data;

    do
    {
        if(remainBitLen < curBitLen)
        {
            curBitLen = remainBitLen;
        }

        //计算当前要更新数据掩码，获得更新数据在目标字节中的位数据
        mask = 0;
        for(i = 0; i < curBitLen; i++)
        {
            mask = (mask << 1) + 1;
        }
        temp = (uint8_t)(remainData >> (remainBitLen - curBitLen) & mask) << (byteStartBit + 1 - curBitLen);

        //清除目标字节中的要更新的位
        mask <<= (byteStartBit + 1 - curBitLen);
        canData[curByte] = canData[curByte] & (~mask);

        //合并数据
        canData[curByte] |= temp;

        //更新剩余未合并数据，前面已经校验过，这里不再考虑溢出
        remainBitLen -= curBitLen;
        curByte++;
        //除起始位所在字节外，其他字节起始位都是7，长度为8
        byteStartBit = 7;
        curBitLen = 8;
    }while(remainBitLen > 0);

    return MCU_OK;
}

/*************************************************
函数名称: CanUpdateMode
函数功能: 更新CAN报文数据模式选择
输入参数: updData - 本次更新的数据信息
输出参数: canData - 更新后的数据及其他原数据
函数返回类型值：MCU_OK - 更新成功，MCU_PARA_ERROR - 配置有错
编写者 zhengyong
编写日期 :2020/07/31
*************************************************/
static int CanUpdateMode(CanUpdDataInfo_s *updData, uint8_t *canData, uint8_t canIndex)
{
    int      ret        = MCU_OK;
    uint32_t groupIndex = 0;
    if (CAN_SEND_MAX_DATA_COUNT <= canIndex)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can index out of range\r\n");
        return MCU_PARA_ERROR;
    }
    // WARNING: 注意canIndex越界风险

    for (groupIndex = 0; groupIndex < CAN_SEND_ID_MAX_NUM; groupIndex++)
    {
        if (g_canSendDataInfo[groupIndex].sendData[canIndex].canId == updData->canId && g_canSendDataInfo[groupIndex].canCh == updData->canCh)
        {
            break;
        }
    }
    if (groupIndex == CAN_SEND_ID_MAX_NUM)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT,
                           "Not support id(0x%X) and ch(%u) for update groupIndex:%u canIndex:%u\r\n",
                           updData->canId,
                           updData->canCh,
                           groupIndex,
                           canIndex);
        return MCU_NOT_SUPPORT;
    }
    if (g_canSendDataInfo[groupIndex].byteOrder == CAN_INTEL)
    {
        ret = CanUpdateData(updData, g_canSendDataInfo[groupIndex].sendData[canIndex].canData);
    }
    else if (g_canSendDataInfo[groupIndex].byteOrder == CAN_MOTOROLA_LSB)
    {
        ret = MotLsbCanUpdateData(updData, g_canSendDataInfo[groupIndex].sendData[canIndex].canData);
    }
    else if (g_canSendDataInfo[groupIndex].byteOrder == CAN_MOTOROLA_MSB)
    {
        ret = MotMsbCanUpdateData(updData, g_canSendDataInfo[groupIndex].sendData[canIndex].canData);
    }
    return ret;
}

/*************************************************
函数名称: CanUpdateSendData
函数功能: 更新CAN报文发送数据
输入参数: updData - 本次更新的数据信息
输出参数: 无
函数返回类型值：MCU_OK - 更新成功，MCU_BUSY - 事件型数据BUSY，MCU_PARA_ERROR - 参数错误
编写者 zhengyong
编写日期 :2020/07/30
*************************************************/
int CanUpdateSendData(CanUpdDataInfo_s *updData)
{
    int          ret        = MCU_OK;
    uint32_t     groupIndex = 0;
    uint32_t     canIndex   = 0;
    SystemTick_t systime    = g_osCurrentTickTime;
    bool         foundCAN   = false;
    if (NULL == updData)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can update data null\r\n");
        return MCU_PARA_ERROR;
    }
    for (groupIndex = 0; groupIndex < CAN_SEND_ID_MAX_NUM; groupIndex++)
    {
        for (canIndex = 0; canIndex < CAN_SEND_MAX_DATA_COUNT; canIndex++)
        {
            if (g_canSendDataInfo[groupIndex].sendData[canIndex].canId == updData->canId && g_canSendDataInfo[groupIndex].canCh == updData->canCh)
            {
                foundCAN = true;
                break;
            }
        }
        if (foundCAN)
        {
            break;
        }
    }
    if (groupIndex == CAN_SEND_ID_MAX_NUM || canIndex == CAN_SEND_MAX_DATA_COUNT || !foundCAN)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT,
                           "Not support id(%u) and ch(%u) canIndex(%u)\r\n",
                           updData->canId,
                           updData->canCh,
                           canIndex);
        return MCU_NOT_SUPPORT;
    }

    if (CAN_TYPE_EVENT == updData->sendType && g_canSendDataInfo[groupIndex].sendEnable)
    {
        if (!updData->merge)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT,
                               "CAN[ID:0x%x CH:%d] busy, merge:%d\r\n",
                               updData->canId,
                               updData->canCh,
                               updData->merge);
            return MCU_BUSY;
        }
    }
    ret = CanUpdateMode(updData, g_canSendDataInfo[groupIndex].sendData[canIndex].canData, canIndex);
    if (MCU_OK != ret)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT,
                           "Update CAN[ID:0x%x CH:%d] data fail, startBit:%d bitLen:%d data:0x%x ret:%d\r\n",
                           updData->canId,
                           updData->canCh,
                           updData->startBit,
                           updData->bitLen,
                           updData->data,
                           ret);
        return ret;
    }

    //周期型数据还需要将数据保存至备份数组内，这样发了其他事件型数据后才可以还原
    if (CAN_TYPE_CYCLE == updData->sendType)
    {
        //有事件型报文正在发送时，不能全部备份，只能更新原备份数据
        if (g_canSendDataInfo[groupIndex].sendEnable)
        {
            CanUpdateMode(updData, g_canSendDataInfo[groupIndex].canDataBak, canIndex);
        }
        else
        {
            // 备份指定索引的数据，并设置bakIndex
            memcpy(g_canSendDataInfo[groupIndex].canDataBak, g_canSendDataInfo[groupIndex].sendData[canIndex].canData, CAN_DATA_LENGTH);
            g_canSendDataInfo[groupIndex].bakIndex = canIndex;
        }
    }
    else
    {
        //事件型数据需要设置发送次数，并enable
        //（在周期型CAN上发事件型数据也需要，enable后才开始计数）
        g_canSendDataInfo[groupIndex].sendCount  = updData->sendCnt;
        g_canSendDataInfo[groupIndex].timeOutCnt = updData->sendCnt * 3;
        g_canSendDataInfo[groupIndex].sendEnable = true;

        //将last tick设为当前时间的interval之前，第一个CAN报文可以10ms内（can周期）发出去
        if (CAN_TYPE_EVENT == g_canSendDataInfo[groupIndex].sendType)
        {
            systime                       = g_osCurrentTickTime;
            g_canSendDataInfo[groupIndex].lastTick = McuCalcOsRevertTick(systime, g_canSendDataInfo[groupIndex].signalInterval);
#if (LOG_TEST_SWITCH == SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT,
                               "CAN[ID:0x%x CH:%d] lastTick:%d, type:%d\r\n",
                               g_canSendDataInfo[groupIndex].sendData->canId,
                               g_canSendDataInfo[groupIndex].canCh,
                               g_canSendDataInfo[groupIndex].lastTick,
                               g_canSendDataInfo[groupIndex].sendType);
#endif
        }
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT,
                       "RCTL: update send can, data(%x-%x-%x-%x-%x-%x-%x-%x), id:%x, count=%d, type=%d\r\n",
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[0],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[1],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[2],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[3],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[4],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[5],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[6],
                       g_canSendDataInfo[groupIndex].sendData[canIndex].canData[7],
                       g_canSendDataInfo[groupIndex].sendData->canId,
                       g_canSendDataInfo[groupIndex].sendCount,
                       g_canSendDataInfo[groupIndex].sendType);
    return MCU_OK;
}

/*************************************************
 函数名称: CanSendBcmOrVbuAuthResponse
 函数功能: 更新需要发送Tbox的认证结果
 输入参数: id:报文ID
           data:认证响应报文
           datalen:响应报文长度
 输出参数:
 函数返回类型值：无
 编写者: PC
 编写日期 :2022/08/09
 *************************************************/
int8_t CanUpdateAuthResponse(uint32_t id,uint8_t* data,uint8_t datalen)
{
    SystemTick_t systime;
    uint8_t i = 0;

    if(data ==NULL)
    {
    return MCU_ERROR;
    }
    systime = g_osCurrentTickTime;

    for(i = 0; i < CAN_SEND_ID_MAX_NUM; i++)
    {
        if(g_canSendDataInfo[i].sendData->canId == id)
        {
            g_canSendDataInfo[i].sendEnable = true;
            g_canSendDataInfo[i].sendCount = 1;
            memcpy(g_canSendDataInfo[i].sendData->canData, data, PEPS_REPONSE_FRAME_LEN);
            g_canSendDataInfo[i].lastTick = McuCalcOsRevertTick(systime, g_canSendDataInfo[i].signalInterval);
            break;
        }
    }
    if(i == CAN_SEND_ID_MAX_NUM)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Not support id(0x%X) \r\n", id);
        return MCU_NOT_SUPPORT;
    }

    return MCU_OK;
}

/*************************************************
 函数名称: CanUpdateAllData
函数功能: 更新整条CAN报文发送数据
输入参数: canId, canCh, sendCnt - 连续发送次数（仅针对事件型），data（8字节）, canIndex - 表示要更新第几条CAN数据，默认为0，兼容旧版本, groupIndex - 要更新第几组规则，默认为0
输出参数: 无
函数返回类型值：MCU_OK - 更新成功，MCU_PARA_ERROR - 参数错误
编写者: zhengyong
编写日期 :2021/01/12
修改日期 :2023/07/25 增加groupIndex参数
*************************************************/
int CanUpdateAllData(uint32_t canId, uint8_t canCh, uint16_t sendCnt, uint8_t *data, uint8_t canIdIndex)
{
    uint32_t     groupIndex = 0;
    uint32_t     canIndex   = 0;
    SystemTick_t systime;
    bool         foundMatch = false;

    if (NULL == data)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "can update all data null\r\n");
        return MCU_PARA_ERROR;
    }

    for (groupIndex = 0; groupIndex < CAN_SEND_ID_MAX_NUM; groupIndex++)
    {
        // 0的话就自动查找，否则就直接使用指定的索引
        if (0 == canIdIndex)
        {
            // 在指定组中遍历所有可能的CAN数据
            for (canIndex = 0; canIndex < g_canSendDataInfo[groupIndex].canCount; canIndex++)
            {
                if (g_canSendDataInfo[groupIndex].sendData[canIndex].canId == canId)
                {
                    foundMatch = true;
                    SystemApiLogPrintf(LOG_INFO_OUTPUT,
                                       "Found CAN ID 0x%x in auto search mode - Group:%d, Index:%d\r\n",
                                       canId,
                                       groupIndex,
                                       canIndex);
                    canIdIndex = canIndex;
                    break;
                }
            }
        }
        else
        {
            foundMatch = g_canSendDataInfo[groupIndex].sendData[canIdIndex].canId == canId;
            if (foundMatch)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT,
                                   "Found CAN ID 0x%x in specified index mode - Group:%d, Index:%d\r\n",
                                   canId,
                                   groupIndex,
                                   canIdIndex);
            }
        }
        if (foundMatch)
        {
            break;
        }
    }
    if (groupIndex >= CAN_SEND_ID_MAX_NUM || !foundMatch
        || g_canSendDataInfo[groupIndex].sendData->canId == CAN_INVALID_CAN_ID || canIdIndex >= CAN_SEND_MAX_DATA_COUNT)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT,
                           "Not support id(%u) and ch(%u), groupIndex:%u, canIndex:%u\r\n",
                           canId,
                           canCh,
                           groupIndex,
                           canIdIndex);
        return MCU_NOT_SUPPORT;
    }

    g_canSendDataInfo[groupIndex].sendCount = sendCnt;
    g_canSendDataInfo[groupIndex].sendEnable = true;

    // 备份要更新的元素的数据，并存储其索引
    memcpy(g_canSendDataInfo[groupIndex].canDataBak, g_canSendDataInfo[groupIndex].sendData[canIdIndex].canData, CAN_DATA_LENGTH);
    g_canSendDataInfo[groupIndex].bakIndex = groupIndex;

    // 更新指定index的CAN数据
    memcpy(g_canSendDataInfo[groupIndex].sendData[canIdIndex].canData, data, CAN_DATA_LENGTH);

    // 如果是事件型数据，更新lastTick使其尽快发送
    if (CAN_TYPE_EVENT == g_canSendDataInfo[groupIndex].sendType)
    {
        systime = g_osCurrentTickTime;
        g_canSendDataInfo[groupIndex].lastTick = McuCalcOsRevertTick(systime, g_canSendDataInfo[groupIndex].signalInterval);
    }
    // 如果sendCount大于0则取消组间等待
    if (g_canSendDataInfo[groupIndex].sendCount > 0)
    {
        g_canSendDataInfo[groupIndex].needWaitGroup = false;
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT,
                       "Update CAN data: ID=0x%x, CH=%d, Index=%d, SendCnt=%d, GroupIndex=%d, CanIdIndex=%d\r\n",
                       canId,
                       canCh,
                       groupIndex,
                       g_canSendDataInfo[groupIndex].sendCount,
                       groupIndex,
                       canIdIndex);

    return MCU_OK;
}


/*************************************************
函数名称: RemoteControlServiceReqEvent
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: pc
编写日期 :2022/05/18
*************************************************/
void RemoteControlServiceReqEvent(uint8_t *data, uint32_t dataLen)
{
    Msg                     msg;
    int                     ret                                     = MCU_OK;
    uint32_t                i                                       = 0;
    CanSendPassInfo_s      *passInfo                                = NULL;
    CanUpdDataInfo_s        canUpdInfo                              = {0};
    uint8_t                 resData[2]                              = {0};
    static uint32_t         mergeCount                              = 0;
    static uint32_t         errBusyCnt                              = 0; //添加Busy恢复机制
    static CanUpdDataInfo_s canMergeUpdInfo[CAN_DATA_MERGE_MAX_NUM] = {0};
    passInfo                                                        = (CanSendPassInfo_s *) data;

    if (passInfo == NULL || sizeof(CanSendPassInfo_s) != dataLen)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Can pass arm send data failed, datalen=%u\r\n", dataLen);
        return;
    }

    if (passInfo->single)
    {
        SystemApiLogPrintf(
            LOG_INFO_OUTPUT,
            "Can info send pass, merge:%u, canId:%03x, sendCnt:%u sendtype:%u startBit:%u bitLen:%u data:0x%x\r\n",
            passInfo->u.singleData.mergeInfo,
            passInfo->canId,
            passInfo->sendCnt,
            passInfo->u.singleData.sendType,
            passInfo->u.singleData.startBit,
            passInfo->u.singleData.bitLen,
            passInfo->u.singleData.data);
        canUpdInfo.canId    = passInfo->canId;
        canUpdInfo.canCh    = passInfo->canCh;
        canUpdInfo.sendCnt  = passInfo->sendCnt;
        canUpdInfo.sendType = passInfo->u.singleData.sendType;
        canUpdInfo.startBit = passInfo->u.singleData.startBit;
        canUpdInfo.bitLen   = passInfo->u.singleData.bitLen;
        canUpdInfo.data     = passInfo->u.singleData.data;

        switch (passInfo->u.singleData.mergeInfo)
        {
            case DATA_MERGE_NONE:
                canUpdInfo.merge = false;
                ret              = CanUpdateSendData(&canUpdInfo);
                break;
            case DATA_WAIT_MERGE:
                if (0 < mergeCount)
                {
                    errBusyCnt++;
                    if (errBusyCnt <= 10)
                    {
                        ret = MCU_BUSY;
                        break;
                    }
                    //如果连续10次都未发送出去，可能是有错，需要尝试恢复
                    errBusyCnt = 0;
                    mergeCount = 0;
                    SystemApiLogPrintf(LOG_WARING_OUTPUT, "Send pass busy count recover\r\n");
                }
                canUpdInfo.merge = false;
                memcpy((void *) &canMergeUpdInfo[mergeCount++], &canUpdInfo, sizeof(CanUpdDataInfo_s));
                break;
            case DATA_WAIT_AND_MERGE:
                if (mergeCount >= CAN_DATA_MERGE_MAX_NUM - 1)
                {
                    ret        = MCU_QUEUE_FULL;
                    mergeCount = 0;
                    break;
                }
                canUpdInfo.merge = true;
                memcpy((void *) &canMergeUpdInfo[mergeCount++], &canUpdInfo, sizeof(CanUpdDataInfo_s));
                break;
            case DATA_MERGE_LAST:
                canUpdInfo.merge = true;
                memcpy((void *) &canMergeUpdInfo[mergeCount++], &canUpdInfo, sizeof(CanUpdDataInfo_s));
                for (i = 0; i < mergeCount; i++)
                {
                    ret = CanUpdateSendData(&canMergeUpdInfo[i]);
                    if (MCU_OK != ret)
                    {
                        SystemApiLogPrintf(LOG_DEBUG_OUTPUT,
                                           "Can update send data failed, ret=%d, merge:%u\r\n",
                                           ret,
                                           canMergeUpdInfo[i].merge);
                        break;
                    }
                }
                mergeCount = 0;
                break;
            default:
                break;
        }
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,
                           "Can info send pass, canId:%03x, sendCnt:%u data:%x-%x-%x-%x-%x-%x-%x-%x\r\n",
                           passInfo->canId,
                           passInfo->sendCnt,
                           passInfo->u.allData[0],
                           passInfo->u.allData[1],
                           passInfo->u.allData[2],
                           passInfo->u.allData[3],
                           passInfo->u.allData[4],
                           passInfo->u.allData[5],
                           passInfo->u.allData[6],
                           passInfo->u.allData[7]);
        ret = CanUpdateAllData(passInfo->canId,
                               passInfo->canCh,
                               passInfo->sendCnt,
                               passInfo->u.allData,
                               passInfo->canIdIndex);
    }

    if (MCU_OK != ret)
    {
        SystemApiLogPrintf(LOG_WARING_OUTPUT,
                           "Send can pass data failed for id=%x, single=%u, cnt=%u, type=%u, ret=%d\r\n",
                           passInfo->canId,
                           passInfo->single,
                           passInfo->sendCnt,
                           passInfo->u.singleData.sendType,
                           ret);
    }
    resData[0] = CTRL_DATA_RMTCTRL_RES;
    resData[1] = (uint8_t) ret;

    msg.event  = MESSAGE_TX_REMOTE_CONTROL_RESPONSE;
    msg.len    = 2;
    msg.lparam = (uint32_t) &resData;
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: CanCheckSendBusy
函数功能: 检查对应的CAN ID是否有事件报文正在发送
输入参数: canId， canCh
输出参数: 无
函数返回类型值：TRUE - 有事件报文正在发送，FALSE - 无
编写者: zhengyong
编写日期 :2020/07/31
*************************************************/
boolean CanCheckSendBusy(uint32_t canId, uint8_t canCh)
{
    uint32_t i = 0;

    for(i = 0; i < CAN_SEND_ID_MAX_NUM; i++)
    {
        if(g_canSendDataInfo[i].sendData->canId == canId && g_canSendDataInfo[i].canCh == canCh)
        {
            break;
        }
    }

    if(i == CAN_SEND_ID_MAX_NUM)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Not support busy id(%u) and ch(%u)\r\n", canId, canCh);
        return false;
    }

    if(g_canSendDataInfo[i].sendEnable)
    {
        return true;
    }

    return false;
}

/*************************************************
函数名称: CanSendPassIpcCb
函数功能: IPC回调函数，接收ARM发的远控相关命令
输入参数: data - 控制数据，dataLen - 数据长度
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2021/02/04
*************************************************/
void CanSendPassIpcCb(Msg msg)
{
    uint8_t resData[2] = {0};
    uint8_t *data=(uint8_t*)msg.lparam;
    uint32_t dataLen=msg.len;
    CanCheckBusyInfo_s *busyInfo = (CanCheckBusyInfo_s *)data;
    if(NULL == data || 0 == dataLen)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Can pass send data error, datalen=%u\r\n", dataLen);
        return;
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Got send pass cb data[%x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x] len:%d \r\n",\
                                         data[0],data[1],data[2],data[3],data[4],data[5],data[6],data[7], \
                                         data[8],data[9],data[10],data[11],data[12],data[13],data[14],data[15],\
                                         dataLen);

    switch(data[0])
    {
        case CTRL_DATA_RMTCTRL_CMD:
            RemoteControlServiceReqEvent(data, dataLen);
            break;
        case CTRL_DATA_AUTH_REQ:
            break;
        case CTRL_DATA_CHECK_BUSY:
            resData[0] = CTRL_DATA_BUSY_RES;
            resData[1] = CanCheckSendBusy(busyInfo->canId, busyInfo->canCh);

            msg.event=MESSAGE_TX_REMOTE_CONTROL_RESPONSE;
            msg.len=2;
            msg.lparam=(uint32_t)&resData;
            SystemSendMessage(TASK_ID_IPC,msg);
            break;
        default:
            break;
    }
}

/*************************************************
函数名称: Update0x180AA7A3FrameData
函数功能: 更新时间及等信息到0x180AA7A3报文中
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2025/01/20
*************************************************/
void Update0x180AA7A3FrameData(void)
{
    uint8_t               buf[CAN_DATA_LENGTH] = {0x00};
    TboxSystemTimeStruct *pTimeStruct          = NULL;

    pTimeStruct = TboxSystemTimeInfoRead();
    /*当前时间有效*/
    if (TRUE == pTimeStruct->currentTime.timeIsValid)
    {
        // 处理时间数据，使用掩码确保数值在有效范围内
        buf[0] = pTimeStruct->currentTime.year & 0xFF;   // 年: 通常为 0-99，使用 0xFF(255) 限制在一个字节内
        buf[1] = pTimeStruct->currentTime.month & 0x0F;  // 月: 使用 0x0F(15) 限制在 0-15 范围内，有效值 1-12
        buf[2] = pTimeStruct->currentTime.day & 0x1F;    // 日: 使用 0x1F(31) 限制在 0-31 范围内，有效值 1-31
        buf[3] = pTimeStruct->currentTime.hour & 0x1F;   // 时: 使用 0x1F(31) 限制在 0-31 范围内，有效值 0-23
        buf[4] = pTimeStruct->currentTime.minute & 0x3F; // 分: 使用 0x3F(63) 限制在 0-63 范围内，有效值 0-59
        buf[5] = pTimeStruct->currentTime.second & 0x3F; // 秒: 使用 0x3F(63) 限制在 0-63 范围内，有效值 0-59
    }

    // 终端状态位定义 (BIT7 ~ BIT0)
    // BIT7: 1=以太网模块正常；0=异常（初始化模块）
    // BIT6: 1=WiFi模块正常；0=异常（初始化模块）
    // BIT5: 1=CAN2功能正常；0=异常
    // BIT4: 1=CAN1功能正常；0=异常
    // BIT3: 1=3/4G通讯正常；0=异常（能够打开网络）
    // BIT2: 1=GPS正常；0=异常（有无定位、模块问题）
    // BIT1: 1=已登录数据中心；0=未登录数据中心
    // BIT0: 1=存储设备正常；0=异常
    buf[6] = (uint8_t) ((g_commonInfo.ethStatus > 0 ? 1 : 0) << 7 | (g_commonInfo.wifiStatus > 0 ? 1 : 0) << 6
                        | (CanGetChannelStatus(CAN_CHANNEL_2) == CAN_STATUS_NORMAL ? 1 : 0) << 5
                        | (CanGetChannelStatus(CAN_CHANNEL_1) == CAN_STATUS_NORMAL ? 1 : 0) << 4
                        | (g_commonInfo.g4ModuleStatus > 0 ? 1 : 0) << 3
                        | (g_commonInfo.gnssFixStatus == GNSS_MODULE_FIX ? 1 : 0) << 2
                        | (g_commonInfo.tspConnectStatus == TSP_STATUS_LOGINED ? 1 : 0) << 1
                        | (g_commonInfo.emmcStatus > 0 ? 1 : 0) << 0);

    // Life值，0~255，发送成功一次则自增
    buf[7] = g_canSendDataInfo[0].sendData->canData[7];

    memcpy(g_canSendDataInfo[0].sendData->canData, buf, sizeof(buf));
}

/*************************************************
函数名称: Update0x180BA7A3FrameData
函数功能: 更新纬度信息到0x180BA7A3报文中
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: guoyuchen
编写日期: 2025/05/16
*************************************************/
void Update0x180BA7A3FrameData(void)
{
    uint8_t buf[CAN_DATA_LENGTH - 2] = {0x00};
    // 处理纬度数据
    double latitude    = (double) g_commonInfo.gnssLatitude / 1000000.0;
    int    degrees     = (int) latitude;                         // 度
    double minutes     = (latitude - degrees) * 60.0;            // 转换成分
    int    minutesInt  = (int) minutes;                          // 分的整数部分
    int    minutesFrac = (int) ((minutes - minutesInt) * 10000); // 分的小数部分(4位精度)
    // 周期打印
    static TickType_t lastPrintTime  = 0;
    TickType_t        currentTime    = xTaskGetTickCount();
    const TickType_t  PRINT_INTERVAL = pdMS_TO_TICKS(5000); // 5秒间隔

    // 检查定位状态
    if (g_commonInfo.gnssFixStatus)
    {
        buf[0] = 'A'; // 有效定位
    }
    else
    {
        buf[0] = 'V'; // 无效定位
    }

    // BYTE2: 度(0-90)
    buf[1] = (uint8_t) (degrees % 91); // 确保范围在0-90

    // BYTE3: 分的整数部分(0-60)
    buf[2] = (uint8_t) (minutesInt % 61); // 确保范围在0-60

    // BYTE4: 分的小数部分(前2位)
    buf[3] = (uint8_t) ((minutesFrac / 100) % 100);

    // BYTE5: 分的小数部分(后2位)
    buf[4] = (uint8_t) (minutesFrac % 100);

    // BYTE6: 南北纬标识
    if (90 >= degrees)
    {
        buf[5] = 'N'; // 北纬
    }
    else
    {
        buf[5] = 'S'; // 南纬
    }

    if (lastPrintTime == 0 || (currentTime - lastPrintTime) >= PRINT_INTERVAL)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,
                           "Latitude - Raw:%u, Decimal:%f, Position: %d deg %d min %04d sec %c\r\n",
                           g_commonInfo.gnssLatitude,
                           latitude,
                           degrees,
                           minutesInt,
                           minutesFrac,
                           (90 >= degrees) ? 'N' : 'S');

        lastPrintTime = currentTime;
    }
    memcpy(g_canSendDataInfo[1].sendData->canData, buf, sizeof(buf));
}

/*************************************************
函数名称: Update0x180BA7A3FrameData
函数功能: 更新经度信息到0x180BA7A3报文中
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: guoyuchen
编写日期: 2025/05/16
*************************************************/
void Update0x180CA7A3FrameData(void)
{
    uint8_t buf[CAN_DATA_LENGTH - 2] = {0x00};
    // 处理经度数据
    double longitude   = (double) g_commonInfo.gnssLongitude / 1000000.0;
    int    degrees     = (int) longitude;                        // 度
    double minutes     = (longitude - degrees) * 60.0;           // 转换成分
    int    minutesInt  = (int) minutes;                          // 分的整数部分
    int    minutesFrac = (int) ((minutes - minutesInt) * 10000); // 分的小数部分(4位精度)
    // 周期打印
    static TickType_t lastPrintTime  = 0;
    TickType_t        currentTime    = xTaskGetTickCount();
    const TickType_t  PRINT_INTERVAL = pdMS_TO_TICKS(5000); // 5秒间隔

    // BYTE1: 度(0-180)
    buf[0] = (uint8_t) (degrees % 181); // 确保范围在0-180

    // BYTE2: 分的整数部分(0-60)
    buf[1] = (uint8_t) (minutesInt % 61); // 确保范围在0-60

    // BYTE3: 分的小数部分(前2位)
    buf[2] = (uint8_t) ((minutesFrac / 100) % 100);

    // BYTE4: 分的小数部分(后2位)
    buf[3] = (uint8_t) (minutesFrac % 100);

    // BYTE5: 东西经标识
    if (180 >= degrees)
    {
        buf[4] = 'E'; // 东经
    }
    else
    {
        buf[4] = 'W'; // 西经
    }

    // BYTE6: 地面速率 (km/h / 1.852 = 海里/小时)
    buf[5] = (uint8_t) (g_commonInfo.gnssSpeed / 1000);

    if (lastPrintTime == 0 || (currentTime - lastPrintTime) >= PRINT_INTERVAL)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,
                           "Longitude - Raw:%u, Decimal:%f, Position: %d deg %d min %04d sec %c\r\n",
                           g_commonInfo.gnssLongitude,
                           longitude,
                           degrees,
                           minutesInt,
                           minutesFrac,
                           (180 >= degrees) ? 'E' : 'W');

        lastPrintTime = currentTime;
    }

    memcpy(g_canSendDataInfo[2].sendData->canData, buf, sizeof(buf));
}

/*************************************************
函数名称: Update0x180BA7A3FrameData
函数功能: 更新纬度信息到0x180BA7A3报文中
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2025/01/20
*************************************************/
void Update0x1803A709FrameData(void)
{
    uint8_t buf[CAN_DATA_LENGTH] = {0x00};
    CarInfo *carInfo = GetCanInfo();
    uint8_t count = (uint8_t)(g_vinSendNum % 3);

    if(0 == count)
    {
        buf[0] = count + 0x01;
        buf[1] = carInfo->vinCode[0];
        buf[2] = carInfo->vinCode[1];
        buf[3] = carInfo->vinCode[2];
        buf[4] = carInfo->vinCode[3];
        buf[5] = carInfo->vinCode[4];
        buf[6] = carInfo->vinCode[5];
        buf[7] = carInfo->vinCode[6];
    }
    else if(1 == count)
    {
        buf[0] = count + 0x01;
        buf[1] = carInfo->vinCode[7];
        buf[2] = carInfo->vinCode[8];
        buf[3] = carInfo->vinCode[9];
        buf[4] = carInfo->vinCode[10];
        buf[5] = carInfo->vinCode[11];
        buf[6] = carInfo->vinCode[12];
        buf[7] = carInfo->vinCode[13];
    }
    else if(2 == count)
    {
        buf[0] = count + 0x01;
        buf[1] = carInfo->vinCode[14];
        buf[2] = carInfo->vinCode[15];
        buf[3] = carInfo->vinCode[16];
        buf[4] = 0xff;
        buf[5] = 0xff;
        buf[6] = 0xff;
        buf[7] = 0xff;
    }

    memcpy(g_canSendDataInfo[3].sendData->canData, buf, sizeof(buf));
}

// 将指定CAN ID的发送次数修改为0
int CanSetSendCountToZero(const uint32_t canId, const uint8_t canCh)
{
    uint32_t groupIndex = 0;
    uint32_t canIndex   = 0;
    bool     foundMatch = false;

    for (groupIndex = 0; groupIndex < CAN_SEND_ID_MAX_NUM; groupIndex++)
    {
        // 在指定组中遍历所有可能的CAN数据
        for (canIndex = 0; canIndex < g_canSendDataInfo[groupIndex].canCount; canIndex++)
        {
            if (g_canSendDataInfo[groupIndex].sendData[canIndex].canId == canId)
            {
                foundMatch = true;
                SystemApiLogPrintf(LOG_INFO_OUTPUT,
                                   "Found CAN ID 0x%x in auto search mode - Group:%d, Index:%d\r\n",
                                   canId,
                                   groupIndex,
                                   canIndex);
                break;
            }
        }
        if (foundMatch)
        {
            break;
        }
    }
    if (groupIndex >= CAN_SEND_ID_MAX_NUM || !foundMatch
        || g_canSendDataInfo[groupIndex].sendData->canId == CAN_INVALID_CAN_ID)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT,
                           "Not support id(%u) and ch(%u), groupIndex:%u\r\n",
                           canId,
                           canCh,
                           groupIndex);
        return MCU_NOT_SUPPORT;
    }

    g_canSendDataInfo[groupIndex].sendCount = 0;

    return MCU_OK;
}

/*************************************************
函数名称: ControlArmResetEnable
函数功能: 控制ARM重启管脚的使能
输入参数:   无
输出参数: 无
函数返回类型值：无
编写者: PC
编写日期 :2022/07/01
*************************************************/
void ControlArmResetEnable(void)
{
    if(g_armresetdisable==true)
    {
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ARM_RESET], GPIO_OUTPUT_HIGH);
    }
    return;
}

/*************************************************
函数名称: DiagagosticPhysicalRequestAddress
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2024/11/20
*************************************************/
uint32_t DiagagosticPhysicalRequestAddress(void)
{
    return TBOX_PHYSICAL_REQUEST_ID;
}

/*************************************************
函数名称: DiagagosticPhysicalResponseAddress
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2024/11/20
*************************************************/
uint32_t DiagagosticPhysicalResponseAddress(void)
{
    return TBOX_RESPONSE_ID;
}

/*************************************************
函数名称: DiagagosticFunctionReqestAddress
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2024/11/20
*************************************************/
uint32_t DiagagosticFunctionReqestAddress(void)
{
    return TBOX_FUNCTION_REQUEST_ID;
}

#ifdef CAN_ENABLE_OSEKNM
/*************************************************
函数名称: OsekNm_UserHandle
函数功能: OSEK网络管理用户处理调用部分,调用周期10ms
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNm_UserHandleInit(void)
{
    g_OsekNmUserHandle.cbk           = NULL;
    g_OsekNmUserHandle.readyHandle   = FALSE;
    g_OsekNmUserHandle.Started       = FALSE;
    g_OsekNmUserHandle.readyHandle   = HANDLE_REQUEST_INIT;
    g_OsekNmUserHandle.timeRemain    = 0x00;
    g_commonInfo.canNetRequestStatus = WORK_STATUS_INACTIVE;
}

/*************************************************
函数名称: OsekNm_UserHandleNetwork
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNetworkReleae(void)
{
    if (WORK_STATUS_ACTIVE == g_commonInfo.canNetRequestStatus)
    {
        ComM_RequestComMode(0, COMM_SILENT_COMMUNICATION);
        g_commonInfo.canNetRequestStatus = WORK_STATUS_INACTIVE;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "nm net release\r\n");
    }
}

/*************************************************
函数名称: OsekNm_UserHandleNetwork
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNetworkRequest(void)
{
    if (WORK_STATUS_INACTIVE == g_commonInfo.canNetRequestStatus)
    {
        Can_SetControllerMode(CAN_CONTROLLER_C, CAN_T_START);
        GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_LOW);
        ComM_RequestComMode(0, COMM_FULL_COMMUNICATION);
        g_commonInfo.canNetRequestStatus = WORK_STATUS_ACTIVE;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "nm net request\r\n");
    }
}

/*************************************************
函数名称: OsekNetworkCanShutDown
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNetworkCanShutDown(void)
{
    ConfigCanPinStatus(CAN3_PIN_INTERRUPT_ENABLE);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_HIGH);
    Can_SetControllerMode(CAN_CONTROLLER_C, CAN_T_STOP);
}

/*************************************************
函数名称: OsekNetworkCanEnable
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNetworkCanEnable(void)
{
    CanSM_Init();
    Can_SetControllerMode(CAN_CONTROLLER_C, CAN_T_START);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_LOW);
}

/*************************************************
函数名称: OsekNetworkStatusSet
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNetworkStatusSet(uint8_t sta)
{
    g_OsekNmUserHandle.nmStatus = sta;
}

/*************************************************
函数名称: OsekNetworkStatusSet
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
boolean OsekNetworkIsLimpHome(void)
{
    if (g_OsekNmUserHandle.nmStatus == 0x02)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/*************************************************
函数名称: OsekNm_UserHandleEvenSet
函数功能: OSEK网络管理用户设置时间处理类型
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNm_UserHandleEvenSet(OsekNm_HandleType type)
{
    switch (type)
    {
    case HANDLE_REQUEST_INIT: {
        break;
    }
    case HANDLE_REQUEST_ALIVE: {
        g_OsekNmUserHandle.handleEvent = TRUE;
        g_OsekNmUserHandle.Started     = (g_OsekNmUserHandle.Started == TRUE) ? (TRUE) : (FALSE);
        g_OsekNmUserHandle.timeRemain  = OSEK_NM_USER_HANDLE_TIME;
        g_OsekNmUserHandle.readyHandle = type;
        g_OsekNmUserHandle.cbk         = OsekNetworkRequest;
        break;
    }
    case HANDLE_REQUEST_RELEASE: {
        g_OsekNmUserHandle.handleEvent = TRUE;
        g_OsekNmUserHandle.Started     = TRUE;
        g_OsekNmUserHandle.timeRemain  = OSEK_NM_USER_HANDLE_TIME;
        g_OsekNmUserHandle.readyHandle = type;
        g_OsekNmUserHandle.cbk         = OsekNetworkReleae;
        break;
    }
    case HANDLE_REQUEST_CAN_DISABLE: {
        g_OsekNmUserHandle.handleEvent = TRUE;
        g_OsekNmUserHandle.Started     = FALSE;
        g_OsekNmUserHandle.timeRemain  = 0x00;
        g_OsekNmUserHandle.readyHandle = type;
        g_OsekNmUserHandle.cbk         = OsekNetworkCanShutDown;
        break;
    }
    case HANDLE_REQUEST_CAN_ENABLE: {
        if (g_OsekNmUserHandle.readyHandle == HANDLE_REQUEST_CAN_DISABLE)
        {
            g_OsekNmUserHandle.handleEvent = TRUE;
            g_OsekNmUserHandle.Started     = FALSE;
            g_OsekNmUserHandle.timeRemain  = 0x00;
            g_OsekNmUserHandle.readyHandle = type;
            g_OsekNmUserHandle.cbk         = OsekNetworkCanEnable;
        }
        break;
    }
    default: {
        break;
    }
    }
}

/*************************************************
函数名称: OsekNm_UserHandleMainFunction
函数功能: OSEK网络管理用户处理调用部分,调用周期10ms
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/09/01
*************************************************/
void OsekNm_UserHandleMainFunction(void)
{
    uint16_t stopTime = 0x00;

    if (g_OsekNmUserHandle.handleEvent)
    {
        g_OsekNmUserHandle.handleEvent = FALSE;
        switch (g_OsekNmUserHandle.readyHandle)
        {
        case HANDLE_REQUEST_ALIVE: {
            if (g_OsekNmUserHandle.cbk)
            {
                g_OsekNmUserHandle.cbk();
            }
            g_OsekNmUserHandle.readyHandle = HANDLE_REQUEST_INIT;

            if (g_commonInfo.accStatus == WORK_STATUS_INACTIVE)
            {
                OsekNm_UserHandleEvenSet(HANDLE_REQUEST_RELEASE);
            }
            break;
        }
        case HANDLE_REQUEST_CAN_DISABLE: {
            if (g_OsekNmUserHandle.cbk)
            {
                g_OsekNmUserHandle.cbk();
            }
            break;
        }
        case HANDLE_REQUEST_CAN_ENABLE: {
            if (g_OsekNmUserHandle.cbk)
            {
                g_OsekNmUserHandle.cbk();
            }
            break;
        }
        default:
            break;
        }
    }

    if (g_OsekNmUserHandle.Started)
    {
        if (g_OsekNmUserHandle.readyHandle == HANDLE_REQUEST_RELEASE)
        {
            if (OsekNetworkIsLimpHome() == FALSE)
            {
                stopTime = OSEK_NM_USER_HANDLE_TIME_DELAY;
            }
        }

        if (--g_OsekNmUserHandle.timeRemain == stopTime)
        {
            if (g_commonInfo.remoteControlStatus == WORK_STATUS_ACTIVE)
            {
                g_OsekNmUserHandle.timeRemain = OSEK_NM_USER_HANDLE_TIME;
                return;
            }

            if (g_OsekNmUserHandle.cbk)
            {
                g_OsekNmUserHandle.cbk();
            }
            g_OsekNmUserHandle.Started     = FALSE;
            g_OsekNmUserHandle.readyHandle = HANDLE_REQUEST_INIT;
        }

        if (g_OsekNmUserHandle.timeRemain > OSEK_NM_USER_HANDLE_TIME)
        {
            g_OsekNmUserHandle.timeRemain = OSEK_NM_USER_HANDLE_TIME;
        }
    }
}
#else
/*************************************************
函数名称: AutoNm_NetworkReleae
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2025/01/02
*************************************************/
void AutoNm_NetworkReleae(void)
{
    ComM_RequestComMode(NM_NETWORK_CHANNEL_ID, COMM_SILENT_COMMUNICATION);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "nm net release\r\n");
}

/*************************************************
函数名称: AutoNm_NetworkRequest
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2025/01/02
*************************************************/
void AutoNm_NetworkRequest(void)
{
    Can_SetControllerMode(CAN_CONTROLLER_A, CAN_T_START);
    ComM_RequestComMode(NM_NETWORK_CHANNEL_ID, COMM_FULL_COMMUNICATION);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_LOW);
    // SystemApiLogPrintf(LOG_INFO_OUTPUT, "nm net request\r\n");
}

/*************************************************
函数名称: AutoNm_NetworkCanShutDown
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2025/01/02
*************************************************/
void AutoNm_NetworkCanShutDown(void)
{
    Can_SetControllerMode(CAN_CONTROLLER_A, CAN_T_SLEEP);
    CanIf_SetControllerMode(NM_NETWORK_CHANNEL_ID, CANIF_CS_SLEEP);
}

/*************************************************
函数名称: AutoNm_NetworkCanEnable
函数功能:
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: xiazhichuan
编写日期: 2025/01/02
*************************************************/
void AutoNm_NetworkCanEnable(void)
{
    // CanSM_Init();
    ComM_RequestComMode(NM_NETWORK_CHANNEL_ID, COMM_FULL_COMMUNICATION);
    CanIf_SetControllerMode(NM_NETWORK_CHANNEL_ID, CANIF_CS_STARTED);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_LOW);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_LOW);
}
#endif

// 所有CAN控制器退出Standby, GPIO拉低
void CANAllExitStandby(void)
{
    ComM_RequestComMode(CAN_CONTROLLER_A, COMM_FULL_COMMUNICATION);
    ComM_RequestComMode(CAN_CONTROLLER_B, COMM_FULL_COMMUNICATION);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_LOW);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_LOW);
}

void CANAllEnterStandby(void)
{
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_HIGH);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_HIGH);
}

void WakeupAllCANByCANIf(void)
{
    CanIf_SetControllerMode(CAN_CONTROLLER_A, CANIF_CS_STARTED);
    CanIf_SetControllerMode(CAN_CONTROLLER_B, CANIF_CS_STARTED);
    ComM_RequestComMode(CAN_CONTROLLER_A, COMM_FULL_COMMUNICATION);
    ComM_RequestComMode(CAN_CONTROLLER_B, COMM_FULL_COMMUNICATION);
}

/*************************************************
函数名称: CanPeriodNetHandle
函数功能: CAN网络管理周期性函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/30
*************************************************/
void CanPeriodNetHandle(void)
{
    if (CAN_UDS_INIT == g_udsStatus)
    {
        EcuM_MainFunction();
        CanSM_MainFunction();
        ComM_MainFunction(0);
        ComM_MainFunction(1);
        // ComM_MainFunction(2);
        // ComM_MainFunction(3);
        #if defined(CAN_ENABLE_OSEKNM)
        /**
         * @brief OSEK NM模式周期处理
         *
         * 调用OSEK网络管理主函数和用户处理主函数
         */
        OsekNm_MainFunction();
        OsekNm_UserHandleMainFunction();
        #elif defined(CAN_ENABLE_AUTOSAR_NM)
        /**
         * @brief AUTOSAR NM模式周期处理
         *
         * 调用AUTOSAR网络管理主函数和CAN网络管理主函数
         */
        Nm_MainFunction();
        CanNm_MainFunction(NM_NETWORK_CHANNEL_ID);
        #elif defined(CAN_ENABLE_NO_NM)
        /**
         * @brief 无网管模式周期处理
         *
         * 在无网络管理模式下，不进行网络管理相关的周期处理
         */
        /* 无网管模式下的空实现 */
        #else
        /**
         * @brief 默认情况（未定义任何网管模式）
         *
         * 如果没有定义任何网络管理模式，则使用AUTOSAR NM作为默认
         */
        Nm_MainFunction();
        CanNm_MainFunction(NM_NETWORK_CHANNEL_ID);
        #endif /* 网络管理条件编译结束 */
        CanTxConfirmInMain();
    }
}

/*************************************************
函数名称: CanPeriodUdsHandle
函数功能: CAN任务协议周期性执行
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/12/15
*************************************************/
void CanPeriodUdsHandle(void)
{
    if (CAN_UDS_INIT == g_udsStatus)
    {
        CanTp_MainFunction();
        Dcm_MainFunction();
        CanRxDiagFrameHandle();
        CanTp_MainFunction();
        Can_MainFunction_Read();
    }
}

/*************************************************
函数名称: CanPeriodNotifyFunction
函数功能: Can周期性执行任务
输入参数: 参数指针和长度  1ms周期
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void CanPeriodNotifyFunction(Msg msg)
{
    uint8_t status = 0;
    uint8_t mode = 0;
    static uint8_t laststatus = 0;

    // LinPeriodFunction();
    McuScheduleTimeOutReInit(TASK_ID_CAN, 0x00);
    CanPeriodNetHandle();

    if (100 > g_commonInfo.canIdleCount)
    {
        g_commonInfo.canIdleCount = g_commonInfo.canIdleCount + 1;
    }

    /*更新0x180AA7A3报文*/
    Update0x180AA7A3FrameData();
    /*更新0x180BA7A3报文*/
    Update0x180BA7A3FrameData();
    /*更新0x180CA7A3报文*/
    Update0x180CA7A3FrameData();
    /*更新0x1803A709报文*/
    Update0x1803A709FrameData();

    /*发送报文使能，前晨只支持00 03子功能 00:使能RX TX 03:禁止RX TX*/
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    if (status != laststatus)
    {
        laststatus = status;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "nm net status:%d\r\n", status);
    }

    if((g_commonInfo.udsCtrlNormPduStatus & 0X01) == 0x00 && (g_ensureFlag == ACC_ON_ENSURE_FLAG || WORK_STATUS_ACTIVE == g_commonInfo.canStatus))
    {
        CanPeriodSendData();
    }

    PmDetectAccStatus();
    FtmCanPeriodFunction();

    /*MCU控制使能ARM重启*/
    // ControlArmResetEnable();

    /*ECU节点丢失检测*/
    DiagEcuCodeLostSelfCheck();

}

/*************************************************
函数名称: CanEventFunction
函数功能:
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void CanEventFunction(Msg msg)
{
    uint8_t index = 0;

    for (index = 0; index < (sizeof(g_canEventFunctionMap) / sizeof(g_canEventFunctionMap[0])); index++)
    {
        if (g_canEventFunctionMap[index].event == msg.event)
        {
            if (NULL != g_canEventFunctionMap[index].TaskFunctionHook)
            {
                g_canEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

/*************************************************
函数名称: CanEventFunction
函数功能:
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
int CanProtocolSdkTpDataSend(uint8_t channel, uint32_t canId, uint64_t length, uint8_t *buf)
{
    PduInfoType pduInfo;

    if(length > CAN_TP_TX_DATA_MAX_LENGTH || channel > CAN_CHANNEL_4)
    {
        return 1;
    }

    pduInfo.SduDataPtr = NULL;
//    g_canTpInfo.txId   = canId;
    g_canTpInfo.reqcanId   = canId;
    g_canTpInfo.rxId   = 0;
    g_canTpInfo.idType = ECU_ID_PHY;
    g_canTpInfo.txLen  = length;

    memset(g_canTpInfo.txData, 0x00, CAN_TP_TX_DATA_MAX_LENGTH);
    memcpy(g_canTpInfo.txData, buf, length);
    pduInfo.SduLength = g_canTpInfo.txLen;

    if(g_canTpInfo.localDiagTBox == FALSE)
    {
        g_canTpInfo.remoteDiagFlag = TRUE;
        uint16_t  Offset = (Dcm_DslCfg.pDcmChannelCfg)[0].offset;
        uint16_t  bufLen = (Dcm_DslCfg.pDcmChannelCfg)[0].Dcm_DslBufferSize;
        if(DiagagosticPhysicalRequestAddress() == g_canTpInfo.reqcanId)
        {
            memcpy(&Dcm_Channel[Offset],buf,length);
            Dcm_MsgCtrl[0].MsgContext.ReqDataLen = length;
            Dcm_MsgCtrl[0].MsgContext.pReqData   = &Dcm_Channel[Offset];
            Dcm_RxIndication(0, 0);
            memset(&Dcm_Channel[Offset],0x00,bufLen);
            return 0;
        }
        else if(DiagagosticFunctionReqestAddress() == g_canTpInfo.reqcanId)
        {
            memcpy(&Dcm_Channel[Offset],buf,length);
            Dcm_MsgCtrl[0].MsgContext.ReqDataLen = length;
            Dcm_MsgCtrl[0].MsgContext.pReqData   = &Dcm_Channel[Offset];
            Dcm_RxIndication(1, 0);
            memset(&Dcm_Channel[Offset],0x00,bufLen);
        }
        PduR_DcmTransmit(CAN_TP_TX_DIAG_OTHER, &pduInfo);
    }

    return 0;
}