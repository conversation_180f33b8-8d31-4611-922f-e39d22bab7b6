/*
      PmApi.c
描述：此文件主要是Pm任务实现的MCU睡眠和唤醒功能
作者：廖勇刚
时间：2016.7.5
*/
#include "PmApi.h"
#include "BatApi.h"
#include "CanApi.h"
#include "CanFtm.h"
#include "HrApi.h"
#include "IpcApi.h"
#include "LedApi.h"
#include "LogApi.h"
#include "event.h"
#include "pwm.h"
#include "r_can.h"
#ifdef CAN_ENABLE_OSEKNM
#include "OsekNm.h"
#else
#include "CanNm.h"
#endif
#include "BLELin.h"
#include "BtApi.h"
#include "CanSM_EcuM.h"
#include "DiagApi.h"
#include "Nm.h"
#include "NmStack_Types.h"
#include "SystemApi.h"
#include "pins_driver.h"
#include "rtc.h"
#include "wku_config.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "BtTask.h"
#include "NvApi.h"
#include "RemoteControlTask.h"
#include "power_manager.h"

/************************宏定义***************************/
#define ON                            1
#define OFF                           0

#define WAKUP_INST    0

/************************外部全局变量****************************/
extern CommonInfo               g_commonInfo;
extern GpioInfo                 g_gpioPowerOnInfoList[];
extern DtcInfo                  g_dtcInfo;
extern uint32_t                 g_osCurrentTickTime;
extern ModulePowerStatus        g_powerModuleStatus;
extern FtmWakeupType            g_ftmWakeup;
extern FtmTestStage             g_FtmTestStage;
extern uint32_t                 g_AliveOstick;
extern uint32_t                 g_AccOnOstick;
extern uint8_t                  MainPowerExit_SleepWait;
extern LinInfo                  g_linInfo;
extern GpioControlInfoStruct    g_GpioControlInfoStruct[GPIO_CONTROL_ENUM_MAX];
extern BackupRamInfo            g_backupRamInfo;
extern NvItemInfo               g_nvInfoMap[NV_MAX_NUMBER];
extern CanUdsStatus             g_udsStatus;

/************************外部函数接口***************************/

extern status_t POWER_SYS_GetLastMode(uint8_t * powerModeIndexPtr);

/************************内部全局变量****************************/
PmInfo  g_pmInfo;
volatile uint8_t g_lptmrIsrCnt = 0;
uint8_t g_ensureFlag = ACC_OFF_ENSURE_FLAG;
uint8_t g_canNetStatus = 0x00;

/************************内部函数接口***************************/
static void PmResetArmFunction(Msg msg);
static void PmBplusStatusFunction(Msg msg);
static void PmArmStatusFunction(Msg msg);
static void PmPowerOffFunction(Msg msg);
static void PmVoltageDiagNotifyFunction(Msg msg);
static void PmCanNetControlFunction(uint8_t armStatus, uint8_t bit);
static void SetPmEnterSleepMode(void);

EventInfo g_pmEventFunctionMap[] = 
{
//    {EVENT_ID_PM_PERIOD_NOTIFY,  PmPeriodNotifyFunction},
    {EVENT_ID_HEARTBEAT_TIMEOUT, PmResetArmFunction},
    {EVENT_ID_BPLUS_STATUS,      PmBplusStatusFunction},
    {EVENT_ID_ARM_STATUS,        PmArmStatusFunction},
    {EVENT_ID_POWER_OFF,         PmPowerOffFunction},
    {EVENT_ID_PM_BATTERY_NOTIFY, PmVoltageDiagNotifyFunction},
};


/*************************************************
函数名称: PmRtcOrSensorWakeupSource
函数功能: 通过读取RTC中断寄存器判断是否是RTC唤醒或SENSOR唤醒
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/10/27
*************************************************/
void PmRtcOrSensorWakeupSource(void)
{
    uint8_t tempBuf = 0x00;
    NvErrorCode errorCode = NV_NO_ERROR;
    BackupRamInfo *backupRamInfo = NULL;

    backupRamInfo = (BackupRamInfo *)GetBackupRamInfo();
    errorCode = NvApiReadData(NV_ID_BACKUP_RAM, (uint32_t *)backupRamInfo, g_nvInfoMap[NV_ID_BACKUP_RAM].nvValidLen);

    if(0x40 == (tempBuf&0x40))
    {
        g_backupRamInfo.mcuWakeupSource = TBOX_WAKEUP_RTC;
        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu is rtc wakeup\r\n");
        #endif
    }
    else
    {
        g_backupRamInfo.mcuWakeupSource = TBOX_WAKEUP_SENSOR;
        #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu is sensor wakeup\r\n");
        #endif
    }

    errorCode = NvApiWriteData(NV_ID_BACKUP_RAM, (uint8_t *)g_nvInfoMap[NV_ID_BACKUP_RAM].pNvData, g_nvInfoMap[NV_ID_BACKUP_RAM].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Wakeup source Rtc Or Sensor update NV failed:%d\r\n", errorCode);
    }
}

/*************************************************
函数名称: PmPowerSetRtcAndStartTick
函数功能: MCU开机配置RTC相关寄存器和开启系统节拍
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/05/22
*************************************************/
void PmPowerSetRtcAndStartTick(void)
{
    RtcTime rtcTime;

    RtcGetDateTime(&rtcTime);

    TboxSystemTimeSet(rtcTime.year, rtcTime.month, rtcTime.weekday, rtcTime.day, rtcTime.hour, rtcTime.minute, rtcTime.second, RTC_TYPE_TIME);
    TboxSystemRtcWakeupSet(0, 0, 0, 0, RTC_WAKEUP_NONE);
}

void lpTMR0_IRQHandler(void)
{
    PRINTF("LpTMR ISR enter!\n");
    g_lptmrIsrCnt++;
    PRINTF("LpTMR ISR exit!\n");
}

/*************************************************
函数名称: PmPowerOnPrepare
函数功能: MCU开机准备LED点亮和给ARM侧供电
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/03/06
*************************************************/
void PmPowerOnPrepare(void)
{
    SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "Power On Prepare was called to power ON arm and lighting LEDs.\r\n");

    //to control the V4.1 voltage output to high
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_4V1_PWR_SW], GPIO_OUTPUT_HIGH);
    //to control the VCC_GNSS_Backup voltage output to high
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_POWER_GNSSBACKUP], GPIO_OUTPUT_HIGH);
    //to delay 10 ms to get stable high level output
    vTaskDelay(10);

    //to output the VCC_4V1_Boost
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_EN_VCC8V], GPIO_OUTPUT_HIGH);
    //to output the SYS_3V3_SW
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_3V3_PWR_SW], GPIO_OUTPUT_HIGH);
    //to output the WIFI_3V3
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_POWER_WIFI], GPIO_OUTPUT_HIGH);
    //to delay 10 ms to get stable high level output before rest the GNSS
    vTaskDelay(10);

    //to output high level for the GNSS RESET 
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_RST_GNSS], GPIO_OUTPUT_HIGH);

    //to output high level for the LTE ANT SW to select internal ANT
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_MAN_ANT_SW], GPIO_OUTPUT_HIGH);
    //to output low level for the GNSS ANT SW to select outside ANT
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_GNSS_ANT_SW], GPIO_OUTPUT_LOW);

    g_backupRamInfo.lightSleepCount = 0;

    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BLE_WAKEUP_CTL], GPIO_OUTPUT_LOW);

    g_powerModuleStatus = MODULE_POWER_ON_GPRS_PWR_EN_LOW;

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Task start power on arm\r\n");

    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_ECALL_BTN_BL], GPIO_OUTPUT_HIGH);
    g_pmInfo.workStatus = PM_STATUS_NORAML;

    // 远控唤醒处理
    RemoteControlWakeupHandle();
}

/*************************************************
函数名称: PmWakeupFromLightSleep
函数功能: MCU轻度休眠唤醒后的上电逻辑
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/02
*************************************************/
void PmWakeupFromLightSleep(void)
{
    //need to sync time first
    PmWakeupTimeSync();

    for(uint8_t i = 0; i < MCU_MAX_NUM; i++)
    {
        McuSetPinModule(i, g_gpioPowerOnInfoList[i].gpioPowerConfig);
        if(g_gpioPowerOnInfoList[i].gpioPowerLevel  != GPIO_DEFAULT_LEVEL)
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[i], g_gpioPowerOnInfoList[i].gpioPowerLevel);
        }
    }


    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup LightSleep wakeup src:%d\r\n", g_backupRamInfo.mcuWakeupSource);

    switch(g_backupRamInfo.mcuWakeupSource)
    {
        case TBOX_WAKEUP_BPLUS:
        case TBOX_WAKEUP_ACC:
        case TBOX_WAKEUP_LPS:
        case TBOX_WAKEUP_RTC:
        case TBOX_WAKEUP_CAN1:
        case TBOX_WAKEUP_CAN2:
        case TBOX_WAKEUP_LIN:
        case TBOX_WAKEUP_CAN3:
        case TBOX_WAKEUP_CAN4:
        case TBOX_WAKEUP_BLE:
        case TBOX_WAKEUP_FCHG:
        case TBOX_WAKEUP_SCHG:
            {
                g_powerModuleStatus = MODULE_POWER_WAKEUP_ARM_HIGH;
            }
            break;
        case TBOX_WAKEUP_MODULE:
            {

                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup LightSleep detected MCU_WAKEUP_MODULE.\r\n");
            }
            break;
        case TBOX_WAKEUP_WATCHDOE:
        case TBOX_WAKEUP_EXTERN_RESET:
            {
                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup LightSleep start reset arm\r\n");
                #endif
            }
            break;
        case TBOX_WAKEUP_SOFT_RESET:
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup LightSleep detected soft reset.\r\n");
            }
            break;
        case TBOX_WAKEUP_ECALL:
            {
                //ECALL 唤醒
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup LightSleep detected MCU_WAKEUP_ECALL.\r\n");
            }
            break;
        default:
        {
            g_powerModuleStatus = MODULE_POWER_WAKEUP_ARM_HIGH;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Wakeup LightSleep mcu unknown wakeup\r\n");
        }
            break;
    }

    g_pmInfo.workStatus = PM_STATUS_NORAML;
}
/*************************************************
函数名称: PmWakeupFromDeepSleep
函数功能: MCU深度休眠唤醒后的上电逻辑
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/02
*************************************************/
void PmWakeupFromDeepSleep(void)
{
    SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "Pm Wakeup from deep sleep mode.\r\n");

    for(uint8_t i = 0; i < MCU_MAX_NUM; i++)
    {
        McuSetPinModule(i, g_gpioPowerOnInfoList[i].gpioPowerConfig);
        if(g_gpioPowerOnInfoList[i].gpioPowerLevel  != GPIO_DEFAULT_LEVEL)
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[i], g_gpioPowerOnInfoList[i].gpioPowerLevel);
        }
    }
}

/*************************************************
函数名称: PmGpioControlInDeepSleepMode
函数功能: MCU进入深度休眠时的GPIO控制逻辑
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/07
*************************************************/
void PmGpioControlInDeepSleepMode(void)
{
    SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "Pm InDeepSleep was called.\r\n");

    for(uint8_t i = 0; i < MCU_MAX_NUM; i++)
    {
        if(g_gpioPowerOnInfoList[i].gpioDeepSleepLevel  != GPIO_DEFAULT_LEVEL)
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[i], g_gpioPowerOnInfoList[i].gpioDeepSleepLevel);
        }
        if(g_gpioPowerOnInfoList[i].gpioDeepSleepConfig == PCTRL_PIN_DISABLED){
            McuSetPinModule(i, g_gpioPowerOnInfoList[i].gpioDeepSleepConfig);
        }
    }
}

/*************************************************
函数名称: PmGpioControlInLightSleepMode
函数功能: MCU进入浅度休眠时的GPIO控制逻辑
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/07
*************************************************/
void PmGpioControlInLightSleepMode(void)
{
    SystemApiLogPrintf(LOG_DEBUG_OUTPUT, "Pm Gpio In Light Sleep Mode.\r\n");

    for(uint8_t i = 0; i < MCU_MAX_NUM; i++)
    {
        if(g_gpioPowerOnInfoList[i].gpioSleepLevel  != GPIO_DEFAULT_LEVEL)
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[i], g_gpioPowerOnInfoList[i].gpioSleepLevel);
        }
        if(g_gpioPowerOnInfoList[i].gpioSleepConfig == PCTRL_PIN_DISABLED){
            McuSetPinModule(i, g_gpioPowerOnInfoList[i].gpioSleepConfig);
        }
    }
}

/*
 *  @brief 配置CAN RX引脚
 *  @param state 1 打开中断 0 取消中断
 *  @note 休眠前打开，确保休眠时能被CAN唤醒；中断触发时关闭，避免一直触发
 */
void ConfigCanPinStatus(CanPinStatus state)
{
    switch (state)
    {
        case CAN1_PIN_INTERRUPT_ENABLE:
        {
            FLEXCAN_DRV_EnableSelfWakeUp(1);
            break;
        }
        case CAN2_PIN_INTERRUPT_ENABLE:
        {
            FLEXCAN_DRV_EnableSelfWakeUp(2);
            break;
        }
        case CAN3_PIN_INTERRUPT_ENABLE:
        {
            FLEXCAN_DRV_EnableSelfWakeUp(3);
            break;
        }
        case CAN5_PIN_INTERRUPT_ENABLE:
        {
            FLEXCAN_DRV_EnableSelfWakeUp(5);
            break;
        }
        case CAN1_PIN_INTERRUPT_DISENABLE:
        {
            FLEXCAN_DRV_DisableSelfWakeUp(1);
            break;
        }
        case CAN2_PIN_INTERRUPT_DISENABLE:
        {
            FLEXCAN_DRV_DisableSelfWakeUp(2);
            break;
        }
        case CAN3_PIN_INTERRUPT_DISENABLE:
        {
            FLEXCAN_DRV_DisableSelfWakeUp(3);
            break;
        }
        case CAN5_PIN_INTERRUPT_DISENABLE:
        {
            FLEXCAN_DRV_DisableSelfWakeUp(5);
            break;
        }
        case ALLCAN_PIN_INTERRUPT_ENABLE:
        {
            FLEXCAN_DRV_EnableSelfWakeUp(1);
            FLEXCAN_DRV_EnableSelfWakeUp(2);
            break;
        }
        case ALLCAN_PIN_INTERRUPT_DISENABLE:
        {
            FLEXCAN_DRV_DisableSelfWakeUp(1);
            FLEXCAN_DRV_DisableSelfWakeUp(2);
            break;
        }
        default:
        {
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "CanPinStatus Type Set error, type=%d \r\n", state);
            break;
        }
        return;
    }
}

/*************************************************
函数名称: PmCanEnterStandby
函数功能: MCU Can进入Standby模式
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/29
*************************************************/
static void PmCanEnterStandby(void)
{
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_HIGH);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_HIGH);
    // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_RLIN21_SLP_N], GPIO_OUTPUT_LOW);
    // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN3_STB], GPIO_OUTPUT_HIGH);
    // GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN5_STB], GPIO_OUTPUT_HIGH);

    //CAN RX需要设置为GPIO模式，才能触发中断
    ConfigCanPinStatus(ALLCAN_PIN_INTERRUPT_ENABLE);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Set can standby\r\n");
}

/*************************************************
函数名称: PmSleepGpioConfig
函数功能: 电源管理睡眠GPIO电平配置
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/11/07
*************************************************/
static void PmSleepGpioConfig(void)
{
    if(MCU_MODE_LIGHT_SLEEP == g_backupRamInfo.mcuModeStatus)
    {
        //休眠前设置的中断可能被触发，进入standby前再设置一次中断
        McuSetPinIntConfig(MCU_GPIO_ACC, PCTRL_INT_RISING_EDGE);
        McuSetPinIntConfig(MCU_GPIO_SCHG_STA_INPUT, PCTRL_INT_RISING_EDGE);
        McuSetPinIntConfig(MCU_GPIO_FCHG_STA_INPUT, PCTRL_INT_RISING_EDGE);
        McuSetPinIntConfig(MCU_GPIO_M_WAKEUP_OUT, PCTRL_INT_FALLING_EDGE);
        McuSetPinIntConfig(MCU_GPIO_SOS_KEY_INPUT, PCTRL_INT_FALLING_EDGE); // Ecall按键低电平触发
        BtModuleSleep();
        PmGpioControlInLightSleepMode();
    }
    else if(MCU_MODE_DEEP_SLEEP == g_backupRamInfo.mcuModeStatus)
    {
        // 电源休眠后的唤醒管理配置
        WKU_DRV_InitReset(WAKUP_INST, &wkup_resetConfig0);
        WKU_DRV_InitPinWakeup(WAKUP_INST, WKP_PIN_CH_NUM0, wkup_pinsConfig0);
        PmGpioControlInDeepSleepMode();
    }

    for(int index = GPIO_CONTROL_CAN_RED_LED; index < GPIO_CONTROL_ENUM_MAX; index++)
    {
        //重置LED状态
        GpioLedControlUpdateStatusFunction(index,GPIO_STATUS_INVALID, 0, 0);
    }
}

/*************************************************
函数名称: PmSleepModeConfig
函数功能: MCU睡眠模式配置
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/02
*************************************************/
void PmSleepModeConfig(void)
{
    PmCanEnterStandby();
    PmSleepGpioConfig();
    RemoteControlEnterSleepHandle();
    g_osCurrentTickTime = 0x00;
}

/*************************************************
函数名称: PmBplusStatusFunction
函数功能: MCU b+ 状态事件处理
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/18
*************************************************/
static void PmBplusStatusFunction(Msg msg)
{
    uint8_t *statusTemp = NULL;

    if(NULL == (void *)msg.lparam)
    {
        return;
    }

    statusTemp = (uint8_t *)msg.lparam;
    g_commonInfo.bplusVoltStatus = (BplusVoltStatus)*statusTemp;

    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu b+ status is %d\r\n", g_commonInfo.bplusVoltStatus); 
    #endif

    //B+电压异常
    if(BPLUS_VOLT_STATUS_ABNORMAL == g_commonInfo.bplusVoltStatus)
    {
        if(PM_STATUS_BACKUP_NORAML != g_pmInfo.workStatus)
        {
            g_pmInfo.workStatus = PM_STATUS_BACKUP_NORAML;
            g_backupRamInfo.lightSleepCount = 0;

            if(HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
            {
                msg.event = MESSAGE_TX_PM_REQUEST;
                msg.len   = 1;
                msg.lparam = (uint32_t)&g_pmInfo.workStatus;
                SystemSendMessage(TASK_ID_IPC, msg);
            }

            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter backup bat normal\r\n");
            #endif
        }
    }

    //B+电压正常
    else
    {
        if(PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            g_pmInfo.workStatus  = PM_STATUS_NORAML;

            if(HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
            {
                msg.event = MESSAGE_TX_PM_REQUEST;
                msg.len   = 1;
                msg.lparam = (uint32_t)&g_pmInfo.workStatus;
                SystemSendMessage(TASK_ID_IPC, msg);
            }

            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter b+ normal\r\n"); 
            #endif
        }
    }     
}


/*************************************************
函数名称: PmBplusNotSendCan
函数功能: MCU B+低电不发CAN数据
输入参数: 无
输出参数: uint8_t
函数返回类型值：0 ----正常
                1-----低电
编写者: liaoyonggang
编写日期 :2018/12/10
*************************************************/
uint8_t PmBplusNotSendCan(void)
{
    uint16_t bPplusValue = ReadBpPlusValue();
    if(NOT_IN == g_commonInfo.intoInterrupt)
    {
        return 0;
    }

    if(BPLUS_VOL_LOW_CAN_OFF < bPplusValue)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

/*************************************************
函数名称: PmArmStatusFunction
函数功能: MCU ARM状态处理
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/26
*************************************************/
static void PmArmStatusFunction(Msg msg)
{
    uint8_t *tempBuf = NULL;
    uint8_t armIndex = 0;
    uint8_t armStatus = 0;
    uint8_t bit = 0;
    uint8_t buf[8] = {0x00};
    FtmInfo  *pFtmInfo = NULL;

    pFtmInfo = FtmInitRead();

    if(NULL == (void *)msg.lparam)
    {
        return;
    }

    tempBuf = (uint8_t *)msg.lparam;
    armIndex = tempBuf[0];
    armStatus = tempBuf[1];
    bit = tempBuf[2];

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu armStatus index is %d, status is %d\r\n", armIndex, armStatus);

    switch(armIndex)
    {
        case TBOX_TSP_STATUS:
        {
            /*When T-Box client enter sleep, we need hard reset ble module one time*/
            if(((WorkStatus)armStatus == WORK_STATUS_INACTIVE) && (g_commonInfo.tspStatus != (WorkStatus)armStatus))
            {
                if(g_commonInfo.mainPowerLowSleep)
                {
                    BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_HARD_RESET_CLOSE_ADV);
                }
                {
                    BtModuleHandShakeSend(HANDSHAKE_FAIL, BT_SOFT_RESET_OPEN_ADV);
                }
            }
            g_commonInfo.tspStatus = (WorkStatus)armStatus;
            if(WORK_STATUS_INACTIVE == g_commonInfo.tspStatus)
            {
                g_commonInfo.diagStatus = WORK_STATUS_INACTIVE;
                g_commonInfo.remoteControlStatus = WORK_STATUS_INACTIVE;
            }
            break;
        }
        case TBOX_CAN_NET_STATUS:
        {
            PmCanNetControlFunction(armStatus, bit);
            break;
        }
        case TBOX_DIAG_STATUS:
        {
            #ifndef CAN_ENABLE_OSEKNM
            if(WORK_STATUS_INACTIVE == armStatus)
            {
                if(WORK_STATUS_INACTIVE == g_commonInfo.accStatus)
                {
                    PmNofityArmSleepFunction();
                }
            }
            #endif
            g_commonInfo.diagStatus = (WorkStatus)armStatus;
            break;
        }
        case ARM_TYPE_CAN_CONTROL:
        {
            if(armStatus)
            {
                g_commonInfo.RemoteUpgradeFlag = Remote_UpGrade_Start;
            }
            else
            {
                g_commonInfo.RemoteUpgradeFlag = Remote_UpGrade_End;
            }
            break;
        }
        case ARM_TYPE_BLE_KEY_AUTH_STATUS:
        {
            if(g_commonInfo.bleKeyAuthStatus != armStatus)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "bleKeyAuth:%s,%d\r\n", ((armStatus==1)? "Success":"Fail"), armStatus);
            }
            g_commonInfo.bleKeyAuthStatus = armStatus;
            break;
        }
        case ARM_TYPE_UPGRADE_STATUS:
        {
            g_commonInfo.upgradeStatus = (WorkStatus)armStatus;
            break;
        }
        case TBOX_ARM_TYPE_FTM:
        {
            /*带电池测试时会出现此异常，ARM唤醒后通知底板*/
            if(pFtmInfo->ftmMode == FTM_MODE_ENTER)
            {
                return;
            }
            if(0x00 == armStatus)
            {
                #ifdef CAN_ENABLE_OSEKNM
                OsekNm_UserHandleEvenSet(HANDLE_REQUEST_ALIVE);
                #elif defined (CAN_ENABLE_AUTOSAR_NM)
                AutoNm_NetworkRequest();
                #endif
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm wake,ftm\r\n");
            }
            else
            {
                g_commonInfo.RemoteUpgradeFlag    = Remote_UpGrade_End;
                MCUCanSendFtmData(CAN_CHANNEL_2, 0x644, 8, buf);
            }
            g_commonInfo.tspStatus = WORK_STATUS_ACTIVE;
            break;
        }
        case ARM_TYPE_WAKEUP_SRC_SYNC:
        {
            if((armStatus == TBOX_WAKEUP_SMS) || (armStatus == TBOX_WAKEUP_PHONECALL) || (armStatus == TBOX_WAKEUP_ECALL))
            {
                g_backupRamInfo.mcuWakeupSource = armStatus;
            }
            IpcTxWakeupStatus();
            break;
        }
        case ARM_TYPE_MCU_LOG_PRINTF:
        {
            break;
        }
        case TBOX_ECALL_STATUS:
        case TBOX_SVT_STATUS:
        {
            // send message to battery task
            SystemSendMessage(TASK_ID_BAT, msg);
            break;
        }
        default:
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "Arm status index=%d status=%d in msg 0x804. \r\n", armIndex, armStatus);
            break;
        }
    }
}


/*************************************************
函数名称: PmVehicleIgStatusNotify
函数功能: 车辆点火状态通知
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/02/13
*************************************************/
void PmVehicleIgStatusNotify(WorkStatus status)
{
    uint8_t tempBuffer[2];
    Msg msg;

    tempBuffer[0] = MCU_FAULT_IG_STATUS;
    tempBuffer[1] = status;

    msg.event = MESSAGE_TX_MCU_FAULT_INFO;
    msg.len   = 2;
    msg.lparam = (uint32_t)&tempBuffer[0];
    SystemSendMessage(TASK_ID_IPC, msg);
}

/*************************************************
函数名称: PmDetectChgStatus
函数功能: MCU检测充电枪状态
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/02/26
*************************************************/
void PmDetectChgStatus(void)
{
    GpioLevel levelFchg = GPIO_OUTPUT_LOW;
    GpioLevel levelSchg = GPIO_OUTPUT_LOW;

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_FCHG_STA_INPUT], &levelFchg);
    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_SCHG_STA_INPUT], &levelSchg);

    g_commonInfo.fchgStatus = levelFchg == GPIO_OUTPUT_HIGH ? WORK_STATUS_ACTIVE : WORK_STATUS_INACTIVE;         // 快充电抢状态
    g_commonInfo.schgStatus = levelSchg == GPIO_OUTPUT_HIGH ? WORK_STATUS_ACTIVE : WORK_STATUS_INACTIVE;         // 慢充电抢状态
}

/*
 *此接口的作用是强制将ACC软件部分的状态设置为WORK_STATUS_INACTIVE
 *目的是服务器强制让tbox进入到休眠模式，无论can与acc是否存在，那么利用acc off的逻辑完成进入休眠的流程
 */
void ForceSetLevelLow(GpioLevel *level)
{
    //获取用户设置状态
    uint8_t forceStatus = CheckSleepRequestFromServer();
    if(forceStatus == 1)
    {
        *level = GPIO_OUTPUT_LOW;
    }
    else if(forceStatus == 2)
    {
        *level = GPIO_OUTPUT_LOW;
        SetPmEnterSleepMode();
        
    }
}

static void SetPmEnterSleepMode(void)
{
    g_backupRamInfo.lightSleepCount = LIGHT_SLEEP_MAX_COUNT +1;
}

/*************************************************
函数名称: PmDetectAccStatus
函数功能: MCU检测ACC状态
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2019/06/05
*************************************************/
void PmDetectAccStatus(void)
{
    static uint8_t count1 = 0x00;
    static uint8_t count2 = 0x00;
    GpioLevel level = GPIO_OUTPUT_LOW;
    FtmInfo  *pFtmInfo = FtmInitRead();

    GpioReadInputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ACC], &level);
    if(pFtmInfo->ftmMode == FTM_MODE_ENTER)
    {
        level = GPIO_OUTPUT_HIGH;
    }

    ForceSetLevelLow(&level);

    if(GPIO_OUTPUT_HIGH == level)
    {
        count2 = 0x00;
        count1++;
        g_commonInfo.canSendOutStat = CAN_SEND_OUT_ENABLE;
        g_commonInfo.accStatus = WORK_STATUS_ACTIVE;
        if ((count1 % ACC_ON_COUNTER_THRESHOLD) == 0)
        {
            if(ACC_OFF_ENSURE_FLAG == g_ensureFlag && CAN_UDS_INIT == g_udsStatus)
            {
                g_ensureFlag = ACC_ON_ENSURE_FLAG;
                g_AccOnOstick = xTaskGetTickCount();
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "acc on,tick %u\r\n",g_AccOnOstick);
                PmVehicleIgStatusNotify(g_commonInfo.accStatus);
#ifdef CAN_ENABLE_OSEKNM
                OsekNm_UserHandleEvenSet(HANDLE_REQUEST_ALIVE);
#elif defined(CAN_ENABLE_AUTOSAR_NM)
                AutoNm_NetworkRequest();
                CanNm_NetworkRequest(NM_NETWORK_CHANNEL_ID);
#elif defined(CAN_ENABLE_NO_NM)
                CANAllExitStandby(); // 防止ACC ON唤醒后未退出standby导致无法收发报文
#endif
            }
        }
    }
    else
    {
        count1 = 0x00;
        count2++;
        if((count2 % ACC_OFF_COUNTER_THRESHOLD) == 0)
        {
            if(ACC_ON_ENSURE_FLAG == g_ensureFlag)
            {
                g_ensureFlag = ACC_OFF_ENSURE_FLAG;
                g_commonInfo.accStatus = WORK_STATUS_INACTIVE;
                g_commonInfo.canSendOutStat = CAN_SEND_OUT_DISABLE;
                PmVehicleIgStatusNotify(g_commonInfo.accStatus);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "acc off,tick %u\r\n",g_osCurrentTickTime);
                if(g_canNetStatus == 0x00)
                {
                    #ifdef CAN_ENABLE_OSEKNM
                    OsekNm_UserHandleEvenSet(HANDLE_REQUEST_RELEASE);
                    #elif defined (CAN_ENABLE_AUTOSAR_NM)
                    CanNm_NetworkRelease(NM_NETWORK_CHANNEL_ID);
                    #endif
                }
            }
        }
    }
}


/*************************************************
函数名称: PmPowerOffFunction
函数功能: MCU 关机命令处理
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/18
*************************************************/
static void PmPowerOffFunction(Msg msg)
{
    g_pmInfo.workStatus = PM_STATUS_POWER_OFF;
    g_pmInfo.entersleepCount = ENTER_SLEEP_COUNT;

    msg.event = MESSAGE_TX_PM_REQUEST;
    msg.len   = 1;
    msg.lparam = (uint32_t)&g_pmInfo.workStatus;
    SystemSendMessage(TASK_ID_IPC, msg);

    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter power off\r\n");
    #endif
}

/*************************************************
函数名称: PmVoltageDiagNotifyFunction
函数功能: 电源管理模块，接收diag模块发送的电压通知事件
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/11
*************************************************/
static void PmVoltageDiagNotifyFunction(Msg msg)
{
    uint8_t  tempBuffer[4] = {0x00};
    uint8_t *para = NULL;
    DiagSendPmStatusEnum  diagSendPmStatus;
    PmStatus pmStatus;
    uint16_t bPplusValue = ReadBpPlusValue();

    para = (uint8_t *)msg.lparam;
    diagSendPmStatus =(DiagSendPmStatusEnum)para[0];

    if(DIAG_SNED_PM_ENTER_SLEEP == diagSendPmStatus)
    {
        PmVehicleIgStatusNotify(WORK_STATUS_INACTIVE);

        g_commonInfo.mainPowerLowSleep = 1;
        pmStatus = PM_STATUS_ENTER_BPLUS_LOW_SLEEP;
        g_pmInfo.workStatus = PM_STATUS_BACKUP_SLEEP_READY;
        g_pmInfo.entersleepCount = PM_FOUR_SECONDS_TIME;

        tempBuffer[0] = MCU_FAULT_TYPE_BPLUS_LOW;
        tempBuffer[1] = pmStatus;
        tempBuffer[2] = MCU_FAULT_ACTIVE;
        msg.event = MESSAGE_TX_MCU_FAULT_INFO;
        msg.len   = 3;
        msg.lparam = (uint32_t)&tempBuffer[0];
        SystemSendMessage(TASK_ID_IPC, msg);  
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "B+:%d,notify arm to sleep\r\n", bPplusValue);
    }
    else if(DIAG_SNED_PM_ENTER_POWER_OFF == diagSendPmStatus)
    {
        msg.event  = EVENT_ID_POWER_OFF;
        msg.len    = 0;
        msg.lparam = (uint32_t)&tempBuffer[0];;
        SystemSendMessage(TASK_ID_PM, msg);
    }
}

/*************************************************
函数名称: PmReadySleepFunction
函数功能: 电源管理准备睡眠模式处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/21
*************************************************/
static void PmReadySleepFunction(void)
{
    uint8_t status = 0;
    uint8_t mode = 0;

    #if defined(CAN_ENABLE_OSEKNM)
    /**
     * @brief OSEK NM模式下获取网管状态
     */
    status = OsekNm_GetCurrentState();
    #elif defined(CAN_ENABLE_AUTOSAR_NM)
    /**
     * @brief AUTOSAR NM模式下获取网管状态
     */
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    #elif defined(CAN_ENABLE_NO_NM)
    /**
     * @brief 无网管模式下使用ACC状态判断
     */
    status = NM_STATE_BUS_SLEEP;  /* 无网管模式下默认网管同意休眠 */
    #else
    /**
     * @brief 默认情况使用AUTOSAR NM
     */
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    #endif

    g_pmInfo.entersleepCount--;

    #if defined(CAN_ENABLE_NO_NM)
    /* 无网管模式下，如果ACC状态为活跃，则切换到正常工作模式 */
    if(WORK_STATUS_ACTIVE == g_commonInfo.accStatus)
    #else
    /* 有网管模式下，如果网管状态不允许休眠，则切换到正常工作模式 */
    if(NM_STATE_PREPARE_BUS_SLEEP < status)
    #endif
    {
        g_pmInfo.workStatus = PM_STATUS_NORAML;
        g_pmInfo.entersleepCount = 0;

        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu switch normal work\r\n");
        return;
    }

    if(SLEEP_READY_TIME == g_pmInfo.entersleepCount)
    {
        PmNofityArmSleepFunction();
        g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
    }
}
/*************************************************
函数名称: isPeriodResetARM
函数功能: 判断是否重启arm
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/10
*************************************************/
bool isPeriodResetARM(void)
{
    NvErrorCode ret = NV_NO_ERROR;
    TboxSystemTimeStruct *pTimeStruct =  TboxSystemTimeInfoRead();
    SystemTime_s localTime = {0};
    uint32_t currentTime = 0;

    localTime.timeZone = 8;
    localTime.year = pTimeStruct->currentTime.year + 2000;
    localTime.mon = pTimeStruct->currentTime.month;
    localTime.day = pTimeStruct->currentTime.day;
    localTime.hour = pTimeStruct->currentTime.hour;
    localTime.min = pTimeStruct->currentTime.minute;
    localTime.sec = pTimeStruct->currentTime.second;

    LocalToUnixTime( &localTime,&currentTime);

    if (g_backupRamInfo.lastResetARMTime > POSITIONING_TIMESTAMP && currentTime - g_backupRamInfo.lastResetARMTime > RESET_ARM_CYCLE_TIME)
    {
        g_backupRamInfo.lastResetARMTime = currentTime;
        ret = NvApiWriteData(NV_ID_BACKUP_RAM, (uint8_t *) &g_backupRamInfo, sizeof(g_backupRamInfo));
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update and restart arm time : %u ,ret = %u\r\n",g_backupRamInfo.lastResetARMTime, ret);
    }
    else
    {
        return false;
    }

    return ret == NV_NO_ERROR;
}
/*************************************************
函数名称: PmNoramlWorkFunction
函数功能: 电源管理正常工作模式处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/21
*************************************************/
static void PmNoramlWorkFunction(void)
{
    uint8_t status = 0;
    uint8_t mode = 0;

    Msg msg = {0};

    uint8_t sleepRequest = CheckSleepRequestFromServer();

    #if defined(CAN_ENABLE_OSEKNM)
    /**
     * @brief OSEK NM模式下获取网管状态
     */
    status = OsekNm_GetCurrentState();
    #elif defined(CAN_ENABLE_AUTOSAR_NM)
    /**
     * @brief AUTOSAR NM模式下获取网管状态
     */
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    #elif defined(CAN_ENABLE_NO_NM)
    /**
     * @brief 无网管模式下使用ACC状态判断
     */
    status = NM_STATE_BUS_SLEEP;  /* 无网管模式下默认网管同意休眠 */
    #else
    /**
     * @brief 默认情况使用AUTOSAR NM
     */
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    #endif

#if defined(CAN_ENABLE_NO_NM)
if ((PM_STATUS_NORAML == g_pmInfo.workStatus) &&
    (WORK_STATUS_INACTIVE == g_commonInfo.tspStatus) &&
    (HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus) &&
    ((sleepRequest == 2) ||
     ((WORK_STATUS_INACTIVE == g_commonInfo.canStatus) &&
      (WORK_STATUS_INACTIVE == g_commonInfo.accStatus))))
#else
if ((PM_STATUS_NORAML == g_pmInfo.workStatus) &&
    (WORK_STATUS_INACTIVE == g_commonInfo.tspStatus) &&
    (HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus) &&
    ((sleepRequest == 2) ||
     ((WORK_STATUS_INACTIVE == g_commonInfo.canStatus) &&
      (NM_STATE_PREPARE_BUS_SLEEP >= status))))
#endif
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "=======PmNoramlWorkFunction========\r\n");
        if (isPeriodResetARM())  //允许进入休眠才判断是否需要重启arm一次
        {
            PmResetArmFunction(msg); //重启arm
        }
        else
        {
            g_pmInfo.workStatus = PM_STATUS_NORAML_SLEEP_READY;
            g_pmInfo.entersleepCount = PM_FOUR_SECONDS_TIME;

            #if defined (CAN_ENABLE_AUTOSAR_NM)
            AutoNm_NetworkReleae();
            #endif
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter normal sleep ready time is %ds\r\n",g_pmInfo.entersleepCount/10);
            PmNofityArmReadySleepFunction();

            #if defined (CAN_ENABLE_AUTOSAR_NM)
            AutoNm_NetworkCanShutDown();
            #endif
        }

    }
    
}

/*************************************************
函数名称: PmNofityArmReadySleepFunction
函数功能: 通知ARM准备睡眠
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/01/03
*************************************************/
void PmNofityArmReadySleepFunction(void)
{
    Msg msg = {0};
    g_pmInfo.workStatus = PM_STATUS_NORAML_SLEEP_READY;

    msg.event = MESSAGE_TX_PM_REQUEST;
    msg.len   = 1;
    msg.lparam = (uint32_t)&g_pmInfo.workStatus;
    SystemSendMessage(TASK_ID_IPC, msg);

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu notify arm enter sleep success\r\n");

}

/*************************************************
函数名称: PmNofityArmSleepFunction
函数功能: 通知ARM进入睡眠
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/08/08
*************************************************/
void PmNofityArmSleepFunction(void)
{
    Msg msg = {0};

    if(HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus)
    {
        g_pmInfo.workStatus = PM_STATUS_ENTER_NORAML_SLEEP;
        g_pmInfo.entersleepCount = PM_ONE_SECOND_TIME_COUNTER;

        msg.event = MESSAGE_TX_PM_REQUEST;
        msg.len   = 1;
        msg.lparam = (uint32_t)&g_pmInfo.workStatus;
        SystemSendMessage(TASK_ID_IPC, msg);

        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu notify arm enter sleep success\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_WARING_OUTPUT, "mcu notify arm enter sleep fail\r\n");
    }
}

/*************************************************
函数名称: PmEnterReadySleepFunction
函数功能: TBOX开始进入睡眠(奇点)
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/09/03
*************************************************/
void PmEnterReadySleepFunction(void)
{
    g_pmInfo.entersleepCount = ENTER_SLEEP_COUNT;
    g_pmInfo.workStatus = PM_STATUS_ENTER_NORAML_SLEEP;
    g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
    g_commonInfo.tspStatus  = WORK_STATUS_INACTIVE;
    g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
    g_commonInfo.armClientCount = 0;
    #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "arm enter ready sleep\r\n");
    #endif
}

/*************************************************
函数名称: PmEnterNoramlSleep
函数功能: 电源管理正常工作模式进入睡眠处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/21
*************************************************/
static void PmEnterNoramlSleep(void)
{
    char Mode [][32] = {"WORK", "POWEROFF", "DEPP SLEEP", "LIGHT SLEEP"};
    uint8_t sleepMode = PM_MODE_NORMAL_WORKING;

    g_pmInfo.entersleepCount--;

    if(g_pmInfo.entersleepCount != 0)
    {
        return;
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcuModeStatus:%u,WakeupSource:%u,lightSleepCount:%u\r\n", g_backupRamInfo.mcuModeStatus, g_backupRamInfo.mcuWakeupSource, g_backupRamInfo.lightSleepCount);

    if(LIGHT_SLEEP_MAX_COUNT <= g_backupRamInfo.lightSleepCount)
    {
        g_backupRamInfo.mcuModeStatus = MCU_MODE_DEEP_SLEEP;
        sleepMode = PM_MODE_POWER_DOWN;
        g_pmInfo.workStatus = PM_STATUS_DEEP_SLEEP;

        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter deep sleep is %x\r\n", ++g_backupRamInfo.lightSleepCount);
    }
    else
    {
        g_backupRamInfo.mcuModeStatus = MCU_MODE_LIGHT_SLEEP;
        sleepMode = PM_MODE_LIGHT_SLEEP;
        g_pmInfo.workStatus = PM_STATUS_LIGHT_SLEEP;
        g_backupRamInfo.lightSleepCount ++ ; //五菱不进深度休眠,中车需要进入到深度休眠
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter light sleep count is %d\r\n", g_backupRamInfo.lightSleepCount);

        if(2== CheckSleepRequestFromServer()) //检测此休眠是否由服务端要求的
        {
            ConfigCanPinStatus(CAN1_PIN_INTERRUPT_DISENABLE);
        }

    }

    PmSleepModeConfig();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "==================%s Mode==================\r\n", Mode[sleepMode]);

    SetWakeupSource(TBOX_WAKEUP_UNKNOWN);// 默认置为未知唤醒源
    
    //task will be stop/suspended due to entering into sleep mode after below code
    POWER_SYS_SetMode(sleepMode, POWER_MANAGER_POLICY_FORCIBLE);

    g_pmInfo.workStatus = PM_STATUS_NORAML;
    g_ensureFlag = ACC_OFF_ENSURE_FLAG;

    //to disenable the CAN interrupt after wake up from sleep mode
    ConfigCanPinStatus(ALLCAN_PIN_INTERRUPT_DISENABLE);

    //to recover the CAN Controller after wake up from sleep mode
#if defined (CAN_ENABLE_AUTOSAR_NM)
    AutoNm_NetworkCanEnable();
#elif defined(CAN_ENABLE_NO_NM)
    WakeupAllCANByCANIf();
#endif
    //below code will be executed after system was wakeup by ACC or ARM or other sources in the interrupt handler
    if(MCU_MODE_LIGHT_SLEEP == g_backupRamInfo.mcuModeStatus)
    {
        PmWakeupFromLightSleep();
        //to recover the UART comm with ARM and BLE module after wake up from sleep mode
        PmRecoverIpcFromWakeup();
    #if (BT_TASK_ENABLED == 1)
        PmRecoverBleFromWakeup();
    #endif
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "wakeup from light sleep mode=%d\r\n", sleepMode);
    }
    else if(MCU_MODE_DEEP_SLEEP == g_backupRamInfo.mcuModeStatus)
    {
        PmWakeupFromDeepSleep();
        PmPowerOnPrepare();
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "wakeup from deep sleep mode=%d\r\n", sleepMode);

        //to recover the IPC comm with ARM after wake up from sleep mode
        PmRecoverIpcFromWakeup();
    #if (BT_TASK_ENABLED == 1)
        PmRecoverBleFromWakeup();
    #endif
    }
    else
    {
        g_pmInfo.workStatus = g_backupRamInfo.mcuModeStatus;
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Pm Enter Noraml Slp running after wakeup from sleep mode=%d.\r\n", g_backupRamInfo.mcuModeStatus);
    }
  
}

/*************************************************
函数名称: PmEnterBackupSleep
函数功能: 电源管理备电工作模式进入睡眠处理
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/09/21
*************************************************/
static void PmEnterBackupSleep(void)
{
    NvErrorCode errorCode = NV_NO_ERROR;

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu enter backup sleep\r\n");
    
    g_backupRamInfo.mcuModeStatus = MCU_MODE_BACKUP_SLEEP;
    errorCode = NvApiWriteData(NV_ID_BACKUP_RAM, (uint8_t *)g_nvInfoMap[NV_ID_BACKUP_RAM].pNvData, g_nvInfoMap[NV_ID_BACKUP_RAM].nvValidLen);
    if(NV_NO_ERROR != errorCode)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "mcu enter backup sleep update NV failed:%d\r\n", errorCode);
    }
    PmSleepModeConfig();
}

/*************************************************
函数名称: PmEnterPowerOff
函数功能: 电源管理进入关机流程
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2018/07/13
*************************************************/
static void PmEnterPowerOff(void)
{
    g_pmInfo.entersleepCount--;

    if(0 != g_pmInfo.entersleepCount)
    {
        return;
    }

    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_BATTERY_CUTOFF], GPIO_OUTPUT_LOW);
}

/*************************************************
函数名称: CalcRtcTime
函数功能: 计算Can的相对时间，每隔一个小时校准CAN和Log的时间
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2017/05/15
*************************************************/
void CalcRtcTime(void)
{
    RtcTime rtcTime;
    TboxSystemTimeStruct *pTimeStruct = NULL;
    static uint32_t lastSec = 0;
    uint32_t curSec = xTaskGetTickCount() / 1000;

    if(lastSec == 0)
    {
        lastSec = curSec;
        return;
    }
    else if(lastSec == curSec)
    {
        return;
    }

    
    pTimeStruct = TboxSystemTimeInfoRead();
    pTimeStruct->currentTime.second += (curSec - lastSec);
    if(pTimeStruct->currentTime.second >= 60)
    {
        pTimeStruct->currentTime.minute += (pTimeStruct->currentTime.second / 60);
        pTimeStruct->currentTime.second = (pTimeStruct->currentTime.second % 60);
        if(pTimeStruct->currentTime.minute >= 60)
        {
            //pTimeStruct->currentTime.hour += (pTimeStruct->currentTime.minute / 60);
            //pTimeStruct->currentTime.minute = (pTimeStruct->currentTime.minute % 60);
            //if(pTimeStruct->currentTime.hour >=24)
            {
                //pTimeStruct->currentTime.hour = (pTimeStruct->currentTime.hour % 24);
                RtcGetDateTime(&rtcTime);
                TboxSystemTimeSet(rtcTime.year, rtcTime.month, rtcTime.weekday, rtcTime.day, rtcTime.hour, rtcTime.minute, rtcTime.second, RTC_TYPE_TIME);
            }
            
        }
    }
    lastSec = curSec;
}

/*************************************************
函数名称: PmControlSys3V3Restart
函数功能: 控制GNSS模块重启
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/11/06
*************************************************/
void PmControlSys3V3Restart(void)
{
    static uint8_t toggleFlag = 0x00;
    if((g_commonInfo.gnssIsOnline == GNSS_IS_OFFLINE) && (toggleFlag < 0x05))
    {
        toggleFlag++;
        if(toggleFlag == 0x01)
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_3V3_PWR_SW], GPIO_OUTPUT_LOW);
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "repower on gnss module\r\n");
        }

        if(toggleFlag >= 0x03)
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_3V3_PWR_SW], GPIO_OUTPUT_HIGH);
        }
    }

    if(g_commonInfo.gnssIsOnline == GNSS_IS_ONLINE)
    {
        toggleFlag = 0x00;
    }
}

/*************************************************
函数名称: PmMcuPeriodSendToArm
函数功能: 周期性发送给ARM的消息 0x201
输入参数:   无
输出参数: 无
函数返回类型值：无
编写者: lfc
编写日期 :2025/02/20
*************************************************/
void PmMcuPeriodSendToArm(void)
{
    Msg msg = {0};
    uint8_t index = 0;
    uint8_t tembuff[15] = {0};
    uint8_t status = 0;
    uint8_t mode = 0;
    static uint8_t count = 0;
    static uint8_t statusLast = 0;

    if (g_commonInfo.handshakeStatus == HANDSHAKE_FAIL)
    {
        return;
    }

    #if defined(CAN_ENABLE_OSEKNM)
    /**
     * @brief OSEK NM模式下获取网管状态
     */
    status = OsekNm_GetCurrentState();
    #elif defined(CAN_ENABLE_AUTOSAR_NM)
    /**
     * @brief AUTOSAR NM模式下获取网管状态
     */
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    #elif defined(CAN_ENABLE_NO_NM)
    /**
     * @brief 无网管模式下默认网管同意休眠
     */
    status = NM_STATE_BUS_SLEEP;  /* 无网管模式下默认网管同意休眠 */
    #else
    /**
     * @brief 默认情况使用AUTOSAR NM
     */
    CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
    #endif

    if (statusLast != status || ++count >= 10)
    {
        statusLast = status;
        count = 0;

        uint16_t bPplusValue = ReadBpPlusValue();
        uint16_t batVoltAdcValue = BatReadVoltage();

        tembuff[index++] = (bPplusValue >> 8) & 0xFF;
        tembuff[index++] = bPplusValue & 0xFF;               // KL30电压
        tembuff[index++] = g_commonInfo.accStatus;           // ACC状态
        tembuff[index++] = (status > NM_STATE_PREPARE_BUS_SLEEP) ? 1 : 0; // NM管理状态
        tembuff[index++] = g_commonInfo.fchgStatus;         // 快充电抢状态
        tembuff[index++] = g_commonInfo.schgStatus;         // 慢充电抢状态
        tembuff[index++] = (batVoltAdcValue >> 8) & 0xFF;     // 电池电压高字节
        tembuff[index++] = batVoltAdcValue & 0xFF;           // 电池电压低字节

        msg.event = MESSAGE_TX_MCU_STATUS;
        msg.len = index;
        msg.lparam = (uint32_t) &tembuff[0];
        SystemSendMessage(TASK_ID_IPC, msg);
    }
}

/*************************************************
函数名称: PmPeriodNotifyFunction
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void PmPeriodNotifyFunction(Msg msg)
{
    static uint8_t count = 0;
    FtmInfo  *pFtmInfo = FtmInitRead();
    McuScheduleTimeOutReInit(TASK_ID_PM, 0x00);

    CalcRtcTime();
    PmDetectChgStatus();
    PmMcuPeriodSendToArm();

    count++;
    if(count == 10) // 1秒
    {
        count = 0;
        SetMcuDynamicDid();
    }
    

    if(FTM_MODE_ENTER == pFtmInfo->ftmMode)
    {
        if(FTM_NULL_WAKEUP == g_ftmWakeup)
        {
           g_commonInfo.tspStatus = WORK_STATUS_ACTIVE; 
        }
        else
        {
            g_commonInfo.tspStatus = WORK_STATUS_INACTIVE;
        }
    }

    switch(g_pmInfo.workStatus)
    {      
        case PM_STATUS_NORAML:
        {
            PmNoramlWorkFunction();
            break;
        }
        case PM_STATUS_ENTER_NORAML_SLEEP:
        {
            PmEnterNoramlSleep();
            break;
        }
        case PM_STATUS_NORAML_SLEEP_READY:
        case PM_STATUS_BACKUP_SLEEP_READY:
        {
            PmReadySleepFunction();
            break;
        }
        case PM_STATUS_ENTER_BACKUP_SLEEP:
        {
            PmEnterBackupSleep();
            break;
        }
        case PM_STATUS_POWER_OFF:
        {   
            PmEnterPowerOff();
            break;
        }
        default:
        {
            break; 
        }
    }
    g_commonInfo.canRxStatus = CAN_RX_IDLE;
}

/*************************************************
函数名称: PmResetArmFunction
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
static void PmResetArmFunction(Msg msg)
{
    uint8_t  tempBuf[1] = {0x00};
    Msg    msg2;

    if (g_powerModuleStatus == MODULE_POWER_IDLE)
    {
        tempBuf[0] = PM_STATUS_POWER_OFF;
        msg2.event  = MESSAGE_TX_PM_REQUEST;
        msg2.len    = 1;
        msg2.lparam = (uint32_t)&tempBuf[0];
        SystemSendMessage(TASK_ID_IPC, msg2);

        g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
        g_powerModuleStatus = MODULE_RESET_LOW;
        g_commonInfo.tspStatus  = WORK_STATUS_UNINITIALIZED;
        g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
        g_commonInfo.armClientCount = 0;
    }
}

/*************************************************
函数名称: PmEventFunction
函数功能: 
输入参数: 参数指针和长度
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void PmEventFunction(Msg msg)
{
    uint8_t index = 0;

    for(index = 0; index < (sizeof(g_pmEventFunctionMap)/sizeof(g_pmEventFunctionMap[0])); index++)
    {
        if(g_pmEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_pmEventFunctionMap[index].TaskFunctionHook)
            {
                g_pmEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

/*************************************************
函数名称: GPIOE_IRQHandler
函数功能: 处理PTE 端口中的所有中断处理函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RihardChen
编写日期 :2024/03/28
*************************************************/
void GPIOE_IRQHandler(void)
{
    if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_ACC].gpioNumber))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, g_gpioPowerOnInfoList[MCU_GPIO_ACC].gpioNumber);

        if((PM_STATUS_ENTER_NORAML_SLEEP == g_pmInfo.workStatus) || (PM_STATUS_NORAML_SLEEP_READY == g_pmInfo.workStatus))
        {
            PmDetectAccStatus();
        }
        else if(PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            McuSetPinIntConfig(MCU_GPIO_ACC, PCTRL_DMA_INT_DISABLED);
            SetWakeupSource(TBOX_WAKEUP_ACC);

            g_commonInfo.tspStatus  = WORK_STATUS_INACTIVE;
            g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
            g_commonInfo.armClientCount = 0;
            g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
        }
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_M_WAKEUP_OUT].gpioNumber))
    {
        if(PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            McuSetPinIntConfig(MCU_GPIO_M_WAKEUP_OUT, PCTRL_DMA_INT_DISABLED);
            SetWakeupSource(TBOX_WAKEUP_MODULE);

            g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
            g_commonInfo.tspStatus  = WORK_STATUS_INACTIVE;
            g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
            g_commonInfo.armClientCount = 0;
        }

        PINS_DRV_ClearPinIntFlagCmd(GPIOE, g_gpioPowerOnInfoList[MCU_GPIO_M_WAKEUP_OUT].gpioNumber);
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_BLE_STATUS_INT].gpioNumber))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, g_gpioPowerOnInfoList[MCU_GPIO_BLE_STATUS_INT].gpioNumber);
        
        if(PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            SetWakeupSource(TBOX_WAKEUP_BLE);
            if((PM_STATUS_LIGHT_SLEEP == g_pmInfo.workStatus) || 
               (PM_STATUS_DEEP_SLEEP == g_pmInfo.workStatus)  ||
               (PM_STATUS_BACKUP_SLEEP == g_pmInfo.workStatus))
            {
                g_commonInfo.tspStatus  = WORK_STATUS_INACTIVE;
                g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
                g_commonInfo.armClientCount = 0;
                g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
            }
            else
            {
                BtModulePassReqRxEventCheck();
            }
        }
        else
        {
            BtModulePassReqRxEventCheck();
        }
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_FCHG_STA_INPUT].gpioNumber))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, g_gpioPowerOnInfoList[MCU_GPIO_FCHG_STA_INPUT].gpioNumber);
        if (PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            McuSetPinIntConfig(MCU_GPIO_FCHG_STA_INPUT, PCTRL_DMA_INT_DISABLED);
            SetWakeupSource(TBOX_WAKEUP_FCHG);

            g_commonInfo.tspStatus  = WORK_STATUS_UNINITIALIZED;
            g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
            g_commonInfo.armClientCount = 0;
            g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
        }
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_SCHG_STA_INPUT].gpioNumber))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, g_gpioPowerOnInfoList[MCU_GPIO_SCHG_STA_INPUT].gpioNumber);
        if (PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            McuSetPinIntConfig(MCU_GPIO_SCHG_STA_INPUT, PCTRL_DMA_INT_DISABLED);
            SetWakeupSource(TBOX_WAKEUP_SCHG);

            g_commonInfo.tspStatus  = WORK_STATUS_UNINITIALIZED;
            g_commonInfo.armClinetStatus = ARM_CLINET_OFFLINE;
            g_commonInfo.armClientCount = 0;
            g_commonInfo.handshakeStatus = HANDSHAKE_FAIL;
        }
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<g_gpioPowerOnInfoList[MCU_GPIO_SOS_KEY_INPUT].gpioNumber))
    {
        // Ecall按键唤醒
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, g_gpioPowerOnInfoList[MCU_GPIO_SOS_KEY_INPUT].gpioNumber);
        if (PM_STATUS_NORAML != g_pmInfo.workStatus)
        {
            McuSetPinIntConfig(MCU_GPIO_SOS_KEY_INPUT, PCTRL_DMA_INT_DISABLED);
            SetWakeupSource(TBOX_WAKEUP_ECALL);
        }
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<4))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 4);
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<5))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 5);
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<6))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 6);
    }
    else if(PINS_DRV_GetPortIntFlag(GPIOE)&(1<<7))
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 7);
    }
    else
    {
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 10);
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 11);
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 12);
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 13);
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 14);
        // PINS_DRV_ClearPinIntFlagCmd(GPIOE, 15);
        PINS_DRV_ClearPinIntFlagCmd(GPIOE, 16);
    }
   
}

/*************************************************
函数名称: SetWakeupSource
函数功能: MCU唤醒后设置唤醒源，同时会复位arm状态和握手状态，因此只能在唤醒时调用
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: zhengyong
编写日期 :2024/04/26
*************************************************/
void SetWakeupSource(TboxWakeupSource wakeupSrc)
{
    static volatile bool firstWakeup = true;
    if(firstWakeup && (wakeupSrc != TBOX_WAKEUP_UNKNOWN))
    {
        firstWakeup = false;
        g_backupRamInfo.mcuWakeupSource = wakeupSrc;
        POWER_SYS_SetMode(PM_MODE_NORMAL_WORKING, POWER_MANAGER_POLICY_FORCIBLE);
    }
    else if(wakeupSrc == TBOX_WAKEUP_UNKNOWN)
    {
        firstWakeup = true;
        g_backupRamInfo.mcuWakeupSource = TBOX_WAKEUP_UNKNOWN;
    }
}

/*************************************************
函数名称: PmRecoverIpcFromWakeup
函数功能: MCU轻度休眠唤醒后的Ipc恢复
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/08
*************************************************/
void PmRecoverIpcFromWakeup(void)
{
    IpcUartReInit();
}

/*************************************************
函数名称: PmRecoverBleFromWakeup
函数功能: MCU轻度休眠唤醒后的BLE UART恢复
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/08
*************************************************/
void PmRecoverBleFromWakeup(void)
{
    BTWakeupInit();
}

/*************************************************
函数名称: PmWakeupTimeSync
函数功能: MCU休眠唤醒后的RTC时间同步
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/11
*************************************************/
void PmWakeupTimeSync(void)
{
    RtcTime rtcTime = {0};
    RtcRecoverDateTime(&rtcTime);

}

/*************************************************
函数名称: calcTimeDiffInSecond
函数功能: 计算两个时间的时差秒数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/11
*************************************************/
uint32_t calcTimeDiffInSecond(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur)
{
    int diffDay = dayCur - dayPrev;
    int diffHour = hourCur - hourPrev;
    int diffMinute = minuteCur - minutePrev;
    int diffSecond = secondCur - secondPrev;
    if(0 != diffDay)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "Time Diff In Second with current day: %d, previous day:%d\r\n", dayCur, dayPrev);
        diffHour += 24;
    }
    if(diffSecond < 0)
    {
        diffMinute --;
    }
    if(diffMinute < 0)
    {
        diffHour --;
    }
    diffSecond += diffHour*60*60 + diffMinute*60;
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Time Diff InSecond with diffHour: %d, diffMinute:%d, diffSecond:%d\r\n", diffHour, diffMinute, diffSecond);

    return(diffSecond);
    
}

/*************************************************
函数名称: calcTimeDiffInMinute
函数功能: 计算两个时间的时差分钟数，不足一分钟不算
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/11
*************************************************/
uint32_t calcTimeDiffInMinute(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur)
{
    uint32_t diffSeconds =  calcTimeDiffInSecond(dayPrev, hourPrev, minutePrev, secondPrev, dayCur, hourCur, minuteCur, secondCur);
    uint32_t diffMinutes = diffSeconds/60;
    return(diffMinutes);
}

/*************************************************
函数名称: calcTimeDiffInHour
函数功能: 计算两个时间的时差小时数，不足一小时不算
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/11
*************************************************/
uint32_t calcTimeDiffInHour(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur)
{
    uint32_t diffMinutes =  calcTimeDiffInMinute(dayPrev, hourPrev, minutePrev, secondPrev, dayCur, hourCur, minuteCur, secondCur);
    uint32_t diffHours = diffMinutes/60;
    return(diffHours);
}

/*************************************************
函数名称: calcTimeDiffInDay
函数功能: 计算两个时间的时差天数，不足一天不算
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/11
*************************************************/
uint32_t calcTimeDiffInDay(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur)
{
    uint32_t diffHours =  calcTimeDiffInHour(dayPrev, hourPrev, minutePrev, secondPrev, dayCur, hourCur, minuteCur, secondCur);
    uint32_t diffDays = diffHours/24;
    return(diffDays);
}

/*************************************************
函数名称: PmCanNetControlFunction
函数功能: 远程控制及远程诊断请求与释放CAN网络判断函数
输入参数: armStatus - arm系统状态, bit: 1 - 请求CAN网络，bit: 0 - 释放CAN网络
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/01/15
*************************************************/
static void PmCanNetControlFunction(uint8_t armStatus, uint8_t bit)
{
    if (armStatus == WORK_STATUS_ACTIVE)
    {
        g_canNetStatus |= (uint8_t)(1 << bit);
    }
    else if (armStatus == WORK_STATUS_INACTIVE)
    {
        g_canNetStatus &= ~(uint8_t)(1 << bit);
    }

    #if defined(CAN_ENABLE_OSEKNM)
    /* OSEK NM模式下的网络管理 */
    if ((g_canNetStatus | g_ensureFlag) != 0x00)
    {
        OsekNm_UserHandleEvenSet(HANDLE_REQUEST_ALIVE);
    }
    else if ((g_canNetStatus | g_ensureFlag) == 0x00)
    {
        OsekNm_UserHandleEvenSet(HANDLE_REQUEST_RELEASE);
    }
    #elif defined(CAN_ENABLE_AUTOSAR_NM)
    /* AUTOSAR NM模式下的网络管理 */
    if ((g_canNetStatus | g_ensureFlag) != 0x00)
    {
        AutoNm_NetworkRequest();
        CanNm_NetworkRequest(NM_NETWORK_CHANNEL_ID);
    }
    else if ((g_canNetStatus | g_ensureFlag) == 0x00)
    {
        CanNm_NetworkRelease(NM_NETWORK_CHANNEL_ID);
    }
    #elif defined(CAN_ENABLE_NO_NM)
    /* 无网管模式下不需要网络管理操作 */
    /* 可以在这里添加其他必要的CAN控制逻辑 */
    #endif

}

/*************************************************
函数名称: PmCanIntStbFunction
函数功能: CAN接收中断及Standby控制
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: hyh
编写日期 :2025/02/26
*************************************************/
void PmCanIntStbFunction(void)
{
    ConfigCanPinStatus(ALLCAN_PIN_INTERRUPT_ENABLE);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN1_STB], GPIO_OUTPUT_HIGH);
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_CAN2_STB], GPIO_OUTPUT_HIGH);
}