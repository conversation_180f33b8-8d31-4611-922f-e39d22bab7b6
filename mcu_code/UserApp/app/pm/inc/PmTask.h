/*
      PmTask.h
描述：定义Pm TASK回调函数头文件
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _PM_TASK_H_
#define  _PM_TASK_H_

#include "PmApi.h"
#include "task.h"


#ifdef WINDOWS_SIM
#define PRINTF printf
#endif

#define PM_TASK_STACK_SIZE 384
#define PM_TASK_PRIORITY   4

extern TaskHandle_t PMTask_Handle;

void StartPMTask(void);
void PMTask(void* param);


/************************函数接口***************************/
void PmTaskInitHook(void);
void PmTaskPostInitHook(void);
void PmTaskPeriodHook(void);
void PmTaskFunctionHook(Msg msg);
#endif

