/*
      PmApi.h
描述：此文件主要是Pm任务实现的MCU睡眠和唤醒功能头文件
作者：廖勇刚
时间：2016.7.5
*/

#ifndef  _PM_API_H_
#define  _PM_API_H_

#include "AppTask.h"


/************************宏定义***************************/
#define     LIGHT_SLEEP_MAX_COUNT                        15    /* 浅度睡眠15次进去深度睡眠 */
#define     ENTER_SLEEP_COUNT                            (1000/TASK_PM_PERIOD_TIME)     //最后1秒进入睡眠，不能打断
#define     SLEEP_READY_TIME                             (1000/TASK_PM_PERIOD_TIME)     //最后1秒通知ARM进入休眠
#define     PM_FOUR_SECONDS_TIME                         (4000/TASK_PM_PERIOD_TIME)     //正常进入4秒
#define     PM_ONE_SECOND_TIME_COUNTER                   (1000/TASK_PM_PERIOD_TIME)


#define     BPLUS_VOL_HIGH_VALUE                         33265      // 32V供电时，测得ADC转换后的B+电压mV
#define     BPLUS_VOL_HIGH_RECOVER_VALUE                 32799      // 31.5V供电时，测得ADC转换后的B+电压mV
#define     BPLUS_VOL_LOW_VALUE                          7900       // 8V供电时，测得ADC转换后的B+电压mV
#define     BPLUS_VOL_LOW_RECOVER_VALUE                  8400       // 8.5V供电时，测得ADC转换后的B+电压mV

#define     BPLUS_VOL_LOSS_VAULE                         3000       // 断掉B+时，测得ADC转换后的B+电压mV
#define     BPLUS_VOL_LOW_CAN_OFF                        6400       // 6.5V供电时，测得ADC转换后的B+电压mV
#define     BPLUS_VOL_LOW_STB_ALIVE                      7900       // 8.1V供电时，测得ADC转换后的B+电压mV


#define     ARM_POWER_ON_WAIT_TIME                       30
#define     ARM_POWER_OFF_WAIT_TIME                      (ARM_POWER_ON_WAIT_TIME + 5)

#define     PM_MODE_POWER_DOWN                           1
#define     PM_MODE_LIGHT_SLEEP                          3
#define     PM_MODE_DEEP_SLEEP                           2
#define     PM_MODE_NORMAL_WORKING                       0

#define     ACC_OFF_ENSURE_FLAG                          0
#define     ACC_ON_ENSURE_FLAG                           1
#define     ACC_OFF_COUNTER_THRESHOLD                    40   // 40ms
#define     ACC_ON_COUNTER_THRESHOLD                     5    // 5ms， 时间太短：影响NM的快发报文发送

#define     RESET_ARM_CYCLE_TIME                         259200   //秒，3天
/************************数据结构定义***************************/
typedef enum
{
    PM_STATUS_NORAML                = 0,    // MCU 正常工作模式
    PM_STATUS_LIGHT_SLEEP           = 1,    // MCU 轻度睡眠模式
    PM_STATUS_DEEP_SLEEP            = 2,    // MCU 深度睡眠模式
    PM_STATUS_BACKUP_NORAML         = 3,    // MCU 备电正常模式
    PM_STATUS_BACKUP_SLEEP          = 4,    // MCU 备电睡眠模式
    PM_STATUS_POWER_OFF             = 5,    // MCU 关机模式
    PM_STATUS_NORAML_SLEEP_READY    = 6,    // MCU 准备正常模式睡眠中
    PM_STATUS_BACKUP_SLEEP_READY    = 7,    // MCU 准备备电模式睡眠中
    PM_STATUS_ENTER_NORAML_SLEEP    = 8,    // MCU 进入正常模式睡眠中
    PM_STATUS_ENTER_BPLUS_LOW_SLEEP = 9,    // MCU 进入bPlus低电睡眠
    PM_STATUS_ENTER_BACKUP_SLEEP    = 10,   // MCU 进入备电模式睡眠中
    PM_STATUS_MCU_SOFTRESET         = 0x55, // MCU 进入软复位模式，此时ARM工作正常，用于升级中使用
} PmStatus;

typedef struct pmInfo
{
    PmStatus    workStatus;
    uint16_t    entersleepCount;
    uint8_t     armRestartCount;
    uint8_t     wakeupTime;
}PmInfo;

typedef enum
{
    CAN1_PIN_INTERRUPT_ENABLE,
    CAN2_PIN_INTERRUPT_ENABLE,
    CAN3_PIN_INTERRUPT_ENABLE,
    CAN5_PIN_INTERRUPT_ENABLE,
    CAN1_PIN_INTERRUPT_DISENABLE,
    CAN2_PIN_INTERRUPT_DISENABLE,
    CAN3_PIN_INTERRUPT_DISENABLE,
    CAN5_PIN_INTERRUPT_DISENABLE,
    ALLCAN_PIN_INTERRUPT_ENABLE,
    ALLCAN_PIN_INTERRUPT_DISENABLE,
}CanPinStatus;

/************************函数接口***************************/
void PmPeriodNotifyFunction(Msg msg);
void ConfigCanPinStatus(CanPinStatus state);
void PmPowerSetRtcAndStartTick(void);
void PmRtcOrSensorWakeupSource(void);
void PmEventFunction(Msg msg);
void PmPowerOnPrepare(void);

void PmBatLowVoltDetect(void);
void PmEnterReadySleepFunction(void);
uint8_t PmBplusNotSendCan(void);
void PmNofityArmReadySleepFunction(void);
void PmNofityArmSleepFunction(void);
void PmCanIntStbFunction(void);

void PmDetectAccStatus(void);
void PmVehicleIgStatusNotify(WorkStatus status);

void GPIOE_IRQHandler(void);
void PmWakeupFromLightSleep(void);
void PmGpioControlInLightSleepMode(void);
void PmGpioControlInDeepSleepMode(void);
void PmRecoverIpcFromWakeup(void);
void PmRecoverBleFromWakeup(void);
void PmWakeupTimeSync(void);
void SetWakeupSource(TboxWakeupSource wakeupSrc);
uint32_t calcTimeDiffInSecond(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur);
uint32_t calcTimeDiffInMinute(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur);
uint32_t calcTimeDiffInHour(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur);
uint32_t calcTimeDiffInDay(uint8_t dayPrev, uint8_t hourPrev, uint8_t minutePrev, uint8_t secondPrev,
                             uint8_t dayCur, uint8_t hourCur, uint8_t minuteCur, uint8_t secondCur);                                                                                       

#endif

