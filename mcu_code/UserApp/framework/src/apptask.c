/*
      Task.c
描述：此头文件主要定义任务函数和结构体，包括任务初始化函数、任务第二次初始化函数、任务周期性执行函数、任务主功能执行函数。
      任务初始化函数：任务第一次运行对任务的一些初始化操作。
      任务第二次初始化函数：等待其他任务第一次初始化执行完成以后，任务进行第二次执行的函数。
      任务周期性执行函数：任务周期性执行的回调函数。
      任务主功能函数：任务实时运行的回调函数。
作者：廖勇刚
时间：2016.6.28
*/
#include "AppTask.h"

#include "NvApi.h"
#include "PmTask.h"
#include "CanTask.h"
#include "LedTask.h"
#include "LogTask.h"
#include "IpcTask.h"
#include "DiagTask.h"
#include "BatTask.h"
#include "UpdateTask.h"
#include "BtTask.h"
#include "CommonTask.h"
#include "CANDataUploadTask.h"

#include "printf.h"

/************************全局变量****************************/
TmrNode    g_tmrHeadNode;  // TMR头节点
CommonInfo g_commonInfo = {0};
UINT32     g_osCurrentTickTime = 0;

// 任务队列分配
UINT8 g_pmQueueBuf[QUEUE_PM_MAX_LEN]                  = {0};
UINT8 g_canQueueBuf[QUEUE_CAN_MAX_LEN]                = {0};
UINT8 g_ledQueueBuf[QUEUE_LED_MAX_LEN]                = {0};
UINT8 g_logQueueBuf[QUEUE_LOG_MAX_LEN]                = {0};
UINT8 g_lpctxQueueBuf[QUEUE_IPC_TX_MAX_LEN]           = {0};
UINT8 g_diagQueueBuf[QUEUE_DIAG_MAX_LEN]              = {0};
UINT8 g_batQueueBuf[QUEUE_BAT_MAX_LEN]                = {0};
UINT8 g_updateQueueBuf[QUEUE_UPDATE_MAX_LEN]          = {0};
UINT8 g_btQueueBuf[QUEUE_BT_MAX_LEN]                  = {0};
UINT8 g_ipcrxQueueBuf[QUEUE_IPC_RX_MAX_LEN]           = {0};
UINT8 g_commonQueueBuf[QUEUE_COMMON_MAX_LEN]          = {0};
UINT8 g_canduploadQueueBuf[QUEUE_CAND_UPLOAD_MAX_LEN] = {0};

RxQueue g_taskRxQueue[QUEUE_ID_MAX] =
    {
        {0, 0, QUEUE_PM_MAX_LEN, &g_pmQueueBuf[0], QUEUE_PM_MAX_LEN},
        {0, 0, QUEUE_CAN_MAX_LEN, &g_canQueueBuf[0], QUEUE_CAN_MAX_LEN},
        {0, 0, QUEUE_LED_MAX_LEN, &g_ledQueueBuf[0], QUEUE_LED_MAX_LEN},
        {0, 0, QUEUE_LOG_MAX_LEN, &g_logQueueBuf[0], QUEUE_LOG_MAX_LEN},
        {0, 0, QUEUE_IPC_TX_MAX_LEN, &g_lpctxQueueBuf[0], QUEUE_IPC_TX_MAX_LEN},
        {0, 0, QUEUE_DIAG_MAX_LEN, &g_diagQueueBuf[0], QUEUE_DIAG_MAX_LEN},
        {0, 0, QUEUE_BAT_MAX_LEN, &g_batQueueBuf[0], QUEUE_BAT_MAX_LEN},
        {0, 0, QUEUE_UPDATE_MAX_LEN, &g_updateQueueBuf[0], QUEUE_UPDATE_MAX_LEN},
        {0, 0, QUEUE_BT_MAX_LEN, &g_btQueueBuf[0], QUEUE_BT_MAX_LEN},
        {0, 0, QUEUE_IPC_RX_MAX_LEN, &g_ipcrxQueueBuf[0], QUEUE_IPC_RX_MAX_LEN},
        {0, 0, QUEUE_COMMON_MAX_LEN, &g_commonQueueBuf[0], QUEUE_COMMON_MAX_LEN},
        {0, 0, QUEUE_CAND_UPLOAD_MAX_LEN, &g_canduploadQueueBuf[0], QUEUE_CAND_UPLOAD_MAX_LEN},
};

TmrNode g_tmrNode[TMR_ID_MAX] =
    {
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_PM_PERIOD_TIME, PmTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_CAN_PERIOD_TIME, CanTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_LEDGPIO_PERIOD_TIME, LedTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_LOG_PERIOD_TIME, LogTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_IPC_PERIOD_TIME, IpcTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_SELFCHECK_PERIOD_TIME, DiagTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_BAT_PERIOD_TIME, BatTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_UPGRADE_PERIOD_TIME, UpdateTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_BLE_PERIOD_TIME, BtTaskPeriodHook},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_COMMON_PERIOD_TIME, NULL},
        {TASK_PERIOD_TMR, TASK_EXEC_INACTIVE_STATUS, 0x00, 0x00, TASK_CAND_UPLOAD_PERIOD_TIME, NULL},
};

TaskInfo g_taskInfoMap[TASK_ID_MAX] =
    {
        {TASK_ID_PM, PmTaskInitHook, PmTaskPostInitHook, PmTaskFunctionHook, &g_tmrNode[TMR_ID_PM], MASK_VALUE_PM_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_PM_TASK]},
        {TASK_ID_CAN, CanTaskInitHook, CanTaskPostInitHook, CanTaskFunctionHook, &g_tmrNode[TMR_ID_CAN], MASK_VALUE_CAN_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_CAN_TASK]},
        {TASK_ID_LED, LedTaskInitHook, LedTaskPostInitHook, LedTaskFunctionHook, &g_tmrNode[TMR_ID_LED], MASK_VALUE_LED_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_LED_TASK]},
        {TASK_ID_LOG, LogTaskInitHook, LogTaskPostInitHook, LogTaskFunctionHook, &g_tmrNode[TMR_ID_LOG], MASK_VALUE_LOG_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_LOG_TASK]},
        {TASK_ID_IPC, IpcTaskInitHook, IpcTaskPostInitHook, IpcTaskFunctionHook, &g_tmrNode[TMR_ID_IPC], MASK_VALUE_IPC_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_IPC_TX_TASK]},
        {TASK_ID_DIAG, DiagTaskInitHook, DiagTaskPostInitHook, DiagTaskFunctionHook, &g_tmrNode[TMR_ID_DIAG], MASK_VALUE_DIAG_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_DIAG_TASK]},
        {TASK_ID_BAT, BatTaskInitHook, BatTaskPostInitHook, BatTaskFunctionHook, &g_tmrNode[TMR_ID_BAT], MASK_VALUE_BAT_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_BAT_TASK]},
        {TASK_ID_UPDATE, UpdateTaskInitHook, UpdateTaskPostInitHook, UpdateTaskFunctionHook, &g_tmrNode[TMR_ID_UPDATE], MASK_VALUE_UPDATE_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_UPDATE_TASK]},
        {TASK_ID_BT, BtTaskInitHook, BtTaskPostInitHook, BtTaskFunctionHook, &g_tmrNode[TMR_ID_BT], MASK_VALUE_BLE_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_BT_TASK]},
        {TASK_ID_COMMON, CommonTaskInitHook, NULL, NULL, &g_tmrNode[TMR_ID_COMMON], MASK_VALUE_COMMON_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_COMMON_TASK]},
        {TASK_ID_CANDUPLOAD, CANDataUploadTaskInitHook, NULL, NULL, &g_tmrNode[TMR_ID_CANDUPLOAD], MASK_VALUE_CANDUPL_TASK, 0x00, &g_taskRxQueue[QUEUE_ID_CAND_UPLOAD_TASK]},
};

/*************************************************
函数名称: GetOsTickHandle
函数功能: 获取当前系统tickTime
输入参数: void
输出参数: void
函数返回值: g_osCurrentTickTime
编写者:xiazhichuan
编写日期: 2024/9/27
*************************************************/
uint32_t GetOsTickHandle(void)
{
    return g_osCurrentTickTime;
}

/*************************************************
函数名称: McuOsTickHandle
函数功能: MCU系统调度处理
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: liaoyonggang
编写日期 :2016/08/26
*************************************************/
int McuScheduleTimeOutReInit(TaskId taskId, UINT32 valueTime)
{
    TaskId id;

    for (id = TASK_ID_PM; id < TASK_ID_MAX; id++)
    {
        if (id == taskId)
        {
            g_taskInfoMap[id].TaskTmrNode->taskScheduleTimeoutCnt = valueTime;
            break;
        }
    }

    return 0;
}

/*************************************************
函数名称: McuOsTickHandle
函数功能: MCU系统调度处理
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: liaoyonggang
编写日期 :2016/08/26
*************************************************/
void McuOsTickHandle(void)
{
}

/*************************************************
函数名称: McuPowerOnLog
函数功能: MCU打印开机LOG
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: liaoyonggang
编写日期 :2016/08/26
*************************************************/
void McuPowerOnLog(void)
{
    UINT8  mcuHrMainVersion    = MCU_HR_MAIN_VERSION;
    UINT8  mcuHrSubVersion     = MCU_HR_SUB_VERSION;
    UINT8  mcuSoftVersion      = MCU_SOFT_MAIN_VERSION;
    UINT8  mcuSoftSubVersion   = MCU_SOFT_SUB_VERSION;
    UINT8  mcuSoftPatchVersion = MCU_SOFT_PATCH_VERSION;  // 当主版本不变时，使用子版本区分这是什么时候得版本
    UINT16 mcuNvVerson         = NV_VERSION;

    // 分隔符，方便调试时查看
    PRINTF("\r\n<-------------------------------------------------->\r\n");
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "App* HV:%u.%u SV:%u.%u.%u NV:%u\r\n", mcuHrMainVersion, mcuHrSubVersion, mcuSoftVersion, mcuSoftSubVersion, mcuSoftPatchVersion, mcuNvVerson);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Build on: %s %s, Free Heap: %.2fKB RSSR:0x%X\r\n", __DATE__, __TIME__, xPortGetFreeHeapSize()/1024.0,RCU->RSSR);
    RCU->RSSR = 0xFFFFFFFF; //清除复位标志位
}

void ReadResetWakeSource(void)
{
    uint32_t RSSR =  ReadValueOfFlashAddress(WAKEUP_SOURCE_ADDR);
    BackupRamInfo *backupRamInfo = GetBackupRamInfo();

    if (RSSR & RCU_RSSR_WDG_MASK)
    {
        backupRamInfo->mcuWakeupSource = TBOX_WAKEUP_WATCHDOE;
    }
    else if(RSSR & RCU_RSSR_SW_MASK)
    {
        backupRamInfo->mcuWakeupSource = TBOX_WAKEUP_SOFT_RESET;
    }
    else if(RSSR & RCU_RSSR_PIN_MASK)
    {
        backupRamInfo->mcuWakeupSource = TBOX_WAKEUP_EXTERN_RESET;
    }
    else
    {
        backupRamInfo->mcuWakeupSource = TBOX_WAKEUP_BPLUS;
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "wakeup src:%d\r\n", backupRamInfo->mcuWakeupSource);
}
/*************************************************
函数名称: TaskQueueInit
函数功能: 初始化每个任务接收循环队列数据结构的初始化
输入参数: void
输出参数: void
函数返回值: 无
编写者: liaoyonggang
编写日期 :2016/07/06
*************************************************/
void TaskQueueInit(void)
{
    QueueId id;
    bool initSuccess = true;

    for (id = QUEUE_ID_PM_TASK; id < QUEUE_ID_MAX; id++)
    {
        if (QUEUE_NO_ERROR == CreateQueue(&g_taskRxQueue[id]))
        {
            //SystemApiLogPrintf(LOG_INFO_OUTPUT, "[%d] Queue Init sucess\r\n", id);
        }
        else
        {
            initSuccess = false;
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_ERROR_OUTPUT, "[%d] Queue Init fail\r\n", id);
#endif
        }
    }
    if(true == initSuccess)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "All Queue Init sucess.\r\n");
    }
}

/*************************************************
函数名称: IsQueueSpaceEnough
函数功能: 检查队列剩余空间是否足够
输入参数: taskId,len-待写数据长度
输出参数: 无
函数返回类型值：
            QUEUE_NO_ERROR --- 空间足够
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE---输入写入长度大于最大长度
编写者: zhengyong
编写日期 :2024/03/19
*************************************************/
queueErrorCode IsQueueSpaceEnough(TaskId taskId, UINT16 len)
{
    if(taskId < TASK_ID_MAX)
    {
        return IsQueueFull(g_taskInfoMap[taskId].rxQueue, len);
    }
    else
    {
        return QUEUE_WRITE_LEN_BEYOND_SPACE_SZIE;
    }
}

/*************************************************
函数名称: SystemSendMessage
函数功能: 向每个任务发送消息函数
输入参数: taskId,msg
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_NO_ERROR --- 发送成功
            QUEUE_PRAR_POINT_NULL---队列输入为空
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE---输入写入长度大于最大长度
            QUEUE_PRAR_ADDRESS_NULL---发送数据长度非0，但是发送参数地址为NULL
            查询循环队列已满返回值
编写者: liaoyonggang
编写日期 :2016/07/07
*************************************************/
queueErrorCode SystemSendMessage(TaskId taskId, Msg msg)
{
    queueErrorCode errorCode = QUEUE_NO_ERROR;

    if (TASK_ID_IPC == taskId)
    {
        if (HANDSHAKE_FAIL == g_commonInfo.handshakeStatus)
        {
            errorCode = QUEUE_IPC_NOT_HANDSHAKE;
            return errorCode;
        }
    }

    if (TASK_ID_MAX <= taskId)
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        //SystemApiLogPrintf(LOG_ERROR_OUTPUT, "System Send Message task id is %d\r\n", taskId);
#endif
        errorCode = QUEUE_PARA_ERROR;
        return errorCode;
    }

    if (NULL == g_taskInfoMap[taskId].rxQueue)
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        //SystemApiLogPrintf(LOG_ERROR_OUTPUT, "System Send Message task id is %d\r\n", taskId);
#endif
        errorCode = QUEUE_PRAR_POINT_NULL;
        return errorCode;
    }

    // 检查发送是否成功还是失败
    errorCode = SystemApiSendMessage(g_taskInfoMap[taskId].rxQueue, msg);
    if (QUEUE_NO_ERROR != errorCode)
    {
#if (LOG_SWITCH_CONFIG_ERROR == LOG_SWITCH_ON)
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "queue tx msg(0x%x) len(%d) is error: %d taskId: %d\r\n", msg.event, msg.len, errorCode, taskId);
#endif
        return errorCode;
    }
    else
    {
        return errorCode;
    }
}

/*************************************************
函数名称: TaskInit
函数功能: 创建TMR双链表，插入需要执行的TMR节点，初始化任务循环队列，执行任务初始化函数和第二次初始化函数
输入参数: void
输出参数: void
函数返回值: 无
编写者: liaoyonggang
编写日期 :2016/07/07
*************************************************/
void TaskInit(void)
{
    PmPowerSetRtcAndStartTick();
}

/*************************************************
函数名称: GlobalParamsInit
函数功能: 初始化全局参数
输入参数: void
输出参数: void
函数返回值: 无
编写者:guoyuchen
编写日期: 2024/3/19
*************************************************/
void GlobalParamsInit(void)
{
    g_commonInfo.bplusStatus         = BPLUS_STATUS_PLUG;
    g_commonInfo.remoteControlStatus = WORK_STATUS_INACTIVE;
    g_commonInfo.accStatus           = WORK_STATUS_INACTIVE;
    g_commonInfo.diagStatus          = WORK_STATUS_INACTIVE;
    g_commonInfo.Nm_State            = NM_STATE_UNINIT;
    g_commonInfo.canTotalNum         = 0;
    g_commonInfo.canLostNum          = 0;
    g_commonInfo.mainPowerLowSleep   = 0x00;
    g_commonInfo.armClinetStatus     = ARM_CLINET_OFFLINE;
    g_commonInfo.armClientCount      = 0;
    g_commonInfo.tspStatus           = WORK_STATUS_UNINITIALIZED;
}