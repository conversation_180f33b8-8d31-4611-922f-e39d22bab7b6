/*
      Queue.c
描述：此头文件包括创建循环队列、判断循环队列是否是空、判断循环队列是否已满、向队列发送消息、接收队列消息
作者：廖勇刚
时间：2016.6.28
*/
#include <string.h>
#include "appqueue.h"
#include "portmacro.h"
#include "SystemApi.h"
#include "projdefs.h"





/************************外部全局变量****************************/
extern UINT8  g_txTempBuf[];
extern UINT8  g_dmaCompelte;
extern UINT32 g_osCurrentTickTime;
extern CommonInfo g_commonInfo;

#define TASK_QUEUE_SIZE     3

/*************************************************
函数名称: CreateQueue
函数功能: 创建循环队列，初始化相应的队列参数
输入参数: 循环队列指针
输出参数: queueErrorCode
函数返回类型值：
         QUEUE_NO_ERROR---执行正确
         QUEUE_PRAR_POINT_NULL --- 输入参数指针为空    
编写者: liaoyonggang
编写日期 :2016/07/06
*************************************************/

queueErrorCode CreateQueue(RxQueue *pQueue)
{
    //add FreeRTOS support
    queueErrorCode result = QUEUE_NO_ERROR;
    //输入参数指针为空
    if(NULL == pQueue)
    {
        result = QUEUE_PRAR_POINT_NULL;
    }
    else
    {
        //pQueue->handle_queue = xQueueCreate(pQueue->maxLen, sizeof(Msg));
        //pQueue->handle_queue = xQueueCreate(TASK_QUEUE_SIZE, pQueue->maxLen);
        //if (NULL != pQueue->handle_queue)
        {
            memset(pQueue->data, 0x00, pQueue->maxLen);
        }
        //else
        {
            //result = QUEUE_HANDLE_IS_INVALID;
        }
    }
    return result;
}


/*************************************************
函数名称: IsQueueFull
函数功能: 查询循环队列是否已满，即要写入数据不能满足写入
输入参数: 循环队列指针，待写入数据长度
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_NO_ERROR---执行正确
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空 
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE ---要写入数据长度超过最大队列长度
            QUEUE_WRITE_LEN_BEYOND_SPACE_SZIE---要写入数据长度超过可写入数据长度
编写者: liaoyonggang
编写日期 :2016/07/06
*************************************************/

queueErrorCode IsQueueFull(RxQueue *pQueue, UINT16 writeLen)
{
    //输入参数指针为空
    if(NULL == pQueue)
    {
        return QUEUE_PRAR_POINT_NULL;
    }

    //要写入的数据长度大于队列数据数组长度
    if(pQueue->maxLen <= writeLen)
    {
        return QUEUE_WRITE_LEN_BEYOND_MAX_SZIE;
    }
    
    // 判断循环队列是否有足够的空间写入下一包数据
    if(pQueue->spaceLen >= writeLen)
    {
        return QUEUE_NO_ERROR;
    }
    else
    {
        return QUEUE_WRITE_LEN_BEYOND_SPACE_SZIE;
    }
}


/*************************************************
函数名称: IsQueueEmpty
函数功能: 查询循环队列是否为空
输入参数: 循环队列指针
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_IS_EMPTY---循环队列没有写入数据
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空 
            QUEUE_WRITED_LEN_LESS_THAN_MSG_LEN ---循环队列已写入数据小于消息的最小长度
            QUEUE_NOT_EMPTY---循环队列非空，有数据写入
编写者: liaoyonggang
编写日期 :2016/07/06
*************************************************/

queueErrorCode IsQueueEmpty(RxQueue *pQueue)
{
    //已写入数据长度
    UINT16 writeDataLen = 0;     

    //输入参数指针为空    
    if(NULL == pQueue)
    {
        return QUEUE_PRAR_POINT_NULL;
    }

    writeDataLen = pQueue->maxLen - pQueue->spaceLen;
        
    //队列的读位置和写位置相等并且空白空间大于0则判断循环队列为空
    if((pQueue->readPos == pQueue->writePos) && (0 == writeDataLen))
    {
        return QUEUE_IS_EMPTY;
    }
    else if((pQueue->readPos != pQueue->writePos)&&((MSG_EVENT_SIZE + MSG_LEN_SIZE) > writeDataLen))
    {
        return QUEUE_WRITED_LEN_LESS_THAN_MSG_LEN;
    }
    else
    {
        return QUEUE_NOT_EMPTY;
    }
}


/*************************************************
函数名称: SystemApiSendMessage
函数功能: 向循环队列发送消息
输入参数: 循环队列指针，待发送消息
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_IS_EMPTY---循环队列没有写入数据
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE---写入长度超过循环队列最大长度
            QUEUE_PRAR_ADDRESS_NULL---发送数据长度非0，但是发送参数地址为NULL
            查询循环队列已满返回值
编写者: liaoyonggang
编写日期 :2016/07/06
*************************************************/

queueErrorCode SystemApiSendMessage(RxQueue *pQueue, Msg msg)
{
    UINT16 writeLen = 0;
    UINT16 paraPos  = 0;
    UINT8 *para = NULL;
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UBaseType_t sendResult = pdPASS;

    //输入参数指针为空  
    if(NULL == pQueue)
    {
        errorCode = QUEUE_PRAR_POINT_NULL;
        return errorCode;
    }

    writeLen = MSG_EVENT_SIZE + MSG_LEN_SIZE + msg.len;
    if(pQueue->maxLen < writeLen)
    {
       errorCode = QUEUE_WRITE_LEN_BEYOND_MAX_SZIE;
       return errorCode;
    }

    //判断写入字节空间是否够
    errorCode = IsQueueFull(pQueue, writeLen);
    if(QUEUE_NO_ERROR != errorCode)
    {
        return errorCode;
    }
    
    pQueue->data[pQueue->writePos]  = (UINT8)(msg.event >> 8); //事件的高字节放入低位地址
    pQueue->writePos = (pQueue->writePos + 1)%pQueue->maxLen;    
    pQueue->data[pQueue->writePos]   = (UINT8)msg.event;    
    pQueue->writePos = (pQueue->writePos + 1)%pQueue->maxLen;

    pQueue->data[pQueue->writePos] = (UINT8)(msg.len >> 8);    //长度的高字节放入低位地址
    pQueue->writePos = (pQueue->writePos + 1)%pQueue->maxLen;            
    pQueue->data[pQueue->writePos]  = (UINT8)msg.len;     
    pQueue->writePos = (pQueue->writePos + 1)%pQueue->maxLen;    

    pQueue->spaceLen = pQueue->spaceLen - MSG_EVENT_SIZE - MSG_LEN_SIZE;

    //如果参数部分长度为0，则返回
    if(0 == msg.len)
    {
        msg.lparam = 0;
        //sendResult = xQueueSend(pQueue->handle_queue, pQueue->data, 0);
        if(pdPASS != sendResult)
        {
            errorCode = QUEUE_SEND_FAILED;
        }
        
        return errorCode;
    }

    if(NULL == (UINT8*)msg.lparam)
    {
        errorCode = QUEUE_PRAR_ADDRESS_NULL;
        
        //xQueueSend(pQueue->handle_queue, pQueue->data, 0);
        return errorCode;         
    }

    para = (UINT8*)msg.lparam;
    for(paraPos = 0; paraPos < msg.len; paraPos++)
    {
        pQueue->data[pQueue->writePos] = para[paraPos];
        pQueue->writePos = (pQueue->writePos + 1)%pQueue->maxLen;
    }
    pQueue->spaceLen = pQueue->spaceLen - msg.len;  

    //sendResult = xQueueSend(pQueue->handle_queue, pQueue->data, 0);
    if(pdPASS != sendResult)
    {
        errorCode = QUEUE_SEND_FAILED;
    }
    
    return errorCode;
}


/*************************************************
函数名称: SystemApiReceiveMessage
函数功能: 查询循环队列接收消息
输入参数: 循环队列指针，待接收消息
         waitTime:如果接收队列中没有消息的等待时间
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_IS_EMPTY---循环队列没有写入数据
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
            查询循环队列是否空返回值
编写者: liaoyonggang
编写日期 :2016/07/06
*************************************************/

queueErrorCode SystemApiReceiveMessage(RxQueue *pQueue, Msg *msg, UINT8 *tempBuf, UINT32 waitTime)
{
    UINT8 highEvent = 0;
    UINT8 lowEvent = 0;
    UINT8 highLen = 0;
    UINT8 lowLen = 0;
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT16 i = 0;
    BaseType_t result = pdPASS;

    //输入参数指针为空
    if((NULL == pQueue)||(NULL == msg) ||(NULL == tempBuf))
    {
        errorCode = QUEUE_PRAR_POINT_NULL;
        return errorCode;
    }

    
    // result = xQueueReceive(pQueue->handle_queue, pQueue->data, pdMS_TO_TICKS(waitTime));
    //result = xQueueReceive(pQueue->handle_queue, pQueue->data, pdMS_TO_TICKS(0));
    if (pdPASS != result)
    {
        errorCode = QUEUE_IS_EMPTY;
    }    
    else
    {
        errorCode = IsQueueEmpty(pQueue);
        if(QUEUE_NOT_EMPTY != errorCode)
        {
            return errorCode;
        }

        highEvent = pQueue->data[pQueue->readPos];
        pQueue->readPos = (pQueue ->readPos + 1)%pQueue->maxLen;
        lowEvent  = pQueue->data[pQueue->readPos];
        pQueue->readPos = (pQueue ->readPos + 1)%pQueue->maxLen;

        highLen = pQueue->data[pQueue->readPos];
        pQueue->readPos = (pQueue ->readPos + 1)%pQueue->maxLen;
        lowLen = pQueue->data[pQueue->readPos];
        pQueue->readPos = (pQueue ->readPos + 1)%pQueue->maxLen;

        msg->event = (UINT16)(highEvent << 8)  + lowEvent;
        msg->len   = (UINT16)(highLen << 8) + lowLen;
        pQueue->spaceLen = pQueue->spaceLen + MSG_EVENT_SIZE + MSG_LEN_SIZE;

        if(0 == msg->len)
        {                
            msg->lparam = 0;
            errorCode = QUEUE_NO_ERROR;
        
            return errorCode;
        }

        msg->lparam = (UINT32)&pQueue->data[pQueue->readPos];
        for(i = 0; i < msg->len; i++)
        {
            tempBuf[i] = pQueue->data[pQueue->readPos];
            pQueue->readPos = (pQueue->readPos + 1)%pQueue->maxLen;
        }
        pQueue->spaceLen = pQueue->spaceLen + msg->len;
        msg->lparam = (UINT32)tempBuf;
        
        errorCode = QUEUE_NO_ERROR;
        
    }
    
    return errorCode;
}



/*************************************************
函数名称: CreateDoubleQueue
函数功能: 创建二维数组循环队列，初始化相应的队列参数
输入参数: 循环队列指针
输出参数: queueErrorCode
函数返回类型值：
         QUEUE_NO_ERROR---执行正确
         QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
编写者: liaoyonggang
编写日期 :2019/05/26
*************************************************/

queueErrorCode CreateDoubleQueue(DoubleQueue *pDoubleQueue)
{
    UINT8 row = 0;

    //输入参数指针为空
    if(NULL == pDoubleQueue)
    {
        return QUEUE_PRAR_POINT_NULL;
    }

    pDoubleQueue->readPos = 0;
    pDoubleQueue->writePos = 0;
    pDoubleQueue->spaceLen = TBOX_DIAG_ROW_COUNT;
    for(row = 0; row < TBOX_DIAG_ROW_COUNT;row++)
    {
        memset(&pDoubleQueue->data[row][0], 0x00, TBOX_DIAG_COLUMN_COUNT);
    }

    return QUEUE_NO_ERROR;
}


/*************************************************
函数名称: SystemApiWriteRowData
函数功能: 向循环队列写一行数据
输入参数: 循环队列指针，待写入数据
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_WRITE_LEN_BEYOND_MAX_SZIE---循环队列满
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
编写者: liaoyonggang
编写日期 :2019/05/26
*************************************************/

queueErrorCode SystemApiWriteRowData(DoubleQueue *pDoubleQueue, UINT8 *buf)
{
    queueErrorCode errorCode = QUEUE_NO_ERROR;

    //输入参数指针为空
    if((NULL == pDoubleQueue)||(NULL == buf))
    {
        errorCode = QUEUE_PRAR_POINT_NULL;
        return errorCode;
    }

    if(0 != pDoubleQueue->spaceLen)
    {
        memcpy(&pDoubleQueue->data[pDoubleQueue->writePos][0],buf,TBOX_DIAG_COLUMN_COUNT);
        pDoubleQueue->writePos = (pDoubleQueue->writePos + 1)%TBOX_DIAG_ROW_COUNT;
        pDoubleQueue->spaceLen = pDoubleQueue->spaceLen - 1;
    }
    else
    {
        errorCode = QUEUE_WRITE_LEN_BEYOND_MAX_SZIE;
    }

    return errorCode;
}


/*************************************************
函数名称: SystemApiReadRowData
函数功能: 向循环队列读取一行数据
输入参数: 循环队列指针，待读取数据
输出参数: queueErrorCode
函数返回类型值：
            QUEUE_IS_EMPTY---循环队列空
            QUEUE_PRAR_POINT_NULL --- 输入参数指针为空
编写者: liaoyonggang
编写日期 :2019/05/26
*************************************************/

queueErrorCode SystemApiReadRowData(DoubleQueue *pDoubleQueue, UINT8 *buf)
{
    queueErrorCode errorCode = QUEUE_NO_ERROR;

    //输入参数指针为空
    if((NULL == pDoubleQueue)||(NULL == buf))
    {
        errorCode = QUEUE_PRAR_POINT_NULL;
        return errorCode;
    }

    if(TBOX_DIAG_ROW_COUNT == pDoubleQueue->spaceLen)
    {
       errorCode = QUEUE_IS_EMPTY;
    }
    else
    {
        memcpy(buf, &pDoubleQueue->data[pDoubleQueue->readPos], TBOX_DIAG_COLUMN_COUNT);
        pDoubleQueue->readPos = (pDoubleQueue->readPos + 1)%TBOX_DIAG_ROW_COUNT;
        pDoubleQueue->spaceLen = pDoubleQueue->spaceLen + 1;
    }

    return errorCode;
}
