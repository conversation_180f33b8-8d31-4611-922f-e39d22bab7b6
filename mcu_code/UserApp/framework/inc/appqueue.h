/*
     queue.h
描述：此文件包括创建循环队列、向队列发送消息、接收队列消息函数接口和数据结构定义函数头文件
作者：廖勇刚
时间：2016.6.28
*/
#ifndef  _APP_QUEUE_H_
#define  _APP_QUEUE_H_

#include "FreeRTOS.h"
#include "event.h"
#include "queue.h"


/************************宏定义***************************/
#define MSG_EVENT_SIZE                      0x02
#define MSG_LEN_SIZE                        0x02
#define EVENT_HIGH_BYTE                     0x00
#define EVENT_LOW_BYTE                      0x01
#define LEN_HIGH_BYTE                       0x02
#define LEN_LOW_BYTE                        0x03
#define MSG_PARA_BYTE                       0x04
#define TBOX_DIAG_ROW_COUNT                 50
#define TBOX_DIAG_COLUMN_COUNT              14


/************************数据结构定义***************************/
typedef enum
{
    QUEUE_NO_ERROR = 0,                     //队列执行正确
    QUEUE_PRAR_POINT_NULL,                  //队列输入为空 
    QUEUE_WRITE_LEN_BEYOND_MAX_SZIE,        //输入写入长度大于最大长度
    QUEUE_WRITE_LEN_BEYOND_SPACE_SZIE,      //输入写入长度大于可用长度
    QUEUE_WRITED_LEN_LESS_THAN_MSG_LEN,     //已写入数据小于消息最小长度
    QUEUE_PRAR_ADDRESS_NULL,                //消息长度非0，但是参数地址为0
    QUEUE_NOT_EMPTY,                        //队列非空
    QUEUE_IS_EMPTY,                         //队列空
    QUEUE_PARA_ERROR,                       //输入参数错误
    QUEUE_IPC_NOT_HANDSHAKE,                //IPC 没有握手
    //add FreeRTOS support
    QUEUE_HANDLE_IS_INVALID,                //队列句柄为空
    QUEUE_SEND_FAILED,                      //发送失败
} queueErrorCode;


typedef struct msg
{
    UINT16 event;          //定义的事件类型
    UINT16 len;            //定义的事件数据长度
    UINT32 lparam;         //传递的数据地址
}Msg;

typedef struct rxqueue
{
    UINT16  readPos;
    UINT16  writePos;
    UINT16  spaceLen;
    UINT8   *data;
    UINT16  maxLen;
    //added below queue handle to support the FreeRTOS
    QueueHandle_t handle_queue;
}RxQueue;

typedef struct
{
    UINT8 readPos;
    UINT8 writePos;
    UINT8 spaceLen;
    UINT8 data[TBOX_DIAG_ROW_COUNT][TBOX_DIAG_COLUMN_COUNT];
}DoubleQueue;

/************************函数接口***************************/
queueErrorCode CreateQueue(RxQueue *pQueue);
queueErrorCode IsQueueFull(RxQueue *pQueue, UINT16 writeLen);
queueErrorCode SystemApiSendMessage(RxQueue *pQueue, Msg msg);
queueErrorCode SystemApiReceiveMessage(RxQueue *pQueue, Msg*msg, UINT8 *tempBuf, UINT32 waitTime);
queueErrorCode CreateDoubleQueue(DoubleQueue* pDoubleQueue);
queueErrorCode SystemApiWriteRowData(DoubleQueue* pDoubleQueue, UINT8* buf);
queueErrorCode SystemApiReadRowData(DoubleQueue* pDoubleQueue, UINT8* buf);

#endif
