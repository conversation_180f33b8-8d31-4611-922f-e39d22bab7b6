/*
     Task.h
描述：定义TASK头文件ID, 任务信息结构体
作者：廖勇刚
时间：2016.6.28
*/
#ifndef  _TASK_H_
#define  _TASK_H_


#include "event.h"
#include "queue.h"
#include "NvApi.h"
#include "IpcApi.h"
#include "gpio.h"
#include "HrApi.h"
#include "Pwm.h"


/************************宏定义***************************/
#define     QUEUE_PM_MAX_LEN                         50
#define     QUEUE_CAN_MAX_LEN                        128
#define     QUEUE_LED_MAX_LEN                        40
#define     QUEUE_LOG_MAX_LEN                        40
#define     QUEUE_IPC_TX_MAX_LEN                     512
#define     QUEUE_DIAG_MAX_LEN                       40
#define     QUEUE_BAT_MAX_LEN                        40
#define     QUEUE_UPDATE_MAX_LEN                     100
#define     QUEUE_BT_MAX_LEN                         512
#define     QUEUE_IPC_RX_MAX_LEN                     512


#define    TASK_MIN_TICK_TIME                        1                 /*Task Period Tick*/
#define    TASK_CAN_PERIOD_TIME                      10
#define    TASK_LEDGPIO_PERIOD_TIME                  101
#define    TASK_IPC_PERIOD_TIME                      102               /*错开一点时间*/
#define    TASK_SELFCHECK_PERIOD_TIME                103               /*错开一点时间*/
#define    TASK_LOG_PERIOD_TIME                      204
#define    TASK_PM_PERIOD_TIME                       1001
#define    TASK_UPGRADE_PERIOD_TIME                  1002              /*错开一点时间*/
#define    TASK_BLE_PERIOD_TIME                      1003              /*错开一点时间*/
#define    TASK_BAT_PERIOD_TIME                      2004

#define    TASK_SCHEDULE_TIMEOUNT_MAX                55
#define    TASK_SCHEDULE_TIMEOUNT_READYRESET         35


/************************数据结构定义***************************/
//定义任务函数指针
typedef  void(*pTaskHook)(void);
typedef  void(*pTaskFunctionHook)(Msg msg);
typedef  void(*pTmrCallBack)(void); 


typedef enum
{
    QUEUE_ID_PM_TASK = 0,
    QUEUE_ID_CAN_TASK,
    QUEUE_ID_LED_TASK,
    QUEUE_ID_LOG_TASK,
    QUEUE_ID_IPC_TX_TASK,
    QUEUE_ID_DIAG_TASK,
    QUEUE_ID_BAT_TASK,
    QUEUE_ID_UPDATE_TASK,
    QUEUE_ID_BT_TASK,
    QUEUE_ID_IPC_RX_TASK,
    QUEUE_ID_MAX,
} QueueId;


typedef enum
{
    TASK_ID_PM = 0,
    TASK_ID_CAN,
    TASK_ID_LED,
    TASK_ID_LOG,
    TASK_ID_IPC,
    TASK_ID_DIAG,
    TASK_ID_BAT,
    TASK_ID_UPDATE,
    TASK_ID_BT,
    TASK_ID_MAX,
}TaskId;


typedef enum
{
    TMR_ID_PM = 0,
    TMR_ID_CAN,
    TMR_ID_LED,
    TMR_ID_LOG,
    TMR_ID_IPC,
    TMR_ID_DIAG,
    TMR_ID_BAT,
    TMR_ID_UPDATE,
    TMR_ID_BT,
    TMR_ID_MAX,
}TmrId;

typedef enum
{
    TASK_EXEC_INACTIVE_STATUS = 0x00,
    TASK_EXEC_ACTIVE_STATUS,
}TaskExecStatus;

typedef enum
{
    TASK_ONCE_TMR = 0x00,
    TASK_PERIOD_TMR,
}TaskTmrType;


typedef struct tmrNode
{
    TaskTmrType          taskTmrType;
    TaskExecStatus       taskExecStatus;
    UINT32               taskScheduleTimeoutCnt;
    UINT32               taskTmrCurrentTickCnt;
    UINT32               taskTmrInitValue;
    pTmrCallBack         taskTmrCallBack;
}TmrNode;



//任务结构体
typedef struct taskInfo
{
    TaskId                 id;                       //任务ID
    pTaskHook              TaskInitHook;             //任务第一次初始化回调函数
    pTaskHook              TaskPostInitHook;         //任务第二次初始化回调函数 (可选,比如有些任务的初始化需要依赖其他任务)
    pTaskFunctionHook      TaskFunctionHook;         //任务主函数执行函数
    TmrNode*               TaskTmrNode;              //任务周期性节点
    UINT8                  eventMaskVaule;           //任务接收事件屏蔽位
    UINT32                 TaskPeriodCheckTick;      //任务周期运行变量
    RxQueue*               rxQueue;                  //任务主功能函数接收数据缓存空间(这里用循环队列实现)        
}TaskInfo;


typedef struct eventInfo
{
    UINT32 event;
    pTaskFunctionHook TaskFunctionHook;
}EventInfo;


/************************函数接口***************************/
void TaskInit(void);
void TaskMainFunction(void);
void McuPowerOnLog(void);
void McuOsTickHandle(void);
int McuScheduleTimeOutReInit(TaskId taskId, UINT32 valueTime);

#endif

