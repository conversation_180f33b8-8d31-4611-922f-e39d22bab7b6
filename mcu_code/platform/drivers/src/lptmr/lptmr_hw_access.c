/*
 * Copyright 2020-2022 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may only
 * be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

/*!
 * @file lptmr_hw_access.c
 *
 */

#include "lptmr_hw_access.h"

/*******************************************************************************
 * Code
 ******************************************************************************/

/*FUNCTION**********************************************************************
 *
 * Function Name : lpTMR_Init
 * Description   : This function configures all registers of the LPTMR instance to reset value.
 *
 *END**************************************************************************/
void lpTMR_Init(lpTMR_Type* const base)
{
    DEV_ASSERT(base != NULL);

    /* First, disable the module so we can write the registers */
    uint32_t tmp = base->CTRL;
    tmp &= ~(lpTMR_CTRL_EN_MASK);
    tmp |= lpTMR_CTRL_EN(0u);
    base->CTRL = tmp;

    base->CTRL = lpTMR_CTRL_EN(0u)      | \
                 lpTMR_CTRL_MODE(0u)    | \
                 lpTMR_CTRL_TMODE(0u)     | \
                 lpTMR_CTRL_PINPOL(0u)  | \
                 lpTMR_CTRL_PINSEL(0u);
    base->STS = lpTMR_STS_CCF_MASK;
    base->DIE = lpTMR_DIE_DMAEN(0u)     | \
                lpTMR_DIE_IE(0u);

    base->PRS = lpTMR_PRS_BYPASS(0u)   | \
                lpTMR_PRS_PRES(0u);

    base->CMP = lpTMR_CMP_CMP(0u);
}
/*******************************************************************************
 * EOF
 ******************************************************************************/
