/*!
@defgroup lptmr Low Power Timer (lpTMR)
@details
The YTMicro SDK provides Peripheral Drivers for the Low Power Timer (LPTMR) module of YTMicro SDK devices.

The lpTMR is a configurable 16-bit counter that can operate in two functional
modes:
    - Timer mode with selectable prescaler and clock source (periodic or free-running).
    - Pulse-Counter mode, with configurable glitch filter, that can count events (internal or external)
 */
