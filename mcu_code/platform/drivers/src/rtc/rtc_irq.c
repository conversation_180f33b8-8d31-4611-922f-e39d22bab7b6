/*
 * Copyright 2020-2022 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may
 * only be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

#include "rtc_driver.h"

void RTC_IRQHandler(void);

#if FEATURE_RTC_HAS_SEPARATE_SECOND_IRQ

void RTC_Seconds_IRQHandler(void);

#endif

void RTC_IRQHandler(void)
{
    RTC_DRV_IRQHandler(0U);
#if (FEATURE_RTC_HAS_SEPARATE_SECOND_IRQ == 0)
    RTC_DRV_SecondsIRQHandler(0U);
#endif
}

#if FEATURE_RTC_HAS_SEPARATE_SECOND_IRQ

void RTC_Seconds_IRQHandler(void)
{
    RTC_DRV_SecondsIRQHandler(0U);
}

#endif
