/*
 * Copyright 2020-2022 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may
 * only be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

/*!
 * @file hcu_irq.c
 */

#include "device_registers.h"
#include "hcu_driver.h"

/*!
 * @addtogroup hcu_driver_v1
 * @{
 */

/*******************************************************************************
 * Variables
 ******************************************************************************/

/*******************************************************************************
 * Code
 ******************************************************************************/

#ifdef HCU

/*!
 * @brief This function is the implementation of HCU handler named in startup code.
 *
 * It passes the instance to the shared HCU IRQ handler.
 */
void HCU_IRQHandler(void);

void HCU_IRQHandler(void)
{
    HCU_DRV_IRQHandler();
}

#endif
/*******************************************************************************
 * EOF
 ******************************************************************************/

