/*
 * Copyright 2020-2022 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may
 * only be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

#ifndef FLEXCAN_IRQ_H
#define FLEXCAN_IRQ_H

#include "device_registers.h"

/*!
 * @brief Interrupt handler for a FlexCAN instance.
 *
 * @param   instance        The FlexCAN instance number.
 * @param   mb_idx_start    The FlexCAN Start MB index.
 * @param   mb_idx_end      The FlexCAN End MB index.
 */
void FLEXCAN_IRQHandler(uint8_t instance, uint8_t mb_idx_start, uint8_t mb_idx_end);

/*!
 * @brief Error interrupt handler for a FlexCAN instance.
 *
 * @param   instance    The FlexCAN instance number.
 */
void FLEXCAN_Error_IRQHandler(uint8_t instance);

/*!
 * @brief Error interrupt handler for a FlexCAN instance.
 *
 * @param   instance    The FlexCAN instance number.
 */
void FLEXCAN_BusOff_IRQHandler(uint8_t instance);

#if FEATURE_CAN_HAS_WAKE_UP_IRQ

/*!
 * @brief Wake up handler for a FlexCAN instance.
 *
 * @param   instance    The FlexCAN instance number.
 */
void FLEXCAN_WakeUpHandler(uint8_t instance);

#endif /* FEATURE_CAN_HAS_WAKE_UP_IRQ */

#if defined (YTM32B1L_SERIES)

/*******************************************************************************
 * Default interrupt handlers signatures
 ******************************************************************************/
void CAN0_ORed_0_15_MB_IRQHandler(void);
void CAN0_ORed_16_31_MB_IRQHandler(void);

#elif (defined (YTM32B1M_SERIES) || defined (YTM32B1H_SERIES))
/*******************************************************************************
 * Default interrupt handlers signatures
 ******************************************************************************/
void CAN0_ORed_IRQHandler(void);                                        /* CAN0_ORed Handler*/
void CAN0_Error_IRQHandler(void);                                       /* CAN0_Error Handler*/
void CAN0_Wake_Up_IRQHandler(void);                                     /* CAN0_Wake_Up Handler*/
void CAN0_ORed_0_15_MB_IRQHandler(void);                                /* CAN0_ORed_0_15_MB Handler*/
void CAN0_ORed_16_31_MB_IRQHandler(void);                               /* CAN0_ORed_16_31_MB Handler*/
void CAN0_ORed_32_47_MB_IRQHandler(void);                               /* CAN0_ORed_32_47_MB Handler*/
void CAN0_ORed_48_63_MB_IRQHandler(void);                               /* CAN0_ORed_48_63_MB Handler*/
void CAN1_ORed_IRQHandler(void);                                        /* CAN1_ORed Handler*/
void CAN1_Error_IRQHandler(void);                                       /* CAN1_Error Handler*/
void CAN1_Wake_Up_IRQHandler(void);                                     /* CAN1_Wake_Up Handler*/
void CAN1_ORed_0_15_MB_IRQHandler(void);                                /* CAN1_ORed_0_15_MB Handler*/
void CAN1_ORed_16_31_MB_IRQHandler(void);                               /* CAN1_ORed_16_31_MB Handler*/
void CAN1_ORed_32_47_MB_IRQHandler(void);                               /* CAN1_ORed_32_47_MB Handler*/
void CAN1_ORed_48_63_MB_IRQHandler(void);                               /* CAN1_ORed_48_63_MB Handler*/
void CAN2_ORed_IRQHandler(void);                                        /* CAN2_ORed Handler*/
void CAN2_Error_IRQHandler(void);                                       /* CAN2_Error Handler*/
void CAN2_Wake_Up_IRQHandler(void);                                     /* CAN2_Wake_Up Handler*/
void CAN2_ORed_0_15_MB_IRQHandler(void);                                /* CAN2_ORed_0_15_MB Handler*/
void CAN2_ORed_16_31_MB_IRQHandler(void);                               /* CAN2_ORed_16_31_MB Handler*/
void CAN2_ORed_32_47_MB_IRQHandler(void);                               /* CAN2_ORed_32_47_MB Handler*/
void CAN2_ORed_48_63_MB_IRQHandler(void);                               /* CAN2_ORed_48_63_MB Handler*/
void CAN3_ORed_IRQHandler(void);                                        /* CAN3_ORed Handler*/
void CAN3_Error_IRQHandler(void);                                       /* CAN3_Error Handler*/
void CAN3_Wake_Up_IRQHandler(void);                                     /* CAN3_Wake_Up Handler*/
void CAN3_ORed_0_15_MB_IRQHandler(void);                                /* CAN3_ORed_0_15_MB Handler*/
void CAN3_ORed_16_31_MB_IRQHandler(void);                               /* CAN3_ORed_16_31_MB Handler*/
void CAN4_ORed_IRQHandler(void);                                        /* CAN4_ORed Handler*/
void CAN4_Error_IRQHandler(void);                                       /* CAN4_Error Handler*/
void CAN4_Wake_Up_IRQHandler(void);                                     /* CAN4_Wake_Up Handler*/
void CAN4_ORed_0_15_MB_IRQHandler(void);                                /* CAN4_ORed_0_15_MB Handler*/
void CAN4_ORed_16_31_MB_IRQHandler(void);                               /* CAN4_ORed_16_31_MB Handler*/
void CAN5_ORed_IRQHandler(void);                                        /* CAN5_ORed Handler*/
void CAN5_Error_IRQHandler(void);                                       /* CAN5_Error Handler*/
void CAN5_Wake_Up_IRQHandler(void);                                     /* CAN5_Wake_Up Handler*/
void CAN5_ORed_0_15_MB_IRQHandler(void);                                /* CAN5_ORed_0_15_MB Handler*/
void CAN5_ORed_16_31_MB_IRQHandler(void);                               /* CAN5_ORed_16_31_MB Handler*/
#else
    #error "No valid CPU defined!"
#endif

#endif /* FLEXCAN_IRQ_H */

/*******************************************************************************
 * EOF
 ******************************************************************************/
