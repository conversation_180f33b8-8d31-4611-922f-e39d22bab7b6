# 代码驱动基于开沃海外项目,业务基于前晨项目

## 项目介绍

## 代码风格统一

使用`Clang-Format`进行代码风格统一，配置文件为`.clang-format`，需配合插件`Clang-Format`使用。

需要注意：
由于我们修改了YCT自动生成的代码中的platform和board中的相关配置，如果用YCT工具重新生成了代码，需要手动比较上一个版本和这次自动生成的部分代码。
一下是需要关注的文件列表：
1. Power_config.c
2  power_config.h
3. Fee_config.c 
4. fee_config.h
5. linflexd_uart_driver.c    （老代码中的1129行，需要屏蔽timeout error中断）
6. Spi_master_driver.c       （需要在SPI_SetPinConfigMode中交换MISO和MOSI的管脚配置  改成SPI_SDI_OUT_SDO_IN）
7. Can_config.c                 （关注这行： is_rx_fifo_needed=true） 

## 关于时代新安的Mcu移植

### 移植进度

    1.airbag模块，修改格式屏蔽删除无用代码。业务函数依赖外部输入，且airbug模块全面覆盖前晨的airbag模块，所以未直接移植前晨代码。
    2.bat模块，修改格式屏蔽删除无用代码。主要是功能函数，功能函数接口，所以未移植前晨代码。
    3.bt模块，修改格式屏蔽删除无用代码。bt模块全面覆盖前晨的bt模块，变化主要为适配硬件接口、新日志、变量名称等，所以未移植前晨代码。
    4.can模块，大量移植前晨业务，保留开沃硬件适配的功能代码。已调试保障基本can传输业务。大量业务接口暂时屏蔽。can模块包括can报文收发及can透传，spi模块等。
    5.CANDataUpload模块，开沃拆分模块，代码位于can模块。
    6.cgw模块，前晨网关模块，目前mcu暂不支持网关，故未移植
    7.Common模块，开沃拆分模块，执行一些模块的业务函数，代码位于can、pm、diag等模块中
    8.Diag模块，DiagApi.c中业务移植前晨diag模块，硬件相关的保留开沃代码。仅编译通过调试基础服务，子服务相关的业务未调试。
    9.ipc模块，移植前晨ipc业务，整合保留开沃功能代码。暂未调试
    10.led模块，led主要为功能模块开沃代码覆盖前晨业务代码范围，未移植前晨业务代码，仅修改格式屏蔽删除无用代码。
    11.log模块，仅修改格式，无业务代码。
    12.navigate模块，前晨代码为空，故仅修改格式，保留开沃代码。
    13.pm模块，保留开沃代码，整合合入前晨代码，修改格式。
    14.update模块，功能代码及业务代码与前晨一致，仅变量名不一致，保留开沃代码，修改格式
    15.xcall模块，仅开沃代码，保留。 
功能模块移植完毕，业务模块待调试修改
以下模块保留原因，cgw为前晨独有模块，开沃硬件不支持。xcall为开沃独有模块，保留。CANDataUpload，common模块开沃独有，原属于can模块，业务代码已移植，功能代码保留。

