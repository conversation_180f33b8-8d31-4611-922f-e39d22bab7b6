# Secure Boot （云途安全启动）

安全启动（Secure Boot）流程确保了系统在启动时验证启动代码的完整性和真实性，防止未授权的代码执行。这个过程的安全性在于使用加密技术（如AES加密和CMAC消息认证）来验证代码的签名。如果验证失败，系统将不会启动错误的代码，从而保护系统不受恶意软件的影响。

## 文档

~~云途官方文档-应用笔记：[点击下载](uploads/d49b571165e26798f8148566767c4bae/AN_0061_YTM32B1ME0_Secure_Boot_Guide_zh_review_.pdf)~~ 该文档过老，已弃用

云途官方文档-SDK使用介绍：在项目的svn目录`/svn/Smarthardware/05_项目文档/40_开沃海外T-Box信息安全项目/01_工程/04_软件设计/01_文档输入/云途MCU/SDK应用_Secure_Boot 模块配置及应用.pdf`

(该文档以`YTM32B1HA01`型号为例介绍,操作有一定出入,尽量以本文档为准)

## 基本操作介绍

开沃海外版本MCU使用的云途芯片，型号为`YTM32B1ME05G0MLQT`，该型号为软件SecureBoot，即在用户程序区分出⼀块区域⽤于放置SecureBoot固件。

云途不提供安全启动的源码，所以实际拥有安全启动的应用程序是通过上位机生成的,具体操作为:

1. 我们正常编写开发的程序编译得到hex后缀的文件
2. 点击YT Config Tool上位机程序外设选项卡中的SecureBoot选项卡下方的Upload或直接拖拽到Upload处
3. 在上位机内配置我们安全启动需要的密钥，需要加密的扇区的密钥等信息完毕后，点击下方的`Generate`(不是生成项目文件，而是外设SecureBoot选项页面最下方的Generate)，将安全启动程序、我们带验签结果的应用程序等信息打包好后生成一个新的`xxx.bin`文件，最终刷写此文件即可,注意偏移量为0,因为其已经包含boot程序。

关键点在于:

1. 配置用于加密

### 基于AES的CMAC算法

介绍:采用AES加密算法，使用密钥K，对明文P进行加密，得到的密文C，作为明文P的认证码，和明文P一起传输给接收方。接收方收到后，再使用自己的密钥，对明文再做一次AES加密，生成新的认证码，与接收到的发送方的认证码进行对比验证。如果相等，说明明文没有被篡改，接收方就可以接收明文并处理；如果不相等，说明明文被篡改，数据不安全，则丢弃.

### 验签

所以,针对本项目来说,上位机程序会使用我们的应用程序进行验签,并放置在一定区域,而SecureBoot程序则会在启动的时候使用预先配置好的密钥再去生成一次签名,对比两次签名是否相等,则是验签的过程,验签配置的密钥实际上是Section的密钥

## 安全启动步骤

1. 读取 REGFILE->REG0 内容，确认 TAG 不⼀致，则进⼊ Normal Boot 模式
2. 解析 BVT(Boot Vector Table)，确认 BVT 信息是否有效（BVT 详细内容在下⽂会介绍），若 BVT⾮法则程序停留在 ROM 内，拒绝跳转⾄App，否则进⼊下⼀步
3. 确认 Secure Boot 是否使能，若未使能，则不对⽤⼾程序区与数据区作验签，直接跳转⾄`步骤8`，否则进⼊下⼀步
4. 确认 Secure Boot Group 是否有效（Secure Boot Group 详细内容在下⽂会介绍），若⾮法则跳转⾄`步骤8`，否则进⼊下⼀步
5. 确认 Secure Boot Section 配置信息是否被加密（Secure Boot Section 详细内容在下⽂会介
绍），若扇区配置信息被加密，则先对扇区信息进⾏解密（AES-ECB），随后进⼊下⼀步
6. 确认 Secure Boot Section 是否有效，若⾮法则跳转⾄`步骤8`，否则进⼊下⼀步
7. 根据扇区配置信息⾥的内容，对该扇区所保护的区域进⾏验签（AES-CMAC），若验签失败，则跳转⾄`步骤8`；若验签成功，则确认是否所有扇区都验签完成，若所有扇区均验签成功，则跳转⾄`步骤9`
8. 确认是否严格执⾏安全启动，若严格执⾏安全启动，则程序停留在 ROM 内，拒绝跳转⾄App；若⾮严格执⾏安全启动，则禁⽌⽤⼾程序加载 HCU 硬件密钥（HCU_NVR），随后跳转⾄`步骤9`
9. 确认是否需要进⾏内核⾃测试（Core Test），若需要则进⾏内核⾃测试，否则跳转⾄下⼀步
10. 根据 BVT 信息配置 WDG
11. 根据 BVT 信息配置获取应⽤程序的跳转地址
12. 跳转⾄⽤⼾的应⽤程序

注: 本项目默认启用了严格模式

## 相关配置

### BVT

BVT（Boot Vector Table）是 Secure Boot 的配置信息区域，地址不同芯片略有差异，本项目芯片该配置地址为0x0007F800和0x000FF800。Secure Boot 程序会检测这两个位置是否有合法的 BVT 内容。

### BCW

BCW（Boot Config Word）是 BVT 内的⼀个 Word，其中每个⽐特都有其独特意义。

### Secure Boot Group

Secure Boot 配置通过 Secure Boot 分组配置与Secure Boot 扇区配置来实现，最多可⽀持8块Secure Boot 扇区配置。

指向Secure Boot 分组配置信息的地址存储于 BVT->Secure_Boot_Group_Config_Address(0x08h)
中，细节如下所⽰：

1. 分组信息标志: 0xACBEEFDD (4B)
2. Secure Boot 扇区个数(SBSN)，最多⽀持8组 (1B)
3. 是否加密扇区配置信息 (1B)
4. AES Key 的类别 (1B)
5. ⽤于解密扇区配置信息的密钥索引 (1B)
6. 指向Secure Boot 扇区配置信息的地址 (4B*SBSN)

### Secure Boot Section

Secure Boot 的扇区配置信息需要16 字节对⻬，具体信息如下所⽰：

1. 扇区信息标志：0x5AA5 (2B)
2. AES-128/192/256 密钥类型选择 (1B)
3. ⽤于校验CMAC 的密钥索引 (1B)
4. 需要进⾏验签的Flash 起始地址 (4B)
5. 需要进⾏验签的数据⻓度 (4B)
6. 验签结果的存储地址 (4B)

### 各配置信息关系图

BVT 中包含 Secure Boot Group 的物理地址，Secure Boot Group 内有 Secure Boot Section 的物
理地址，Secure Boot Section 有 CMAC 验签结果的物理地址，这些配置信息如下图所⽰。

![配置信息关系图](image.png)

此外，下图还展⽰了与用户应⽤程序的关系图，供参考理解。

![配置信息与用户应⽤程序的关系图](image-1.png)

图的配置由于Gitlab设置上传不了,请参考SVN里的文档中的图片.

### HCU 密钥

在 Secure Boot 中，主要⽤到了 AES-ECB ⽤于解密 Secure Boot Section 配置信息，以及 AES-CMAC ⽤于对⽤⼾程序数据进⾏验签。其中使⽤的密钥均存储在 HCU_NVR 中，该区域⽆法被任何用户读取，只能通过 HCU 模块进⾏加载密钥，且用户⽆法在 HCU 模块中读取该密钥。
