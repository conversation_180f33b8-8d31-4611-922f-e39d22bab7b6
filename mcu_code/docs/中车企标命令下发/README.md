# 企标远控外发报文

## 远程锁车

锁车报文:
ID 0x180109A7
格式 Intel
发送周期 0.5S
Byte1（数据从 Byte0 开始） = 0xAA，表示上锁请求
Byte1（数据从 Byte0 开始） = 0x55，表示解锁请求
Byte1（数据从 Byte0 开始） = 0xFF，表示远程终端不请求上锁和解锁指令

持续发送并等待整车控制器反馈数据（停止发送）
T-Box需要保存锁车状态，当T-Box重启或复位后，需要继续发送该命令。

## 远程限速

限速报文:
ID 0x180109A7
格式 Intel
发送周期 0.5S
Byte2(数据从 Byte0 开始) = 0：未远程限制
Byte2(数据从 Byte0 开始) = 1：开钥匙收到限速请求
Byte2(数据从 Byte0 开始) = 2：关钥匙收到限速请求
Byte2(数据从 Byte0 开始) = 3：处于远程限速状态
Byte2(数据从 Byte0 开始) = 4：开钥匙收到解除限速请求
Byte2(数据从 Byte0 开始) = 5：关钥匙收到解除
T-Box需要保存限速状态，当T-Box重启或复位后，需要继续发送该命令。

## 天气下发

平台可下发命令0x306，让T-Box转发天气信号，最终由仪表进行显示。
1.T-Box接收到平台下发的指令后，根据指令内容解析出CANID及参数内容；
2.将解析出的CAN数据发送至整车CAN2网络（T-Box CAN2与整车CAN2相连接）；
3.T-Box持续发送，发送频率为1次/min；
4.下发命令中ID需一致，否则无法更新发送数据。

天气报文：
ID：0x181FA7A3
格式 Intel
发送周期 1min
该命令每次上电或是休眠唤醒后，先不外发，等收到平台后再外发。

## 驾驶行为分析

1.T-Box接收到平台下发的指令，根据指令内容解析出CANID及参数内容（解析出来的数据列表有6条数据，CANID相同）；
2.将解析出的CAN数据至整车CAN2网络（T-Box CAN2与整车CAN2相连接）；
3.发送规则：6条CAN数据发送间隔是10ms，整个数据连续发送完成周期为1s；循环发送周期为1min；

报文:
ID 0x180EA7A3
格式 Intel
6条CAN数据发送间隔是10ms，整个数据连续发送完成周期为1s；循环发送周期为1min；
仪表上电后即发送
