#include "LogApi.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "task.h"
#include <string.h>

#include "LedTask.h"
#include "AppTask.h"
#include "IpcApi.h"
//extern int counter;
TaskHandle_t LEDTask_Handle = NULL;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartLEDTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)LEDTask,
                         "LEDTask",
                         LED_TASK_STACK_SIZE,
                         NULL,
                         LED_TASK_PRIORITY,
                         &LEDTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the LED Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the LED Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void LEDTask(void* param)
{
    int counter = 0;
    Msg msg = {0};
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int id = TASK_ID_LED;
    LedTaskInitHook();
    LedTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "LED Task entered with counter = %d. \r\n", counter);
    while (1)
    {
        GpioControlPeriodNotifyFunction(msg);
        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_LEDGPIO_PERIOD_TIME);
        if (QUEUE_NO_ERROR == errorCode)
        {
            
            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && 
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
                SystemApiLogPrintf(LOG_WARING_OUTPUT, "LED id is %d, event is 0x%x\r\n", id, msg.event);
                continue;
            }

            if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            {
                g_taskInfoMap[id].TaskFunctionHook(msg);
            }

        }

        vTaskDelay(TASK_LEDGPIO_PERIOD_TIME);
        counter ++;
        if(counter % 100 == 0)
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "LED cnt=%d\r\n", counter);
        }
    }
}


/*************************************************
函数名称: LedTaskInitHook
函数功能: LED任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LedTaskInitHook(void)
{
    GpioLedControlFirstInit();
}

/*************************************************
函数名称: LedTaskPostInitHook
函数功能: LED任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LedTaskPostInitHook(void)
{

}

/*************************************************
函数名称: LedTaskPeriodHook
函数功能: LED任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LedTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_GPIO_CONTROL_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_LED, msg);
}

/*************************************************
函数名称: LedTaskFunctionHook
函数功能: LED任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LedTaskFunctionHook(Msg msg)
{
    LedEventFunction(msg);
}