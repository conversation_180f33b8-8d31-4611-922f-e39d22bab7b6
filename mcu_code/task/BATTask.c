#include <stdint.h>
#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "AppTask.h"

#include <string.h>

#include "queue.h"
#include "BatTask.h"
#include "BatApi.h"
#include "IpcApi.h"
#include "LogApi.h"

//extern int counter;
TaskHandle_t BATTask_Handle = NULL;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartBATTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)BATTask,
                         "BATTask",
                         BAT_TASK_STACK_SIZE,
                         NULL,
                         BAT_TASK_PRIORITY,
                         &BATTask_Handle);
    if (pdFAIL == result)
    {
       SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the BAT Task failed.\r\n");
    }
    else
    {
       SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the BAT Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void BATTask(void* param)
{
    TaskId id = TASK_ID_BAT;
    Msg msg;
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int counter = 0;
    BatTaskInitHook();
    BatTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BATT entered with counter = %d. \r\n", counter);
    while (1)
    {
        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_BAT_PERIOD_TIME);
        if (QUEUE_NO_ERROR == errorCode)
        {
            //接收的事件类型非本任务或公共事件
            uint8_t tmp_flag = (MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule));

            if (!tmp_flag) 
            {
                if (NULL != g_taskInfoMap[id].TaskFunctionHook)
                {
                    g_taskInfoMap[id].TaskFunctionHook(msg);
                }
            }
            else
            {
                //接收的事件类型非本任务或公共事件
                #if(LOG_SWITCH_CONFIG_WARING == LOG_SWITCH_ON)
                //SystemApiLogPrintf(LOG_WARING_OUTPUT, "TaskMainFunction id is %d, event is 0x%x\r\n", id, msg.event);
                #endif
            }
        }

        vTaskDelay(TASK_BAT_PERIOD_TIME);
        //BatTaskPeriodHook();
        BatPeriodNotifyFunction(msg);
        
        if(counter++ %10 == 0)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "BATT cnt=%d. \r\n", counter);
        }
    }
}



/*************************************************
函数名称: BatTaskInitHook
函数功能: 内置电池任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BatTaskInitHook(void)
{
    BatPowerOnRamInit();
}

/*************************************************
函数名称: BatTaskPostInitHook
函数功能: 内置电池任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BatTaskPostInitHook(void)
{

}

/*************************************************
函数名称: BatTaskPeriodHook
函数功能: 内置电池任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BatTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_BAT_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_BAT, msg);
}

/*************************************************
函数名称: BatTaskFunctionHook
函数功能: 内置电池任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BatTaskFunctionHook(Msg msg)
{
    BatEventFunction(msg);
}

