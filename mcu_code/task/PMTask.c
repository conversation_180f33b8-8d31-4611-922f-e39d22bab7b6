#include "LogApi.h"
#include "event.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif

#include "task.h"
#include <string.h>

#include "AppTask.h"
#include "PmTask.h"
#include "wdg_driver.h"
#if defined(CAN_ENABLE_OSEKNM)
#include "OsekNm.h"
#elif defined(CAN_ENABLE_AUTOSAR_NM)
#include "CanNm.h"
#include "Nm.h"
#elif defined(CAN_ENABLE_NO_NM)
/* 无网管模式下不需要包含网络管理头文件 */
#else
/* 默认情况下包含AUTOSAR NM头文件 */
#include "CanNm.h"
#include "Nm.h"
#endif
#include "IpcApi.h"
#include "NvApi.h"

TaskHandle_t        PMTask_Handle = NULL;
extern TaskInfo     g_taskInfoMap[TASK_ID_MAX];
extern CommonInfo   g_commonInfo;
extern PmInfo       g_pmInfo;
extern UINT8        MainPowerExit_SleepWait;

void StartPMTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)PMTask,
                         "PMTask",
                         PM_TASK_STACK_SIZE,
                         NULL,
                         PM_TASK_PRIORITY,
                         &PMTask_Handle);
    if (pdPASS == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the PM Task succeed.\r\n");
    }
    else
    {   
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the PM Task failed.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void PMTask(void* param)
{
    TaskId id = TASK_ID_PM;
    Msg msg = {0};
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int counter = 0;
    
    PmPowerOnPrepare();
    
    INT_SYS_EnableIRQ(GPIOE_IRQn);
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "PM Task entered with counter=%d\r\n", counter);
    while (1)
    {
        //电源周期函数
        PmPeriodNotifyFunction(msg);

        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_PM_PERIOD_TIME);
        if (QUEUE_NO_ERROR == errorCode)
        {

            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) &&
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
                //SystemApiLogPrintf(LOG_WARING_OUTPUT, "Task MainFunction id is %d, event is 0x%x\r\n", id, msg.event);
                continue;
            }

            if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            {
                g_taskInfoMap[id].TaskFunctionHook(msg);
            }

        }
        vTaskDelay(TASK_PM_PERIOD_TIME);

        if(counter++ % (3 * PM_ONE_SECOND_TIME_COUNTER) == 0) //每3秒喂一次狗
        {
            WDG_DRV_Trigger(0);
        }
        if(counter % (5 * PM_ONE_SECOND_TIME_COUNTER) == 0)     //5秒打印一次日志
        {
            uint8_t status = 0;
            uint8_t mode = 0;
            uint8_t nmSleepReady = 0;

            #if defined(CAN_ENABLE_OSEKNM)
            /**
             * @brief OSEK NM模式下获取网管状态
             */
            status = OsekNm_GetCurrentState();
            nmSleepReady = (NM_STATE_PREPARE_BUS_SLEEP >= status) ? 1 : 0;
            #elif defined(CAN_ENABLE_AUTOSAR_NM)
            /**
             * @brief AUTOSAR NM模式下获取网管状态
             */
            CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
            nmSleepReady = (NM_STATE_PREPARE_BUS_SLEEP >= status) ? 1 : 0;
            #elif defined(CAN_ENABLE_NO_NM)
            /**
             * @brief 无网管模式下使用ACC状态判断
             */
            status = NM_STATE_BUS_SLEEP;
            nmSleepReady = (WORK_STATUS_INACTIVE == g_commonInfo.accStatus) ? 1 : 0;  /* 无网管模式下使用ACC状态判断 */
            #else
            /**
             * @brief 默认情况使用AUTOSAR NM
             */
            CanNm_GetState(NM_NETWORK_CHANNEL_ID, &status, &mode);
            nmSleepReady = (NM_STATE_PREPARE_BUS_SLEEP >= status) ? 1 : 0;
            #endif

            #if defined(CAN_ENABLE_NO_NM)
            SystemApiLogPrintf(LOG_INFO_OUTPUT,
                               "Sleep Conditions: PM:%u | TSP:%d IPC:%d CAN:%d ACC:%d\r\n",
                               g_pmInfo.workStatus,
                               (WORK_STATUS_INACTIVE == g_commonInfo.tspStatus) ? 1 : 0,
                               (HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus) ? 1 : 0,
                               (WORK_STATUS_INACTIVE == g_commonInfo.canStatus) ? 1 : 0,
                               nmSleepReady);
            #else
            SystemApiLogPrintf(LOG_INFO_OUTPUT,
                               "Sleep Conditions: PM:%u | TSP:%d IPC:%d CAN:%d NM:%d\r\n",
                               // "Sleep Conditions: PM:%u | TSP:%d UP:%d IPC:%d ARM:%d ECALL:%d CAN:%d NM:%d\r\n",
                               g_pmInfo.workStatus,
                               (WORK_STATUS_INACTIVE == g_commonInfo.tspStatus) ? 1 : 0,
//                               (WORK_STATUS_INACTIVE == g_commonInfo.upgradeStatus) ? 1 : 0,
                               (HANDSHAKE_SUCESS == g_commonInfo.handshakeStatus) ? 1 : 0,
                               // (ARM_CLINET_ONLINE == g_commonInfo.armClinetStatus) ? 1 : 0,
                               // (ECALL_STATUS_INACTIVE == g_commonInfo.ecallStatus) ? 1 : 0,
                               (WORK_STATUS_INACTIVE == g_commonInfo.canStatus) ? 1 : 0,
                               nmSleepReady);
            #endif
        }
    }
}



/*************************************************
函数名称: PmTaskInitHook
函数功能: 电源任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void PmTaskInitHook(void)
{
    //PmPowerOnPrepare();
}

/*************************************************
函数名称: PmTaskPostInitHook
函数功能: 电源任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void PmTaskPostInitHook(void)
{
    //PmPowerOnPreparePostInit();
}

/*************************************************
函数名称: PmTaskPeriodHook
函数功能: 电源任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void PmTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_PM_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_PM, msg);
}

/*************************************************
函数名称: PmTaskFunctionHook
函数功能: 电源任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void PmTaskFunctionHook(Msg msg)
{
    PmEventFunction(msg);
}
