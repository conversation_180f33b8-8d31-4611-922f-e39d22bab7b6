#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "FreeRTOS.h"
#include "task.h"
#include <string.h>

#include "LogTask.h"
#include "AppTask.h"
#include "IpcApi.h"
TaskHandle_t LOGTask_Handle = NULL;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartLOGTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)LOGTask,
                         "LOGTask",
                         LOG_TASK_STACK_SIZE,
                         NULL,
                         LOG_TASK_PRIORITY,
                         &LOGTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the LOG Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the LOG Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void LOGTask(void* param)
{
    TaskId id = TASK_ID_LOG;
    Msg msg;
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int counter = 0;
    LogTaskInitHook();
    LogTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "LOG entered counter = %d. \r\n", counter);
    while (1)
    {
        //LogTaskPeriodHook();
        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_LOG_PERIOD_TIME);
        if ((QUEUE_NO_ERROR == errorCode) && (msg.len > 0))
        {
            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && 
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
    #if(LOG_SWITCH_CONFIG_WARING == LOG_SWITCH_ON)
                //SystemApiLogPrintf(LOG_WARING_OUTPUT, "Log Task MainFunction id is %d, event is 0x%x\r\n", id, msg.event);
    #endif
                continue;
            }

            if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            {
                g_taskInfoMap[id].TaskFunctionHook(msg);
            }
        }
        else
        {
            vTaskDelay(TASK_LOG_PERIOD_TIME);
            LogPeriodNotifyFunction(msg);
            if(counter++ %100 == 0)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Log cnt=%u.\r\n", counter);
            }
        }
    }
}

/*************************************************
函数名称: LogTaskInitHook
函数功能: 日志任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LogTaskInitHook(void)
{
    LogPowerOnInit();
}

/*************************************************
函数名称: LogTaskPostInitHook
函数功能: 日志任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LogTaskPostInitHook(void)
{

}

/*************************************************
函数名称: LogTaskPeriodHook
函数功能: 日志任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LogTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_LOG_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_LOG, msg);
}

/*************************************************
函数名称: LogTaskFunctionHook
函数功能: 日志任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void LogTaskFunctionHook(Msg msg)
{
    LogEventFunction(msg);
}
