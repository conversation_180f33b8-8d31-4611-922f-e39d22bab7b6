#include "FreeRTOS.h"
#include "LogApi.h"
#include "event.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif

#include "task.h"
#include "queue.h"
#include "semphr.h"
#include <string.h>

#include "BtTask.h"
//#include "BtTask.h"
#include "AppTask.h"

#include "BtApi.h"
#include "BLELin.h"
#include "rlin30.h"

//extern int counter;
TaskHandle_t BTTask_Handle = NULL;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartBTTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)BTTask,
                         "BTTask",
                         BT_TASK_STACK_SIZE,
                         NULL,
                         BT_TASK_PRIORITY,
                         &BTTask_Handle);
    if (pdPASS == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the BT Task succeed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the BT Task failed.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void BTTask(void* param)
{
    TaskId id = TASK_ID_BT;
    Msg msg;
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int counter = 0;
    BtTaskInitHook();
    BtTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "BT Task entered with counter = %d. \r\n", counter);
    while (1)
    {
        //BtTaskPeriodHook();
        //
        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_BLE_PERIOD_TIME);
        if ((QUEUE_NO_ERROR == errorCode) && (msg.len > 0))
        {
            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && 
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
    #if(LOG_SWITCH_CONFIG_WARING == LOG_SWITCH_ON)
                //SystemApiLogPrintf(LOG_WARING_OUTPUT, "TaskMainFunction id is %d, event is 0x%x\r\n", id, msg.event);
    #endif
                continue;
            }

            //if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            //{
            //    g_taskInfoMap[id].TaskFunctionHook(msg);
            //}
            BtTaskFunctionHook(msg);
        }
        else
        {
            vTaskDelay(TASK_BLE_PERIOD_TIME);
            //BtDataPassServicesMainFunction();
            if(++counter % 50 == 0)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "BT cnt=%d. \r\n", counter);
            }
            if(++counter % 10 == 0)
            {
                BtModuleEventPeriodFunction();
            }
            
        }
    }
}


/************************外部全局变量****************************/







/*************************************************
函数名称: BtTaskInitHook
函数功能: 蓝牙任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BtTaskInitHook(void)
{
    BtModuleServiceInit();
    //to init the UART BLE to prepare the data transfer
    BtModuleDriverInit();
    
}

/*************************************************
函数名称: BtTaskPostInitHook
函数功能: 蓝牙任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BtTaskPostInitHook(void)
{
    BtModulePostInit();
}

/*************************************************
函数名称: BtTaskPeriodHook
函数功能: 蓝牙任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BtTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_BT_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_BT, msg);
}

/*************************************************
函数名称: BtTaskFunctionHook
函数功能: 蓝牙任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void BtTaskFunctionHook(Msg msg)
{
    BtEventFunction(msg);
}
