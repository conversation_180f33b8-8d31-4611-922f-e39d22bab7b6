#include "LogApi.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "FreeRTOS.h"
#include "task.h"

#include "IpcTask.h"
#include "AppTask.h"

TaskHandle_t IPCTask_Handle = NULL;

/************************外部全局变量****************************/
extern RxQueue   g_taskRxQueue[QUEUE_ID_MAX];
extern TaskInfo  g_taskInfoMap[TASK_ID_MAX];
extern IpcInfo   g_ipcInfo;
extern UINT32    g_osCurrentTickTime;

void StartIPCTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)IPCTask,
                         "IPCTask",
                         IPC_TASK_STACK_SIZE,
                         NULL,
                         IPC_TASK_PRIORITY,
                         &IPCTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the IPC Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the IPC Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void IPCTask(void* param)
{
    TaskId id = TASK_ID_IPC;
    Msg msg = {0};
    uint32_t counter = 0;
    IpcTaskInitHook();
    IpcTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC Task entered with counter = %d. \r\n", counter);

    while (1)
    {
        IpcTaskFunctionHook(msg);

        g_osCurrentTickTime = xTaskGetTickCount();

        vTaskDelay(TASK_IPC_PERIOD_TIME);
        counter++;
        if(counter % 10000 == 0)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "IPC TX=%d RX=%d Err=%d Lost=%dbyte ErrFlag=0x%X\r\n",
                               g_ipcInfo.TxCount, g_ipcInfo.RxCount, g_ipcInfo.uartErrCount, g_ipcInfo.uartLostBytes, g_ipcInfo.lastUartStatus);
        }
        if(counter % 100 == 0)
        {
            IpcPeriodFunction();
        }
        if(counter % 2 == 0)
        {
            IpcCheckUartRxStatus();
            IpcUartMainFunction();
        }
    }
}


/*************************************************
函数名称: IpcTaskInitHook
函数功能: IPC任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void IpcTaskInitHook(void)
{
    IpcInitRamData();
}

/*************************************************
函数名称: IpcTaskPostInitHook
函数功能: IPC任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void IpcTaskPostInitHook(void)
{

}

/*************************************************
函数名称: IpcTaskPeriodHook
函数功能: IPC任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void IpcTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_IPC_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemApiSendMessage(&g_taskRxQueue[QUEUE_ID_IPC_RX_TASK], msg);
}

/*************************************************
函数名称: IpcTaskFunctionHook
函数功能: IPC任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void IpcTaskFunctionHook(Msg msg)
{
    IpcFunctionCallback();
}

