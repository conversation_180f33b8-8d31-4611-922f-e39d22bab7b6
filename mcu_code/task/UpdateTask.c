#include "LogApi.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "task.h"
#include <string.h>

#include "UpdateTask.h"
#include "AppTask.h"
#include "IpcApi.h"

TaskHandle_t UPDATETask_Handle = NULL;
/************************外部全局变量****************************/
extern UpdateInfo  g_updateInfo;
extern CommonInfo  g_commonInfo;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartUpdateTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)UpdateTask,
                         "UpdateTask",
                         UPDATE_TASK_STACK_SIZE,
                         NULL,
                         UPDATE_TASK_PRIORITY,
                         &UPDATETask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the Update Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the Update Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void UpdateTask(void* param)
{
    int counter = 0;
    Msg msg;
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int id = TASK_ID_UPDATE;
    //UINT8  g_updateQueueBuf[QUEUE_UPDATE_MAX_LEN] = { 0 };
    //RxQueue* rxQueue = g_taskInfoMap[TASK_ID_UPDATE].rxQueue;                  //任务主功能函数接收数据缓存空间(这里用循环队列实现) 
    UpdateTaskInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Update Task entered with counter = %d. \r\n", counter);
    while (1)
    {
        UpdatePeriodNotifyFunction();

        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_UPGRADE_PERIOD_TIME);
        if (QUEUE_NO_ERROR == errorCode)
        {

            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && 
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
                //SystemApiLogPrintf(LOG_WARING_OUTPUT, "UpdateTask id is %d, event is 0x%x\r\n", id, msg.event);
                continue;
            }

            if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            {
                g_taskInfoMap[id].TaskFunctionHook(msg);
            }
        }
        vTaskDelay(TASK_UPGRADE_PERIOD_TIME);
        counter ++;
        if(counter % 10 == 0)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT,"Update task cnt=%d\r\n", counter);
        }
    }
}

/*************************************************
函数名称: UpdateTaskInitHook
函数功能: 升级任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskInitHook(void)
{
    g_updateInfo.status = UPDATE_STATUS_END;
    g_updateInfo.count = 0;
    g_commonInfo.updateConditionStatus = UPDATE_CONDITION_MEET;
}

/*************************************************
函数名称: UpdateTaskPostInitHook
函数功能: 升级任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskPostInitHook(void)
{

}

/*************************************************
函数名称: UpdateTaskPeriodHook
函数功能: 升级任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_UPDATA_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_UPDATE, msg);
}

/*************************************************
函数名称: UpdateTaskFunctionHook
函数功能: 升级任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void UpdateTaskFunctionHook(Msg msg)
{
    UpdateEventFunction(msg);
}
