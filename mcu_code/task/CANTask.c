
#include "LogApi.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#else
#define PRINTF printf
#endif

#include "task.h"
#include <string.h>
#include "CanTask.h"
#include "CanFtm.h"
#include "AppTask.h"
#include "CanApi.h"
#include "CanMsgApi.h"
#include "RemoteControlTask.h"

//extern int counter;
TaskHandle_t CANTask_Handle = NULL;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartCANTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)CANTask,
                         "CANTask",
                         CAN_TASK_STACK_SIZE,
                         NULL,
                         CAN_TASK_PRIORITY,
                         &CANTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the CAN Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the CAN Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void CANTask(void* param)
{
    TaskId id = TASK_ID_CAN;
    Msg msg = {0};
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int counter = 0;
    CanTaskInitHook();
    CanTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Can task enter loop\r\n");
    while (1)
    {
        CanPeriodNotifyFunction(msg);
        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_CAN_PERIOD_TIME);
        if (QUEUE_NO_ERROR == errorCode)
        {
            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && 
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
                //SystemApiLogPrintf(LOG_WARING_OUTPUT, "TaskMainFunction id is %d, event is 0x%x\r\n", id, msg.event);
                continue;
            }
            if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            {
                g_taskInfoMap[id].TaskFunctionHook(msg);
            }
        }

        vTaskDelay(TASK_CAN_PERIOD_TIME);

        counter++;
        if(counter % 10000 == 0)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "CAN cnt:%d\r\n",counter);
        }
    }

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "Can task End\r\n");
}


/*************************************************
函数名称: CanTaskInitHook
函数功能: CAN任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void CanTaskInitHook(void)
{
    CanLogInitBuf();
    FtmInitRamData();
    CanUdsInit();
    CanSendInfoInit();

    /* Initialize the Remote Control Task */
    RemoteControlTask_Init();
}

/*************************************************
函数名称: CanTaskPostInitHook
函数功能: CAN任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void CanTaskPostInitHook(void)
{

}

/*************************************************
函数名称: CanTaskPeriodHook
函数功能: CAN任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void CanTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_CAN_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_CAN, msg);
}

/*************************************************
函数名称: CanTaskFunctionHook
函数功能: CAN任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void CanTaskFunctionHook(Msg msg)
{
    CanEventFunction(msg);
}

