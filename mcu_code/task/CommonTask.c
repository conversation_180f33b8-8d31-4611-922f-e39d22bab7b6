#include "LogApi.h"
#include "event.h"
//#include "spi_slave_driver.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif

#include "task.h"

#include "CommonTask.h"
#include "CanApi.h"
#include "CanFtm.h"
#include "BtApi.h"
#include "BLELin.h"
#include "r_wdt.h"
//#include "r_can.h"
//#include "spi_slave_driver.h"
//#include "spi_config.h"
#include "CanMsgApi.h"
#include "airbag.h"
#include "RemoteControlTask.h"


extern  UINT32  g_osCurrentTickTime;
extern CommonInfo g_commonInfo;

TaskHandle_t CommonTask_Handle = NULL;

void StartCommonTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)CommonTask,
                         "CommonTask",
                         COMMON_TASK_STACK_SIZE,
                         NULL,
                         COMMON_TASK_PRIORITY,
                         &CommonTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,"create the Common Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,"create the Common Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

extern uint8_t g_canErrorCnt[4][32];
static void PrintfCanErrorCnt(void)
{
    uint32_t i = 0;
    uint32_t j = 0;

    for(i = 0; i < 4; i++)
    {
        for(j = 0; j < 32; j++)
        {
            if(g_canErrorCnt[i][j] > 0)
            {
                SystemApiLogPrintf(LOG_WARING_OUTPUT, "Can error for ch:%u, event:%d, error Count:%u\r\n", i, 0x01 << j, g_canErrorCnt[i][j]);
            }
        }
    }
}

void CommonTask(void* param)
{
    int counter = 0;

    SystemApiLogPrintf(LOG_INFO_OUTPUT,"Comm task entered with counter = %d. \r\n", counter);

    while (1)
    {
        CanPeriodUdsHandle();
        McuProcessCanBuferData();

        FtmMainRxMsgFunction();
        Can_BusOff_Main_Handler();
        BtDataPassServicesMainFunction();
        LinUpdatePeriodHandle();
        CanTransmitDataMcuOut();

        // 处理远控反馈消息
        RemoteControlTask_Process();

        McuClearWatchdog();

        vTaskDelay(1);
    #if (CAN_UPLOAD_TEST == 1)
        // CanLogSimulationTest();
        if(counter % 5000 == 0)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT,"Common called Can Log Simulation Test=%d. \r\n", counter);
        }
    #endif

        g_osCurrentTickTime = xTaskGetTickCount();
        counter ++;
        if(counter % 30000 == 0)
        {
            //EnsPeriodNotifyFuncion();
            SystemApiLogPrintf(LOG_INFO_OUTPUT,"Common cnt=%d\r\n", counter);
            PrintfCanErrorCnt();
        }
#if (AIRBAG_TEST == 1)
        airBagSigDisplay(counter);
#endif

    }
}

#if (AIRBAG_TEST == 1)
void airBagSigDisplay(uint32 counter)
{
    // UINT8  canChannnel = CAN_CHANNEL_4;
    // UINT32 canId       = 0x200;
    // UINT8  canLen      = 8;
    // UINT8  canBuf[8]      = {1,1,1,1,1,1,1,1};
    // if(counter % 100000 == 0)
    // {
    //     if(1 == canBuf[7])
    //     {
    //         canBuf[7] = 2;
    //     }
    //     else
    //     {
    //         canBuf[7] = 1;
    //     }
    // SystemApiLogPrintf(LOG_INFO_OUTPUT,"Common canBuf[7]=%d. \r\n", canBuf[7]);
    // }
    if(counter % 3000 == 0)
    {
        // MCUCanSendData(canChannnel, canId, canLen, canBuf);
        // to test the airbag check function
        EnsPeriodNotifyFuncion();
    }
}
#endif


/*************************************************
函数名称: CommonTaskInitHook
函数功能: Common任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期:2024/03/12
修改日期:
*************************************************/
void CommonTaskInitHook(void)
{

}

void CanLogSimulationTest(void)
{
    const UINT8 MAX_CAN_CHAN_NUM = 4;
    const UINT32 MAX_CAN_ID_NUM = 4;
    const UINT8 TEST_FREQUENCE = 2;

    UINT8 canDlc = 8;
    static UINT8 canChannel = 0;
    static UINT32 canID = 0x111;
    UINT8 canData[8] = {1,2,3,4,5,6,7,8};
    for(UINT8 index = 0; index < TEST_FREQUENCE; index ++)
    {
        CanLogWriteTxBuf((canChannel++)%MAX_CAN_CHAN_NUM, (canID++)%MAX_CAN_ID_NUM, canDlc, canData);
    }
}