#include "FreeRTOS.h"
#include "LogApi.h"
#include "event.h"
#include "status.h"
#ifndef WINDOWS_SIM
#include "spi_slave_driver.h"
#include "printf.h"
#endif

#include "task.h"
#include "queue.h"
#include "semphr.h"
#include <string.h>

#include "CANDataUploadTask.h"
#include "AppTask.h"
#include "PmApi.h"
#include "CanApi.h"
#include "CanFtm.h"
#include "BtApi.h"
#include "BLELin.h"
#include "r_wdt.h"


extern  UINT32  g_osCurrentTickTime;
extern CommonInfo g_commonInfo;
#include "BatApi.h"
#include "spi_slave_driver.h"
#include "spi_config.h"
#include "status.h"
#include "SystemApi.h"
#include "CanMsgApi.h"

#define DATA_TRANS_WAIT_TIME        5       //max waiting time ms for each data transfer blocking waiting

TaskHandle_t CANDataUploadTask_Handle = NULL;
//const UINT32 SPI_INST = 3;

//UINT8 g_spi_tx_buff[CAN_LOG_ARM_LEN] = {0};
//UINT8 g_spi_rx_buff[CAN_LOG_ARM_LEN] = {0};

void StartCANDataUploadTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)CANDataUploadTask,
                         "CANUpldTsk",
                         CAN_DATA_UPLOAD_TASK_STACK_SIZE,
                         NULL,
                         CAN_DATA_UPLOAD_TASK_PRIORITY,
                         &CANDataUploadTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,"create the CAN Data Upload Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT,"create the CAN Data Upload Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void CANDataUploadTask(void* param)
{
    UINT32 txTimeOutCounter = 0;
    UINT32 txSuccessCounter = 0;
    UINT32 txFailCounter   = 0;
    UINT32 noDataCounter  = 0;

    UINT32 counter = 0;

    UINT8 spi_tx_buff[CAN_LOG_ARM_LEN] = {0};

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "CAN Data Upload Task started.\r\n");

    while (1)
    {
        status_t status = STATUS_SUCCESS;

        if (true == CanLogReadBuf(spi_tx_buff))
        {
            status = CAN_DATA_TRANSFER(SPI_INST_HANDLE, spi_tx_buff, NULL, CAN_LOG_ARM_LEN, DATA_TRANS_WAIT_TIME);
            if (status == STATUS_SUCCESS)
            {
                txSuccessCounter++;
            }
            else if (status == STATUS_TIMEOUT)
            {
                txTimeOutCounter++;
                if (txTimeOutCounter == 1)
                {
                    #ifdef CAN_DATA_UPLOAD_MASTER_MODE
                    SPI_DRV_MasterDeinit(SPI_INST_HANDLE);
                    status = SPI_DRV_MasterInit(SPI_INST_HANDLE, &lpspi_MasterConfig_2Mbps_State, &lpspi_MasterConfig_2Mbps);
                    #else
                    SPI_DRV_SlaveDeinit(SPI_INST_HANDLE);
                    SPI_DRV_SlaveInit(SPI_INST_HANDLE, &lpspi_SlaveConfig0_State, &lpspi_SlaveConfig0);
                    #endif
                    SystemApiLogPrintf(LOG_ERROR_OUTPUT, "CAN Upload SPI Reinitialization (status=0x%X)\r\n",status);
                }
            }
            else
            {
                txFailCounter++;
            }
        }
        else
        {
            noDataCounter ++;
        }

        counter++;

        if (counter % 5000 == 0)
        {
            SystemApiLogPrintf(LOG_INFO_OUTPUT,
                               "CAN Upload TxSuccess=%u,TxFail=%u, TxTimeout=%u, NoData=%u, MinStackFree=%uByte\r\n",
                               txSuccessCounter, txFailCounter, txTimeOutCounter, noDataCounter, uxTaskGetStackHighWaterMark(NULL) * 4);
            if (txTimeOutCounter + txFailCounter >= 10)
            {
                #ifdef CAN_DATA_UPLOAD_MASTER_MODE
                SPI_DRV_MasterDeinit(SPI_INST_HANDLE);
                status = SPI_DRV_MasterInit(SPI_INST_HANDLE, &lpspi_MasterConfig_2Mbps_State, &lpspi_MasterConfig_2Mbps);
                #else
                SPI_DRV_SlaveDeinit(SPI_INST_HANDLE);
                SPI_DRV_SlaveInit(SPI_INST_HANDLE, &lpspi_SlaveConfig0_State, &lpspi_SlaveConfig0);
                #endif
                SystemApiLogPrintf(LOG_ERROR_OUTPUT, "CAN Upload SPI Reinitialization (status=0x%X)\r\n",status);
            }

            txSuccessCounter = 0;
            noDataCounter  = 0;
            txTimeOutCounter = 0;
            txFailCounter   = 0;
        }

        vTaskDelay(TASK_CAND_UPLOAD_PERIOD_TIME);
    }
}



/*************************************************
函数名称: CANDataUploadTaskInitHook
函数功能: CandDataUploadTask任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期:2024/03/14
修改日期:
*************************************************/
void CANDataUploadTaskInitHook(void)
{
    
}