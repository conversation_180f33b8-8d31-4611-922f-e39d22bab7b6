#include "DiagApi.h"
#include "LogApi.h"
#ifndef WINDOWS_SIM
#include "printf.h"
#endif
#include "task.h"
#include "queue.h"
#include "semphr.h"
#include <string.h>

#include "DiagTask.h"
#include "AppTask.h"
#include "IpcApi.h"
//extern int counter;
TaskHandle_t DIAGTask_Handle = NULL;
extern TaskInfo    g_taskInfoMap[TASK_ID_MAX];

void StartDIAGTask(void)
{
    BaseType_t result = pdPASS;
    //taskENTER_CRITICAL();
    result = xTaskCreate((TaskFunction_t)DIAGTask,
                         "DIAGTask",
                         DIAG_TASK_STACK_SIZE,
                         NULL,
                         DIAG_TASK_PRIORITY,
                         &DIAGTask_Handle);
    if (pdFAIL == result)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the DIAG Task failed.\r\n");
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "create the DIAG Task success.\r\n");
    }
    //taskEXIT_CRITICAL();

}

void DIAGTask(void* param)
{
    TaskId id = TASK_ID_DIAG;
    Msg msg = {0};
    queueErrorCode errorCode = QUEUE_NO_ERROR;
    UINT8 tempBuf[IPC_MESSAGE_MAX_LEN];
    int counter = 0;
    //RtcTime rtcTime = {0};
    
    DiagTaskInitHook();
    DiagTaskPostInitHook();
    SystemApiLogPrintf(LOG_INFO_OUTPUT, "DIAG Task entered with counter = %d. \r\n", counter);
    while (1)
    {
        DiagPeriodNotifyFunction(msg);
        //未接收到事件
        memset(tempBuf, 0x00, IPC_MESSAGE_MAX_LEN);
        errorCode = SystemApiReceiveMessage(g_taskInfoMap[id].rxQueue, &msg, tempBuf, TASK_SELFCHECK_PERIOD_TIME);
        if (QUEUE_NO_ERROR == errorCode)
        {
            //接收的事件类型非本任务或公共事件
            if ((MASK_VALUE_COMMON_EVENT != (UINT8)(msg.event >> 12)) && 
                (g_taskInfoMap[id].eventMaskVaule != ((msg.event >> 12) & g_taskInfoMap[id].eventMaskVaule)))
            {
                SystemApiLogPrintf(LOG_WARING_OUTPUT, "DIAG Task id is %d, event is 0x%x\r\n", id, msg.event);
                continue;
            }

            if (NULL != g_taskInfoMap[id].TaskFunctionHook)
            {
                g_taskInfoMap[id].TaskFunctionHook(msg);
            }
        }
        else
        {
            if(counter == 0)
            {
                //TboxSystemTimeSet(0x24, 0x03, 0, 0x14, 0x21, 0x15, 0, NET_TYPE_TIME);
            }
            if(counter++ % 100 == 0)
            {
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "Diag cnt=%d. \r\n", counter);
            }
        }
        vTaskDelay(TASK_SELFCHECK_PERIOD_TIME);
    }
}

/*************************************************
函数名称: DiagTaskInitHook
函数功能: 诊断任务初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void DiagTaskInitHook(void)
{
    DiagDtcReqSystemInit();
}

/*************************************************
函数名称: DiagTaskPostInitHook
函数功能: 诊断任务第二次初始化函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void DiagTaskPostInitHook(void)
{
    //SystemApiLogPrintf(LOG_INFO_OUTPUT, "DiagTaskPostInitHook\r\n");
}

/*************************************************
函数名称: DiagTaskPeriodHook
函数功能: 诊断任务周期回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void DiagTaskPeriodHook(void)
{
    Msg msg;

    msg.event = EVENT_ID_DIAG_PERIOD_NOTIFY;
    msg.len = 0;
    msg.lparam = 0;

    SystemSendMessage(TASK_ID_DIAG, msg);
}

/*************************************************
函数名称: DiagTaskFunctionHook
函数功能: 诊断任务功能总回调函数
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期:2016/09/14
修改日期:2016/09/19  增加函数注释
*************************************************/
void DiagTaskFunctionHook(Msg msg)
{
    DiagEventFunction(msg);
}
