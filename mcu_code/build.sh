#!/bin/bash

# T-Box MCU Code Build Script
# CMake build system only

set -e # Exit on any error

# Function to show help
show_help() {
    echo "T-Box MCU Code Build Script"
    echo ""
    echo "Usage: $0 [BUILD_MODE] [OPTIONS]"
    echo ""
    echo "BUILD_MODE:"
    echo "  debug     Build debug version with debug info (default)"
    echo "  release   Build release version with optimizations"
    echo ""
    echo "OPTIONS:"
    echo "  -h, --help           Show this help message"
    echo "  -m, --mode MODE      Set build mode (debug|release)"
    echo ""
    echo "Examples:"
    echo "  $0                    # CMake debug (default)"
    echo "  $0 release            # CMake release"
    echo "  $0 --mode=release     # CMake release"
    echo "  $0 -m debug           # CMake debug"
    echo ""
}

# Parse command line arguments
BUILD_MODE="debug" # Default to debug

# Function to parse arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        -h | --help)
            show_help
            exit 0
            ;;
        -m | --mode)
            if [[ -n "$2" && "$2" =~ ^(debug|release)$ ]]; then
                BUILD_MODE="$2"
                shift 2
            else
                echo "Error: --mode requires debug or release"
                exit 1
            fi
            ;;
        --mode=*)
            MODE_VALUE="${1#*=}"
            if [[ "$MODE_VALUE" =~ ^(debug|release)$ ]]; then
                BUILD_MODE="$MODE_VALUE"
                shift
            else
                echo "Error: --mode requires debug or release"
                exit 1
            fi
            ;;
        debug | release)
            BUILD_MODE="$1"
            shift
            ;;
        *)
            echo "Error: Unknown argument '$1'"
            echo "Use '$0 --help' for usage information."
            exit 1
            ;;
        esac
    done
}

# Parse all arguments
parse_arguments "$@"

# 定义源文件和目标文件
LOACL_PATH=$PWD

echo "=== T-Box MCU Code Build Script ==="
echo "Build mode: $BUILD_MODE"

echo "Building with CMake..."
DEST_PATH="${LOACL_PATH}/build"
input_file="${DEST_PATH}/mcu_app.bin"

# Convert build mode to CMake format
if [ "$BUILD_MODE" = "debug" ]; then
    CMAKE_BUILD_TYPE="Debug"
else
    CMAKE_BUILD_TYPE="Release"
fi

# 生成目标文件
rm -rf build
mkdir -p build
cd build || {
    echo "Failed to enter build directory!"
    exit 1
}
cmake -DCMAKE_BUILD_TYPE:STRING=$CMAKE_BUILD_TYPE -DCMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE -DCMAKE_TOOLCHAIN_FILE=../cmake/gcc.cmake -DARM_CPU=cortex-m33 --no-warn-unused-cli -G Ninja ..
cmake --build . --target all
sleep 0.5
cd ..

# 检查源文件是否存在
if [ ! -f "$input_file" ]; then
    echo "Error: $input_file does not exist."
    exit 1
fi

echo ""
echo "=== Build Successful! ==="
echo "Generated files:"
ls -la build/mcu_app.*
echo ""
echo "File sizes:"
if [ "$BUILD_MODE" = "debug" ]; then
    echo "- ELF file: $(du -h build/mcu_app.elf | cut -f1) (with debug info)"
else
    echo "- ELF file: $(du -h build/mcu_app.elf | cut -f1) (optimized)"
fi
echo "- BIN file: $(du -h build/mcu_app.bin | cut -f1) (firmware)"
echo "- HEX file: $(du -h build/mcu_app.hex | cut -f1) (Intel HEX)"
if [ -f "build/mcu_app.map" ]; then
    echo "- MAP file: $(du -h build/mcu_app.map | cut -f1) (memory map)"
fi
echo ""
echo "Memory usage:"
arm-none-eabi-size --format=berkeley build/mcu_app.elf

# 显示栈使用分析
echo ""
echo "Stack usage analysis:"
if find build -name "*.su" -type f | head -1 >/dev/null 2>&1; then
    echo "Top 10 functions with highest stack usage:"
    find build -name "*.su" -type f -exec cat {} \; | sort -k2 -nr | head -10 |
        awk '{printf "  %-50s %6s bytes\n", $1, $2}'
else
    echo "  No stack usage files (.su) found"
fi

# CMake: 生成在构建目录中
output_file="${DEST_PATH}/UserApp_md5.bin"

# 计算SHA256校验值并创建带校验值的文件
md5_checksum=$(sha256sum "$input_file" | awk '{ print $1 }')

# 创建新文件并附加SHA256校验值
cp "$input_file" "$output_file"
echo -n "$md5_checksum" >>"$output_file"

echo ""
echo "Successfully created $output_file with SHA256 checksum appended."
echo "Build completed successfully!"
