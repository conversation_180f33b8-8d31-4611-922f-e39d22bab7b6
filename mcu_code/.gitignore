# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

.idea
.code
.vscode
.cache/
build
cmake-build-*

*fuzzer*
bin
tbox.tar.gz
tbox/
DefaultBuild
UserApp*.bin
UserApp*.mtud
*.mtsp
.history/*
GTAGS
GRTAGS
GPATH
\[object Object\]
CMakeFiles/cmake.check_cache
