
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file wku_config.c
 * @brief 
 * 
 */


#include "wku_config.h"





/*  */
/* Reset config*/
const wku_reset_cfg_t wkup_resetConfig0={
    .wakeupEn=true,
    .filterEn=false,
    .filterClkSrc=WKU_FILTER_CLK_SIRC,
};
/* Wakeup pins config*/
const wku_pin_wakeup_cfg_t wkup_pinsConfig0[WKP_PIN_CH_NUM0]={
    {
        .hwChannel=7,
        .wakeupEn=true,
        .edgeEvent=WKU_EDGE_RISING,
        .filterEn=false,
        .filterClkSrc=WKU_FILTER_CLK_SIRC,
    },
};


