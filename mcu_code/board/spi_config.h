
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file spi_config.h
 * @brief 
 * 
 */




#ifndef __SPI_CONFIG_H__
#define __SPI_CONFIG_H__




#include "spi_shared_function.h"
#include "spi_master_driver.h"
#include "spi_slave_driver.h"

/* SPI master */
/*lpspi_MasterConfig_2Mbps*/
extern spi_state_t lpspi_MasterConfig_2Mbps_State;
extern const spi_master_config_t lpspi_MasterConfig_2Mbps;
/* SPI slave */
/*lpspi_SlaveConfig0*/
extern spi_state_t lpspi_SlaveConfig0_State;
extern spi_slave_config_t lpspi_SlaveConfig0;




#endif