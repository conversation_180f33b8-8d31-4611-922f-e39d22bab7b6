
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file linflexd_uart_config.h
 * @brief 
 * 
 */




#ifndef __LINFLEXD_UART_CONFIG_H__
#define __LINFLEXD_UART_CONFIG_H__




#include "linflexd_uart_driver.h"



//576000bps
extern linflexd_uart_state_t linflexd3_uart_config_576000bps_State;
extern const linflexd_uart_user_config_t linflexd3_uart_config_576000bps;
//linflexd3_uart_config_921600bps
extern linflexd_uart_state_t linflexd3_uart_config_921600bps_State;
extern const linflexd_uart_user_config_t linflexd3_uart_config_921600bps;
//linflexd0_uart_config_115200bps
extern linflexd_uart_state_t linflexd0_uart_config_115200bps_State;
extern const linflexd_uart_user_config_t linflexd0_uart_config_115200bps;
//linflexd0_uart_config_256000bps
extern linflexd_uart_state_t linflexd0_uart_config_256000bps_State;
extern const linflexd_uart_user_config_t linflexd0_uart_config_256000bps;
//linflexd4_uart_config_115200bps
extern linflexd_uart_state_t linflexd4_uart_config_115200bps_State;
extern const linflexd_uart_user_config_t linflexd4_uart_config_115200bps;




#endif