
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file etmr_config.c
 * @brief 
 * 
 */


#include <stddef.h>
#include "etmr_config.h"

/*
 * Common
*/

etmr_pwm_sync_t ETMR_CM_Config0PwmSync={
    .regSyncFreq=1,
    .regSyncSel=REG_SYNC_DISABLED,
    .cntInitSyncSel=CNT_SYNC_WITH_REG,
    .maskOutputSyncSel=CHMASK_SYNC_WITH_REG,
    .regSyncTrigSrc=DISABLE_TRIGGER,
    .cntInitSyncTrigSrc=DISABLE_TRIGGER,
    .maskOutputSyncTrigSrc=DISABLE_TRIGGER,
    .hwTrigFromTmuEnable=false,
    .hwTrigFromCimEnable=false,
    .hwTrigFromPadEnable=false,
};

etmr_trig_ch_param_t ETMR_CM_Config0TrigConfChannels[0]={
};

etmr_trig_config_t ETMR_CM_Config0TrigConf={
    .trigSrc=TRIGGER_FROM_MATCHING_EVENT,
    .pwmOutputChannel=0,
    .outputTrigWidth=0,
    .outputTrigFreq=0,
    .modMatchTrigEnable=false,
    .midMatchTrigEnable=false,
    .initMatchTrigEnable=false,
    .numOfChannels=0,
    .channelTrigParamConfig=ETMR_CM_Config0TrigConfChannels,
};

etmr_user_config_t ETMR_CM_Config0={
    .etmrClockSource=eTMR_CLOCK_SOURCE_INTERNALCLK,
    .etmrPrescaler=128,
    .debugMode=false,
    .syncMethod=&ETMR_CM_Config0PwmSync,
    .outputTrigConfig=&ETMR_CM_Config0TrigConf,
    .isTofIntEnabled=false,
};


etmr_state_t ETMR_CM_Config0_State;

etmr_state_t ETMR_CM_Config1_State;


etmr_fault_param_t ETMR_PWM_Config0FaultConfig={
    .pwmFaultInterrupt=false,
    .faultFilterSampleCounter=0,
    .faultFilterSamplePeriod=0,
    .faultInputStrentch=0,
    .pwmRecoveryOpportunity=eTMR_FAULT_PWM_RECOVERY_DISABLED,
    .pwmAutoRecoveryMode=eTMR_MANUAL_CLEAR_FAULT_FLAG_THEN_AUTO_RECOVERY,
    .faultMode=eTMR_FAULT_WITH_CLK,
    .etmrFaultChannelParam=
    {
        {
            .faultChannelEnabled=false,
            .faultInputPolarity=eTMR_FAULT_SIGNAL_HIGH,
        },
        {
            .faultChannelEnabled=false,
            .faultInputPolarity=eTMR_FAULT_SIGNAL_HIGH,
        },
        {
            .faultChannelEnabled=false,
            .faultInputPolarity=eTMR_FAULT_SIGNAL_HIGH,
        },
        {
            .faultChannelEnabled=false,
            .faultInputPolarity=eTMR_FAULT_SIGNAL_HIGH,
        },
    },
    .safeState={
        eTMR_LOW_STATE,
        eTMR_LOW_STATE,
        eTMR_LOW_STATE,
        eTMR_LOW_STATE,
    }
};

etmr_trig_config_t ETMR_CM_Config1TrigConf={
    .trigSrc=TRIGGER_FROM_MATCHING_EVENT,
    .pwmOutputChannel=0,
    .outputTrigWidth=0,
    .outputTrigFreq=1,
    .modMatchTrigEnable=false,
    .midMatchTrigEnable=false,
    .initMatchTrigEnable=false,
    .numOfChannels=0,
    .channelTrigParamConfig=NULL,
};


etmr_pwm_sync_t ETMR_CM_Config1PwmSync={
    .regSyncFreq=1,
    .regSyncSel=REG_SYNC_WITH_MOD,
    .cntInitSyncSel=CNT_SYNC_WITH_REG,
    .maskOutputSyncSel=CHMASK_SYNC_WITH_REG,
    .regSyncTrigSrc=DISABLE_TRIGGER,
    .cntInitSyncTrigSrc=DISABLE_TRIGGER,
    .maskOutputSyncTrigSrc=DISABLE_TRIGGER,
    .hwTrigFromTmuEnable=false,
    .hwTrigFromCimEnable=false,
    .hwTrigFromPadEnable=false,
};

etmr_user_config_t ETMR_CM_Config1={
    .etmrClockSource=eTMR_CLOCK_SOURCE_INTERNALCLK,
    .etmrPrescaler=128,
    .debugMode=false,
    .syncMethod=&ETMR_CM_Config1PwmSync,
    .outputTrigConfig=&ETMR_CM_Config1TrigConf,
    .isTofIntEnabled=false,
};



etmr_pwm_ch_param_t ETMR_PWM_Config0IndChConfig[1]={
    {
        .hwChannelId=4,
        .polarity=eTMR_POLARITY_NORMAL,
        .pwmSrcInvert=false,
        .align=eTMR_PWM_RIGHT_EDGE_ALIGN,
        .channelInitVal=0,
        .typeOfUpdate=eTMR_PWM_UPDATE_IN_DUTY_CYCLE,
        //.dutyCycle=6554,         //20% * 32768 = 6554     100%占空比=0x8000
        .dutyCycle=26214,         //80% * 32768 = 26214     100%占空比=0x8000
        .offset=0,
        .enableSecondChannelOutput=false,
        .secondChannelPolarity=eTMR_POLARITY_NORMAL,
        .enableDoubleSwitch=false,
        .evenDeadTime=0,
        .oddDeadTime=0,
    },
    
};

etmr_pwm_param_t ETMR_PWM_Config1={
    .nNumPwmChannels=1,
    .mode=eTMR_PWM_MODE,
    .uFrequencyHZ=20,
    .counterInitValFromInitReg=true,
    .cntVal=0,
    .pwmChannelConfig=ETMR_PWM_Config0IndChConfig,
    .faultConfig=&ETMR_PWM_Config0FaultConfig,
};


/*
 * MC
*/


/*
 * PWM
*/


/*
 * IC
*/




etmr_ic_ch_param_t ETMR_IC_Config0InputCh[1]={
    {
        .hwChannelId=7,
        .edge=eTMR_DUAL_EDGES,
        .measurementType=eTMR_POS_PULSE_MEASUREMENT,
        .filterSampleCounter=0,
        .filterSamplePeriod=0,
        .interruptEnable=true,
        .dmaEnable=false,
        .enableNotification=false,
        .channelsCallbacks=NULL,
        .channelsCallbacksParams=NULL,
    },
};

etmr_ic_param_t ETMR_IC_Config0={
    .numChannels=1,
    .countValue=65535,
    //.countValue=1000000,
    .inputChConfig=ETMR_IC_Config0InputCh,
};


/*
 * OC
*/


/*
 * QD
*/


