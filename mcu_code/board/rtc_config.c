
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file rtc_config.c
 * @brief 
 * 
 */

#include <stddef.h>
#include "rtc_config.h"

/*rtc_config0*/
static rtc_alarm_config_t rtc_config0Alarm={
    .alarmTime={
        .year=2024,
        .month=5,
        .day=1,
        .hour=0,
        .minutes=5,
        .seconds=0
    },
    .repetitionInterval=0,
    .numberOfRepeats=0,
    .repeatForever=true,
    .alarmIntEnable=true,
    .rtcAlarmCallback=NULL,
    .callbackParams=NULL,
};

static rtc_overflow_config_t rtc_config0Overflow={
    .overflowIntEnable=false,
    .rtcOverflowCallback=NULL,
    .callbackParams=NULL,
};

static rtc_seconds_config_t rtc_config0Seconds={
    .secondsIntConfig=RTC_INT_1HZ,
    .secondsIntEnable=false,
    .rtcSecondsCallback=NULL,
    .callbackParams=NULL,
};


const rtc_init_config_t rtc_config0 = {
    .compensationInterval=0,
    .compensation=0,
    .clockSource=RTC_CLK_SRC_OSC_32KHZ,
    .clockOutConfig=RTC_CLKOUT_SRC_SEC,
    .debugEnable=false,
    .rtcAlarmConfig=&rtc_config0Alarm,
    .rtcOverflowConfig=&rtc_config0Overflow,
    .rtcSecondsConfig=&rtc_config0Seconds,
};
