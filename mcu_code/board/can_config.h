
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file can_config.h
 * @brief 
 * 
 */




#ifndef __CAN_CONFIG_H__
#define __CAN_CONFIG_H__




#include "flexcan_driver.h"


#define flexcan1_InitConfig_INST 1
#define flexcan2_InitConfig_INST 2
#define flexcan3_InitConfig_INST 3
#define flexcan5_InitConfig_INST 5


extern flexcan_state_t flexcan1_InitConfig_State;
extern const  flexcan_user_config_t  flexcan1_InitConfig;


extern flexcan_state_t flexcan2_InitConfig_State;
extern const  flexcan_user_config_t  flexcan2_InitConfig;


extern flexcan_state_t flexcan3_InitConfig_State;
extern const  flexcan_user_config_t  flexcan3_InitConfig;


extern flexcan_state_t flexcan5_InitConfig_State;
extern const  flexcan_user_config_t  flexcan5_InitConfig;







#endif