
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file sdk_project_config.h
 * @brief 
 * 
 */



#ifndef SDK_PROJECT_CONFIG_H_
#define SDK_PROJECT_CONFIG_H_

#include "clock_config.h"
#include "pin_mux.h"
#include "FreeRTOSConfig.h"
#include "interrupt_config.h"
#include "dma_config.h"
#include "spi_config.h"
#include "linflexd_uart_config.h"
#include "linflexd_lin_config.h"
#include "can_config.h"
#include "i2c_config.h"
#include "rtc_config.h"
#include "ptmr_config.h"
#include "etmr_config.h"
#include "adc_config.h"
#include "power_config.h"
#include "wku_config.h"
#include "wdg_config.h"
#include "hcu_config.h"
#include "fee_config.h"
#include "flash_config.h"
#include "secure_boot_config.h"
#include "utility_print_config.h"


#endif

