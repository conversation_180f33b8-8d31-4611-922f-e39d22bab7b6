
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 *
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 *
 * @file secure_boot_config.c
 * @brief
 *
 */

#include "secure_boot_config.h"

/*
 * add the CAMC result array definition of sections as below with "SB_CMAC_SEG" to place it into
 * section ".sb_cmac" allocated in linker file
 */
#if defined(__ICCARM__)
__root const uint8_t cmac0[16] SB_CMAC_SEG = {0};
__root const uint8_t cmac1[16] SB_CMAC_SEG = {0};
__root const uint8_t cmac2[16] SB_CMAC_SEG = {0};
__root const uint8_t cmac3[16] SB_CMAC_SEG = {0};
__root const uint8_t cmac4[16] SB_CMAC_SEG = {0};
#else
const uint8_t cmac0[16] SB_CMAC_SEG = {0};
const uint8_t cmac1[16] SB_CMAC_SEG = {0};
const uint8_t cmac2[16] SB_CMAC_SEG = {0};
const uint8_t cmac3[16] SB_CMAC_SEG = {0};
const uint8_t cmac4[16] SB_CMAC_SEG = {0};
#endif

/* add the secure boot section config structure definitions as below with "SB_CONFIG_SECTION_SEG"
 * to place it into section ".sb_config_section" allocated in linker file
 */
#if defined(__ICCARM__)
__root const secure_boot_section_config_t secure_boot_section_config0 SB_CONFIG_SECTION_SEG = {
#else
const secure_boot_section_config_t secure_boot_section_config0 SB_CONFIG_SECTION_SEG = {
#endif
    .secure_boot_marker = SECURE_BOOT_SECTION_MARKER,
    .aes_key_size       = KEY_LEN_128_BITS,
    .key_slot           = 1,
    .start_addr         = 0x4000,
    .length             = 0x20,
    .cmac_addr          = (uint32_t)&cmac0[0],
};
#if defined(__ICCARM__)
__root const secure_boot_section_config_t secure_boot_section_config1 SB_CONFIG_SECTION_SEG = {
#else
const secure_boot_section_config_t secure_boot_section_config1 SB_CONFIG_SECTION_SEG = {
#endif
    .secure_boot_marker = SECURE_BOOT_SECTION_MARKER,
    .aes_key_size       = KEY_LEN_128_BITS,
    .key_slot           = 2,
    .start_addr         = 0x4000,
    .length             = 0x200,
    .cmac_addr          = (uint32_t)&cmac1[0],
};
#if defined(__ICCARM__)
__root const secure_boot_section_config_t secure_boot_section_config2 SB_CONFIG_SECTION_SEG = {
#else
const secure_boot_section_config_t secure_boot_section_config2 SB_CONFIG_SECTION_SEG = {
#endif
    .secure_boot_marker = SECURE_BOOT_SECTION_MARKER,
    .aes_key_size       = KEY_LEN_128_BITS,
    .key_slot           = 3,
    .start_addr         = 0x4000,
    .length             = 0x4000,
    .cmac_addr          = (uint32_t)&cmac2[0],
};
#if defined(__ICCARM__)
__root const secure_boot_section_config_t secure_boot_section_config3 SB_CONFIG_SECTION_SEG = {
#else
const secure_boot_section_config_t secure_boot_section_config3 SB_CONFIG_SECTION_SEG = {
#endif
    .secure_boot_marker = SECURE_BOOT_SECTION_MARKER,
    .aes_key_size       = KEY_LEN_192_BITS,
    .key_slot           = 4,
    .start_addr         = 0x4000,
    .length             = 0x10,
    .cmac_addr          = (uint32_t)&cmac3[0],
};
#if defined(__ICCARM__)
__root const secure_boot_section_config_t secure_boot_section_config4 SB_CONFIG_SECTION_SEG = {
#else
const secure_boot_section_config_t secure_boot_section_config4 SB_CONFIG_SECTION_SEG = {
#endif
    .secure_boot_marker = SECURE_BOOT_SECTION_MARKER,
    .aes_key_size       = KEY_LEN_256_BITS,
    .key_slot           = 5,
    .start_addr         = 0x4000,
    .length             = 0x100,
    .cmac_addr          = (uint32_t)&cmac4[0],
};

/* add the secure boot group config structure definitions as below with "SB_CONFIG_GROUP_SEG"
 * to place it into section ".sb_group_section" allocated in linker file
 */
#if defined(__ICCARM__)
__root const secure_boot_group_config_t secure_boot_group SB_CONFIG_GROUP_SEG = {
#else
const secure_boot_group_config_t secure_boot_group SB_CONFIG_GROUP_SEG = {
#endif
    .secure_boot_group_marker = SECURE_BOOT_GROUP_MARKER,
    .secure_boot_section_num  = 5,
    .encrypt                  = true,
    .aes_key_size             = KEY_LEN_128_BITS,
    .key_slot                 = 0,
    .section_config_addr[0]   = (uint32_t)&secure_boot_section_config0,
    .section_config_addr[1]   = (uint32_t)&secure_boot_section_config1,
    .section_config_addr[2]   = (uint32_t)&secure_boot_section_config2,
    .section_config_addr[3]   = (uint32_t)&secure_boot_section_config3,
    .section_config_addr[4]   = (uint32_t)&secure_boot_section_config4,
};

/* BVT Header section
 * include configuration refer to BVT_t
 */
#define SECURE_BOOT_ENABLE
#ifdef SECURE_BOOT_ENABLE
#define BOOT_CONFIG_WORD (BVT_BCW_CPDIVS_SET(0) | \
                          BVT_CPSRC_PLL_EN |      \
                          BOOT_SEQ_EN |           \
                          BOOT_SEQ_STRICT |       \
                          CM33_0_M_EN)
#else
#define BOOT_CONFIG_WORD (BVT_BCW_CPDIVS_SET(0) | \
                          BVT_CPSRC_PLL_EN |      \
                          CM33_0_M_EN)
#endif

/* BVT Header configuration */
#if defined(__ICCARM__)
__root const bvt_header_config_t bvt_header BVT_HEADER_SEG = {
#else
const bvt_header_config_t bvt_header BVT_HEADER_SEG = {
#endif
    .bvt_marker            = BVT_VALID_MARK,
    .boot_config_word      = BOOT_CONFIG_WORD,
    .sbt_config_group_addr = (uint32_t)&secure_boot_group,
    .cm33_0_main_app_addr  = 0x4000,
    .app_wdg_timeout       = 3000,
};
