
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file interrupt_config.c
 * @brief 
 * 
 */


#include <stddef.h>
#include "interrupt_config.h"


void INT_SYS_ConfigInit()
{
    INT_SYS_DisableIRQGlobal();
    /* LINFlexD0_IRQn(31) LINFlexD0_IRQHandler*/
    INT_SYS_SetPriority(LINFlexD0_IRQn,3);
    INT_SYS_EnableIRQ(LINFlexD0_IRQn);
    /* ADC0_IRQn(39) ADC0_IRQHandler*/
    INT_SYS_SetPriority(ADC0_IRQn,3);
    INT_SYS_EnableIRQ(ADC0_IRQn);
    /* RTC_IRQn(46) RTC_IRQHandler*/
    INT_SYS_SetPriority(RTC_IRQn,2);
    INT_SYS_EnableIRQ(RTC_IRQn);
    /* RTC_Seconds_IRQn(47) RTC_Seconds_IRQHandler*/
    INT_SYS_SetPriority(RTC_Seconds_IRQn,2);
    INT_SYS_EnableIRQ(RTC_Seconds_IRQn);
    /* pTMR_Ch0_IRQn(48) pTMR_Ch0_IRQHandler*/
    INT_SYS_SetPriority(pTMR_Ch0_IRQn,4);
    INT_SYS_EnableIRQ(pTMR_Ch0_IRQn);
    /* pTMR_Ch1_IRQn(49) pTMR_Ch1_IRQHandler*/
    INT_SYS_SetPriority(pTMR_Ch1_IRQn,4);
    INT_SYS_EnableIRQ(pTMR_Ch1_IRQn);
    /* GPIOA_IRQn(59) GPIOA_IRQHandler*/
    INT_SYS_SetPriority(GPIOA_IRQn,3);
    INT_SYS_EnableIRQ(GPIOA_IRQn);
    /* GPIOB_IRQn(60) GPIOB_IRQHandler*/
    INT_SYS_SetPriority(GPIOB_IRQn,3);
    INT_SYS_EnableIRQ(GPIOB_IRQn);
    /* GPIOC_IRQn(61) GPIOC_IRQHandler*/
    INT_SYS_SetPriority(GPIOC_IRQn,3);
    INT_SYS_EnableIRQ(GPIOC_IRQn);
    /* GPIOD_IRQn(62) GPIOD_IRQHandler*/
    INT_SYS_SetPriority(GPIOD_IRQn,3);
    INT_SYS_EnableIRQ(GPIOD_IRQn);
    /* GPIOE_IRQn(63) GPIOE_IRQHandler*/
    INT_SYS_SetPriority(GPIOE_IRQn,2);
    INT_SYS_EnableIRQ(GPIOE_IRQn);
    /* CAN1_ORed_IRQn(85) CAN1_ORed_IRQHandler*/
    INT_SYS_SetPriority(CAN1_ORed_IRQn,1);
    INT_SYS_EnableIRQ(CAN1_ORed_IRQn);
    /* CAN1_Error_IRQn(86) CAN1_Error_IRQHandler*/
    INT_SYS_SetPriority(CAN1_Error_IRQn,3);
    INT_SYS_EnableIRQ(CAN1_Error_IRQn);
    /* CAN1_ORed_0_15_MB_IRQn(88) CAN1_ORed_0_15_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN1_ORed_0_15_MB_IRQn,1);
    INT_SYS_EnableIRQ(CAN1_ORed_0_15_MB_IRQn);
    /* CAN1_ORed_16_31_MB_IRQn(89) CAN1_ORed_16_31_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN1_ORed_16_31_MB_IRQn,1);
    INT_SYS_EnableIRQ(CAN1_ORed_16_31_MB_IRQn);
    /* CAN1_ORed_32_47_MB_IRQn(90) CAN1_ORed_32_47_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN1_ORed_32_47_MB_IRQn,1);
    INT_SYS_EnableIRQ(CAN1_ORed_32_47_MB_IRQn);
    /* CAN1_ORed_48_63_MB_IRQn(91) CAN1_ORed_48_63_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN1_ORed_48_63_MB_IRQn,1);
    INT_SYS_EnableIRQ(CAN1_ORed_48_63_MB_IRQn);
    /* CAN2_ORed_IRQn(92) CAN2_ORed_IRQHandler*/
    INT_SYS_SetPriority(CAN2_ORed_IRQn,3);
    INT_SYS_EnableIRQ(CAN2_ORed_IRQn);
    /* CAN2_Error_IRQn(93) CAN2_Error_IRQHandler*/
    INT_SYS_SetPriority(CAN2_Error_IRQn,3);
    INT_SYS_EnableIRQ(CAN2_Error_IRQn);
    /* CAN2_ORed_0_15_MB_IRQn(95) CAN2_ORed_0_15_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN2_ORed_0_15_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN2_ORed_0_15_MB_IRQn);
    /* CAN2_ORed_16_31_MB_IRQn(96) CAN2_ORed_16_31_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN2_ORed_16_31_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN2_ORed_16_31_MB_IRQn);
    /* CAN2_ORed_32_47_MB_IRQn(97) CAN2_ORed_32_47_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN2_ORed_32_47_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN2_ORed_32_47_MB_IRQn);
    /* CAN2_ORed_48_63_MB_IRQn(98) CAN2_ORed_48_63_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN2_ORed_48_63_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN2_ORed_48_63_MB_IRQn);
    /* eTMR2_Ch6_Ch7_IRQn(114) eTMR2_Ch6_Ch7_IRQHandler*/
    INT_SYS_SetPriority(eTMR2_Ch6_Ch7_IRQn,2);
    INT_SYS_EnableIRQ(eTMR2_Ch6_Ch7_IRQn);
    /* LINFlexD3_IRQn(163) LINFlexD3_IRQHandler*/
    INT_SYS_SetPriority(LINFlexD3_IRQn,1);
    INT_SYS_EnableIRQ(LINFlexD3_IRQn);
    /* LINFlexD4_IRQn(164) LINFlexD4_IRQHandler*/
    INT_SYS_SetPriority(LINFlexD4_IRQn,3);
    INT_SYS_EnableIRQ(LINFlexD4_IRQn);
    /* I2C2_Master_IRQn(166) I2C2_Master_IRQHandler*/
    INT_SYS_SetPriority(I2C2_Master_IRQn,3);
    INT_SYS_EnableIRQ(I2C2_Master_IRQn);
    /* SPI3_IRQn(168) SPI3_IRQHandler*/
    INT_SYS_SetPriority(SPI3_IRQn,3);
    INT_SYS_EnableIRQ(SPI3_IRQn);
    /* CAN3_ORed_IRQn(171) CAN3_ORed_IRQHandler*/
    INT_SYS_SetPriority(CAN3_ORed_IRQn,3);
    INT_SYS_EnableIRQ(CAN3_ORed_IRQn);
    /* CAN3_Error_IRQn(172) CAN3_Error_IRQHandler*/
    INT_SYS_SetPriority(CAN3_Error_IRQn,3);
    INT_SYS_EnableIRQ(CAN3_Error_IRQn);
    /* CAN3_ORed_0_15_MB_IRQn(174) CAN3_ORed_0_15_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN3_ORed_0_15_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN3_ORed_0_15_MB_IRQn);
    /* CAN3_ORed_16_31_MB_IRQn(175) CAN3_ORed_16_31_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN3_ORed_16_31_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN3_ORed_16_31_MB_IRQn);
    /* CAN5_ORed_IRQn(185) CAN5_ORed_IRQHandler*/
    INT_SYS_SetPriority(CAN5_ORed_IRQn,3);
    INT_SYS_EnableIRQ(CAN5_ORed_IRQn);
    /* CAN5_Error_IRQn(186) CAN5_Error_IRQHandler*/
    INT_SYS_SetPriority(CAN5_Error_IRQn,3);
    INT_SYS_EnableIRQ(CAN5_Error_IRQn);
    /* CAN5_ORed_0_15_MB_IRQn(188) CAN5_ORed_0_15_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN5_ORed_0_15_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN5_ORed_0_15_MB_IRQn);
    /* CAN5_ORed_16_31_MB_IRQn(189) CAN5_ORed_16_31_MB_IRQHandler*/
    INT_SYS_SetPriority(CAN5_ORed_16_31_MB_IRQn,3);
    INT_SYS_EnableIRQ(CAN5_ORed_16_31_MB_IRQn);
    /* WKU_IRQn(190) WKU_IRQHandler*/
    INT_SYS_SetPriority(WKU_IRQn,2);
    INT_SYS_EnableIRQ(WKU_IRQn);
    INT_SYS_EnableIRQGlobal();
}
