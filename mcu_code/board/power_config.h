
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file power_config.h
 * @brief 
 * 
 */




#ifndef __POWER_CONFIG_H__
#define __POWER_CONFIG_H__

#include "power_manager.h"

#define POWER_MANAGER_CONFIG_CNT                (4U)
#define POWER_MANAGER_CALLBACK_CNT              (0U)

extern power_manager_user_config_t* powerConfigsArr[4];
extern power_manager_callback_user_config_t* powerStaticCallbacksConfigsArr[0];


#define POWER_MANAGER_CONFIG_MACRO (&powerConfigsArr,POWER_MANAGER_CONFIG_CNT,&powerStaticCallbacksConfigsArr,POWER_MANAGER_CALLBACK_CNT)
#endif


