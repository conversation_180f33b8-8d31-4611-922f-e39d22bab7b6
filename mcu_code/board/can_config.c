
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 *
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 *
 * @file can_config.c
 * @brief
 *
 */

#include "can_config.h"

/*flexcan1_InitConfig*/

flexcan_state_t flexcan1_InitConfig_State;

const flexcan_user_config_t flexcan1_InitConfig = {
    .max_num_mb        = 32UL,
    .num_id_filters    = FLEXCAN_RX_FIFO_ID_FILTERS_8,
    .is_rx_fifo_needed = true,
    .flexcanMode       = FLEXCAN_NORMAL_MODE,
    .payload           = FLEXCAN_PAYLOAD_SIZE_8,
    .fd_enable         = false,
    .pe_clock          = FLEXCAN_CLK_SOURCE_OSC,
    .transfer_type     = FLEXCAN_RXFIFO_USING_INTERRUPTS,
    .bitrate           = {
                  .propSeg    = 6, /*!< Propagation segment*/
                  .phaseSeg1  = 5, /*!< Phase segment 1*/
                  .phaseSeg2  = 1, /*!< Phase segment 2*/
                  .preDivider = 5, /*!< Clock prescaler division factor*/
                  .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .bitrate_cbt = {
        .propSeg    = 6, /*!< Propagation segment*/
        .phaseSeg1  = 2, /*!< Phase segment 1*/
        .phaseSeg2  = 1, /*!< Phase segment 2*/
        .preDivider = 0, /*!< Clock prescaler division factor*/
        .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .rxFifoDMAChannel                  = 0,
    .rxFifoDMALastWord                 = 0,
    .is_enhance_rx_fifo_needed         = false,
    .num_enhance_rx_fifo_filters       = 0,
    .num_enhance_rx_fifo_extid_filters = 0,
    .num_enhance_rx_fifo_min_messages  = 0,
};
/*flexcan2_InitConfig*/

flexcan_state_t flexcan2_InitConfig_State;

const flexcan_user_config_t flexcan2_InitConfig = {
    .max_num_mb        = 32UL,
    .num_id_filters    = FLEXCAN_RX_FIFO_ID_FILTERS_8,
    .is_rx_fifo_needed = true,
    .flexcanMode       = FLEXCAN_NORMAL_MODE,
    .payload           = FLEXCAN_PAYLOAD_SIZE_8,
    .fd_enable         = false,
    .pe_clock          = FLEXCAN_CLK_SOURCE_OSC,
    .transfer_type     = FLEXCAN_RXFIFO_USING_INTERRUPTS,
    .bitrate           = {
                  .propSeg    = 6, /*!< Propagation segment*/
                  .phaseSeg1  = 5, /*!< Phase segment 1*/
                  .phaseSeg2  = 1, /*!< Phase segment 2*/
                  .preDivider = 5, /*!< Clock prescaler division factor*/
                  .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .bitrate_cbt = {
        .propSeg    = 6, /*!< Propagation segment*/
        .phaseSeg1  = 2, /*!< Phase segment 1*/
        .phaseSeg2  = 1, /*!< Phase segment 2*/
        .preDivider = 0, /*!< Clock prescaler division factor*/
        .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .rxFifoDMAChannel                  = 0,
    .rxFifoDMALastWord                 = 0,
    .is_enhance_rx_fifo_needed         = false,
    .num_enhance_rx_fifo_filters       = 0,
    .num_enhance_rx_fifo_extid_filters = 0,
    .num_enhance_rx_fifo_min_messages  = 0,
};
/*flexcan3_InitConfig*/

flexcan_state_t flexcan3_InitConfig_State;

const flexcan_user_config_t flexcan3_InitConfig = {
    .max_num_mb        = 32UL,
    .num_id_filters    = FLEXCAN_RX_FIFO_ID_FILTERS_8,
    .is_rx_fifo_needed = true,
    .flexcanMode       = FLEXCAN_NORMAL_MODE,
    .payload           = FLEXCAN_PAYLOAD_SIZE_8,
    .fd_enable         = false,
    .pe_clock          = FLEXCAN_CLK_SOURCE_OSC,
    .transfer_type     = FLEXCAN_RXFIFO_USING_INTERRUPTS,
    .bitrate           = {
                  .propSeg    = 6, /*!< Propagation segment*/
                  .phaseSeg1  = 5, /*!< Phase segment 1*/
                  .phaseSeg2  = 1, /*!< Phase segment 2*/
                  .preDivider = 5, /*!< Clock prescaler division factor*/
                  .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .bitrate_cbt = {
        .propSeg    = 6, /*!< Propagation segment*/
        .phaseSeg1  = 2, /*!< Phase segment 1*/
        .phaseSeg2  = 1, /*!< Phase segment 2*/
        .preDivider = 0, /*!< Clock prescaler division factor*/
        .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .rxFifoDMAChannel                  = 0,
    .rxFifoDMALastWord                 = 0,
    .is_enhance_rx_fifo_needed         = false,
    .num_enhance_rx_fifo_filters       = 0,
    .num_enhance_rx_fifo_extid_filters = 0,
    .num_enhance_rx_fifo_min_messages  = 0,
};
/*flexcan5_InitConfig*/

flexcan_state_t flexcan5_InitConfig_State;

const flexcan_user_config_t flexcan5_InitConfig = {
    .max_num_mb        = 32UL,
    .num_id_filters    = FLEXCAN_RX_FIFO_ID_FILTERS_8,
    .is_rx_fifo_needed = true,
    .flexcanMode       = FLEXCAN_NORMAL_MODE,
    .payload           = FLEXCAN_PAYLOAD_SIZE_8,
    .fd_enable         = false,
    .pe_clock          = FLEXCAN_CLK_SOURCE_OSC,
    .transfer_type     = FLEXCAN_RXFIFO_USING_INTERRUPTS,
    .bitrate           = {
                  .propSeg    = 6, /*!< Propagation segment*/
                  .phaseSeg1  = 5, /*!< Phase segment 1*/
                  .phaseSeg2  = 1, /*!< Phase segment 2*/
                  .preDivider = 5, /*!< Clock prescaler division factor*/
                  .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .bitrate_cbt = {
        .propSeg    = 6, /*!< Propagation segment*/
        .phaseSeg1  = 2, /*!< Phase segment 1*/
        .phaseSeg2  = 1, /*!< Phase segment 2*/
        .preDivider = 0, /*!< Clock prescaler division factor*/
        .rJumpwidth = 1, /*!< Resync jump width*/
    },
    .rxFifoDMAChannel                  = 0,
    .rxFifoDMALastWord                 = 0,
    .is_enhance_rx_fifo_needed         = false,
    .num_enhance_rx_fifo_filters       = 0,
    .num_enhance_rx_fifo_extid_filters = 0,
    .num_enhance_rx_fifo_min_messages  = 0,
};
