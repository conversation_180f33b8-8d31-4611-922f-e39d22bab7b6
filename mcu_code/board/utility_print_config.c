
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file undefined
 * @brief 
 * 
 */



#include "utility_print_config.h"
#include "linflexd_uart_config.h"
#include "SEGGER_RTT.h"

status_t UTILITY_PRINT_Init()
{
    status_t status=STATUS_SUCCESS;

#ifndef NDEBUG
    status=LINFlexD_UART_DRV_Init(UART_PORT_LOG_OUTPUT, &linflexd4_uart_config_115200bps_State, &linflexd4_uart_config_115200bps);
    //LINFlexD_DRV_Init(4, &linflexd_lin_config0, &linflexd_lin_config0_State);
    SEGGER_RTT_Init();
    // SEGGER_RTT_WriteString(0, "Using RTT as console\r\n");
#endif

    return status;
}

void printf_char(char ch)
{
#ifndef NDEBUG
    LINFlexD_UART_DRV_SendDataPolling(UART_PORT_LOG_OUTPUT, (const uint8_t *) &ch, 1);

    SEGGER_RTT_Write(0, (const uint8_t *) &ch, 1);
#endif
}


