
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file power_config.c
 * @brief 
 * 
 */


#include <stddef.h>
#include "power_config.h"




/* RUN */
power_manager_user_config_t RUN={
    .powerMode=POWER_MANAGER_RUN,
    .sleepOnExitValue=false,
};
/* PowerDown */
power_manager_user_config_t PowerDown={
    .powerMode=POWER_MANAGER_POWERDOWN,
    .sleepOnExitValue=false,
};


/* pwrMan_InitConfigDeepSleep */
power_manager_user_config_t DeepSleep={
    .powerMode=POWER_MANAGER_DEEPSLEEP,
    .sleepOnExitValue=false,
};
/* pwrMan_InitConfigStandBy */
power_manager_user_config_t StandBy={
    .powerMode=POWER_MANAGER_STANDBY,
    .sleepOnExitValue=true,
};

power_manager_user_config_t* powerConfigsArr[POWER_MANAGER_CONFIG_CNT]={
    &RUN,
    &PowerDown,
    &DeepSleep,
    &StandBy,
};

power_manager_callback_user_config_t* powerStaticCallbacksConfigsArr[0]={
};
