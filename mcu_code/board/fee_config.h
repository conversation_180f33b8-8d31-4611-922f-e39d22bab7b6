
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 *
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 *
 * @file fee_config.h
 * @brief
 *
 */




#ifndef __FEE_CONFIG_H__
#define __FEE_CONFIG_H__



#include "fee.h"

#define FLS_CONFIGURED_SECTOR_NUMBER 76U/*Count of user configuration flash sectors*/
#define FEE_NUMBER_OF_CLUSTER_GROUPS 32U  /* Number of configured Fee cluster group */
#define FEE_CRT_CFG_NR_OF_BLOCKS  32U /* Number of configured Fee blocks */


/* Symbolic names of configured Fee blocks */
#define CarInfo_Block      (1U)
#define StaticDid1_Block      (2U)
#define StaticDid2_Block      (3U)
#define SelfConfig_Block      (4U)
#define BleInfo_Block      (5U)
#define BackupRam_Block      (6U)
#define dtc1      (7U)
#define dtc2      (8U)
#define dtc3      (9U)
#define dtc4      (10U)
#define dtc5      (11U)
#define dtc6      (12U)
#define dtc7      (13U)
#define dtc8      (14U)
#define dtc9      (15U)
#define dtc10      (16U)
#define dtc11      (17U)
#define dtc12      (18U)
#define dtc13      (19U)
#define dtc14      (20U)
#define dtc15      (21U)
#define dtc16      (22U)
#define dtc17      (23U)
#define dtc18      (24U)
#define dtc19      (25U)
#define dtc20      (26U)
#define dtc21      (27U)
#define Reserved1      (28U)
#define Reserved2      (29U)
#define Reserved3      (30U)
#define Reserved4      (31U)
#define Reserved5      (32U)




extern const Fee_ModuleUserConfig_t FEEGenConfig;

#endif


