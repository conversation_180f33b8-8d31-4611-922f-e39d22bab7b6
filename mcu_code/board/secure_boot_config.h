
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file secure_boot_config.h
 * @brief 
 * 
 */




#ifndef __SECURE_BOOT_CONFIG_H__
#define __SECURE_BOOT_CONFIG_H__ 

#include <stdint.h>
#include <stdbool.h>

#if defined (__ICCARM__)
#define SB_CONFIG_GROUP_SEG    @".sb_config_group"
#define SB_CONFIG_SECTION_SEG  @".sb_config_section"
#define SB_CMAC_SEG            @".sb_cmac"
#define BVT_HEADER_SEG         @".bvt_header"
#else
#define SB_CONFIG_GROUP_SEG    __attribute__((used)) __attribute__((section (".sb_config_group")))
#define SB_CONFIG_SECTION_SEG  __attribute__((used)) __attribute__((section (".sb_config_section")))
#define SB_CMAC_SEG            __attribute__((used)) __attribute__((section (".sb_cmac")))
#define BVT_HEADER_SEG         __attribute__((used)) __attribute__((section (".bvt_header")))
#endif

/* the max. secure boot section number, support max. 8 section for YTM32B1HAx */
#define MAX_SECURE_BOOT_SECT_NUM        ( 8u ) 
/* BVT valid marker */
#define BVT_VALID_MARK                  ( 0xA55AA55A )
/* Valid marker for secure boot group */
#define SECURE_BOOT_GROUP_MARKER        ( 0xACBEEFDD )
/* Valid marker for secure boot section */
#define SECURE_BOOT_SECTION_MARKER      ( 0x5AA5 ) 

/* boot configuration word bit-filed definiton */
#define BVT_CPSRC_PLL_EN_BIT   (17)
#define CPDIVS_SHIFT           (10)
#define APP_WDG_EN_BIT         (8)
#define BOOT_SEQ_STRICT_BIT    (7)
#define BOOT_SEQ_EN_BIT        (6)
#define CM33_0_M_EN_BIT        (0)

#define CPDIVS_MASK           (0xF)

/* BCW config operation macros */
#define BVT_CPSRC_PLL_EN       (1<<BVT_CPSRC_PLL_EN_BIT)
#define BVT_BCW_CPDIVS_SET(x)  (((x) & CPDIVS_MASK)<<CPDIVS_SHIFT)
#define BVT_BCW_CPDIVS_GET(x)  (((x) >> CPDIVS_SHIFT) & CPDIVS_MASK)
#define APP_WDG_EN             (1<<APP_WDG_EN_BIT)
#define BOOT_SEQ_STRICT        (1<<BOOT_SEQ_STRICT_BIT)
#define BOOT_SEQ_EN            (1<<BOOT_SEQ_EN_BIT)
#define CM33_0_M_EN            (1<<CM33_0_M_EN_BIT)

/*!
 * @brief Specify the key length to be used to implement the requested cryptographic
 * operation.
 *
 * Implements : hcu_key_len_t_Class
 */
typedef enum
{
    KEY_LEN_128_BITS = 0x00U, /*!< 128-bit key length */
    KEY_LEN_192_BITS = 0x01U, /*!< 192-bit key length */
    KEY_LEN_256_BITS = 0x02U, /*!< 256-bit key length */
} hcu_key_len_t;

/*!
 * @brief the config struct of secure boot section.
 *
 * Implements : secure_boot_section_config_t_Class
 */
typedef struct {
    uint16_t secure_boot_marker; /* 2B: fixed marker to identify the secure config */
    hcu_key_len_t aes_key_size; /* 1B: AES key type/size for the CMAC calculation, AES-128/192/256 */
    uint8_t key_slot;            /* 1B: allow to use 0~31 key group */
    uint32_t start_addr;         /* 4B: the start address of secure boot section for CMAC generation/authorization */
    uint32_t length;             /* 4B: the length of secure boot section for CMAC generation/authorization */
    uint32_t cmac_addr;          /* 4B: the CMAC result store address */
}secure_boot_section_config_t;

/*!
 * @brief the config struct of secure boot group
 *
 * Implements : secure_boot_group_config_t_Class
 */
typedef struct{
    uint32_t secure_boot_group_marker;/* 4B: fixed marker to identify the the secure boot group info */
    uint8_t secure_boot_section_num;  /* 1B: the secure boot section config number, limited by the max. support number of the device family */
    bool encrypt;                     /* 1B: the section config encrypt or not */ 
    hcu_key_len_t aes_key_size;      /* 1B: the AES key type/size for the encrypt */
    uint8_t key_slot;                 /* 1B: the key for section config encrypt, allow to use 0~31 key group */
    /* the secure boot section config struct pointers, it's configured per actual project/application requirements */
    uint32_t section_config_addr[MAX_SECURE_BOOT_SECT_NUM];
}secure_boot_group_config_t;

/*!
 * @brief the BVT structure type definition
 *
 * Implements : bvt_header_config_t_Class
 */
typedef struct{
    uint32_t bvt_marker;                /* BVT marker */
    uint32_t boot_config_word;          /* Boot configuration word */
    uint32_t sbt_config_group_addr;     /* secure boot start address */
    uint32_t reserved0[1];
    uint32_t cm33_0_main_app_addr;      /* CM33_0 main core start address */
    uint32_t reserved1[5];
    uint32_t app_wdg_timeout;           /* timeout set of the WDG watchdog of the application core */
} bvt_header_config_t;



#endif /* __SECURE_BOOT_CONFIG_H__ */