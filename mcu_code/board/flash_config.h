
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file flash_config.h
 * @brief 
 * 
 */




#ifndef __FLASH_CONFIG_H__
#define __FLASH_CONFIG_H__




#include "flash_driver.h"

#define PFLASH_0_ADDR 0x00000000
#define PFLASH_0_END 0x0007FFFF
#define PFLASH_0_SIZE 0x80000
#define PFLASH_0_SECTOR_SIZE 0x800 

#define PFLASH_1_ADDR 0x00080000
#define PFLASH_1_END 0x000FFFFF
#define PFLASH_1_SIZE 0x80000 
#define PFLASH_1_SECTOR_SIZE 0x800

//The NV zone used the address=0x00100000, so need to avoid
#define DFLASH_ADDR 0x00130000
#define DFLASH_END 0x0016FFFF
#define DFLASH_SIZE 0x40000 
#define DFLASH_SECTOR_SIZE 0x400


#define HCU_NVR_ADDR 0x10000000
#define HCU_NVR_END 0x100003FF
#define HCU_NVR_SIZE 0x400
#define HCU_NVR_SECTOR_SIZE 0x400


#define OTP_NVR_ADDR 0x10010000
#define OTP_NVR_END 0x100103FF
#define OTP_NVR_SIZE 0x400
#define OTP_NVR_SECTOR_SIZE 0x400



#define CUS_NVR_ADDR 0x10030000
#define CUS_NVR_END 0x100303FF
#define CUS_NVR_SIZE 0x400
#define CUS_NVR_SECTOR_SIZE 0x400



/*flash_config0*/
extern flash_state_t flash_config0_State;
extern flash_user_config_t flash_config0;

#endif