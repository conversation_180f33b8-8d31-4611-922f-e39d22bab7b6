
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file yt_linker.ld
 * @brief 
 * 
 */




HEAP_SIZE  = DEFINED(__heap_size__)  ? __heap_size__  : 3968; /* 减少为 4K - 128 字节，解决堆与栈重叠问题 */






/* MEMORY MAP */
MEMORY
{
    /*
    IVT   (RX) : ORIGIN = 0x0, LENGTH = 1024
    TEXT   (RX) : ORIGIN = 0x400, LENGTH = 0xFFC00
    IVT_RAM   (RW) : ORIGIN = 0x1fff0000, LENGTH = 0x400
    STACK   (RW) : ORIGIN = 0x2000fc00, LENGTH = 0x400
    RAM   (RW) : ORIGIN = 0x1fff0400, LENGTH = 0x1F800
    */

    IVT      (RX) : ORIGIN = 0x0002C000, LENGTH = 1024
    TEXT     (RX) : ORIGIN = 0x0002C400, LENGTH = (1024K - 0x0002C400)
    IVT_RAM  (RW) : ORIGIN = 0x1fff0000, LENGTH = 0x400
    STACK    (RW) : ORIGIN = 0x2000fc00, LENGTH = 0x400
    RAM      (RW) : ORIGIN = 0x1fff0400, LENGTH = 0x1F800

}
/* SECTIONS */
SECTIONS
{
    .IVT  : {
        
        IVT_start = .;
        isr_vector_region_start = .;
        KEEP(*(.isr_vector))
        isr_vector_region_end = .;
        
        IVT_end = .;
    } > IVT
   
    


    .TEXT  : {
        
        TEXT_start = .;
        rodata_region_start = .;
        *(.rodata)
        *(.rodata*)
        rodata_region_end = .;
        
        text_region_start = .;
        *(.text)
        *(.text*)
        text_region_end = .;
        
        TEXT_end = .;
    } > TEXT
   
    


    .ARM  : {
        
        ARM_start = .;
        ARM.exidx_region_start = .;
        *(.ARM.exidx)
        *(.ARM.exidx*)
        ARM.exidx_region_end = .;
        
        ARM_end = .;
    } > TEXT
   
    


    CODE_RAM_rom_start_not_align = .;
    CODE_RAM_rom_start = CODE_RAM_rom_start_not_align + (CODE_RAM_rom_start_not_align % 4);
   
    CODE_RAM_rom_end = CODE_RAM_rom_start + CODE_RAM_ram_end - CODE_RAM_ram_start;
    


    DATA_RAM_rom_start_not_align = CODE_RAM_rom_end;
    DATA_RAM_rom_start = DATA_RAM_rom_start_not_align + (DATA_RAM_rom_start_not_align % 4);
   
    DATA_RAM_rom_end = DATA_RAM_rom_start + DATA_RAM_ram_end - DATA_RAM_ram_start;
    

/*
    .BVT  : {

        BVT_start = .;
        . = ALIGN(4);
        bvt_header_region_start = .;
        KEEP(*(.bvt_header))
        bvt_header_region_end = .;

        . = ALIGN(16);
        sb_config_group_region_start = .;
        KEEP(*(.sb_config_group))
        sb_config_group_region_end = .;

        . = ALIGN(16);
        sb_config_section_region_start = .;
        KEEP(*(.sb_config_section))
        sb_config_section_region_end = .;

        . = ALIGN(16);
        sb_cmac_region_start = .;
        KEEP(*(.sb_cmac))
        sb_cmac_region_end = .;

        BVT_end = .;
    } > BVT
*/



    .IVT_RAM  : {
        
        . = ALIGN(1024);
        IVT_RAM_start = .;
        . += 0X400;
        IVT_RAM_end = .;
    } > IVT_RAM
   
    


    .STACK  : {
        
        STACK_start = .;
        . += 1024;
        STACK_end = .;
    } > STACK
   
    


    .BSS  (NOLOAD): {
        
        BSS_start = .;
        bss_region_start = .;
        *(.bss)
        *(.bss*)
        bss_region_end = .;
        
        BSS_end = .;
    } > RAM


    .CODE_RAM  : AT(CODE_RAM_rom_start) {
        
        . = ALIGN(4);
        CODE_RAM_ram_start = .;
        CODE_RAM_start = .;
        code_ram_region_start = .;
        *(.code_ram)
        code_ram_region_end = .;
        
        CODE_RAM_end = .;
        CODE_RAM_ram_end = .;
    } > RAM
   
    
    ASSERT((CODE_RAM_ram_end - CODE_RAM_ram_start) == (CODE_RAM_rom_end - CODE_RAM_rom_start), "Copy Section CODE_RAM Size non-aligned")


    .DATA_RAM  : AT(DATA_RAM_rom_start) {
        
        . = ALIGN(4);
        DATA_RAM_ram_start = .;
        DATA_RAM_start = .;
        data_region_start = .;
        *(.data)
        *(.data*)
        data_region_end = .;
        
        DATA_RAM_end = .;
        DATA_RAM_ram_end = .;
    } > RAM
    
      /* Put heap section after the program data */
  .heap :
  {
    . = ALIGN(8);
    __end__ = .;
    __heap_start__ = .;
    PROVIDE(end = .);
    PROVIDE(_end = .);
    PROVIDE(__end = .);
    __HeapBase = .;
    . += HEAP_SIZE;
    __HeapLimit = .;
    __heap_limit = .;
    __heap_end__ = .;
  } > RAM

    ASSERT((DATA_RAM_ram_end - DATA_RAM_ram_start) == (DATA_RAM_rom_end - DATA_RAM_rom_start), "Copy Section DATA_RAM Size non-aligned")

/*
    Boot_memory_start = ORIGIN(Boot);
    Boot_memory_end = ORIGIN(Boot) + LENGTH(Boot);
    Boot_memory_size = LENGTH(Boot);
    */
    IVT_memory_start = ORIGIN(IVT);
    IVT_memory_end = ORIGIN(IVT) + LENGTH(IVT);
    IVT_memory_size = LENGTH(IVT);
    TEXT_memory_start = ORIGIN(TEXT);
    TEXT_memory_end = ORIGIN(TEXT) + LENGTH(TEXT);
    TEXT_memory_size = LENGTH(TEXT);
    /*
    BVT_memory_start = ORIGIN(BVT);
    BVT_memory_end = ORIGIN(BVT) + LENGTH(BVT);
    BVT_memory_size = LENGTH(BVT);
    */
    IVT_RAM_memory_start = ORIGIN(IVT_RAM);
    IVT_RAM_memory_end = ORIGIN(IVT_RAM) + LENGTH(IVT_RAM);
    IVT_RAM_memory_size = LENGTH(IVT_RAM);
    STACK_memory_start = ORIGIN(STACK);
    STACK_memory_end = ORIGIN(STACK) + LENGTH(STACK);
    STACK_memory_size = LENGTH(STACK);
    RAM_memory_start = ORIGIN(RAM);
    RAM_memory_end = ORIGIN(RAM) + LENGTH(RAM);
    RAM_memory_size = LENGTH(RAM);
}