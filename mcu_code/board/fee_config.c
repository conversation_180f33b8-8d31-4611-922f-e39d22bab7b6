
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 *
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 *
 * @file fee_config.c
 * @brief
 *
 */


#include <stddef.h>
#include "fee_config.h"


/* general */
static const Fls_SectorType sectorList[76]={
        {
                .sectorId=0,
                .sectorStartAddress=0x1000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x101000,
                .asyncAccess=1,
        },
        {
                .sectorId=1,
                .sectorStartAddress=0x1400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x101400,
                .asyncAccess=1,
        },
        {
                .sectorId=2,
                .sectorStartAddress=0x1800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x101800,
                .asyncAccess=1,
        },
        {
                .sectorId=3,
                .sectorStartAddress=0x1c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x101c00,
                .asyncAccess=1,
        },
        {
                .sectorId=4,
                .sectorStartAddress=0x2000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x102000,
                .asyncAccess=1,
        },
        {
                .sectorId=5,
                .sectorStartAddress=0x2400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x102400,
                .asyncAccess=1,
        },
        {
                .sectorId=6,
                .sectorStartAddress=0x2800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x102800,
                .asyncAccess=1,
        },
        {
                .sectorId=7,
                .sectorStartAddress=0x2c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x102c00,
                .asyncAccess=1,
        },
        {
                .sectorId=8,
                .sectorStartAddress=0x3000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x103000,
                .asyncAccess=1,
        },
        {
                .sectorId=9,
                .sectorStartAddress=0x3400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x103400,
                .asyncAccess=1,
        },
        {
                .sectorId=10,
                .sectorStartAddress=0x3800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x103800,
                .asyncAccess=1,
        },
        {
                .sectorId=11,
                .sectorStartAddress=0x3c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x103c00,
                .asyncAccess=1,
        },
        {
                .sectorId=12,
                .sectorStartAddress=0x4000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x104000,
                .asyncAccess=1,
        },
        {
                .sectorId=13,
                .sectorStartAddress=0x4400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x104400,
                .asyncAccess=1,
        },
        {
                .sectorId=14,
                .sectorStartAddress=0x4800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x104800,
                .asyncAccess=1,
        },
        {
                .sectorId=15,
                .sectorStartAddress=0x4c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x104c00,
                .asyncAccess=1,
        },
        {
                .sectorId=16,
                .sectorStartAddress=0x5000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x105000,
                .asyncAccess=1,
        },
        {
                .sectorId=17,
                .sectorStartAddress=0x5400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x105400,
                .asyncAccess=1,
        },
        {
                .sectorId=18,
                .sectorStartAddress=0x5800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x105800,
                .asyncAccess=1,
        },
        {
                .sectorId=19,
                .sectorStartAddress=0x5c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x105c00,
                .asyncAccess=1,
        },
        {
                .sectorId=20,
                .sectorStartAddress=0x6000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x106000,
                .asyncAccess=1,
        },
        {
                .sectorId=21,
                .sectorStartAddress=0x6400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x106400,
                .asyncAccess=1,
        },
        {
                .sectorId=22,
                .sectorStartAddress=0x6800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x106800,
                .asyncAccess=1,
        },
        {
                .sectorId=23,
                .sectorStartAddress=0x6c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x106c00,
                .asyncAccess=1,
        },
        {
                .sectorId=24,
                .sectorStartAddress=0x7000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x107000,
                .asyncAccess=1,
        },
        {
                .sectorId=25,
                .sectorStartAddress=0x7400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x107400,
                .asyncAccess=1,
        },
        {
                .sectorId=26,
                .sectorStartAddress=0x7800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x107800,
                .asyncAccess=1,
        },
        {
                .sectorId=27,
                .sectorStartAddress=0x7c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x107c00,
                .asyncAccess=1,
        },
        {
                .sectorId=28,
                .sectorStartAddress=0x8000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x108000,
                .asyncAccess=1,
        },
        {
                .sectorId=29,
                .sectorStartAddress=0x8400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x108400,
                .asyncAccess=1,
        },
        {
                .sectorId=30,
                .sectorStartAddress=0x8800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x108800,
                .asyncAccess=1,
        },
        {
                .sectorId=31,
                .sectorStartAddress=0x8c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x108c00,
                .asyncAccess=1,
        },
        {
                .sectorId=32,
                .sectorStartAddress=0x9000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x109000,
                .asyncAccess=1,
        },
        {
                .sectorId=33,
                .sectorStartAddress=0x9400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x109400,
                .asyncAccess=1,
        },
        {
                .sectorId=34,
                .sectorStartAddress=0x9800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x109800,
                .asyncAccess=1,
        },
        {
                .sectorId=35,
                .sectorStartAddress=0x9c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x109c00,
                .asyncAccess=1,
        },
        {
                .sectorId=36,
                .sectorStartAddress=0xa000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10a000,
                .asyncAccess=1,
        },
        {
                .sectorId=37,
                .sectorStartAddress=0xa400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10a400,
                .asyncAccess=1,
        },
        {
                .sectorId=38,
                .sectorStartAddress=0xa800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10a800,
                .asyncAccess=1,
        },
        {
                .sectorId=39,
                .sectorStartAddress=0xac00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10ac00,
                .asyncAccess=1,
        },
        {
                .sectorId=40,
                .sectorStartAddress=0xb000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10b000,
                .asyncAccess=1,
        },
        {
                .sectorId=41,
                .sectorStartAddress=0xb400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10b400,
                .asyncAccess=1,
        },
        {
                .sectorId=42,
                .sectorStartAddress=0xb800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10b800,
                .asyncAccess=1,
        },
        {
                .sectorId=43,
                .sectorStartAddress=0xbc00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10bc00,
                .asyncAccess=1,
        },
        {
                .sectorId=44,
                .sectorStartAddress=0xc000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10c000,
                .asyncAccess=1,
        },
        {
                .sectorId=45,
                .sectorStartAddress=0xc400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10c400,
                .asyncAccess=1,
        },
        {
                .sectorId=46,
                .sectorStartAddress=0xc800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10c800,
                .asyncAccess=1,
        },
        {
                .sectorId=47,
                .sectorStartAddress=0xcc00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10cc00,
                .asyncAccess=1,
        },
        {
                .sectorId=48,
                .sectorStartAddress=0xd000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10d000,
                .asyncAccess=1,
        },
        {
                .sectorId=49,
                .sectorStartAddress=0xd400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10d400,
                .asyncAccess=1,
        },
        {
                .sectorId=50,
                .sectorStartAddress=0xd800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10d800,
                .asyncAccess=1,
        },
        {
                .sectorId=51,
                .sectorStartAddress=0xdc00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10dc00,
                .asyncAccess=1,
        },
        {
                .sectorId=52,
                .sectorStartAddress=0xe000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10e000,
                .asyncAccess=1,
        },
        {
                .sectorId=53,
                .sectorStartAddress=0xe400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10e400,
                .asyncAccess=1,
        },
        {
                .sectorId=54,
                .sectorStartAddress=0xe800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10e800,
                .asyncAccess=1,
        },
        {
                .sectorId=55,
                .sectorStartAddress=0xec00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10ec00,
                .asyncAccess=1,
        },
        {
                .sectorId=56,
                .sectorStartAddress=0xf000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10f000,
                .asyncAccess=1,
        },
        {
                .sectorId=57,
                .sectorStartAddress=0xf400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10f400,
                .asyncAccess=1,
        },
        {
                .sectorId=58,
                .sectorStartAddress=0xf800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10f800,
                .asyncAccess=1,
        },
        {
                .sectorId=59,
                .sectorStartAddress=0xfc00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x10fc00,
                .asyncAccess=1,
        },
        {
                .sectorId=60,
                .sectorStartAddress=0x10000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x110000,
                .asyncAccess=1,
        },
        {
                .sectorId=61,
                .sectorStartAddress=0x10400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x110400,
                .asyncAccess=1,
        },
        {
                .sectorId=62,
                .sectorStartAddress=0x10800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x110800,
                .asyncAccess=1,
        },
        {
                .sectorId=63,
                .sectorStartAddress=0x10c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x110c00,
                .asyncAccess=1,
        },
        {
                .sectorId=64,
                .sectorStartAddress=0x11000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x111000,
                .asyncAccess=1,
        },
        {
                .sectorId=65,
                .sectorStartAddress=0x11400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x111400,
                .asyncAccess=1,
        },
        {
                .sectorId=66,
                .sectorStartAddress=0x11800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x111800,
                .asyncAccess=1,
        },
        {
                .sectorId=67,
                .sectorStartAddress=0x11c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x111c00,
                .asyncAccess=1,
        },
        {
                .sectorId=68,
                .sectorStartAddress=0x12000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x112000,
                .asyncAccess=1,
        },
        {
                .sectorId=69,
                .sectorStartAddress=0x12400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x112400,
                .asyncAccess=1,
        },
        {
                .sectorId=70,
                .sectorStartAddress=0x12800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x112800,
                .asyncAccess=1,
        },
        {
                .sectorId=71,
                .sectorStartAddress=0x12c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x112c00,
                .asyncAccess=1,
        },
        {
                .sectorId=72,
                .sectorStartAddress=0x13000,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x113000,
                .asyncAccess=1,
        },
        {
                .sectorId=73,
                .sectorStartAddress=0x13400,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x113400,
                .asyncAccess=1,
        },
        {
                .sectorId=74,
                .sectorStartAddress=0x13800,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x113800,
                .asyncAccess=1,
        },
        {
                .sectorId=75,
                .sectorStartAddress=0x13c00,
                .sectorSize=0x400,
                .pageSize=8,
                .sectorHwStartAddress=0x113c00,
                .asyncAccess=1,
        },
};

extern void Fee_JobEndNotification(void);
extern void Fee_JobErrorNotification(void);




static const Fls_ConfigType flashConfig={
        .acEraseFunPtr=NULL,
        .acWriteFunPtr=NULL,
        .jobEndNotificationFunPtr=Fee_JobEndNotification,
        .jobErrorNotificationFunPtr=Fee_JobErrorNotification,
        .eDefaultMode=MEMIF_MODE_SLOW,
        .maxReadFastMode=512,
        .maxReadNormalMode=256,
        .maxWriteFastMode=128,
        .maxWriteNormalMode=8,
        .ConfigSectorNum=76,
        .sectorList=sectorList,
};


/* cluster */
static Fee_ClusterType clrType0[6]={
        {
                .StartAddr=0x1000,
                .Length=0x400,
        },
        {
                .StartAddr=0x1400,
                .Length=0x400,
        },
        {
                .StartAddr=0x1800,
                .Length=0x400,
        },
        {
                .StartAddr=0x1c00,
                .Length=0x400,
        },
        {
                .StartAddr=0x2000,
                .Length=0x400,
        },
        {
                .StartAddr=0x2400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType1[6]={
        {
                .StartAddr=0x2800,
                .Length=0x400,
        },
        {
                .StartAddr=0x2c00,
                .Length=0x400,
        },
        {
                .StartAddr=0x3000,
                .Length=0x400,
        },
        {
                .StartAddr=0x3400,
                .Length=0x400,
        },
        {
                .StartAddr=0x3800,
                .Length=0x400,
        },
        {
                .StartAddr=0x3c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType2[6]={
        {
                .StartAddr=0x4000,
                .Length=0x400,
        },
        {
                .StartAddr=0x4400,
                .Length=0x400,
        },
        {
                .StartAddr=0x4800,
                .Length=0x400,
        },
        {
                .StartAddr=0x4c00,
                .Length=0x400,
        },
        {
                .StartAddr=0x5000,
                .Length=0x400,
        },
        {
                .StartAddr=0x5400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType3[2]={
        {
                .StartAddr=0x5800,
                .Length=0x400,
        },
        {
                .StartAddr=0x5c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType4[2]={
        {
                .StartAddr=0x6000,
                .Length=0x400,
        },
        {
                .StartAddr=0x6400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType5[2]={
        {
                .StartAddr=0x6800,
                .Length=0x400,
        },
        {
                .StartAddr=0x6c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType6[2]={
        {
                .StartAddr=0x7000,
                .Length=0x400,
        },
        {
                .StartAddr=0x7400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType7[2]={
        {
                .StartAddr=0x7800,
                .Length=0x400,
        },
        {
                .StartAddr=0x7c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType8[2]={
        {
                .StartAddr=0x8000,
                .Length=0x400,
        },
        {
                .StartAddr=0x8400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType9[2]={
        {
                .StartAddr=0x8800,
                .Length=0x400,
        },
        {
                .StartAddr=0x8c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType10[2]={
        {
                .StartAddr=0x9000,
                .Length=0x400,
        },
        {
                .StartAddr=0x9400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType11[2]={
        {
                .StartAddr=0x9800,
                .Length=0x400,
        },
        {
                .StartAddr=0x9c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType12[2]={
        {
                .StartAddr=0xa000,
                .Length=0x400,
        },
        {
                .StartAddr=0xa400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType13[2]={
        {
                .StartAddr=0xa800,
                .Length=0x400,
        },
        {
                .StartAddr=0xac00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType14[2]={
        {
                .StartAddr=0xb000,
                .Length=0x400,
        },
        {
                .StartAddr=0xb400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType15[2]={
        {
                .StartAddr=0xb800,
                .Length=0x400,
        },
        {
                .StartAddr=0xbc00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType16[2]={
        {
                .StartAddr=0xc000,
                .Length=0x400,
        },
        {
                .StartAddr=0xc400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType17[2]={
        {
                .StartAddr=0xc800,
                .Length=0x400,
        },
        {
                .StartAddr=0xcc00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType18[2]={
        {
                .StartAddr=0xd000,
                .Length=0x400,
        },
        {
                .StartAddr=0xd400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType19[2]={
        {
                .StartAddr=0xd800,
                .Length=0x400,
        },
        {
                .StartAddr=0xdc00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType20[2]={
        {
                .StartAddr=0xe000,
                .Length=0x400,
        },
        {
                .StartAddr=0xe400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType21[2]={
        {
                .StartAddr=0xe800,
                .Length=0x400,
        },
        {
                .StartAddr=0xec00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType22[2]={
        {
                .StartAddr=0xf000,
                .Length=0x400,
        },
        {
                .StartAddr=0xf400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType23[2]={
        {
                .StartAddr=0xf800,
                .Length=0x400,
        },
        {
                .StartAddr=0xfc00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType24[2]={
        {
                .StartAddr=0x10000,
                .Length=0x400,
        },
        {
                .StartAddr=0x10400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType25[2]={
        {
                .StartAddr=0x10800,
                .Length=0x400,
        },
        {
                .StartAddr=0x10c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType26[2]={
        {
                .StartAddr=0x11000,
                .Length=0x400,
        },
        {
                .StartAddr=0x11400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType27[2]={
        {
                .StartAddr=0x11800,
                .Length=0x400,
        },
        {
                .StartAddr=0x11c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType28[2]={
        {
                .StartAddr=0x12000,
                .Length=0x400,
        },
        {
                .StartAddr=0x12400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType29[2]={
        {
                .StartAddr=0x12800,
                .Length=0x400,
        },
        {
                .StartAddr=0x12c00,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType30[2]={
        {
                .StartAddr=0x13000,
                .Length=0x400,
        },
        {
                .StartAddr=0x13400,
                .Length=0x400,
        },
};
static Fee_ClusterType clrType31[2]={
        {
                .StartAddr=0x13800,
                .Length=0x400,
        },
        {
                .StartAddr=0x13c00,
                .Length=0x400,
        },
};

static const Fee_ClusterGroupType clusterConfig[32]={
        {
                .ReservedSize=0,
                .ClrCount=6,
                .ClrPtr=clrType0,
        },
        {
                .ReservedSize=0,
                .ClrCount=6,
                .ClrPtr=clrType1,
        },
        {
                .ReservedSize=0,
                .ClrCount=6,
                .ClrPtr=clrType2,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType3,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType4,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType5,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType6,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType7,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType8,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType9,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType10,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType11,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType12,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType13,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType14,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType15,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType16,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType17,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType18,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType19,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType20,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType21,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType22,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType23,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType24,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType25,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType26,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType27,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType28,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType29,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType30,
        },
        {
                .ReservedSize=0,
                .ClrCount=2,
                .ClrPtr=clrType31,
        },
};

/* block */
static const Fee_BlockConfigType blockConfig[32]={
        {
                .BlockNumber=1,
                .BlockSize=256,
                .ClrGrp=0,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=2,
                .BlockSize=256,
                .ClrGrp=1,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=3,
                .BlockSize=256,
                .ClrGrp=2,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=4,
                .BlockSize=32,
                .ClrGrp=3,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=5,
                .BlockSize=40,
                .ClrGrp=4,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=6,
                .BlockSize=32,
                .ClrGrp=5,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=7,
                .BlockSize=208,
                .ClrGrp=6,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=8,
                .BlockSize=208,
                .ClrGrp=7,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=9,
                .BlockSize=208,
                .ClrGrp=8,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=10,
                .BlockSize=208,
                .ClrGrp=9,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=11,
                .BlockSize=208,
                .ClrGrp=10,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=12,
                .BlockSize=208,
                .ClrGrp=11,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=13,
                .BlockSize=208,
                .ClrGrp=12,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=14,
                .BlockSize=208,
                .ClrGrp=13,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=15,
                .BlockSize=208,
                .ClrGrp=14,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=16,
                .BlockSize=208,
                .ClrGrp=15,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=17,
                .BlockSize=208,
                .ClrGrp=16,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=18,
                .BlockSize=208,
                .ClrGrp=17,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=19,
                .BlockSize=208,
                .ClrGrp=18,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=20,
                .BlockSize=208,
                .ClrGrp=19,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=21,
                .BlockSize=208,
                .ClrGrp=20,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=22,
                .BlockSize=208,
                .ClrGrp=21,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=23,
                .BlockSize=208,
                .ClrGrp=22,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=24,
                .BlockSize=208,
                .ClrGrp=23,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=25,
                .BlockSize=208,
                .ClrGrp=24,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=26,
                .BlockSize=208,
                .ClrGrp=25,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=27,
                .BlockSize=208,
                .ClrGrp=26,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=28,
                .BlockSize=208,
                .ClrGrp=27,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=29,
                .BlockSize=208,
                .ClrGrp=28,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=30,
                .BlockSize=208,
                .ClrGrp=29,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=31,
                .BlockSize=32,
                .ClrGrp=30,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
        {
                .BlockNumber=32,
                .BlockSize=208,
                .ClrGrp=31,
                .ImmediateData=1,
                .BlockAssignment=FEE_PROJECT_RESERVED,
        },
};



/* top */

const Fee_ModuleUserConfig_t FEEGenConfig={
        .blockCnt=32,
        .clusterCnt=32,
        .clusterConfigPtr=clusterConfig,
        .blockConfigPtr=blockConfig,
        .flashConfigPtr=&flashConfig,
};





