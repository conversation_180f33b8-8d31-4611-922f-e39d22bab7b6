
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file pin_mux.c
 * @brief 
 * 
 */



#include "pin_mux.h"
#include "pins_driver.h"

const pin_settings_config_t g_pin_mux_InitConfigArr0[NUM_OF_CONFIGURED_PINS0] = {
    /*PTE_15-2-GPIO-REBOOT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 15U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_11-5-LINFlexD3_TX-UART_TXD_4G*/
    {
        .base=PCTRLE,
        .pinPortIdx = 11U,
        .pullConfig = PCTRL_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT5,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_10-6-LINFlexD3_RX-UART_RXD_4G*/
    {
        .base=PCTRLE,
        .pinPortIdx = 10U,
        .pullConfig = PCTRL_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT5,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_13-7-GPIO-T106_STATUS_MCU*/
    {
        .base=PCTRLE,
        .pinPortIdx = 13U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_5-8-GPIO-B_KEY_INPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 5U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_4-9-GPIO-I_KEY_INPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 4U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_7-15-EXTAL-24MHz_FXOSC*/
    {
        .base=PCTRLB,
        .pinPortIdx = 7U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_6-16-XTAL-24MHz_FXOSC*/
    {
        .base=PCTRLB,
        .pinPortIdx = 6U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_14-17-GPIO-FWD_IO*/
    {
        .base=PCTRLE,
        .pinPortIdx = 14U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTE_3-18-GPIO-SOS_KEY_INPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 3U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        //.intConfig = PCTRL_INT_RISING_EDGE,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_12-19-GPIO-MCU_power_wifi*/
    {
        .base=PCTRLE,
        .pinPortIdx = 12U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_17-20-GPIO-MCU_POWER_GNSSbackup*/
    {
        .base=PCTRLD,
        .pinPortIdx = 17U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_16-21-EXTAL32-32.768KHz_SXOSC*/
    {
        .base=PCTRLD,
        .pinPortIdx = 16U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_15-22-XTAL32-32.768KHz_SXOSC*/
    {
        .base=PCTRLD,
        .pinPortIdx = 15U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_9-23-GPIO-ACC_INPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 9U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        //.pullConfig = PCTRL_INTERNAL_PULL_DOWN_ENABLED,
        //.pullConfig = PCTRL_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_14-24-GPIO-MCU_RST_GNSS*/
    {
        .base=PCTRLD,
        .pinPortIdx = 14U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTD_13-25-GPIO-T106_NET_STATUS_MCU*/
    {
        .base=PCTRLD,
        .pinPortIdx = 13U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_8-26-GPIO-BLE_OUTPUT_INT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 8U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        //.intConfig = PCTRL_INT_FALLING_EDGE,
        .intConfig = PCTRL_INT_EITHER_EDGE,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_5-27-GPIO-MCU_WAKEUP_T106*/
    {
        .base=PCTRLB,
        .pinPortIdx = 5U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_3-29-LINFlexD0_TX-MCU_UART2_TX*/
    {
        .base=PCTRLC,
        .pinPortIdx = 3U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_2-30-LINFlexD0_RX-MCU_UART2_RX*/
    {
        .base=PCTRLC,
        .pinPortIdx = 2U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_7-31-GPIO-LED1*/
    {
        .base=PCTRLD,
        .pinPortIdx = 7U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_6-32-GPIO-LED2*/
    {
        .base=PCTRLD,
        .pinPortIdx = 6U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_5-33-GPIO-RTC_INT*/
    {
        .base=PCTRLD,
        .pinPortIdx = 5U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_12-34-GPIO-BLE_WAKEUP_CTL*/
    {
        .base=PCTRLD,
        .pinPortIdx = 12U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_11-35-GPIO-BLE_RST_CTL*/
    {
        .base=PCTRLD,
        .pinPortIdx = 11U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTC_1-39-CAN3_TX-CAN3_TX*/
    {
        .base=PCTRLC,
        .pinPortIdx = 1U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_0-40-CAN3_RX-CAN3_RX*/
    {
        .base=PCTRLC,
        .pinPortIdx = 0U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_9-41-LINFlexD4_TX-RLIN21_TX*/
    {
        .base=PCTRLD,
        .pinPortIdx = 9U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT2,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_8-42-LINFlexD4_RX-RLIN21_RX*/
    {
        .base=PCTRLD,
        .pinPortIdx = 8U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        //.mux = PCTRL_MUX_ALT2,
        .mux = PCTRL_MUX_ALT6,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_17-43-ADC0_SE15-BAT_VOLTAGE_DET*/
    {
        .base=PCTRLC,
        .pinPortIdx = 17U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_16-44-ADC0_SE14-MIC_VOLTAGE_DET*/
    {
        .base=PCTRLC,
        .pinPortIdx = 16U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_15-45-ADC0_SE13-BAT_NTC_DET*/
    {
        .base=PCTRLC,
        .pinPortIdx = 15U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_14-46-ADC0_SE12-RUN_VOLTAGE_DET*/
    {
        .base=PCTRLC,
        .pinPortIdx = 14U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_3-47-GPIO-MCU_TRANSLATOR_EN*/
    {
        .base=PCTRLB,
        .pinPortIdx = 3U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_2-48-GPIO-GNSS_ANT_OPEN*/
    {
        .base=PCTRLB,
        .pinPortIdx = 2U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_13-49-GPIO-GNSS_ANT_SHORT*/
    {
        .base=PCTRLC,
        .pinPortIdx = 13U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_11-51-GPIO-MCU_INFO_T106_IO*/
    {
        .base=PCTRLC,
        .pinPortIdx = 11U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_10-52-GPIO-BAT_CHARGE_EN*/
    {
        .base=PCTRLC,
        .pinPortIdx = 10U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_1-53-GPIO-Sensor_INT2*/
    {
        .base=PCTRLB,
        .pinPortIdx = 1U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_0-54-GPIO-Sensor_INT1*/
    {
        .base=PCTRLB,
        .pinPortIdx = 0U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_9-55-GPIO-CAN3_STB*/
    {
        .base=PCTRLC,
        .pinPortIdx = 9U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_8-56-GPIO-CAN4_STB*/
    {
        .base=PCTRLC,
        .pinPortIdx = 8U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_7-57-I2C2_SCL-MCU_I2C_SCL*/
    {
        .base=PCTRLA,
        .pinPortIdx = 7U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT5,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_6-58-I2C2_SDA-MCU_I2C_SDA*/
    {
        .base=PCTRLA,
        .pinPortIdx = 6U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT5,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_7-59-GPIO-MCU_EN_VCC8V*/
    {
        .base=PCTRLE,
        .pinPortIdx = 7U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_17-62-GPIO-LED3*/
    {
        .base=PCTRLA,
        .pinPortIdx = 17U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_17-63-GPIO-LED4*/
    {
        .base=PCTRLB,
        .pinPortIdx = 17U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_16-64-GPIO-BATTERY_CUTOFF*/
    {
        .base=PCTRLB,
        .pinPortIdx = 16U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTB_15-65-GPIO-MCUGPIO_OUT_T106_PWRKEY*/
    {
        .base=PCTRLB,
        .pinPortIdx = 15U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_14-66-GPIO-LED5*/
    {
        .base=PCTRLB,
        .pinPortIdx = 14U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_13-67-CAN2_TX-CAN2_TX*/
    {
        .base=PCTRLB,
        .pinPortIdx = 13U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_12-68-CAN2_RX-CAN2_RX*/
    {
        .base=PCTRLB,
        .pinPortIdx = 12U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_4-69-GPIO-LED6*/
    {
        .base=PCTRLD,
        .pinPortIdx = 4U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_3-70-CAN5_TX-CAN4_TX*/
    {
        .base=PCTRLD,
        .pinPortIdx = 3U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT5,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_2-71-CAN5_RX-CAN4_RX*/
    {
        .base=PCTRLD,
        .pinPortIdx = 2U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT5,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_3-72-GPIO-CAN2_STB*/
    {
        .base=PCTRLA,
        .pinPortIdx = 3U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_2-73-GPIO-CAN1_STB*/
    {
        .base=PCTRLA,
        .pinPortIdx = 2U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_11-74-GPIO-4V1_PWR_SW*/
    {
        .base=PCTRLB,
        .pinPortIdx = 11U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_10-75-GPIO-MCU_ECALL_BTN_BL*/
    {
        .base=PCTRLB,
        .pinPortIdx = 10U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_9-76-GPIO-LTE_ANT_SHORT*/
    {
        .base=PCTRLB,
        .pinPortIdx = 9U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_8-77-GPIO-LTE_ANT_OPEN*/
    {
        .base=PCTRLB,
        .pinPortIdx = 8U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_1-78-GPIO-3V3_PWR_SW*/
    {
        .base=PCTRLA,
        .pinPortIdx = 1U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_0-79-GPIO-MUTE_OUTPUT*/
    {
        .base=PCTRLA,
        .pinPortIdx = 0U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        //.mux = PCTRL_MUX_AS_GPIO,
        .mux = PCTRL_PIN_DISABLED,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_7-80-CAN1_TX-CAN1_TX*/
    {
        .base=PCTRLC,
        .pinPortIdx = 7U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT3,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_6-81-CAN1_RX-CAN1_RX*/
    {
        .base=PCTRLC,
        .pinPortIdx = 6U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT3,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_16-82-GPIO-ACC_OUT_CTL*/
    {
        .base=PCTRLA,
        .pinPortIdx = 16U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_15-83-GPIO-MCU_SOS_LED_RED*/
    {
        .base=PCTRLA,
        .pinPortIdx = 15U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_6-84-GPIO-RLIN21_SLP_N*/
    {
        .base=PCTRLE,
        .pinPortIdx = 6U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTE_2-85-GPIO-SCHG_STA_INPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 2U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_14-88-GPIO-MCU_SOS_LED_GREEN*/
    {
        .base=PCTRLA,
        .pinPortIdx = 14U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_13-89-SPI3_PCS0-MCU_SPI_CS*/
    {
        .base=PCTRLA,
        .pinPortIdx = 13U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_12-90-SPI3_SCK-MCU_SPI_CLK*/
    {
        .base=PCTRLA,
        .pinPortIdx = 12U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_11-91-SPI3_SIN-MCU_SPI_MISO*/
    {
        .base=PCTRLA,
        .pinPortIdx = 11U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_10-92-SPI3_SOUT-MCU_SPI_MOSI*/
    {
        .base=PCTRLA,
        .pinPortIdx = 10U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_1-93-GPIO-FCHG_STA_INPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 1U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTE_0-94-GPIO-T106_WAKEUP_MCU*/
    {
        .base=PCTRLE,
        .pinPortIdx = 0U,
        .pullConfig = PCTRL_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_INT_FALLING_EDGE,
        //.intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_5-95-GPIO-MCUGPIO_OUT_T106_SHDN_N*/
    {
        .base=PCTRLC,
        .pinPortIdx = 5U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_4-96-JTAG_TCK SWD_CLK-MCU_SWDCLK*/
    {
        .base=PCTRLC,
        .pinPortIdx = 4U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT7,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_5-97-RESET_b-T106_RESET_MCU*/
    {
        .base=PCTRLA,
        .pinPortIdx = 5U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT7,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_4-98-JTAG_TMS SWD_IO-MCU_SWDIO*/
    {
        .base=PCTRLA,
        .pinPortIdx = 4U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT7,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_9-99-GPIO-MCUGPIO_OUT_T106_RESET*/
    {
        .base=PCTRLA,
        .pinPortIdx = 9U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTA_8-100-GPIO-MCU_PRTRG_GNSS*/
    {
        .base=PCTRLA,
        .pinPortIdx = 8U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOA,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_12-50-GPIO-MAN_ANT_SW*/
    {
        .base=PCTRLC,
        .pinPortIdx = 12U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTE_16-1-eTMR2_CH7-AIRBAG_IMPUT*/
    {
        .base=PCTRLE,
        .pinPortIdx = 16U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT4,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOE,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTD_10-36-GPIO-GNSS_ANT_SW*/
    {
        .base=PCTRLD,
        .pinPortIdx = 10U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOD,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_4-28-GPIO-WHEEL_TICK_IO*/
    {
        .base=PCTRLB,
        .pinPortIdx = 4U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_HIGH_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
};


/***********************************************************************************************************************
 * EOF
 **********************************************************************************************************************/