/* 
* @brief: The file is used to define the peripherals instances number and IO pin used by the application
* for the detailed configuration, please refer to the config files generated by <PERSON><PERSON> under the "board" folder
* of course, the YCT project is also a good GUI reference for the configurations
*/

#ifndef BOARD_H
#define BOARD_H

#include "sdk_project_config.h"

/*********************************************************************************************
 *  define the used GPIO pins as output for external device control 
 * ******************************************************************************************/

/* use the PTA2 as CAN1_STB control and active(enter standby mode) if it's HIGH */
#define CAN1_STB_GPIO_PORT  GPIOA
#define CAN1_STB_GPIO_PIN   (1U << 2)
#define CAN1_PHY_NORMAL_MODE()  PINS_DRV_ClearPins(CAN1_STB_GPIO_PORT, CAN1_STB_GPIO_PIN)
#define CAN1_PHY_STANDBY_MODE() PINS_DRV_SetPins(CAN1_STB_GPIO_PORT, CAN1_STB_GPIO_PIN)

/* use the PTA3 as CAN2_STB control and active(enter standby mode) if it's HIGH */
#define CAN2_STB_GPIO_PORT  GPIOA
#define CAN2_STB_GPIO_PIN   (1U << 3)
#define CAN2_PHY_NORMAL_MODE()  PINS_DRV_ClearPins(CAN2_STB_GPIO_PORT, CAN2_STB_GPIO_PIN)
#define CAN2_PHY_STANDBY_MODE() PINS_DRV_SetPins(CAN2_STB_GPIO_PORT, CAN2_STB_GPIO_PIN)

/* use the PTC9 as CAN3_STB control and active(enter standby mode) if it's HIGH */
#define CAN3_STB_GPIO_PORT  GPIOC
#define CAN3_STB_GPIO_PIN   (1U << 9)
#define CAN3_PHY_NORMAL_MODE()  PINS_DRV_ClearPins(CAN3_STB_GPIO_PORT, CAN3_STB_GPIO_PIN)
#define CAN3_PHY_STANDBY_MODE() PINS_DRV_SetPins(CAN3_STB_GPIO_PORT, CAN3_STB_GPIO_PIN)

/* use the PTC8 as CAN5_STB control and active(enter standby mode) if it's HIGH */
#define CAN5_STB_GPIO_PORT  GPIOC
#define CAN5_STB_GPIO_PIN   (1U << 8)
#define CAN5_PHY_NORMAL_MODE()  PINS_DRV_ClearPins(CAN5_STB_GPIO_PORT, CAN5_STB_GPIO_PIN)
#define CAN5_PHY_STANDBY_MODE() PINS_DRV_SetPins(CAN5_STB_GPIO_PORT, CAN5_STB_GPIO_PIN)

/* use the PTD14 as MCU_RST_GNSS, output LOW to reset the external GNSS module */
#define MCU_RST_GNSS_GPIO_PORT  GPIOD
#define MCU_RST_GNSS_GPIO_PIN   (1U << 14)
#define MCU_RST_GNSS_LOW()  PINS_DRV_ClearPins(MCU_RST_GNSS_GPIO_PORT, MCU_RST_GNSS_GPIO_PIN)
#define MCU_RST_GNSS_TOGGLE()  PINS_DRV_TogglePins(MCU_RST_GNSS_GPIO_PORT, MCU_RST_GNSS_GPIO_PIN)

/* use PTD17 as MCU_POWER_VCC8V, output LOW/HIGH to disable/enable power  supply of the external eCall module */
#define MCU_POWER_VCC8V_GPIO_PORT  GPIOD
#define MCU_POWER_VCC8V_GPIO_PIN   (1U << 17)
#define MCU_POWER_VCC8V_DISABLE()  PINS_DRV_ClearPins(MCU_POWER_VCC8V_GPIO_PORT, MCU_POWER_VCC8V_GPIO_PIN)
#define MCU_POWER_VCC8V_ENABLE()   PINS_DRV_SetPins(MCU_POWER_VCC8V_GPIO_PORT, MCU_POWER_VCC8V_GPIO_PIN)

/* use PTB5 as MCU_WAKEUP_T106, output HIGH to wakeup the external 4G module */
#define MCU_WAKEUP_T106_GPIO_PORT  GPIOB
#define MCU_WAKEUP_T106_GPIO_PIN   (1U << 5)
#define MCU_WAKEUP_T106_HIGH()  PINS_DRV_SetPins(MCU_WAKEUP_T106_GPIO_PORT, MCU_WAKEUP_T106_GPIO_PIN)
#define MCU_WAKEUP_T106_LOW()   PINS_DRV_ClearPins(MCU_WAKEUP_T106_GPIO_PORT, MCU_WAKEUP_T106_GPIO_PIN)


/*********************************************************************************************
 *  define the used GPIO pins as input for external device status read 
 * ******************************************************************************************/

/* use the PTE16 as AIRBAG_INPUT get the airbag status */
#define AIRBAG_INPUT_GPIO_PORT      GPIOE
#define AIRBAG_INPUT_GPIO_PIN_NUM   (16U)
#define AIRBAG_INPUT_GPIO_PIN       (1U << AIRBAG_INPUT_GPIO_PIN_NUM)

#define AIRBAG_INPUT_STATUS_GET()  ((PINS_DRV_ReadPins(AIRBAG_INPUT_GPIO_PORT) & AIRBAG_INPUT_GPIO_PIN) >> AIRBAG_INPUT_GPIO_PIN_NUM)

/* use the PTE13 as T106_STATUS_MCU get the 4G module input status */
#define T106_STATUS_MCU_GPIO_PORT      GPIOE
#define T106_STATUS_MCU_GPIO_PIN_NUM   (13U)
#define T106_STATUS_MCU_GPIO_PIN       (1U << T106_STATUS_MCU_GPIO_PIN_NUM)

#define T106_STATUS_MCU_GET()  ((PINS_DRV_ReadPins(T106_STATUS_MCU_GPIO_PORT) & T106_STATUS_MCU_GPIO_PIN) >> T106_STATUS_MCU_GPIO_PIN_NUM)  

/* use the PTE5 as B_KEY_INPUT get the Bcall Key input status */
#define B_KEY_INPUT_GPIO_PORT      GPIOE
#define B_KEY_INPUT_GPIO_PIN_NUM   (5U)
#define B_KEY_INPUT_GPIO_PIN       (1U << B_KEY_INPUT_GPIO_PIN_NUM)

#define B_KEY_INPUT_STATUS_GET()  ((PINS_DRV_ReadPins(B_KEY_INPUT_GPIO_PORT) & B_KEY_INPUT_GPIO_PIN) >> B_KEY_INPUT_GPIO_PIN_NUM)

/* use the PTE4 as I_KEY_INPUT get the Icall Key input status */
#define I_KEY_INPUT_GPIO_PORT      GPIOE
#define I_KEY_INPUT_GPIO_PIN_NUM   (4U)
#define I_KEY_INPUT_GPIO_PIN       (1U << I_KEY_INPUT_GPIO_PIN_NUM)

#define I_KEY_INPUT_STATUS_GET()  ((PINS_DRV_ReadPins(I_KEY_INPUT_GPIO_PORT) & I_KEY_INPUT_GPIO_PIN) >> I_KEY_INPUT_GPIO_PIN_NUM)

/* use the PTE3 as SOS_KEY_INPUT get the SOS Key input status */
#define SOS_KEY_INPUT_GPIO_PORT      GPIOE
#define SOS_KEY_INPUT_GPIO_PIN_NUM   (3U)
#define SOS_KEY_INPUT_GPIO_PIN       (1U << SOS_KEY_INPUT_GPIO_PIN_NUM)

#define SOS_KEY_INPUT_STATUS_GET()  ((PINS_DRV_ReadPins(SOS_KEY_INPUT_GPIO_PORT) & SOS_KEY_INPUT_GPIO_PIN) >> SOS_KEY_INPUT_GPIO_PIN_NUM)

/* use the PTE9 as ACC_INPUT get the car ACC ignition input status */
#define ACC_INPUT_GPIO_PORT      GPIOE
#define ACC_INPUT_GPIO_PIN_NUM   (9U)
#define ACC_INPUT_GPIO_PIN       (1U << ACC_INPUT_GPIO_PIN_NUM)

#define ACC_INPUT_STATUS_GET()  ((PINS_DRV_ReadPins(ACC_INPUT_GPIO_PORT) & ACC_INPUT_GPIO_PIN) >> ACC_INPUT_GPIO_PIN_NUM)

/* use the PTD13 as T106_NET_STATUS_MCU get the 4G module networking input status */
#define T106_NET_STATUS_MCU_GPIO_PORT      GPIOD
#define T106_NET_STATUS_MCU_GPIO_PIN_NUM   (13U)
#define T106_NET_STATUS_MCU_GPIO_PIN       (1U << T106_NET_STATUS_MCU_GPIO_PIN_NUM)

#define T106_NET_STATUS_MCU_GET()  ((PINS_DRV_ReadPins(T106_NET_STATUS_MCU_GPIO_PORT) & T106_NET_STATUS_MCU_GPIO_PIN) >> T106_NET_STATUS_MCU_GPIO_PIN_NUM)

#define STATUS_HIGH   (1U)
#define STATUS_LOW    (0U)

#endif /* end of BOARD_H */  
