
/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file wdg_config.c
 * @brief 
 * 
 */



#include "wdg_config.h"


/*wdg_config0*/
const wdg_user_config_t wdg_config0 = {
    .clockSource=WDG_SXOSC_CLOCK,
    .opMode={
        .deepsleep=false,
        .debug=false,
    },
    .updateEnable=false,
    .intEnable=false,
    .winEnable=false,
    .windowValue=0,
    .timeoutValue=327680,
    .apbErrorResetEnable=0,
};
