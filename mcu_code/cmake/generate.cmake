cmake_minimum_required(VERSION 3.16)

set(sources
    ${PROJ_DIR}/board/clock_config.c
    ${PROJ_DIR}/board/pin_mux.c
    ${PROJ_DIR}/board/startup.S
    ${PROJ_DIR}/board/vector.S
    ${PROJ_DIR}/board/vector_table_copy.c
    ${PROJ_DIR}/board/RamInit0.S
    ${PROJ_DIR}/board/RamInit1.c
    ${PROJ_DIR}/board/RamInit2.c
    ${PROJ_DIR}/board/interrupt_config.c
    ${PROJ_DIR}/board/dma_config.c
    ${PROJ_DIR}/board/spi_config.c
    ${PROJ_DIR}/board/linflexd_uart_config.c
    ${PROJ_DIR}/board/linflexd_lin_config.c
    ${PROJ_DIR}/board/hcu_config.c
    ${PROJ_DIR}/board/can_config.c
    ${PROJ_DIR}/board/i2c_config.c
    ${PROJ_DIR}/board/rtc_config.c
    ${PROJ_DIR}/board/ptmr_config.c
    ${PROJ_DIR}/board/adc_config.c
    ${PROJ_DIR}/board/power_config.c
    ${PROJ_DIR}/board/wku_config.c
    ${PROJ_DIR}/board/wdg_config.c
    ${PROJ_DIR}/board/fee_config.c
    ${PROJ_DIR}/board/flash_config.c
    ${PROJ_DIR}/board/secure_boot_config.c
    ${PROJ_DIR}/board/etmr_config.c
    ${PROJ_DIR}/board/utility_print_config.c
    ${PROJ_DIR}/platform/drivers/src/clock/YTM32B1Mx/clock_YTM32B1Mx.c
    ${PROJ_DIR}/platform/drivers/src/pins/pins_driver.c
    ${PROJ_DIR}/platform/drivers/src/pins/pins_port_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/interrupt/interrupt_manager.c
    ${PROJ_DIR}/platform/drivers/src/dma/dma_driver.c
    ${PROJ_DIR}/platform/drivers/src/dma/dma_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/dma/dma_irq.c
    ${PROJ_DIR}/platform/drivers/src/spi/spi_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/spi/spi_irq.c
    ${PROJ_DIR}/platform/drivers/src/spi/spi_master_driver.c
    ${PROJ_DIR}/platform/drivers/src/spi/spi_shared_function.c
    ${PROJ_DIR}/platform/drivers/src/spi/spi_slave_driver.c
    ${PROJ_DIR}/platform/drivers/src/linflexd/linflexd_uart_driver.c
    ${PROJ_DIR}/platform/drivers/src/linflexd/linflexd_uart_irq.c
    ${PROJ_DIR}/platform/drivers/src/linflexd/linflexd_lin_driver.c
    ${PROJ_DIR}/platform/drivers/src/linflexd/linflexd_lin_irq.c
    ${PROJ_DIR}/platform/drivers/src/hcu/hcu_driver.c
    ${PROJ_DIR}/platform/drivers/src/hcu/hcu_irq.c
    ${PROJ_DIR}/platform/drivers/src/flexcan/flexcan_driver.c
    ${PROJ_DIR}/platform/drivers/src/flexcan/flexcan_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/flexcan/flexcan_irq.c
    ${PROJ_DIR}/platform/drivers/src/i2c/i2c_driver.c

    # ${PROJ_DIR}/platform/drivers/src/i2c/i2c_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/i2c/i2c_irq.c
    ${PROJ_DIR}/platform/drivers/src/rtc/rtc_driver.c
    ${PROJ_DIR}/platform/drivers/src/rtc/rtc_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/rtc/rtc_irq.c
    ${PROJ_DIR}/platform/drivers/src/ptmr/ptmr_driver.c
    ${PROJ_DIR}/platform/drivers/src/adc/adc_driver.c
    ${PROJ_DIR}/platform/drivers/src/power/power_manager.c
    ${PROJ_DIR}/platform/drivers/src/power/YTM32B1Mx/power_manager_YTM32B1Mx.c
    ${PROJ_DIR}/platform/drivers/src/wku/wku_driver.c
    ${PROJ_DIR}/platform/drivers/src/wdg/wdg_driver.c
    ${PROJ_DIR}/platform/drivers/src/wdg/wdg_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/fee/fee.c
    ${PROJ_DIR}/platform/drivers/src/fee/fls_async_driver.c
    ${PROJ_DIR}/platform/drivers/src/fee/fls_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/flash/flash_driver.c
    ${PROJ_DIR}/platform/drivers/src/etmr/etmr_common.c
    ${PROJ_DIR}/platform/drivers/src/etmr/etmr_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/etmr/etmr_ic_driver.c
    ${PROJ_DIR}/platform/drivers/src/etmr/etmr_pwm_driver.c

    # ${PROJ_DIR}/platform/devices/startup.c
    ${PROJ_DIR}/platform/devices/YTM32B1ME0/startup/system_YTM32B1ME0.c

    # ${PROJ_DIR}/platform/devices/YTM32B1ME0/startup/gcc/YTM32B1ME0_startup_gcc.S
    ${PROJ_DIR}/rtos/freertos/croutine.c
    ${PROJ_DIR}/rtos/freertos/event_groups.c
    ${PROJ_DIR}/rtos/freertos/list.c
    ${PROJ_DIR}/rtos/freertos/queue.c
    ${PROJ_DIR}/rtos/freertos/stream_buffer.c
    ${PROJ_DIR}/rtos/freertos/tasks.c
    ${PROJ_DIR}/rtos/freertos/timers.c
    ${PROJ_DIR}/rtos/freertos/portable/GCC/ARM_CM33_NTZ/non_secure/port.c
    ${PROJ_DIR}/rtos/freertos/portable/GCC/ARM_CM33_NTZ/non_secure/portasm.c
    ${PROJ_DIR}/rtos/freertos/portable/MemMang/heap_4.c
    ${PROJ_DIR}/rtos/osif/osif_freertos.c
    ${PROJ_DIR}/rtos/rtos.c
    ${PROJ_DIR}/middleware/utility_print/printf/printf.c

    # ${PROJ_DIR}/app/main.c
    ${PROJ_DIR}/task/LEDTask.c
    ${PROJ_DIR}/task/CANTask.c
    ${PROJ_DIR}/task/PMTask.c
    ${PROJ_DIR}/task/BATTask.c
    ${PROJ_DIR}/task/BTTask.c
    ${PROJ_DIR}/task/DIAGTask.c
    ${PROJ_DIR}/task/IPCTask.c
    ${PROJ_DIR}/task/LOGTask.c
    ${PROJ_DIR}/task/UpdateTask.c
    ${PROJ_DIR}/task/CommonTask.c
    ${PROJ_DIR}/task/CANDataUploadTask.c

    ${PROJ_DIR}/UserApp/app/airbag/airbag.c
    ${PROJ_DIR}/UserApp/app/bat/src/BatApi.c
    ${PROJ_DIR}/UserApp/app/bt/src/BtApi.c
    ${PROJ_DIR}/UserApp/app/bt/src/BLELin.c
    ${PROJ_DIR}/UserApp/app/can/src/CanApi.c
    ${PROJ_DIR}/UserApp/app/can/src/CanAuth.c
    ${PROJ_DIR}/UserApp/app/can/src/CanClient.c
    ${PROJ_DIR}/UserApp/app/can/src/CanFtm.c
    ${PROJ_DIR}/UserApp/app/can/src/CanMsgApi.c
    ${PROJ_DIR}/UserApp/app/can/src/RemoteControlTask.c
    ${PROJ_DIR}/UserApp/app/diag/src/DiagApi.c
    ${PROJ_DIR}/UserApp/app/ipc/src/IpcApi.c
    ${PROJ_DIR}/UserApp/app/led/src/LedApi.c
    ${PROJ_DIR}/UserApp/app/log/src/LogApi.c
    ${PROJ_DIR}/UserApp/app/pm/src/PmApi.c
    ${PROJ_DIR}/UserApp/app/update/src/UpdateApi.c
#    ${PROJ_DIR}/UserApp/app/xcall/src/ECallApi.c

    ${PROJ_DIR}/UserApp/framework/src/appqueue.c
    ${PROJ_DIR}/UserApp/framework/src/apptask.c

    ${PROJ_DIR}/UserApp/common/isoft/ComStack/CanIf/CanIf.c
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/CanSM/CanSM.c
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/ComM/ComM.c
    
    # NEW_TODO
    #${PROJ_DIR}/UserApp/common/isoft/ComStack/Nm/OsekNm.c
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/NmIf/Nm.c
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/CanNm/CanNm.c

    ${PROJ_DIR}/UserApp/common/isoft/Config/Can_Cfg.c
    ${PROJ_DIR}/UserApp/common/isoft/Config/CanIf_Cfg.c
    ${PROJ_DIR}/UserApp/common/isoft/Config/CanSM_Cfg.c
    ${PROJ_DIR}/UserApp/common/isoft/Config/CanTp_Cfg.c
    ${PROJ_DIR}/UserApp/common/isoft/Config/ComM_Cfg.c
    ${PROJ_DIR}/UserApp/common/isoft/Config/Dcm_Cfg.c

    ${PROJ_DIR}/UserApp/common/isoft/Config/Nm_Cfg.c
    # NEW_TODO
    # ${PROJ_DIR}/UserApp/common/isoft/Config/OsekNm_Cfg.c
    ${PROJ_DIR}/UserApp/common/isoft/Config/CanNm_Cfg.c

    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/CanTp/CanTp.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/CanTp/CanTp_Cbk.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/CanTp/CanTp_RX.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/CanTp/CanTp_TX.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/ComM/ComM_Dcm.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsd/DcmDsd.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl/DcmDsl_CommManage.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl/DcmDsl_MsgManage.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl/DcmDsl_PendingManage.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl/DcmDsl_ProtocolManage.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl/DcmDsl_SecurityManage.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl/DcmDsl_SessionManage.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsp/DcmDsp.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsp/UDS/UDS.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/Dcm.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/SysTimer/FreeRTimer.c
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dem/Dem.c
    ${PROJ_DIR}/UserApp/common/isoft/Rte/Rte_Dcm.c
    ${PROJ_DIR}/UserApp/common/isoft/SysServices/EcuM/EcuM.c

    # ${PROJ_DIR}/UserApp/common/library/src/clocks.c
    ${PROJ_DIR}/UserApp/common/library/src/gpio.c

    # ${PROJ_DIR}/UserApp/common/library/src/ostm0.c
    ${PROJ_DIR}/UserApp/common/library/src/pwm.c

    # ${PROJ_DIR}/UserApp/common/library/src/r_adc.c
    ${PROJ_DIR}/UserApp/common/library/src/r_can.c

    # ${PROJ_DIR}/UserApp/common/library/src/r_csih.c
    # ${PROJ_DIR}/UserApp/common/library/src/r_iic.c
    ${PROJ_DIR}/UserApp/common/library/src/r_interrupt.c

    # ${PROJ_DIR}/UserApp/common/library/src/r_tauj.c
    ${PROJ_DIR}/UserApp/common/library/src/r_wdt.c
    ${PROJ_DIR}/UserApp/common/library/src/rlin30.c
    ${PROJ_DIR}/UserApp/common/library/src/rtc.c

    ${PROJ_DIR}/UserApp/common/system/src/HrApi.c
    ${PROJ_DIR}/UserApp/common/system/src/NvApi.c
    ${PROJ_DIR}/UserApp/common/system/src/SystemApi.c
    ${PROJ_DIR}/platform/RTT/SEGGER_RTT.c
    ${PROJ_DIR}/platform/RTT/SEGGER_RTT_printf.c
    ${PROJ_DIR}/platform/RTT/SEGGER_RTT_ASM_ARMv7M.S

    #${PROJ_DIR}/tests/testTask_CAN.c
)

set(includes
    ${PROJ_DIR}/board
    ${PROJ_DIR}/platform/drivers/src/clock/YTM32B1Mx
    ${PROJ_DIR}/platform/drivers/src/pins
    ${PROJ_DIR}/platform/drivers/src/dma
    ${PROJ_DIR}/platform/drivers/src/spi
    ${PROJ_DIR}/platform/drivers/src/linflexd
    ${PROJ_DIR}/platform/drivers/src/hcu
    ${PROJ_DIR}/platform/drivers/src/flexcan
    ${PROJ_DIR}/platform/drivers/src/fee
    ${PROJ_DIR}/platform/drivers/src/i2c
    ${PROJ_DIR}/platform/drivers/src/rtc
    ${PROJ_DIR}/platform/drivers/src/ptmr
    ${PROJ_DIR}/platform/drivers/src/adc
    ${PROJ_DIR}/platform/drivers/src/power/YTM32B1Mx
    ${PROJ_DIR}/platform/drivers/src/wku
    ${PROJ_DIR}/platform/drivers/src/wdg

    ${PROJ_DIR}/platform/drivers/src/flash
    ${PROJ_DIR}/platform/drivers/inc
    ${PROJ_DIR}/platform/devices/common
    ${PROJ_DIR}/platform/devices
    ${PROJ_DIR}/platform/devices/YTM32B1ME0/include
    ${PROJ_DIR}/platform/devices/YTM32B1ME0/startup
    ${PROJ_DIR}/platform/drivers/src/lptmr
    ${PROJ_DIR}/platform/drivers/src/etmr
    ${PROJ_DIR}/platform/drivers/inc/etmr
    ${PROJ_DIR}/rtos/freertos/include
    ${PROJ_DIR}/rtos/freertos/portable/GCC/ARM_CM33_NTZ/non_secure
    ${PROJ_DIR}/rtos/osif
    ${PROJ_DIR}/CMSIS/Core/Include
    ${PROJ_DIR}/middleware/utility_print/printf

    ${PROJ_DIR}/UserApp/framework/inc
    ${PROJ_DIR}/UserApp/app/airbag
    ${PROJ_DIR}/UserApp/app/bat/inc
    ${PROJ_DIR}/UserApp/app/bt/inc
    ${PROJ_DIR}/UserApp/app/can/inc
    ${PROJ_DIR}/UserApp/app/diag/inc
    ${PROJ_DIR}/UserApp/app/ipc/inc
    ${PROJ_DIR}/UserApp/app/led/inc
    ${PROJ_DIR}/UserApp/app/log/inc
    ${PROJ_DIR}/UserApp/app/pm/inc
    ${PROJ_DIR}/UserApp/app/update/inc
    ${PROJ_DIR}/UserApp/app/common/inc
    ${PROJ_DIR}/UserApp/app/candataupload/inc
#    ${PROJ_DIR}/UserApp/app/xcall/inc

    ${PROJ_DIR}/UserApp/common/isoft/ComStack/Can
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/CanIf
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/CanSM
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/ComM
    # NEW_TODO
    # ${PROJ_DIR}/UserApp/common/isoft/ComStack/Nm
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/NmIf
    ${PROJ_DIR}/UserApp/common/isoft/ComStack/CanNm

    ${PROJ_DIR}/UserApp/common/isoft/ComStack/PduR
    ${PROJ_DIR}/UserApp/common/isoft/Config
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/CanTp
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/ComM
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsd
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsl
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsp
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/DcmDsp/UDS
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/include
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dcm/SysTimer
    ${PROJ_DIR}/UserApp/common/isoft/Diagnostic/Dem
    ${PROJ_DIR}/UserApp/common/isoft/Include
    ${PROJ_DIR}/UserApp/common/isoft/Rte
    ${PROJ_DIR}/UserApp/common/isoft/SysServices/EcuM
    ${PROJ_DIR}/UserApp/common/isoft/SysServices/MemMap
    ${PROJ_DIR}/UserApp/common/isoft/SysServices/SchM
    ${PROJ_DIR}/UserApp/common/library/inc
    ${PROJ_DIR}/UserApp/common/system/inc
    ${PROJ_DIR}/platform/RTT

    ${PROJ_DIR}/tests
)

add_library(GENERATED_SDK_TARGET STATIC ${sources})

target_include_directories(GENERATED_SDK_TARGET PUBLIC ${includes})
configcore(GENERATED_SDK_TARGET ${CMAKE_SOURCE_DIR})

target_compile_definitions(GENERATED_SDK_TARGET PUBLIC
    YTM32B1ME0
    CPU_YTM32B1ME0
    USING_OS_FREERTOS
    
)
target_compile_options(GENERATED_SDK_TARGET PUBLIC
    -fdiagnostics-color=always
)