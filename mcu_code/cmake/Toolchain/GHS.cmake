function(compilerVersion)
  execute_process(COMMAND "${CMAKE_C_COMPILER}" --version -c t.c
     OUTPUT_VARIABLE CVERSION
     ERROR_VARIABLE CVERSION
    )
  SET(COMPILERVERSION ${CVERSION} PARENT_SCOPE)
  #cmake_print_variables(CVERSION)
  #cmake_print_variables(CMAKE_C_COMPILER)
  #MESSAGE( STATUS "CMD_OUTPUT:" ${CVERSION})
endfunction()

function(compilerSpecificCompileOptions PROJECTNAME ROOT)
  #cmake_print_properties(TARGETS ${PROJECTNAME} PROPERTIES DISABLEOPTIMIZATION)
  get_target_property(DISABLEOPTIM ${PROJECTNAME} DISABLEOPTIMIZATION)
  if ((OPTIMIZED) AND (NOT DISABLEOPTIM))
    #cmake_print_variables(DISABLEOPTIM)
    target_compile_options(${PROJECTNAME} PRIVATE "-Ospeed")
  endif()
  
  if (HARDFP)
    target_compile_options(${PROJECTNAME} PUBLIC "-fhard")
  else()
    target_compile_options(${PROJECTNAME} PUBLIC "-fsoft")
  endif()
  
  if (LITTLEENDIAN)
    target_compile_options(${PROJECTNAME} PUBLIC "-littleendian")
  endif()

  if (CORTEXM OR CORTEXR)
    target_compile_options(${PROJECTNAME} PUBLIC "-thumb")
  endif()
  
  # Core specific config
  if (ARM_CPU STREQUAL "cortex-m33" )
    if(not DEVICCE_NAME STREQUAL "YTM32B1MC0")
        target_compile_options(${PROJECTNAME} PUBLIC "-fpu=vfpv5_d16")
    endif()
  endif()

  if (ARM_CPU STREQUAL "cortex-m7" )
        target_compile_options(${PROJECTNAME} PUBLIC "-fpu=fpv5_d16")
  endif()

  if (ARM_CPU STREQUAL "cortex-m4" )
        target_compile_options(${PROJECTNAME} PUBLIC "-mfpu=fpv4_d16")
  endif()
  

  if(EXPERIMENTAL)
    experimentalCompilerSpecificCompileOptions(${PROJECTNAME} ${ROOT})
  endif()
endfunction()


function(toolchainSpecificLinkForCortexM PROJECTNAME ROOT CORE PLATFORMFOLDER HASCSTARTUP)
endfunction()

function(toolchainSpecificLinkForCortexA PROJECTNAME ROOT CORE PLATFORMFOLDER)
endfunction()

function(toolchainSpecificLinkForCortexR PROJECTNAME ROOT CORE PLATFORMFOLDER)
endfunction()

function(compilerSpecificPlatformConfigLibForM PROJECTNAME ROOT)
endfunction()

function(compilerSpecificPlatformConfigLibForA PROJECTNAME ROOT)
endfunction()

function(compilerSpecificPlatformConfigLibForR PROJECTNAME ROOT)
endfunction()

function(compilerSpecificPlatformConfigAppForM PROJECTNAME ROOT)
  target_link_libraries(${PROJECTNAME} 
  "-nostartfiles"
  )
  target_compile_options(${PROJECTNAME} PRIVATE "-preprocess_assembly_files")
endfunction()

function(compilerSpecificPlatformConfigAppForA PROJECTNAME ROOT)
  
endfunction()

function(compilerSpecificPlatformConfigAppForR PROJECTNAME ROOT)
 
endfunction()