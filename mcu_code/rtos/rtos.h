
#ifndef INCLUDE_RTOS_H_
#define INCLUDE_RTOS_H_

#include "FreeRTOS.h"
#include "queue.h"
#include "task.h"
#include "timers.h"

extern QueueHandle_t xQueueCan;/*CAN FIFO for message RX */

void rtos_start( void );
void vApplicationTickHook( void );
void vApplicationStackOverflowHook( TaskHandle_t pxTask, char *pcTaskName );
void vApplicationIdleHook( void );
void vApplicationMallocFailedHook( void );



#endif /* INCLUDE_RTOS_H_ */
