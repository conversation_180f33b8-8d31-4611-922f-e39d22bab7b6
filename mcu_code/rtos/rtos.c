/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file rtos.c
 * @brief create the RTOS task and start the RTOS
 * 
 */

/*
 *
 * This simple demo project runs 'stand alone' (without the rest of the tower
 * system) on the Freedom Board or Validation Board, which is populated with a
 * S32K144 Cortex-M4 microcontroller.
 *
 * The idle hook function:
 * The idle hook function demonstrates how to query the amount of FreeRTOS heap
 * space that is remaining (see vApplicationIdleHook() defined in this file).
 *
 * The main() Function:
 * main() creates one software timer, one queue, and two tasks.  It then starts
 * the scheduler.
 *
 * The Queue Send Task:
 * The queue send task is implemented by the prvDiagTask() function in
 * this file.  prvDiagTask() sits in a loop that causes it to repeatedly
 * block for 200 milliseconds, before sending the value 100 to the queue that
 * was created within main().  Once the value is sent, the task loops back
 * around to block for another 200 milliseconds.
 *
 * The Queue Receive Task:
 * The queue receive task is implemented by the prvlMainTask() function
 * in this file.  prvlMainTask() sits in a loop that causes it to
 * repeatedly attempt to read data from the queue that was created within
 * main().  When data is received, the task checks the value of the data, and
 * if the value equals the expected 100, toggles the green LED.  The 'block
 * time' parameter passed to the queue receive function specifies that the task
 * should be held in the Blocked state indefinitely to wait for data to be
 * available on the queue.  The queue receive task will only leave the Blocked
 * state when the queue send task writes to the queue.  As the queue send task
 * writes to the queue every 200 milliseconds, the queue receive task leaves the
 * Blocked state every 200 milliseconds, and therefore toggles the blue LED
 * every 200 milliseconds.
 *
 * The LED Software Timer and the Button Interrupt:
 * The user button BTN1 is configured to generate an interrupt each time it is
 * pressed.  The interrupt service routine switches the red LED on, and
 * resets the LED software timer.  The LED timer has a 5000 millisecond (5
 * second) period, and uses a callback function that is defined to just turn the
 * LED off again.  Therefore, pressing the user button will turn the LED on, and
 * the LED will remain on until a full five seconds pass without the button
 * being pressed.
 */

/* Kernel includes. */
#include "rtos.h"
//#include "board.h"
/* SDK includes. */
#include "sdk_project_config.h"
//#include "BatApi.h"

/* Priorities at which the tasks are created. */
#define mainQUEUE_RECEIVE_TASK_PRIORITY		( tskIDLE_PRIORITY + 2 )
#define	mainQUEUE_SEND_TASK_PRIORITY		( tskIDLE_PRIORITY + 1 )

#define ADC_INST               (0U)  /* ADC instance */
#define ADC_CONV_MAX_COUNT     (0xFFFU)  /* ADC conversion max count value for 12bit resolution configuration */

/* for details, please refer to the YCT ADC configuration */
#define ADC_CH_SEQ_LEN         (4U)  /* ADC channel sequence number */
#define RUN_VOLTAGE_DET_ADCH_INDX (0U)  /* ADC channel index for run voltage detection in the convert sequence */ 
#define BAT_NTC_DET_ADCH_INDX     (1U)  /* ADC channel index for battery NTC temperature detection in the convert sequence */
#define MIC_VOLTAGE_DET_ADCH_INDX (2U)  /* ADC channel index for MIC voltage detection in the convert sequence */
#define BAT_VOLTAGE_DET_ADCH_INDX (3U)  /* ADC channel index for battery voltage detection in the convert sequence */


#define PWM_eTMR_INST          (1U)  /* PWM eTMR instance, eTMR1 is used for PWM signal generation as HW design */
#define PWM1_eTMR_CH           (0U)  /* PWM1 eTMR channel */
#define PWM2_eTMR_CH           (2U)  /* PWM2 eTMR channel */

#define CAN1_INST               (1U)  /* CAN1 instance */
#define CAN2_INST               (2U)  /* CAN2 instance */
#define CAN3_INST               (3U)  /* CAN3 instance */
#define CAN5_INST               (5U)  /* CAN5 instance */

#define CAN_TX_MB              (0U)  /* use MB0 for TX message */
#define CAN_RX_MB              (1U)  /* use MB1 for RX message */

#define CAN_TX_ID 			   (0x123U)  /* TX message ID */
#define CAN_RX_ID 			   (0x321U)  /* RX message ID */

#define CAN_ADC_TX_ID          (0x456U)  /* TX message ID for ADC convert result */

#define LONG_TIME   (10U)  /* long time for waiting the event */

/* The queue used by both tasks. */

/* queues used for CAN message receive from CAN1/2/3/5 */
QueueHandle_t xQueueCan1 = NULL;
QueueHandle_t xQueueCan2 = NULL;
QueueHandle_t xQueueCan3 = NULL;
QueueHandle_t xQueueCan5 = NULL;

QueueHandle_t xQueueAdc = NULL;

SemaphoreHandle_t xSemaphore_adc_seq_end = NULL;

/* the CAN message buffer config info for message transmit on CAN1 */
flexcan_data_info_t can1_tx_info =
{
    .msg_id_type = FLEXCAN_MSG_ID_STD,
    .enable_brs = true,
    .fd_padding = 0x00,
    .is_remote = false,
    .fd_enable = true,
    .data_length = 8
};
/* the CAN message buffer config info for message transmit on CAN2 */
flexcan_data_info_t can2_tx_info =
{
	.msg_id_type = FLEXCAN_MSG_ID_STD,
	.enable_brs = true,
	.fd_padding = 0x00,
	.is_remote = false,
	.fd_enable = true,
	.data_length = 8
};
/* the CAN message buffer config info for message transmit on CAN3 */
flexcan_data_info_t can3_tx_info =
{
	.msg_id_type = FLEXCAN_MSG_ID_STD,
	.enable_brs = true,
	.fd_padding = 0x00,
	.is_remote = false,
	.fd_enable = true,
	.data_length = 8
};
/* the CAN message buffer config info for message transmit on CAN5 */
flexcan_data_info_t can5_tx_info =
{
	.msg_id_type = FLEXCAN_MSG_ID_STD,
	.enable_brs = true,
	.fd_padding = 0x00,
	.is_remote = false,
	.fd_enable = true,
	.data_length = 8
};

/* the CAN message buffer config info for message receive on CAN1*/
flexcan_data_info_t can1_rx_info =
{
    .msg_id_type = FLEXCAN_MSG_ID_STD,
    .enable_brs = true,
    .fd_padding = 0x00,
    .is_remote = false,
    .fd_enable = true,
    .data_length = 8
};
/* the CAN message buffer config info for message receive on CAN2*/
flexcan_data_info_t can2_rx_info =
{
	.msg_id_type = FLEXCAN_MSG_ID_STD,
	.enable_brs = true,
	.fd_padding = 0x00,
	.is_remote = false,
	.fd_enable = true,
	.data_length = 8
};
/* the CAN message buffer config info for message receive on CAN3*/
flexcan_data_info_t can3_rx_info =
{
	.msg_id_type = FLEXCAN_MSG_ID_STD,
	.enable_brs = true,
	.fd_padding = 0x00,
	.is_remote = false,
	.fd_enable = true,
	.data_length = 8
};
/* the CAN message buffer config info for message receive on CAN5*/
flexcan_data_info_t can5_rx_info =
{
	.msg_id_type = FLEXCAN_MSG_ID_STD,
	.enable_brs = true,
	.fd_padding = 0x00,
	.is_remote = false,
	.fd_enable = true,
	.data_length = 8
};

/* the CAN message buffer for message receive from CAN1 */
flexcan_msgbuff_t can1_rx_msg =
{
    .msgId = CAN_RX_ID,
    .dataLen = 8,
    .data = {0},
};
/* the CAN message buffer for message receive from CAN2 */
flexcan_msgbuff_t can2_rx_msg =
{
	.msgId = CAN_RX_ID,
	.dataLen = 8,
	.data = {0},
};
/* the CAN message buffer for message receive from CAN3 */
flexcan_msgbuff_t can3_rx_msg =
{
	.msgId = CAN_RX_ID,
	.dataLen = 8,
	.data = {0},
};
/* the CAN message buffer for message receive from CAN5 */
flexcan_msgbuff_t can5_rx_msg =
{
	.msgId = CAN_RX_ID,
	.dataLen = 8,
	.data = {0},
};

/* data to be sent by CAN1/CAN2/CAN3/CAN5 */
uint8_t can1_tx_data[] = {1, 2, 3, 4, 5, 6, 7, 8};
uint8_t can2_tx_data[] = {1, 2, 3, 4, 5, 6, 7, 8};
uint8_t can3_tx_data[] = {1, 2, 3, 4, 5, 6, 7, 8};
uint8_t can5_tx_data[] = {1, 2, 3, 4, 5, 6, 7, 8};

static void prvAdcCoverTask( void *pvParameters );
static void prvDiagTask( void *pvParameters );
static void prvlMainTask( void *pvParameters );
static void prvCanRxTask(void *pvParameters );
/*-----------------------------------------------------------*/

/* callback to handle the CAN message received from CAN1 */
void CAN1_CallBack(uint8_t instance, flexcan_event_type_t eventType,
                                   uint32_t buffIdx, flexcan_state_t *flexcanState)
{
	BaseType_t xHigherPriorityTaskWoken;

	if((eventType == FLEXCAN_EVENT_RX_COMPLETE) && (CAN_RX_MB == buffIdx))
	{
		/* send the data to the queue */
		xQueueSendFromISR( xQueueCan1, &can1_rx_msg, &xHigherPriorityTaskWoken );

		/* re-start the CAN receive*/
    	FLEXCAN_DRV_Receive(CAN1_INST, CAN_RX_MB, &can1_rx_msg);
	}

	if( xHigherPriorityTaskWoken )
	{
		portYIELD_FROM_ISR(true);
	}
}

/* callback to handle the CAN message received from CAN2 */
void CAN2_CallBack(uint8_t instance, flexcan_event_type_t eventType,
								   uint32_t buffIdx, flexcan_state_t *flexcanState)
{
	BaseType_t xHigherPriorityTaskWoken;

	if((eventType == FLEXCAN_EVENT_RX_COMPLETE) && (CAN_RX_MB == buffIdx))
	{
		/* send the data to the queue */
		xQueueSendFromISR( xQueueCan2, &can2_rx_msg, &xHigherPriorityTaskWoken );

		/* re-start the CAN receive*/
		FLEXCAN_DRV_Receive(CAN2_INST, CAN_RX_MB, &can2_rx_msg);
	}

	if( xHigherPriorityTaskWoken )
	{
		portYIELD_FROM_ISR(true);
	}
}

/* callback to handle the CAN message received from CAN3 */
void CAN3_CallBack(uint8_t instance, flexcan_event_type_t eventType,
								   uint32_t buffIdx, flexcan_state_t *flexcanState)
{
	BaseType_t xHigherPriorityTaskWoken;

	if((eventType == FLEXCAN_EVENT_RX_COMPLETE) && (CAN_RX_MB == buffIdx))
	{
		/* send the data to the queue */
		xQueueSendFromISR( xQueueCan3, &can3_rx_msg, &xHigherPriorityTaskWoken );

		/* re-start the CAN receive*/
		FLEXCAN_DRV_Receive(CAN3_INST, CAN_RX_MB, &can3_rx_msg);
	}

	if( xHigherPriorityTaskWoken )
	{
		portYIELD_FROM_ISR(true);
	}
}

/* callback to handle the CAN message received from CAN5 */
void CAN5_CallBack(uint8_t instance, flexcan_event_type_t eventType,
								   uint32_t buffIdx, flexcan_state_t *flexcanState)
{
	BaseType_t xHigherPriorityTaskWoken;

	if((eventType == FLEXCAN_EVENT_RX_COMPLETE) && (CAN_RX_MB == buffIdx))
	{
		/* send the data to the queue */
		xQueueSendFromISR( xQueueCan5, &can5_rx_msg, &xHigherPriorityTaskWoken );

		/* re-start the CAN receive*/
		FLEXCAN_DRV_Receive(CAN5_INST, CAN_RX_MB, &can5_rx_msg);
	}

	if( xHigherPriorityTaskWoken )
	{
		portYIELD_FROM_ISR(true);
	}
}

/* the ADC0 ISR: read the covert result to the user data buffer and set the flag */
// void ADC0_IRQHandler(void)
// {
//     int i = 0;
// 	uint16_t adc_conv_result;
// 	BaseType_t xHigherPriorityTaskWoken;


//     ADC_DRV_ClearEoseqFlagCmd(ADC_INST);

//     for (i = 0; i < ADC_CH_SEQ_LEN; i++)
//     {
//     	/* read the data */
//         adc_conv_result = ADC_DRV_ReadFIFO(ADC_INST);

// 		switch (i) 
// 		{
// 			case RUN_VOLTAGE_DET_ADCH_INDX:
// 				UpdateCommonInfoBpPlusAdcValue(adc_conv_result);
// 				break;
// 			case BAT_NTC_DET_ADCH_INDX:
// 				UpdateCommonInfoBatTempAdcValue(adc_conv_result);
// 				break;
// 			case MIC_VOLTAGE_DET_ADCH_INDX:
// 				UpdateCommonInfoMicAdcValue(adc_conv_result);
// 				break;
// 			case BAT_VOLTAGE_DET_ADCH_INDX:
// 				UpdateCommomInfoBatVoltAdcvalue(adc_conv_result);
// 				break;
// 			default:
// 			break;
// 		}
// 		/* send the data to the queue */
// 		xQueueSendFromISR( xQueueAdc, &adc_conv_result, &xHigherPriorityTaskWoken );
//     }

//     /* set the flag to notice the app */
// 	xSemaphoreGiveFromISR( xSemaphore_adc_seq_end, &xHigherPriorityTaskWoken );

// 	if( xHigherPriorityTaskWoken )
// 	{
// 		portYIELD_FROM_ISR(true);
// 	}
// }

/*
* it's unecessary initialize all the used clock and peripherals as well as the corresponding interrupt w/ proper priority
* as they are done in Board_Init() called at the beginning of main() 
*/
#if 0
static void prvSetupHardware( void )
{
    /* Initialize and configure clocks
     *  -   Setup system clocks, dividers
     *  -   see clock manager component for more details
     */
	status_t ret = STATUS_SUCCESS;

	/* configure the transceiver(PHY) to work in normal mode at first */
	CAN1_PHY_NORMAL_MODE();
	CAN2_PHY_NORMAL_MODE();
	CAN3_PHY_NORMAL_MODE();
	CAN5_PHY_NORMAL_MODE();

	/* configure the CAN1/2/3/5 TX MB */
    ret |= FLEXCAN_DRV_ConfigTxMb(CAN1_INST, CAN_TX_MB, &can1_tx_info, CAN_TX_ID);
	ret |= FLEXCAN_DRV_ConfigTxMb(CAN2_INST, CAN_TX_MB, &can2_tx_info, CAN_TX_ID);
	ret |= FLEXCAN_DRV_ConfigTxMb(CAN3_INST, CAN_TX_MB, &can3_tx_info, CAN_TX_ID);
	ret |= FLEXCAN_DRV_ConfigTxMb(CAN5_INST, CAN_TX_MB, &can5_tx_info, CAN_TX_ID);

    /* configure the CAN1/2/3/5 RX MB */
    ret |= FLEXCAN_DRV_ConfigRxMb(CAN1_INST, CAN_RX_MB, &can1_rx_info, CAN_RX_ID);
	ret |= FLEXCAN_DRV_ConfigRxMb(CAN2_INST, CAN_RX_MB, &can2_rx_info, CAN_RX_ID);
	ret |= FLEXCAN_DRV_ConfigRxMb(CAN3_INST, CAN_RX_MB, &can3_rx_info, CAN_RX_ID);
	ret |= FLEXCAN_DRV_ConfigRxMb(CAN5_INST, CAN_RX_MB, &can5_rx_info, CAN_RX_ID);

	/* install the CAN message TX/RX MB interrupt callback for CAN1/2/3/5 */
	FLEXCAN_DRV_InstallEventCallback(CAN1_INST, CAN1_CallBack, NULL);
	FLEXCAN_DRV_InstallEventCallback(CAN2_INST, CAN1_CallBack, NULL);
	FLEXCAN_DRV_InstallEventCallback(CAN3_INST, CAN1_CallBack, NULL);
	FLEXCAN_DRV_InstallEventCallback(CAN5_INST, CAN1_CallBack, NULL);

	if (ret != (uint16_t)STATUS_SUCCESS)
	{
		/*TODO board init failed*/
	}
}


void rtos_start( void )
{
	BaseType_t ret;
	/* Configure the NVIC, LED outputs and button inputs. */
	prvSetupHardware();  

	/* Create the queue for ADC convert result. */
	xQueueAdc = xQueueCreate(ADC_CH_SEQ_LEN, sizeof(uint16_t));
	if( xQueueAdc == NULL )
	{
		/*TODO queue_create failed*/
	}

	/* Create the queues for CAN1/2/3/5 message receive */
	xQueueCan1 = xQueueCreate(4, sizeof(flexcan_msgbuff_t)); 
	if( xQueueCan1 == NULL )
	{
		/*TODO queue_create failed*/
	}
	xQueueCan2 = xQueueCreate(4, sizeof(flexcan_msgbuff_t));
	if( xQueueCan2 == NULL )
	{
		/*TODO queue_create failed*/
	}
	xQueueCan3 = xQueueCreate(4, sizeof(flexcan_msgbuff_t));
	if( xQueueCan3 == NULL )
	{
		/*TODO queue_create failed*/
	}
	xQueueCan5 = xQueueCreate(4, sizeof(flexcan_msgbuff_t));
	if( xQueueCan5 == NULL )
	{
		/*TODO queue_create failed*/
	}
	else
	{
		/* Start the two tasks as described in the comments at the top of this file. */

		ret =  xTaskCreate(prvlMainTask,  "Main",  128U, NULL, 4U, NULL);
		if (ret != pdPASS)
		{
			/*TODO task_create failed*/
		}
		ret = xTaskCreate(prvDiagTask,     "Diag",     128U, NULL, 3U, NULL);
		if (ret != pdPASS)
		{
			/*TODO task_create failed*/
		}
		ret = xTaskCreate(prvCanRxTask,    "CanRx",    128U, NULL, 2U, NULL);
		if (ret != pdPASS)
		{
			/*TODO task_create failed*/
		}
		ret = xTaskCreate(prvAdcCoverTask, "AdcCover", 128U, NULL, 1U, NULL);
		if (ret != pdPASS)
		{
			/*TODO task_create failed*/
		}
		/* Start the tasks and timer running. */
		vTaskStartScheduler();
	}

	/* If all is well, the scheduler will now be running, and the following line
	will never be reached.  If the following line does execute, then there was
	insufficient FreeRTOS heap memory available for the idle and/or timer tasks
	to be created.  See the memory management section on the FreeRTOS web site
	for more details. */

	for( ;; )
	{;}
}
#endif
/*-----------------------------------------------------------*/


/*-----------------------------------------------------------*/

static void prvAdcCoverTask( void *pvParameters )
{
	/* Casting xTimer to void because it is unused */
	(void)pvParameters;
	uint8_t i = 0;
	volatile uint16_t adc_conv_result;

	/* create the semaphore */
	xSemaphore_adc_seq_end = xSemaphoreCreateBinary();

	for(;;)
	{
		/* trigger the ADC convert */
    	ADC_DRV_Start(ADC_INST);

		/* wait for the convert end and get the convert result */
		if( xSemaphoreTake( xSemaphore_adc_seq_end, LONG_TIME ) == pdTRUE )
		{
			for(i = 0; i < ADC_CH_SEQ_LEN; i++)
			{
				if(pdTRUE == xQueueReceive( xQueueAdc, &adc_conv_result, LONG_TIME ))
				{

					if(RUN_VOLTAGE_DET_ADCH_INDX == i)
					{
						/* send out the RUN voltage sample result via the CAN1 */
						FLEXCAN_DRV_SendBlocking(CAN1_INST,  CAN_TX_MB, &can1_tx_info, CAN_ADC_TX_ID, (uint8_t *)&adc_conv_result,2);
					}
				}
			}
		}

		/* delay 100ms */
		vTaskDelay(100); 
	}

}

#if 0
static void prvDiagTask( void *pvParameters )
{
	(void)pvParameters;
	for( ;; )
	{
		/* below codes are just for the test purpose */
		if(STATUS_HIGH == AIRBAG_INPUT_STATUS_GET())
		{
			/* wake the T106 4G module */
			MCU_WAKEUP_T106_HIGH();
		}
		else
		{
			/* reset the T106 4G module wakeup control pin */
			MCU_WAKEUP_T106_LOW();
		}

		MCU_RST_GNSS_TOGGLE();  /* toggle IO for debug */

		vTaskDelay(200);/* delay 200ms */
	}
}
#endif


/*-----------------------------------------------------------*/

static void prvlMainTask( void *pvParameters )
{
	/* Casting pvParameters to void because it is unused */
	(void)pvParameters;
	for( ;; )
	{
		/* send a CAN message from CAN1/2/3/5 for test */
		FLEXCAN_DRV_SendBlocking(CAN1_INST, CAN_TX_MB, &can1_tx_info, CAN_TX_ID, can1_tx_data, 10);
		FLEXCAN_DRV_SendBlocking(CAN2_INST, CAN_TX_MB, &can2_tx_info, CAN_TX_ID, can2_tx_data, 10);
		FLEXCAN_DRV_SendBlocking(CAN3_INST, CAN_TX_MB, &can3_tx_info, CAN_TX_ID, can3_tx_data, 10);
		FLEXCAN_DRV_SendBlocking(CAN5_INST, CAN_TX_MB, &can5_tx_info, CAN_TX_ID, can5_tx_data, 10);

		PRINTF("The main task is running!\n");

		vTaskDelay(20);/* delay 10ms */
		
	}
}


/***********************************************************/
static void prvCanRxTask(void *pvParameters )
{
	/* Casting pvParameters to void because it is unused */
	(void)pvParameters;

	flexcan_msgbuff_t can_received_msg;

	/* start the 1st CAN message receive of CAN1/2/3/5 */
    FLEXCAN_DRV_Receive(CAN1_INST, CAN_RX_MB, &can1_rx_msg);
	FLEXCAN_DRV_Receive(CAN2_INST, CAN_RX_MB, &can2_rx_msg);
	FLEXCAN_DRV_Receive(CAN3_INST, CAN_RX_MB, &can3_rx_msg);
	FLEXCAN_DRV_Receive(CAN5_INST, CAN_RX_MB, &can5_rx_msg);

	for( ;; )
	{
		/* check whether the CAN1 has received the message sent by CAN1 */
		if(pdTRUE == xQueueReceive( xQueueCan1, &can_received_msg, LONG_TIME ))
		{
			/* send the received message to the CAN1 */
			FLEXCAN_DRV_SendBlocking(CAN1_INST,  CAN_TX_MB, &can1_tx_info,can_received_msg.msgId, can_received_msg.data,10);
		}

		/* check whether the CAN1 has received the message sent by CAN2 */
		if(pdTRUE == xQueueReceive( xQueueCan2, &can_received_msg, LONG_TIME ))
		{
			/* send the received message to the CAN2 */
			FLEXCAN_DRV_SendBlocking(CAN2_INST,  CAN_TX_MB, &can2_tx_info,can_received_msg.msgId, can_received_msg.data,10);
		}

		/* check whether the CAN1 has received the message sent by CAN3 */
		if(pdTRUE == xQueueReceive( xQueueCan3, &can_received_msg, LONG_TIME ))
		{
			/* send the received message to the CAN3 */
			FLEXCAN_DRV_SendBlocking(CAN3_INST,  CAN_TX_MB, &can3_tx_info,can_received_msg.msgId, can_received_msg.data,10);
		}

		/* check whether the CAN1 has received the message sent by CAN5 */
		if(pdTRUE == xQueueReceive( xQueueCan5, &can_received_msg, LONG_TIME ))
		{
			/* send the received message to the CAN5 */
			FLEXCAN_DRV_SendBlocking(CAN5_INST,  CAN_TX_MB, &can5_tx_info,can_received_msg.msgId, can_received_msg.data,10);
		}

		vTaskDelay(10);/* delay 10ms */

	}
}

/*-----------------------------------------------------------*/

void vApplicationMallocFailedHook( void )
{
	/* Called if a call to pvPortMalloc() fails because there is insufficient
	free memory available in the FreeRTOS heap.  pvPortMalloc() is called
	internally by FreeRTOS API functions that create tasks, queues, software
	timers, and semaphores.  The size of the FreeRTOS heap is set by the
	configTOTAL_HEAP_SIZE configuration constant in FreeRTOSConfig.h. */
	taskDISABLE_INTERRUPTS();
	for( ;; ) {;}
}
/*-----------------------------------------------------------*/

void vApplicationStackOverflowHook( TaskHandle_t pxTask, char *pcTaskName )
{
	( void ) pcTaskName;
	( void ) pxTask;

	/* Run time stack overflow checking is performed if
	configCHECK_FOR_STACK_OVERFLOW is defined to 1 or 2.  This hook
	function is called if a stack overflow is detected. */
	taskDISABLE_INTERRUPTS();
	for( ;; ) {;}
}
/*-----------------------------------------------------------*/

void vApplicationIdleHook( void )
{
    volatile size_t xFreeHeapSpace;

	/* This function is called on each cycle of the idle task.  In this case it
	does nothing useful, other than report the amount of FreeRTOS heap that
	remains unallocated. */
	xFreeHeapSpace = xPortGetFreeHeapSize();

	if( xFreeHeapSpace > 100 )
	{
		/* By now, the kernel has allocated everything it is going to, so
		if there is a lot of heap remaining unallocated then
		the value of configTOTAL_HEAP_SIZE in FreeRTOSConfig.h can be
		reduced accordingly. */
	}

}
/*-----------------------------------------------------------*/

/* The Blinky build configuration does not include run time stats gathering,
however, the Full and Blinky build configurations share a FreeRTOSConfig.h
file.  Therefore, dummy run time stats functions need to be defined to keep the
linker happy. */
void vMainConfigureTimerForRunTimeStats( void ) {}
unsigned long ulMainGetRunTimeCounterValue( void ) { return 0UL; }

/* A tick hook is used by the "Full" build configuration.  The Full and blinky
build configurations share a FreeRTOSConfig.h header file, so this simple build
configuration also has to define a tick hook - even though it does not actually
use it for anything. */
void vApplicationTickHook( void ) {}

/*-----------------------------------------------------------*/
